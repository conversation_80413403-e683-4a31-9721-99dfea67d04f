<template>
   <el-card class="box-card">
      <template #header>
         <div style="display: flex; justify-content: space-between">
            <el-button v-if="!modelValue" @click="selectCropperImage" class="button" type="text">
               <input style="position: fixed; left: -9999px; display: none" id="imgReader" ref="upload" type="file"
                  accept="image/*" @change="loadCropperImage" :value="upload_input" />
               裁剪上传
            </el-button>
            <el-button v-if="!modelValue" class="button" type="text">
               <el-upload :show-file-list="false" :http-request="httpUpload" accept="image/jpeg,image/jpg,image/png">
                  原图上传
               </el-upload>
            </el-button>
            <el-button v-else class="button" type="text" @click="deletePhoto">删除</el-button>
         </div>
      </template>
      <el-image style="height: 100px; width: 200px" fit="contain" :preview-src-list="[imgServer + modelValue]"
         :src="imgServer + modelValue">
         <template #error>
            <div class="image-slot">
               <el-icon><icon-picture /></el-icon>
            </div>
         </template>
      </el-image>
   </el-card>

   <el-dialog draggable v-model="cropPic" title="裁剪图像">
      <div style="display: flex">
         <img id="cropImg" />
      </div>
      <div>
         <div style="display: flex; margin-top: 10px">
            <el-button type="primary" @click="uploadCropImg">确认</el-button>
         </div>
      </div>
   </el-dialog>
</template>

<script>
import "cropperjs/dist/cropper.css";
import Cropper from "cropperjs";
import { fileUpload, fileRemove } from "@/api/admin/file";
//import mitt from "@/utils/mitt";

export default {
   props: ["modName", "funcName", "isCommunity", "modelValue"],
   components: {
   },
   data() {
      return {
         loading: false,
         imgServer: import.meta.env.VITE_BASE_API,
         //imageUrl: this.modelValue,
         upload_input: "",
         cropPic: false,
         CROPPER: null
      };
   },
   methods: {
      deletePhoto() {
         if (this.modelValue) {
            let data = {};
            data.fileUrl = this.modelValue;
            fileRemove(data).then((res) => {
               let data = res.data;
               if (data.code == 0) {
                  //this.imageUrl = "";
                  this.$message.success("删除成功");
                  this.$emit("update:modelValue", "");
               } else {
                  this.$message.error(data.msg);
               }
            });
         }
      },
      httpUpload(files) {
         let form = new FormData();
         form.append("file", files.file);
         this.imgUpload(form);
      },
      imgUpload(form) {
         // let form = new FormData();
         // form.append("file", files.file);
         if (!this.modName || !this.funcName) {
            console.warn("图像上传组件未配置正确的参数！");
            this.$message.warn("图像上传参数不正确！");
            return;
         }
         form.append("modulesName", this.modName);
         form.append("functionName", this.funcName);
         if (this.isCommunity == true)
            form.append("communityId", localStorage.getItem("communityId"));
         fileUpload(form).then((res) => {
            let data = res.data;
            if (data.code == 0) {
               //this.imageUrl = data.result.url;
               this.$message.success("上传成功");
               this.$emit("update:modelValue", data.result.url);
            } else {
               this.$message.error(data.msg);
            }
         });
      },
      loadCropperImage(eve) {
         this.upload_input = eve.target.files[0];
         this.cropPic = true;
         //读取上传文件
         let reader = new FileReader();
         if (this.upload_input) {
            //readAsDataURL方法可以将File对象转化为data:URL格式的字符串（base64编码）
            reader.readAsDataURL(this.upload_input);
            reader.onload = (e) => {
               let dataURL = reader.result;
               //将img的src改为刚上传的文件的转换格式
               //document.querySelector("#cropImg").src = dataURL;
               const elImage = document.getElementById("cropImg");
               elImage.src = dataURL;
               //创建cropper实例-----------------------------------------
               this.CROPPER = new Cropper(elImage, {
                  // aspectRatio: 16 / 16,
                  initialAspectRatio: 1 / 1,
                  viewMode: 1,
                  autoCropArea: 1,
                  minCanvasWidth: 100,
                  minCanvasHeight: 100,
                  // minContainerWidth:500,
                  // minContainerHeight:500,
                  dragMode: "move",
               });
            };
         }
         this.upload_input = "";
      },
      selectCropperImage() {
         document.querySelector("#imgReader").click();
         if (this.CROPPER) {
            this.CROPPER.destroy();
            this.CROPPER = null;
         }
      },
      uploadCropImg() {
         this.cropPic = false;
         //getCroppedCanvas方法可以将裁剪区域的数据转换成canvas数据
         this.CROPPER.getCroppedCanvas({
            maxWidth: 800,
            maxHeight: 800,
            fillColor: "#fff",
            imageSmoothingEnabled: true,
            imageSmoothingQuality: "high",
         }).toBlob(
            (blob) => {
               //然后调用浏览器原生的toBlob方法将canvas数据转换成blob数据
               //之后就可以愉快的将blob数据发送至后端啦，可根据自己情况进行发送，我这里用的是axios
               const form = new FormData();
               // 第三个参数为文件名，可选填.
               form.append("file", blob, "example.jpg");
               this.imgUpload(form);
            },
            "image/jpeg",
            1
         );
      }
   },
   mounted() {
   },
   created() {
   },
};
</script>

<style scoped>
#cropImg {
   height: 800px;
   width: 800px;
   display: block;
   overflow: hidden;
   box-shadow: 0 0 5px #adadad;
}

.box-card {
   width: 100%;
}
</style>