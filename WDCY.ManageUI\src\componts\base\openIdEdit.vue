<template>
    <el-dialog draggable width="25%" v-model="dialog.show" :title="dialog.title">
    <el-form :model="openIdModel" label-width="80px">

          <el-form-item label="OpenId">
            <el-input v-model="openIdModel.openId" placeholder="OpenId" clearable></el-input>
          </el-form-item>

      <el-row justify="center">
        <el-button type="primary" style="width: 100px; height: 30px; margin-top: 20px" @click="submit">提 交</el-button>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script>
import mitt from "@/utils/mitt";
import { editPersonOpenId } from "@/api/base/person"
import { editVehicleOpenId } from "@/api/base/vehicle"
import { ElMessage } from 'element-plus';
export default{
    props:{

    },
    data(){
        return{
            dialog: {},
            openIdModel:{

            },
            type:''
        }
    },
    methods:{
        submit(){
            switch(this.type){
                case 'person':
                    editPersonOpenId(this.openIdModel).then(res => {
                        ElMessage.success({
                            message: res.data.msg
                        })
                        this.$emit("search")
                        this.dialog.show = false
                    })
                break;
                case 'vehicle':
                    editVehicleOpenId(this.openIdModel).then(res => {
                        ElMessage.success({
                            message: res.data.msg
                        })
                        this.$emit("search")
                        this.dialog.show = false
                    })
                break;
                case 'community':
                    console.log(345);
                break;
                case 'building':
                    console.log(456);
                break;
                default:
                break;
            }
        }
    },
    mounted() {
    this.$nextTick(function () {
      mitt.on("openOpenIdEdit", (data) => {
        this.openIdModel.openId = data.openId
        this.openIdModel.id = data.id
        this.type = data.type
        this.dialog.show = true;
        this.dialog.title = "修改OpenId";
      });
    });
  },
}
</script>