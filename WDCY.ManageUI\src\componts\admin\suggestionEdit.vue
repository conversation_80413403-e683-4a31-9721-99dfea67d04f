<template>
    <el-dialog draggable width="50%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" :model="suggestionInfo" label-width="100px">
			<el-row>
				<el-col :span="24">
					<el-form-item label="回复内容" prop="title">
						<el-input v-model="suggestionInfo.content" placeholder="标题"></el-input>
					</el-form-item>
				</el-col>
                <el-col :span="6">
					<el-form-item label="是否公开" prop="publicType">
						<el-select style="width: 100%" v-model="suggestionInfo.publicType" placeholder="是否公开">
                            <el-option v-for="item in openList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
                        </el-select>
					</el-form-item>
				</el-col>
				<!-- <el-col :span="12">
					<el-form-item label="类型" prop="type">
						  <el-select style="width: 100%;" v-model="noticeModel.noticeType" class="m-2" placeholder="类型">
						    <el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						  </el-select>
					</el-form-item>
				</el-col> -->
			</el-row>
            <!-- <el-row>
                <el-col :span="12">
                    <el-form-item label="类型" prop="status">
						  <el-select style="width: 100%;" v-model="noticeModel.status" class="m-2" placeholder="状态">
						    <el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						  </el-select>
					</el-form-item>
                </el-col>
            </el-row>
			<el-row>
				<div id="editor">
					
				</div>
                <textarea name="" id="" cols="170" rows="20" style="width:100%" v-model="editorData"></textarea>
			</el-row> -->
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
		</el-row>
	</el-dialog>
</template>
<script>
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import { editSuggestion } from "@/api/admin/opinion"
export default{
    props:['typeList','statusList','openList'],
    data(){
        return{
            loading: false,
            dialog: {},
            noticeModel: {},
            suggestionInfo: {}

        }
    },
    methods:{
        getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
        onSubmit(){
            if (this.suggestionInfo.id == 0) {
                editSuggestion(this.suggestionInfo)
                .then(res => {
                    this.$message.success(res.data.msg)
                    this.$emit("search")
                    this.dialog.show = false
                })
            } else {
                delete(this.suggestionInfo.createTime)
                delete(this.suggestionInfo.replyContent)
                delete(this.suggestionInfo.replyTime)
                delete(this.suggestionInfo.replyUserId)
                delete(this.suggestionInfo.wxUserId)
                // this.suggestionInfo.id = Number(this.suggestionInfo.id)
                editSuggestion(this.suggestionInfo)
                .then(res => {
                    console.log(res);
                    this.$message.success(res.data.msg)
                    this.$emit("search")
                    this.dialog.show = false
                })
            }
        }
    },
    mounted(){
        this.$nextTick(function(){
            mitt.on("openSuggestionEdit",(result)=>{
                this.suggestionInfo = result
                this.suggestionInfo.content = this.suggestionInfo.replyContent
                this.dialog.show = true
                this.dialog.title = "修改公告"
            } )
            mitt.on("openNoticeAdd",()=>{
                this.noticeModel = {
                    id:0
                }
                this.dialog.show = true
                this.dialog.title = "发布公告"
            })
        })
    }
}
</script>