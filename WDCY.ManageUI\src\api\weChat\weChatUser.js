import request from '@/utils/request'

export const weChatList = (data) =>
	request({
		url: '/wxUserInfo/page',
		method: 'get',
		params: data
	})
export const getWeChat = (data) =>
	request({
		url: '/wxUserInfo',
		method: 'get',
		params: data
	})
export const weChatPersonList = (data) =>
	request({
		url: '/wxUserInfo/tourist',
		method: 'get',
		params: data
	})

export const weChatVehicleList = (data) =>
	request({
		url: '/wxUserInfo/vehicle',
		method: 'get',
		params: data
	})

export const wxBindMenu = (data) =>
	request({
		url: '/wxUserInfo/bindRoleAndGroup',
		method: 'put',
		data: data
	})
export const wChatUserRemove = (data) =>
	request({
		url: '/wxUserInfo/clearUserRole',
		method: 'post',
		data: data
	})