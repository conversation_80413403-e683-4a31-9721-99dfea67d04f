<template>
	<warn-event-source-info-edit @search="search" :checkTypeList="checkTypeList" :statusList="statusList"></warn-event-source-info-edit>
	<check-rule-edit @search="search"></check-rule-edit>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.name" placeholder="事件源名称" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="12">
			<el-button style="float: right;" type="primary" @click="add" v-if="hasPerm('warn:warnSourceInfo:add')">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col>
			<el-table :data="personList" border style="width: 100%">
				<el-table-column prop="triggerType" align="center" width="100" :formatter="formatWarnTriggerType" label="触发类型" />
				<el-table-column prop="typeName" width="180" align="left" header-align="center" label="事件源类型" />
				<el-table-column prop="name" width="240" align="left" header-align="center" label="事件源名称" />
				<el-table-column prop="note" align="left" header-align="center" label="规则描述模板" />
				<!-- <el-table-column prop="extendParams" align="center" label="扩展参数" /> -->
				<el-table-column prop="status" align="center" label="状态" width="95">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column align="center" width="240" label="操作" v-if="hasPerm('warn:detectionRule:detail') || hasPerm('warn:warnSourceInfo:copy') || hasPerm('warn:warnSourceInfo:update') || hasPerm('warn:warnSourceInfo:delete')">
					<template #default="scope">
						<el-button type="text" size="default" @click="openCheckRuleEdit(scope.row.id)" v-if="hasPerm('warn:detectionRule:detail')">检测规则</el-button>
						<el-button type="text" size="default" @click="copy(scope.row.id)" v-if="hasPerm('warn:warnSourceInfo:copy')">复制副本</el-button>
						<el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('warn:warnSourceInfo:update')">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('warn:warnSourceInfo:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>

<script>
import { warnEventSourceInfoList,warnEventSourceInfoDelete,getWarnEventSourceInfo,copyEctype,warnEventSourceInfoCheckTypeList } from "@/api/warn/warn"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import warnEventSourceInfoEdit from "@/componts/warn/warnEventSourceInfoEdit.vue"
import checkRuleEdit from "@/componts/warn/checkRuleEdit.vue"
export default {
	components: {
		warnEventSourceInfoEdit,
		checkRuleEdit
	},
	data() {
		return {
			searchModel: {},
			communityId:localStorage.getItem("communityId"),
			personList: [],
			statusList: [],
			warnTriggerTypeList: [],
			checkTypeList:[],
			total: 0,
			pageSize:10
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatWarnTriggerType(row, column, cellValue, index) {
			return formatDict(this.warnTriggerTypeList, cellValue)
		},
		search() {
			this.searchModel.communityId=this.communityId
			warnEventSourceInfoList(this.searchModel)
				.then(res => {
					this.personList = res.data.result.list
					this.total = res.data.result.total
				})
		},
		copy(id) {
			this.$confirm('复制副本, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				copyEctype({id:id})
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => {})
		},
		openCheckRuleEdit(id){
			getWarnEventSourceInfo(id)
				.then(res => {
					mitt.emit('openCheckRuleEdit',res.data.result)
				})
		},
		edit(id) {
			getWarnEventSourceInfo(id)
				.then(res => {
					console.log(res,id);
					mitt.emit('openWarnEventSourceInfoEdit', res.data.result)
				})
		},
		add() {
			mitt.emit('openWarnEventSourceInfoAdd')
		},
		deleted(id) {
			this.$confirm('删除记录, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				warnEventSourceInfoDelete(id)
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => {})
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init() {
			mitt.off('openWarnEventSourceInfoEdit')
			mitt.off('openWarnEventSourceInfoAdd')
			try {
				let checkType_res = await warnEventSourceInfoCheckTypeList()
				this.checkTypeList = checkType_res.data.result
				this.searchModel.communityId=this.communityId
				let res = await warnEventSourceInfoList(this.searchModel)
				this.personList = res.data.result.list
				this.total = res.data.result.total
				let status_res = await listDictByNameEn('common_status')
				this.statusList = status_res.data.result
				
				let warnTriggerTypes = await listDictByNameEn('warn_trigger_type')
				this.warnTriggerTypeList = warnTriggerTypes.data.result
			} catch (err) {
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
