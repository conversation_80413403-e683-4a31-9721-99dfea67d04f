<template>
    <div class="app-container">
      <el-row :gutter="20" v-show="show" style="
		background-color: #fff;
		padding: 12px 10px;
    margin: 0 15px;
		border-radius: 5px;">
        <el-col :span="3">
          <el-input v-model="searchModel.keyword" @keydown.enter="search" placeholder="名称" clearable />
        </el-col>
        <el-col :span="3">
          <el-input v-model="searchModel.filter" @keydown.enter="search" placeholder="过滤" clearable />
        </el-col>
        <el-col :span="2">
          <el-button style="float: right;" type="primary" @click="search">搜 索</el-button>
        </el-col>
      </el-row>
      <el-row v-show="!show">
        <el-col :span="12" class="card-box">
          <el-card>
            <div slot="header" style="border-bottom: 1px solid #eee; line-height: 44px;"><span>堆内存</span></div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%;">
                <thead>
                  <tr>
                    <th style="width:50%" class="is-leaf"><div class="cell">属性</div></th>
                    <th style="width:50%" class="is-leaf"><div class="cell">内存</div></th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><div class="cell">初始内存</div></td>
                    <td><div class="cell" v-if="heapMemory.init">{{ heapMemory.init.toFixed(2) }}</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">已用内存</div></td>
                    <td><div class="cell" v-if="heapMemory.used">{{heapMemory.used.toFixed(2)}}M</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">总内存</div></td>
                    <td><div class="cell" v-if="heapMemory.committed">{{ heapMemory.committed }}M</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">最大内存</div></td>
                    <td><div class="cell" v-if="heapMemory.max">{{ heapMemory.max }}M</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">使用率</div></td>
                    <td><div class="cell" v-if="heapMemory.usage" >{{ heapMemory.usage.toFixed(2) }}%</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </el-card>
        </el-col>
  
        <el-col :span="12" class="card-box">
          <el-card>
            <div slot="header" style="border-bottom: 1px solid #eee; line-height: 44px"><span>栈内存</span></div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%;">
                <thead>
                  <tr>
                    <th style="width:33%" class="is-leaf"><div class="cell">属性</div></th>
                    <th style="width:33%" class="is-leaf"><div class="cell">内存</div></th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><div class="cell">初始内存</div></td>
                    <td><div class="cell" v-if="stackMemory.init">{{ stackMemory.init.toFixed(2) }}</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">已用内存</div></td>
                    <td><div class="cell" v-if="stackMemory.used">{{stackMemory.used.toFixed(2)}}M</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">总内存</div></td>
                    <td><div class="cell" v-if="stackMemory.committed">{{ stackMemory.committed.toFixed(2) }}M</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">最大内存</div></td>
                    <td><div class="cell" v-if="stackMemory.max">{{ stackMemory.max }}</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">使用率</div></td>
                    <td><div class="cell" v-if="stackMemory.usage" >{{ stackMemory.usage.toFixed(2) }}%</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </el-card>
        </el-col>
  
        <!-- <el-col :span="24" class="card-box">
          <el-card>
            <div slot="header" style="border-bottom: 1px solid #eee; line-height: 44px">
              <span>服务器信息</span>
            </div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%;">
                <tbody>
                  <tr>
                    <td class="dengkuan"><div class="cell">服务器名称</div></td>
                    <td class="dengkuan"><div class="cell" v-if="server.sys">{{ server.sys.computerName }}</div></td>
                    <td class="dengkuan"><div class="cell">操作系统</div></td>
                    <td class="dengkuan"><div class="cell" v-if="server.sys">{{ server.sys.osName }}</div></td>
                  </tr>
                  <tr>
                    <td class="dengkuan"><div class="cell">服务器IP</div></td>
                    <td class="dengkuan"><div class="cell" v-if="server.sys">{{ server.sys.computerIp }}</div></td>
                    <td class="dengkuan"><div class="cell">系统架构</div></td>
                    <td class="dengkuan"><div class="cell" v-if="server.sys">{{ server.sys.osArch }}</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </el-card>
        </el-col>
  
        <el-col :span="24" class="card-box">
          <el-card>
            <div slot="header" style="border-bottom: 1px solid #eee; line-height: 44px">
              <span>Java虚拟机信息</span>
            </div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%;">
                <tbody>
                  <tr>
                    <td class="dengkuan"><div class="cell">Java名称</div></td>
                    <td class="dengkuan"><div class="cell" v-if="server.jvm">{{ server.jvm.name }}</div></td>
                    <td class="dengkuan"><div class="cell">Java版本</div></td>
                    <td class="dengkuan"><div class="cell" v-if="server.jvm">{{ server.jvm.version }}</div></td>
                  </tr>
                  <tr>
                    <td class="dengkuan"><div class="cell">启动时间</div></td>
                    <td class="dengkuan"><div class="cell" v-if="server.jvm">{{ server.jvm.startTime }}</div></td>
                    <td class="dengkuan"><div class="cell">运行时长</div></td>
                    <td class="dengkuan"><div class="cell" v-if="server.jvm">{{ server.jvm.runTime }}</div></td>
                  </tr>
                  <tr>
                    <td colspan="1"><div class="cell">安装路径</div></td>
                    <td colspan="3"><div class="cell" v-if="server.jvm">{{ server.jvm.home }}</div></td>
                  </tr>
                  <tr>
                    <td colspan="1"><div class="cell">项目路径</div></td>
                    <td colspan="3"><div class="cell" v-if="server.sys">{{ server.sys.userDir }}</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </el-card>
        </el-col>
  
        <el-col :span="24" class="card-box">
          <el-card>
            <div slot="header" style="border-bottom: 1px solid #eee; line-height: 44px">
              <span>磁盘状态</span>
            </div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%;">
                <thead>
                  <tr>
                    <th class="is-leaf"><div class="cell">盘符路径</div></th>
                    <th class="is-leaf"><div class="cell">文件系统</div></th>
                    <th class="is-leaf"><div class="cell">盘符类型</div></th>
                    <th class="is-leaf"><div class="cell">总大小</div></th>
                    <th class="is-leaf"><div class="cell">可用大小</div></th>
                    <th class="is-leaf"><div class="cell">已用大小</div></th>
                    <th class="is-leaf"><div class="cell">已用百分比</div></th>
                  </tr>
                </thead>
                <tbody v-if="server.sysFiles">
                  <tr v-for="sysFile in server.sysFiles" :key="sysFile">
                    <td><div class="cell">{{ sysFile.dirName }}</div></td>
                    <td><div class="cell">{{ sysFile.sysTypeName }}</div></td>
                    <td><div class="cell">{{ sysFile.typeName }}</div></td>
                    <td><div class="cell">{{ sysFile.total }}</div></td>
                    <td><div class="cell">{{ sysFile.free }}</div></td>
                    <td><div class="cell">{{ sysFile.used }}</div></td>
                    <td><div class="cell" :class="{'text-danger': sysFile.usage > 80}">{{ sysFile.usage }}%</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </el-card>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-table :data="threadInfoList" style="width: 100%;height:calc(100vh - 172px);padding:15px;background-color: rgba(0,0,0,0);">
            <el-table-column prop="id" align="center" label="ID " />
            <el-table-column prop="state" align="center" label="状态" />
            <el-table-column prop="threadName" align="center" label="名称" />
          </el-table>
        </el-col>
        <el-icon v-show="show" style="position:fixed;right: 100px;top:calc(10vh - 5px);z-index: 999;cursor: pointer;" size="40" color="#aaa" @click="refresh"><RefreshLeft /></el-icon>
        <el-icon v-show="show" style="position:fixed;right: 60px;top:calc(10vh - 5px);z-index: 999;cursor: pointer;" size="40" color="#aaa" @click="plus"><Plus /></el-icon>
        <el-icon v-show="!show" style="position:fixed;right: 60px;top:calc(10vh - 5px);z-index: 999;cursor: pointer;" size="40" color="#aaa" @click="minus"><Minus /></el-icon>
      </el-row>
    </div>
  </template>
<script>
import { getJvmMonitor } from "@/api/monitor/jvmMonitor"
// import '@/assets/css/index.scss' // global css
import '@/assets/css/ruoyi.scss' // ruoyi css
import mitt from "@/utils/mitt"
export default {
	components: {  },
	data() {
		return {
            searchModel:{},
			      serverModel: {},
            sys: {},
            // 加载层信息
            loading: [],
            // 服务器信息
            server: [],
            heapMemory:{},
            stackMemory:{},
            threadInfoList:[],
            params: {},
            show:false

		}
	},
	methods: {
		/** 查询服务器信息 */
        async getList(data) {
            await getJvmMonitor(data).then(response => {
                this.heapMemory = response.data.result.jvmInfo.heapMemory;
                this.stackMemory = response.data.result.jvmInfo.stackMemory;
                this.threadInfoList = response.data.result.jvmInfo.threadInfoList;
                console.log(this.heapMemory);
                this.loading.close();
            });
            if (this.searchModel.keyword || this.searchModel.filter) {
            let threadInfoList = []
            this.threadInfoList.forEach(element => {
              if (element.threadName.search(this.searchModel.keyword) != -1) {
                threadInfoList.push(element)
              }
            });
            if (this.searchModel.filter) {
              threadInfoList=threadInfoList.filter(item => {
                console.log(item.threadName.search(this.searchModel.filter) != -1 );
                return item.threadName.search(this.searchModel.filter) == -1 
              })
            }
            this.threadInfoList = threadInfoList
          }else{
            // this.getList(this.params)
          }
        },
        // 打开加载层
        openLoading() {
          this.loading = this.$loading({
              lock: true,
              text: "拼命读取中",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)"
          });
        },
        search(){
          this.getList(this.params)
          if (this.searchModel.keyword || this.searchModel.filter) {
            let threadInfoList = []
            this.threadInfoList.forEach(element => {
              if (element.threadName.search(this.searchModel.keyword) != -1) {
                threadInfoList.push(element)
              }
            });
            if (this.searchModel.filter) {
              threadInfoList=threadInfoList.filter(item => {
                console.log(item.threadName.search(this.searchModel.filter) != -1 );
                return item.threadName.search(this.searchModel.filter) == -1 
              })
            }
            this.threadInfoList = threadInfoList
          }else{
            this.getList(this.params)
          }
        },
        refresh(){
          this.getList(this.params)
        },
        plus(){
          this.show = !this.show
        },
        minus(){
          this.show = !this.show
        }
		
		// async init() {
		// 	try {
        //         let res = await getJvmMonitor()
        //         this.sys = res.data.result.sys
        //         // getJvmMonitor().then(res => {
        //             this.serverModel = res.data.result
        //             const list = []
        //             const cpu = this.serverModel.cpu
        //             for (const key in cpu) {
        //                 list.push({key:key,value:cpu[key]})
        //             }
        //             this.serverModel.cpu = list
        //         // })
		// 	} catch (err) {
		// 	}
		// }
	},
	created() {
    this.params = {port:this.$route.query.port,ip:this.$route.query.ip}
    this.getList(this.params)
    this.openLoading();
	}
}
</script>

<style scoped>
    td{
        border-bottom: 1px solid #eee;
    }
    .dengkuan{
      width:25%
    }
    th{
        border-bottom: 1px solid #eee;
    }
    .is-leaf{
        text-align: left;
    }
    .cell{
        line-height: 44px!important;
    }

</style>
