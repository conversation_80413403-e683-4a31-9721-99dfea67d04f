<template>
	<el-dialog draggable width="25%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="roleModel" label-width="80px">
			<el-row>
				<el-col>
					<el-form-item label="角色名" prop="roleName">
						<el-input v-model="roleModel.roleName" placeholder="用户名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="角色码" prop="roleCode">
						<el-input v-model="roleModel.roleCode" placeholder="角色码"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="排序" prop="sort">
						<!-- <el-input onkeyup = "value=value.replace(/[^\d]/g,'')" v-model="roleModel.sort" placeholder="排序"></el-input> -->
						<el-input-number
							v-model="roleModel.sort"
							:min="1"
							:max="1000"
							:tep="1"
							controls-position="right"
						/>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="状态" prop="status">
						<el-select style="width: 100%;" v-model="roleModel.status" clearable placeholder="状态">
						    <el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="权限" prop="status">
						<div style="display: flex;flex-direction: column;width: 100%;">
							<el-row :span="24">
								<el-col :span="16">
									<el-radio-group v-model="roleModel.checkStrictly">
									  <el-radio :label="false">启用关联</el-radio>
									  <el-radio :label="true">关闭关联</el-radio>
									</el-radio-group>
								</el-col>
							</el-row>
							<el-row :span="24">
								<el-col>
									<el-tree ref="menu" id="menu" :check-strictly="roleModel.checkStrictly" :default-checked-keys="defaultChecked" :data="menuList" show-checkbox node-key="id" :props="permissionProps" />
								</el-col>
							</el-row>
						</div>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input type="textarea" :rows="2" v-model="roleModel.remark" placeholder="备注内容"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { saveRole,roleAuthorization } from "@/api/admin/role";
import mitt from "@/utils/mitt";
export default {
	props: ['statusList', 'scopeList'],
	data() {
		return {
			loading: false,
			checkStrictly: true,
			roleModel: {checkStrictly:true},
			dialog: {},
			menuList: [],
			defaultChecked: [],
			permissionProps: {
				children: 'children',
				label: 'menuName',
			},
			rules: {
				roleName: [{
					required: true,
					message: '请输入角色名',
					trigger: 'blur',
				}],
				roleCode: [{
					required: true,
					message: '请输入角色码',
					trigger: 'blur',
				}],
			}
		}
	},
	methods: {
		   // 所有菜单节点数据
		getMenuAllCheckedKeys() {
		// 目前被选中的菜单节点
		let checkedKeys = this.$refs.menu.getCheckedKeys();
		// 半选中的菜单节点
		let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
		checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
		return checkedKeys;
		},
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					this.roleModel.menuIds = this.getMenuAllCheckedKeys()
					if (this.roleModel.id == 0) {
						saveRole(this.roleModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						this.roleModel.menuIds = [...new Set(this.roleModel.menuIds)]
						saveRole(this.roleModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		},
		loadMenuList() {
			roleAuthorization()
				.then(res => {
					this.menuList = res.data.result
				})
		}
	},
	mounted() {
		this.$nextTick(function () {
			mitt.on('openRoleEdit', (role) => {
				this.roleModel = role
				this.roleModel.checkStrictly = true
				this.defaultChecked = role.menuIds
				this.loadMenuList()
				this.dialog.show = true
				this.dialog.title = "修改信息"

			})
			mitt.on('openRoleCopy', (role) => {
				this.roleModel = role
				this.roleModel.id = 0
				this.roleModel.checkStrictly = true
				this.defaultChecked = role.menuIds
				this.loadMenuList()
				this.dialog.show = true
				this.dialog.title = "复制信息"

			})
			mitt.on('openRoleAdd', () => {
				this.roleModel = {
					id: 0,
					menuIds: [],
					checkStrictly:true
				}
				this.defaultChecked = []
				this.loadMenuList()
				this.dialog.show = true
				this.dialog.title = "添加角色"
			})
		})
	}
}
</script>
<style scoped lang="less">
.el-tree {
    :deep(.el-checkbox .el-checkbox__inner) {
      display: none;
    }
 
    :deep(div[role='group']) {
      .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
    }
  }
</style>