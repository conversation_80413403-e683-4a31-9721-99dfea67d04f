import request from '@/utils/request'

export const pagingUser = (data) =>
	request({
		url: '/user',
		method: 'get',
		params: data
	})
export const listUser = (data) =>
	request({
		url: '/user/list',
		method: 'get',
		params: data
	})
export const resetPassword = (data) =>
	request({
		url: '/user/resetPassword',
		method: 'post',
		data: data
	})
export const systemUserRemove = (data) =>
	request({
		url: '/user/clearUserRole',
		method: 'post',
		data: data
	})

export const currentUser = () =>
	request({
		url: '/user/currentUser',
		method: 'get'
	})
export const updateUserInfo = (data) =>
	request({
		url: '/user/updateUserInfo',
		method: 'put',
		data:data
	})
export const updateCurrentUserPassword = (data) =>
	request({
		url: '/user/updateCurrentUserPassword',
		method: 'put',
		data:data
	})
export const updatePassword = (data) =>
	request({
		url: '/user/updatePassword',
		method: 'put',
		data:data
	})
export const getUser = (id) =>
	request({
		url: '/user/'+id,
		method: 'get'
	})
export const registerUser = (data) =>
	request({
		url: '/user',
		method: 'post',
		data: data
	})
export const editUser = (data) =>
	request({
		url: '/user',
		method: 'put',
		data: data
	})
export const deleteUser = (id) =>
	request({
		url: '/user',
		method: 'delete',
		params: {
			id: id
		}
	})
