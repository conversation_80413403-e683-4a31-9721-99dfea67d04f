<template>
	<menu-edit :statusList="statusList" :typeList="typeList" :menuList="menuList" @search="search" :menuCategoryList="menuCategoryList" :isOrNotList = "isOrNotList"></menu-edit>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.menuName" @keydown.enter="search" placeholder="菜单名" clearable />
		</el-col>
		<el-col :span="4">
			<el-select style="width: 100%;" v-model="searchModel.menuCategory" placeholder="应用分类" clearable>
				<el-option v-for="item in menuCategoryList" :key="item.nameEn" :label="item.nameCn"
					:value="parseInt(item.nameEn)"></el-option>
			</el-select>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="8">
			<el-button style="float: right;" type="primary" @click="add(0)" v-if="hasPerm('sys:menu:add')">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="menuList" row-key="id" border style="width: 100%">
				<el-table-column prop="menuName" sortable header-align="center" label="菜单名称" width="180" />
				<el-table-column prop="path" sortable align="center" label="路径名" width="180" />
				<el-table-column prop="permission" sortable align="center" label="权限" />
				<!-- <el-table-column prop="status" sortable align="center" :formatter="formatStatus" label="状态" /> -->
				
				<el-table-column prop="status" align="center" label="状态" width="88">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList,scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="type" sortable align="center" :formatter="formatType" label="类型" />
				<!-- <el-table-column prop="type" sortable align="center" :formatter="formatDict(menuCategoryList, scope.row.menuCategory)" label="分类" /> -->
				<el-table-column prop="status" align="center" label="分类" width="128">
					<template #default="scope">
						{{ formatDict(menuCategoryList, scope.row.menuCategory) }}
					</template>
				</el-table-column>
				<el-table-column prop="sort" sortable align="center" label="排序" />
				<el-table-column align="center" width="200" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row)" v-if="hasPerm('sys:menu:edit')">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" style="margin-right:10px" v-if="hasPerm('sys:menu:delete')">删除</el-button>
						<el-dropdown v-show="scope.row.type =='M' || scope.row.type =='S'" v-if="hasPerm('sys:menu:add')">
							<el-button type="text" size="default">更多</el-button>
						    <template #dropdown>
						      <el-dropdown-menu>
						        <el-dropdown-item>
									<el-button type="text" size="default" @click="add(scope.row.id)" v-if="hasPerm('sys:menu:add')">添加子级</el-button>
								</el-dropdown-item>
						      </el-dropdown-menu>
						    </template>
						</el-dropdown>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>

<script>
import { listMenu,deleteMenu } from "@/api/admin/menu"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict"
import menuEdit from "@/componts/admin/menu/menuEdit.vue"
export default {
	components:{ menuEdit },
	data() {
		return {
			searchModel: {},
			menuList: [],
			statusList:[],
			typeList:[],
			total:0,
			pageSize:10,
			menuCategoryList: [],
			isOrNotList: []
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatType(row, column, cellValue, index){
			return formatDict(this.typeList, cellValue)
		},
		search() {
			listMenu(this.searchModel)
			.then(res => {
				this.menuList = res.data.result.list
				this.total = res.data.result.total
			})
			.catch(err => {
				this.$message.error(err)
			})
		},
		edit(menu){
			mitt.emit('openMenuEdit',menu)
		},
		add(id){
			mitt.emit('openMenuAdd',id)
		},
		deleted(id){
			this.$confirm('删除菜单, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteMenu(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			mitt.off('openMenuEdit')
			mitt.off('openMenuAdd')
			try{
				let res = await listMenu(this.searchModel)
				let resStatus = await listDictByNameEn('menu_status')
				let resType = await listDictByNameEn('menu_type')
				this.statusList = resStatus.data.result
				this.typeList = resType.data.result
				this.userGroupList = res.data.result.list
				this.menuList = res.data.result.list
				this.total = res.data.result.total
				let menuCategoryList = await listDictByNameEn('menu_category')
				this.menuCategoryList = menuCategoryList.data.result
				let isOrNotList = await listDictByNameEn('is_or_not')
				this.isOrNotList = isOrNotList.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
		
		this.menuList =  [
			{
				id: 1,
				menuName: "测试数据1",
			},
			{
				id: 2,
				menuName: "测试数据2",
				children: [
					{
						id: 21,
						menuName: "测试数据21",
					},
				],
			}
		];
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
