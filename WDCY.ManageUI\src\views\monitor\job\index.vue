<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="任务名称" prop="jobName">
        <el-input
          v-model="queryParams.jobName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务组名" prop="jobGroup">
        <el-select
          v-model="queryParams.jobGroup"
          placeholder="请选择任务组名"
          clearable
          style="width: 168px"
        >
          <el-option
            v-for="dict in sys_job_group"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择任务状态"
          clearable
          style="width: 168px"
        >
          <el-option
            v-for="dict in sys_job_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" size="default" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" size="default" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          size="default"
          @click="handleAdd"
          v-hasPermi="['monitor:job:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          size="default"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['monitor:job:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          size="default"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['monitor:job:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col v-if="false" :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          size="default"
          @click="handleExport"
          v-hasPermi="['monitor:job:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Operation"
          size="default"
          @click="handleJobLog"
          v-hasPermi="['monitor:job:query']"
          >日志</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-row :gutter="10" class="mb8">
      <el-col>
        <el-table
          stripe
          v-loading="loading"
          :data="jobList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="任务编号"
            width="100"
            align="center"
            prop="jobId"
          />
          <el-table-column
            label="任务名称"
            align="left"
            prop="jobName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="任务组名" align="center" prop="jobGroup">
            <template #default="scope">
              <dict-tag :options="sys_job_group" :value="scope.row.jobGroup" />
            </template>
          </el-table-column>
          <el-table-column
            label="调用目标字符串"
            align="left"
            prop="invokeTarget"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="cron执行表达式"
            align="left"
            prop="cronExpression"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="状态" align="center" width="80">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
          width="110"
            label="日志保留天数"
            align="center"
            prop="logSaveDay"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="备注"
            align="center"
            prop="remark"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="操作"
            align="center"
            width="280"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                size="default"
                type="text"
                @click="handleRun(scope.row)"
                icon="CaretRight"
                v-hasPermi="['monitor:job:changeStatus']"
                >执行一次</el-button
              >
              <el-button
                size="default"
                type="text"
                @click="handleJobLog(scope.row)"
                icon="Operation"
                v-hasPermi="['monitor:job:query']"
                >调度日志</el-button
              >
              <el-dropdown
                style="vertical-align: middle"
                size="default"
                @command="(command) => handleCommand(command, scope.row)"
              >
                <span
                  class="el-dropdown-link"
                  v-hasPermi="['monitor:job:changeStatus', 'monitor:job:query']"
                >
                  <i class="el-icon-d-arrow-right el-icon--right"></i>更多
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      @click="handleUpdate(scope.row)"
                      icon="Edit"
                      v-hasPermi="['monitor:job:edit']"
                      >修改</el-dropdown-item
                      >
                    <el-dropdown-item
                      @click="handleDelete(scope.row)"
                      icon="Delete"
                      v-hasPermi="['monitor:job:remove']"
                      >删除</el-dropdown-item
                    >
                    <el-dropdown-item
                      command="handleView"
                      icon="View"
                      v-hasPermi="['monitor:job:query']"
                      >任务详细</el-dropdown-item
                    >
                    
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="mb8">
      <el-col>
        <el-pagination
          background
          v-show="total > 0"
          :total="Number(total)"
          v-model:currentPage="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          @current-change="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改定时任务对话框 -->
    <el-dialog draggable :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="jobRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="jobName">
              <el-input v-model="form.jobName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务分组" prop="jobGroup">
              <el-select v-model="form.jobGroup" placeholder="请选择">
                <el-option
                  v-for="dict in sys_job_group"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日志保留天数" prop="logSaveDay">
              <el-input-number v-model="form.logSaveDay" style="width:100%" placeholder="请输入日志保留天数" :min="0" :max="1000" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="invokeTarget">
              <template #label>
                <span>
                  调用方法
                  <el-tooltip placement="top">
                    <template #content>
                      <div style="font-size: medium">
                        Bean调用示例：ryTask.ryParams('ry')
                        <br />Class类调用示例：com.ruoyi.quartz.task.RyTask.ryParams('ry')
                        <br />参数说明：支持字符串，布尔类型，长整型，浮点型，整型
                      </div>
                    </template>
                    <!-- <i class="el-icon-question"></i> -->
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input
                v-model="form.invokeTarget"
                placeholder="请输入调用目标字符串"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="cronExpression">
              <template #label>
                <span>
                  cron表达式
                  <el-tooltip>
                    <template #content>
                      <div style="width: 800px; font-size: medium">
                        cron表达式语法:
                        <br />[秒] [分] [小时] [日] [月] [周] [年]
                        <p>
                          <b>通配符说明:</b>
                          <br />
                          <b style="color: red">*</b> 表示所有值。
                          例如:在分的字段上设置 *,表示每一分钟都会触发
                          <br />
                          <b style="color: red">?</b>
                          表示不指定值。使用的场景为不需要关心当前设置这个字段的值。例如:要在每月的10号触发一个操作，但不关心是周几，所以需要周位置的那个字段设置为”?”
                          具体设置为 0 0 0 10 * ?
                          <br />
                          <b style="color: red">-</b>
                          表示区间。例如 在小时上设置
                          “10-12”,表示10,11,12点都会触发
                          <br /><b style="color: red">,</b>
                          表示指定多个值，例如在周字段上设置 “MON,WED,FRI”
                          表示周一，周三和周五触发
                          <br /><b style="color: red">/</b>
                          用于递增触发。如在秒上面设置”5/15”
                          表示从5秒开始，每增15秒触发(5,20,35,50)。
                          在月字段上设置’1/3’所示每月1号开始，每隔三天触发一次
                          <br />
                          <b style="color: red">L</b>
                          表示最后的意思。在日字段设置上，表示当月的最后一天(依据当前月份，如果是二月还会依据是否是润年[leap]),
                          在周字段上表示星期六，相当于”7”或”SAT”。如果在”L”前加上数字，则表示该数据的最后一个。
                          例如在周字段上设置”6L”这样的格式,则表示“本月最后一个星期五”
                          <br />
                          <b style="color: red">W</b>
                          表示离指定日期的最近那个工作日(周一至周五).
                          例如在日字段上置”15W”，表示离每月15号最近的那个工作日触发。如果15号正好是周六，则找最近的周五(14号)触发,
                          如果15号是周未，则找最近的下周一(16号)触发.如果15号正好在工作日(周一至周五)，则就在该天触发。如果指定格式为
                          “1W”,它则表示每月1号往后最近的工作日触发。如果1号正是周六，则将在3号下周一触发。(注，”W”前只能设置具体的数字,不允许区间”-“)
                          <br />
                          <b style="color: red">#</b>
                          序号(表示每月的第几个周几)，例如在周字段上设置”6#3”表示在每月的第三个周六.
                          注意如果指定”#5”,正好第五周没有周六，
                          则不会触发该配置(用在母亲节和父亲节再合适不过了)；小提示：’L’和
                          ‘W’可以一组合使用。
                          如果在日字段上设置”LW”,则表示在本月的最后一个工作日触发；周字段的设置，若使用英文字母是不区分大小写的，即MON与mon相同
                        </p>
                      </div>
                    </template>
                    <!-- <i class="el-icon-question"></i> -->
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input
                v-model="form.cronExpression"
                placeholder="请输入cron执行表达式"
              >
                <template #append>
                  <el-button type="primary" @click="handleShowCron">
                    生成表达式
                    <i class="el-icon-time el-icon--right"></i>
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="错误策略" prop="misfirePolicy">
              <el-radio-group v-model="form.misfirePolicy" size="default">
                <el-radio-button label="1">立即执行</el-radio-button>
                <el-radio-button label="2">执行一次</el-radio-button>
                <el-radio-button label="3">放弃执行</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否并发" prop="concurrent">
              <el-radio-group v-model="form.concurrent" size="default">
                <el-radio-button label="0">允许</el-radio-button>
                <el-radio-button label="1">禁止</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in sys_job_status"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="form.remark"
                :rows="2"
                type="textarea"
                placeholder="请输入备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务日志详细 -->
    <el-dialog draggable title="任务详细" v-model="openView" width="700px" append-to-body>
      <el-form :model="form" label-width="120px" size="default">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务编号：">{{ form.jobId }}</el-form-item>
            <el-form-item label="任务名称：">{{ form.jobName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务分组：">{{
              jobGroupFormat(form)
            }}</el-form-item>
            <el-form-item label="创建时间：">{{
              form.createTime
            }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="cron表达式：">{{
              form.cronExpression
            }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下次执行时间：">{{
              parseTime(form.nextValidTime)
            }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="调用目标方法：">{{
              form.invokeTarget
            }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务状态：">
              <div v-if="form.status == 0">正常</div>
              <div v-else-if="form.status == 1">暂停</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否并发：">
              <div v-if="form.concurrent == 0">允许</div>
              <div v-else-if="form.concurrent == 1">禁止</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行策略：">
              <div v-if="form.misfirePolicy == 0">默认策略</div>
              <div v-else-if="form.misfirePolicy == 1">立即执行</div>
              <div v-else-if="form.misfirePolicy == 2">执行一次</div>
              <div v-else-if="form.misfirePolicy == 3">放弃执行</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：">
              {{form.remark}}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="openView = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Job">
import { getCurrentInstance, ref, reactive, toRefs } from "vue";
import {
  listJob,
  getJob,
  delJob,
  addJob,
  updateJob,
  runJob,
  changeJobStatus,
} from "@/api/monitor/job";
import { useRoute, useRouter } from "vue-router";

const router = useRouter();
const { proxy } = getCurrentInstance();
const { sys_job_group, sys_job_status } = proxy.useDict(
  "sys_job_group",
  "sys_job_status"
);

const jobList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const openView = ref(false);
const openCron = ref(false);
const expression = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    jobName: undefined,
    jobGroup: undefined,
    status: undefined,
  },
  rules: {
    jobName: [
      {
        required: true,
        message: "任务名称不能为空",
        trigger: "blur",
      },
    ],
    logSaveDay: [
      {
        required: true,
        message: "日志保留天数不能为空",
        trigger: "blur",
      },
    ],
    invokeTarget: [
      {
        required: true,
        message: "调用目标字符串不能为空",
        trigger: "blur",
      },
    ],
    cronExpression: [
      {
        required: true,
        message: "cron执行表达式不能为空",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询定时任务列表 */
function getList(val) {
  loading.value = true;
  listJob(queryParams.value).then(
    (response) => {
      console.info("getList:", response);
      jobList.value = response.data.rows;
      total.value = response.data.total;
      loading.value = false;
    },
    (err) => {
      loading.value = false;
    }
  );
}
/** 任务组名字典翻译 */
function jobGroupFormat(row, column) {
  return proxy.selectDictLabel(sys_job_group, row.jobGroup);
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    jobId: undefined,
    jobName: undefined,
    jobGroup: undefined,
    invokeTarget: undefined,
    cronExpression: undefined,
    misfirePolicy: 1,
    concurrent: 1,
    status: "0",
  };
  proxy.resetForm("jobRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.jobId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
// 更多操作触发
function handleCommand(command, row) {
  switch (command) {
    case "handleRun":
      handleRun(row);
      break;
    case "handleView":
      handleView(row);
      break;
    case "handleJobLog":
      handleJobLog(row);
      break;
    default:
      break;
  }
}
// 任务状态修改
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal
    .confirm('确认要"' + text + '""' + row.jobName + '"任务吗?')
    .then(function () {
      return changeJobStatus(row.jobId, row.status);
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(function () {
      row.status = row.status === "0" ? "1" : "0";
    });
}
/* 立即执行一次 */
function handleRun(row) {
  proxy.$modal
    .confirm('确认要立即执行一次"' + row.jobName + '"任务吗?')
    .then(function () {
      return runJob(row.jobId, row.jobGroup);
    })
    .then(() => {
      proxy.$modal.msgSuccess("执行成功");
    });
}
/** 任务详细信息 */
function handleView(row) {
  getJob(row.jobId).then((response) => {
    console.log(response.data.data);
    form.value = response.data.data;
    openView.value = true;
  });
}
/** cron表达式按钮操作 */
function handleShowCron() {
  expression.value = form.value.cronExpression;
  openCron.value = true;
}
/** 确定后回传值 */
function crontabFill(value) {
  form.value.cronExpression = value;
}
/** 任务日志列表查询 */
function handleJobLog(row) {
  const jobId = row.jobId || 0;
  router.push({
    path: "/jobLog",
    query: {
      jobId: jobId,
    },
  });
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加任务";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const jobId = row.jobId || ids.value;
  getJob(jobId).then((response) => {
    form.value = response.data.data;
    open.value = true;
    title.value = "修改任务";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["jobRef"].validate((valid) => {
    if (valid) {
      if (form.value.jobId != undefined) {
        updateJob(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addJob(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const jobIds = row.jobId || ids.value;
  proxy.$modal
    .confirm('是否确认删除定时任务编号为"' + jobIds + '"的数据项?')
    .then(function () {
      return delJob(jobIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "schedule/job/export",
    {
      ...queryParams.value,
    },
    `job_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}
</style>
