// import tab from './tab'
// import auth from './auth'
// import cache from './cache'
// import download from './download'

import modal from './modal'

export default function installPlugins(app){
  // // 页签操作
  // app.config.globalProperties.$tab = tab
  // // 认证对象
  // app.config.globalProperties.$auth = auth
  // // 缓存对象
  // app.config.globalProperties.$cache = cache
  // // 下载文件
  // app.config.globalProperties.$download = download
  // 模态框对象
  app.config.globalProperties.$modal = modal
}
