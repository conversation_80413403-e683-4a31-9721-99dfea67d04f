<template>
  <el-dialog
    draggable
    width="50%"
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-table :data="personList" height="500px" border style="width: 100%">
      <el-table-column
        prop="type"
        width="120"
        align="center"
        label="是否关联"
        v-if="
          hasPerm('device:relevancePerson:add') ||
          hasPerm('device:relevancePerson:delete') ||
          hasPerm('device:relevanceVehicle:add') ||
          hasPerm('device:relevanceVehicle:delete')
        "
      >
        <template #default="scope">
          <el-switch
            v-model="scope.row.type"
            inline-prompt
            :active-value="1"
            :inactive-value="0"
            @change="handleStatus(scope.row.id, scope.row.type)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="name" align="center" label="设施名称" />
      <el-table-column prop="status" align="center" label="状态" width="95">
        <template #default="scope">
          <el-tag :type="getDictCss(deviceStatusList, scope.row.status)">{{
            formatDict(deviceStatusList, scope.row.status)
          }}</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-row justify="end">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script>
import { listDictByNameEn } from "@/api/admin/dict";
import { getDictCss, formatDict } from "@/utils/dict";
import { deviceInfoByIdsList } from "@/api/device/device";
import {
  vehicleFlowDeviceListAdd,
  vehicleFlowDeviceListDelete,
  touristFlowDeviceListAdd,
  touristFlowDeviceListDelete,
} from "@/api/situation/situation";
import mitt from "@/utils/mitt";
export default {
	props:['deviceStatusList'],
  data() {
    return {
      loading: false,
      menuModel: {},
      dialog: {},
      personList: [],
      statusList: [],
      type: 0,
      communityId: localStorage.getItem("communityId"),
      rules: {
        name: [
          {
            required: true,
            message: "请输入信息名称",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    onSubmit() {
      this.dialog.show = false;
    },
    // 改变关联状态
    handleStatus(id, type) {
      if (this.type === 2) {
        if (type === 1) {
          vehicleFlowDeviceListAdd({
            vehicleFlowId: this.touristFlowId,
            touristFlowId: this.touristFlowId,
            devId: id,
          }).then((res) => {
            this.$message.success("关联成功");
          });
        } else {
          vehicleFlowDeviceListDelete({
            vehicleFlowId: this.touristFlowId,
            touristFlowId: this.touristFlowId,
            devId: id,
          }).then((res) => {
            this.$message.success("取消关联成功");
          });
        }
      }
      if (this.type === 1) {
        if (type === 1) {
          touristFlowDeviceListAdd({
            vehicleFlowId: this.touristFlowId,
            touristFlowId: this.touristFlowId,
            devId: id,
          }).then((res) => {
            this.$message.success("关联成功");
          });
        } else {
          touristFlowDeviceListDelete({
            vehicleFlowId: this.touristFlowId,
            touristFlowId: this.touristFlowId,
            devId: id,
          }).then((res) => {
            this.$message.success("取消关联成功");
          });
        }
      }
    },
  },

  mounted() {
    mitt.on("openDeviceConnectEdit", (menu) => {
      console.log(this.deviceStatusList);
      this.touristFlowId = menu.id;
      this.type = menu.type;
      deviceInfoByIdsList({
        id: menu.id,
        type: menu.type,
        communityId: this.communityId,
      }).then((res) => {
        this.personList = res.data.result;
      });
      this.dialog.show = true;
      this.dialog.title = "设施关联";
    });
  },
};
</script>
