<template>
  <div
    class="monitor-box"
    :style="{ width: width + 'px', height: height + 'px' }"
  >
    <div
      v-if="
        (videoMode == 'HLS' && playMode == 0) ||
        (videoMode == 'HLS' && playMode == 1)
      "
    >
      <monitor-hls
        ref="monitorHLS"
        :height="height"
        :width="width"
        :communityId="communityId"
        :playMode="playMode"
        :videoMode="videoMode"
        :devId="devId"
        :devNo="devNo"
        :backStartTime="backStartTime"
        :backEndTime="backEndTime"
      >
      </monitor-hls>
    </div>

    <div
      v-if="
        (videoMode == 'WS' && playMode == 0) ||
        (videoMode == 'WS' && playMode == 1)
      "
    >
      <monitor-ws
        ref="monitorWS"
        :height="height"
        :width="width"
        :communityId="communityId"
        :playMode="playMode"
        :videoMode="videoMode"
        :devId="devId"
        :devNo="devNo"
        :backStartTime="backStartTime"
        :backEndTime="backEndTime"
      >
      </monitor-ws>
    </div>

    <div
      v-if="
        (videoMode == 'GBT' && playMode == 0) ||
        (videoMode == 'GBT' && playMode == 1)
      "
    >
      <monitor-gbt
        ref="monitorGBT"
        :height="height"
        :width="width"
        :communityId="communityId"
        :playMode="playMode"
        :videoMode="videoMode"
        :devId="devId"
        :devNo="devNo"
        :backStartTime="backStartTime"
        :backEndTime="backEndTime"
      >
      </monitor-gbt>
    </div>

    <div
      v-if="
        (videoMode == 'PLUGIN' && playMode == 0) ||
        (videoMode == 'PLUGIN' && playMode == 1)
      "
    >
      <monitor-plugin
        ref="monitorPLUGIN"
        :height="height"
        :width="width"
        :communityId="communityId"
        :playMode="playMode"
        :videoMode="videoMode"
        :devId="devId"
        :devNo="devNo"
        :backStartTime="backStartTime"
        :backEndTime="backEndTime"
        :videoConfig="videoConfig"
      >
      </monitor-plugin>
    </div>
  </div>
</template>

<script>
import monitorHls from "@/componts/monitor/monitor-hls.vue";
import monitorWs from "@/componts/monitor/monitor-ws.vue";
import monitorGbt from "@/componts/monitor/monitor-gbt.vue";
import monitorPlugin from "@/componts/monitor/monitor-plugin.vue";
export default {
  components: {
    monitorHls,
    monitorWs,
    monitorGbt,
    monitorPlugin,
  },

  computed: {},

  props: {
    videoConfig: {
      type: Object,
      default: {},
    },
    communityId: {
      type: String,
      default: "",
    },

    isShowCuttingPartWindow: {
      type: Boolean,
      default: false,
    },

    communityId: {
      type: String,
      default: "",
    },
    width: {
      type: Number,
      default: 512,
    },
    height: {
      type: Number,
      default: 294,
    },
    playMode: {
      type: Number,
      default: 0,
    },

    videoMode: {
      type: String,
      default: "PLUGIN",
    },

    // previewMode: {
    //   type: String,
    //   default: "HLS",
    // },

    // playbackMode: {
    //   type: String,
    //   default: "HLS",
    // },

    devId: {
      type: String,
      default: 0,
    },

    devNo: {
      type: String,
      default: "",
    },

    backStartTime: {
      type: String,
      default: "",
    },
    backEndTime: {
      type: String,
      default: "",
    },
  },

  data() {
    return {};
  },

  watch: {
    videoMode: {
      handler: function (val, oldVal) {
        this.initMonitor();
      },
      // 深度观察监听
      deep: true,
    },

    isShowCuttingPartWindow: {
      handler: function (val, oldVal) {
        if (this.videoMode == "PLUGIN") {
          if (val) {
            this.$refs.monitorPLUGIN.startTimeFocus();
          } else {
            this.$refs.monitorPLUGIN.startTimeBlur();
          }
        }
      },
      // 深度观察监听
      deep: true,
    },
  },

  mounted() {
    console.log("videoWidth", this.width);
    console.log("videoHeight", this.height);
    this.initMonitor();
  },

  created() {},

  methods: {
    initMonitor() {
      if (this.playMode == 0) {
        if (this.videoMode == "HLS") {
          this.$refs.monitorHLS.initMonitorHLS();
        }
        if (this.videoMode == "WS") {
          this.$refs.monitorWS.initMonitorWS();
        }
        if (this.videoMode == "GBT") {
          this.$refs.monitorGBT.initMonitorGBT();
        }

        if (this.videoMode == "PLUGIN") {
          this.$refs.monitorPLUGIN.initMonitorPLUGIN();
        }
      } else {
        if (this.videoMode == "HLS") {
          this.$refs.monitorHLS.initMonitorHLS();
        }
        if (this.videoMode == "WS") {
          this.$refs.monitorWS.initMonitorWS();
        }
        if (this.videoMode == "GBT") {
          this.$refs.monitorGBT.initMonitorGBT();
        }

        if (this.videoMode == "PLUGIN") {
          this.$refs.monitorPLUGIN.initMonitorPLUGIN();
        }
      }
    },

    //     destroyMonitor() {
    //       if (this.previewMode == "HLS") {
    //         this.$refs.monitorHLS.destroyMonitorHLS();
    //       }
    //       if (this.previewMode == "WS") {
    //         this.$refs.monitorWS.destroyMonitorWS();
    //       }
    //       if (this.previewMode == "GBT") {
    //         this.$refs.monitorGBT.destroyMonitorGBT();
    //       }
    //       if (this.previewMode == "PLUGIN") {
    //         this.$refs.monitorPLUGIN.destroyMonitorPLUGIN();
    //       }
    //     },
  },

  beforeDestroy() {
    // this.destroyMonitor();
  },
};
</script>
  
  <style scoped lang="scss">
.monitor-box {
  background: #000;
}
</style>
  