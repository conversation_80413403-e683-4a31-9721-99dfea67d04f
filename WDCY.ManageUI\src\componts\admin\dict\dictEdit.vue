<template>
	<el-dialog draggable width="25%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="dictModel" label-width="66px">

			<el-form-item label="名称" prop="nameCn">
				<el-input v-model="dictModel.nameCn" placeholder="名称"></el-input>
			</el-form-item>

			<el-form-item label="编码" prop="nameEn">
				<el-input v-model="dictModel.nameEn" placeholder="编码"></el-input>
			</el-form-item>

			<el-form-item label="样式" prop="cssClass" style="margin-bottom:5px">
				<el-input v-model="dictModel.cssClass" placeholder="可输入类名或者颜色值">
					<template #append>
						<el-color-picker v-model="dictModel.cssClass" />
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="">
				<el-tag type="primary">primary</el-tag>
				<el-tag type="success">success</el-tag>
				<el-tag type="info">info</el-tag>
				<el-tag type="warning">warning</el-tag>
				<el-tag type="danger">danger</el-tag>
			</el-form-item>
			<el-form-item label="状态" prop="status">
			<el-select
				style="width: 100%"
				v-model="dictModel.status"
				placeholder="状态"
			>
				<el-option
				v-for="item in statusList"
				:key="item.nameEn"
				:label="item.nameCn"
				:value="parseInt(item.nameEn)"
				></el-option>
			</el-select>
			</el-form-item>
			<el-form-item label="排序" prop="sort">
				<!-- <el-input v-model="dictModel.sort" placeholder="排序"></el-input> -->
				<el-input-number v-model="dictModel.sort" :min="1" />
			</el-form-item>

			<el-form-item label="备注" prop="remark">
				<el-input
					v-model="dictModel.remark"
					maxlength="200"
					placeholder="请解释字典项代表的意思"
					show-word-limit
					type="textarea"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<!-- <el-button @click="dialogVisible = false">Cancel</el-button> -->
				<el-button type="primary" @click="onSubmit">
					提 交
				</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script>
import { editDict, addDict } from "@/api/admin/dict";
import mitt from "@/utils/mitt";

export default {
	props:['statusList'],
	// emits: ['search'],
	data() {
		return {
			loading: false,
			dictModel: {},
			dialog: { show: false },
			rules: {
				nameCn: [{
					required: true,
					message: '请输入名称',
					trigger: 'blur',
				}],
				nameEn: [{
					required: true,
					message: '请输入编码',
					trigger: 'blur',
				}],
				// cssClass: [{
				// 	required: true,
				// 	message: '请输入样式',
				// 	trigger: 'blur',
				// }],
			}
		}
	},
	methods: {
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.dictModel.id == 0) {
						addDict(this.dictModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						editDict(this.dictModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		}
	},
	mounted() {
    this.$nextTick(function () {
		mitt.on('openDictEdit', (dict) => {
			this.dictModel = JSON.parse(JSON.stringify(dict))
			this.dialog.show = true
			this.dialog.title = "修改信息"
		})
		mitt.on('openDictAdd', (id) => {
			this.dictModel = {
				id: 0,
				parentId: id,
				sort: 1,
				status: 1
			}
			this.dialog.show = true
			this.dialog.title = "添加字典"
		})
    });
	}
}
</script>
<style>
.el-input-group__append {
	padding: 0 !important;
}
</style>