<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="18" >
        <el-input style="width: 138px;margin-right: 10px;" v-model="queryParams.componentTitle" placeholder="请输入标题关键字" clearable @keyup.enter.native="handleQuery" />
        
        <el-select
          v-model="queryParams.themeId"
          clearable
          placeholder="选择主题"
          style="width: 130px;margin-right: 10px;"
        >
          <el-option
            v-for="item in themeList"
            :key="item.id"
            :label="item.themeTitle"
            :value="item.id"
          />
        </el-select>

        <el-select
          v-model="queryParams.componentType"
          clearable
          placeholder="选择组件类型"
          style="width: 130px;margin-right: 10px;"
        >
          <el-option
            v-for="item in componentTypeList"
            :key="item.nameEn"
            :label="item.nameCn"
            :value="item.nameEn"
          />
        </el-select>

        <el-select
          v-model="queryParams.status"
          clearable
          placeholder="选择状态"
          style="width: 130px;margin-right: 10px;"
        >
          <el-option
            v-for="item in statusList"
            :key="item.nameEn"
            :label="item.nameCn"
            :value="item.nameEn"
          />
        </el-select>

        <el-date-picker v-model="daterangeCreateTime"  value-format="YYYY-MM-DD" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 200px;margin-right: 10px;"></el-date-picker>

        <el-button type="primary" @click="handleQuery">搜 索</el-button>
      </el-col>
      <el-col :span="6" >
        <el-button style="float: right;" type="primary" @click="handleAdd" v-if="hasPerm('assy:component:add')">添 加</el-button>
        <el-button style="float: right; margin-right: 10px;" type="primary" :disabled="single" @click="handleUpdate" v-if="hasPerm('assy:component:add')">修 改</el-button>
        <el-button style="float: right; " type="primary" :disabled="multiple" @click="handleDelete" v-if="hasPerm('assy:component:remove')">删 除</el-button>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-table v-loading="loading" :data="componentList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            prop="videoCover"
            width="100"
            align="center"
            label="图片"
          >
            <template #default="scope">
              <el-image
                preview-teleported
                style="
                  width: 60px;
                  height: 36px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
                :src="getVC(scope.row)"
                :preview-src-list="[getVC(scope.row)]"
              >
                <template #error>
                  <span style="display: flex; justify-content: center">暂无图片</span>
                </template>
              </el-image>
            </template>
          </el-table-column>

          <el-table-column label="组件ID" align="center" prop="id" />
          <el-table-column label="主题" align="center" prop="themeId">
            <template #default="scope">
              {{ showThemeTitle(scope.row.themeId) }}
            </template>
          </el-table-column>
          <el-table-column label="组件标题" align="left" prop="componentTitle" show-overflow-tooltip />
          <el-table-column label="组件说明" align="left" prop="componentNote" show-overflow-tooltip />
          <el-table-column label="组件类型" align="center" prop="componentType">
            <template #default="scope">
              <el-tag size="default" :type="tagType(componentTypeList, scope.row.componentType)">{{ tagName(componentTypeList, scope.row.componentType)}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <el-tag size="default" :type="tagType(statusList, scope.row.status)">{{ tagName(statusList,
                scope.row.status)}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="额外数据" align="center" prop="extraData" show-overflow-tooltip />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center" prop="createUser" />
          <el-table-column label="修改人" align="center" prop="updateUser" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
            <template #default="scope">
              <el-button type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['assy:component:edit']">修改</el-button>
              <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['assy:component:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div style="float: right; margin-top: 20px;">
          <el-pagination background :total="Number(total)" v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" @change="getList"
            layout="total, sizes, prev, pager, next, jumper" />
        </div>
      </el-col>
    </el-row>

    <!-- 添加或修改组件对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="componentTitle">
          <el-input v-model="form.componentTitle" placeholder="请输入组件标题"  maxlength="50" show-word-limit/>
        </el-form-item>
        <el-form-item label="类型" prop="componentType">
          <el-select
            v-model="form.componentType"
            clearable
            placeholder="选择组件类型"
            style="width: 240px"
          >
            <el-option
              v-for="item in componentTypeList"
              :key="item.nameEn"
              :label="item.nameCn"
              :value="item.nameEn"
            />
          </el-select>
        </el-form-item> 
        <el-form-item label="主题" prop="themeId">
          <el-select
            v-model="form.themeId"
            clearable
            placeholder="选择主题"
            style="width: 240px"
          >
            <el-option
              v-for="item in themeList"
              :key="item.id"
              :label="item.themeTitle"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="说明" prop="componentNote">
          <el-input v-model="form.componentNote" type="textarea" placeholder="请输入内容"  maxlength="500" show-word-limit/>
        </el-form-item>
      
        <el-form-item label="拓展参数" prop="extraData">
          <!-- <el-input v-model="form.extraData" type="textarea" placeholder="拓展参数" /> -->
          <JsonEditorVue
            class="editor"
            language="cn"
            v-model="form.extraData"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            inline-prompt
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>

        <el-form-item label="预览">
          <ImageUpload v-model="imageUrl" modName="assy" funcName="component" isCommunity="false"/>
          <!-- <el-upload
            class="avatar-uploader"
            accept="image/jpeg,image/jpg,image/png"
            :show-file-list="false"
            :http-request="imgUpload"
          >
            <img v-if="imageUrl" :src="imgServer + imageUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import JsonEditorVue from "json-editor-vue3";
import ImageUpload from "@/componts/fileUpload/imgUpload.vue";
import { listComponent, getComponent, delComponent, addComponent, updateComponent } from "@/api/assy/component";
import { listTheme } from "@/api/assy/theme";
import { listDictByNameEn } from "@/api/admin/dict";
import { getDictCss, formatDict } from "@/utils/dict";

export default {
  name: "Component",  
  components: {
    JsonEditorVue, ImageUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 组件表格数据
      componentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 修改人时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        themeId: null,
        componentTitle: null,
        componentType: null,
        componentNote: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        themeId: [
          { required: true, message: "主题不能为空", trigger: "change" }
        ],
        componentTitle: [
          { required: true, message: "组件标题不能为空", trigger: "blur" }
        ],
        componentType: [
          { required: true, message: "组件类型不能为空", trigger: "change" }
        ],
        componentNote: [
          { required: false, message: "组件说明不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      themeList: [],
      statusList: [],
      componentTypeList: [],
      imageUrl: "",
      imgServer: import.meta.env.VITE_BASE_API
    };
  },
  created() {
    this.asyncInit();
    let queryTheme = {};
    queryTheme.status = 1;
    listTheme(queryTheme).then(response => {
        let data = response.data;
        if (data.code == 0) {
          this.themeList = data.result;
        } else {
          this.$modal.msgError(data.msg);
        }
      });
    this.getList();
  },
  methods: {
    async asyncInit() {
      try {
        let respone = await listDictByNameEn('common_status');
        //console.info("listDictByNameEn", respone, respone.data);
        this.statusList = respone.data.result;
        respone = await listDictByNameEn('assy_component_type');
        this.componentTypeList = respone.data.result;
      } catch (err) {
      }
    },
    tagType(dicList, cellValue) {
      return getDictCss(dicList, cellValue)
    },
    tagName(dicList, cellValue) {
      return formatDict(dicList, cellValue)
    },
    // 格式化图片
    getVC(row) {
      if (row.extraData) {
        let obj = JSON.parse(row.extraData);
        if (obj.previewUrl) {
          return this.imgServer + obj.previewUrl;
        } else {
          return false;
        }
      }
    },
    showThemeTitle(themeId) {
      if (this.themeList) {
        let find = this.themeList.find(m => m.id == themeId);
        if (find)
          return find.themeTitle;
      }
      return themeId;
    },
    /** 查询组件列表 */
    getList() {
      this.loading = true;
      this.queryParams.map = null;
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.map = {};
        this.queryParams.map["beginTime"] = this.daterangeCreateTime[0];
        this.queryParams.map["endTime"] = this.daterangeCreateTime[1];
      }
      listComponent(this.queryParams).then(response => {
        let data = response.data;
        if (data.code == 0) {
          this.componentList = data.result.list;
          this.total = data.result.total;
        } else {
          this.$modal.msgError(data.msg);
        }
        this.loading = false;
      }).catch(res => {        
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        themeId: null,
        componentTitle: null,
        componentType: null,
        componentNote: null,
        status: 1,
        extraData: {"path":"","type":"component","previewUrl":"/assets/images/assy/default.jpg","positionX":100,"positionY":100,"width":100,"height":100,"minWidth":100,"minHeight":100},
        // createTime: null,
        // updateTime: null,
        // createUser: null,
        // updateUser: null
      };
      this.imageUrl = "";
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加组件";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getComponent(id).then(response => {
        let data = response.data;
        if (data.code == 0) {
          this.form = data.result;
          if (this.form.extraData) {
            this.form.extraData = JSON.parse(this.form.extraData);
            this.imageUrl = this.form.extraData.previewUrl;
          }
          this.open = true;
          this.title = "修改组件";
        } else {
          this.$modal.msgSuccess(data.msg);
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      //console.info("component form: imageUrl=" + this.imageUrl);
      this.$refs["form"].validate(valid => {
        if (valid) {
          let formData = JSON.parse(JSON.stringify(this.form));
          if (this.form.extraData) {
            this.form.extraData.previewUrl = this.imageUrl;
            formData.extraData = JSON.stringify(this.form.extraData);
          }
          if (formData.id != null) {
            updateComponent(formData).then(response => {
              let data = response.data;
              if (data.code == 0) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
              } else {
                this.$modal.msgSuccess(data.msg);
              }
              this.getList();
            });
          } else {
            addComponent(formData).then(response => {
              let data = response.data;
              if (data.code == 0) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
              } else {
                this.$modal.msgSuccess(data.msg);
              }
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除组件编号为"' + ids + '"的数据项？').then(function () {
        return delComponent(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('assy/component/export', {
        ...this.queryParams
      }, `component_${new Date().getTime()}.xlsx`)
    },
    // imgUpload(files) {
    //   console.info("开始上传！");
    //   let form = new FormData();
    //   form.append("file", files.file);
    //   form.append("modulesName", "assy");
    //   form.append("functionName", "component");
    //   //form.append("communityId", "");
    //   fileUpload(form).then((res) => {
    //     let data = res.data;
    //     if (data.code == 0) {
    //       this.imageUrl = data.result.url;
    //       this.$message.success("上传成功");
    //     } else {
    //       this.$message.error(data.msg);
    //     }
    //   });
    // }
  }
};
</script>


<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
.editor {
  width: 805px;
}
.dialog-footer{
  text-align: right;
}
/*
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

:deep() .avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep() .avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}*/
</style>