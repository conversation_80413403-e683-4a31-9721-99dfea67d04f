<template>
	<el-dialog draggable width="50%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
        <el-form :rules="rules" :model="actionModel" label-width="120px">
			<el-row>	
				<el-col :span="18">
					<el-form-item label="动作类型" prop="actionType">
						<el-radio v-model="actionModel.actionType" :label="0" @click="refresh(0)">建议提醒</el-radio>
						<el-radio v-model="actionModel.actionType" :label="1" @click="refresh(1)">设施动作</el-radio>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="执行顺序" prop="sequence">
						<el-input-number
							v-model="actionModel.sequence"
							:min="1"
							:max="1000"
							:step="1"
							controls-position="right"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="延迟时间(秒)" prop="delay">
						<!-- <el-input v-model="actionModel.delay" placeholder="延迟时间"></el-input> -->
						<el-input-number
							v-model="actionModel.delay"
							:min="0"
							:max="1000"
							:step="1"
							controls-position="right"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item prop="warnMsg">
						<template v-slot:label>
							<span>
								建议提醒
								<el-tooltip >
									<template #content> 模板占位标识（替换为条件描述）：#@#<br /> 例：请注意，#@#，请上门查看<br />可转换为：请注意，系统在每5天的9:00检查老人是否没有出现，请上门查看</template>
									<!-- <el-icon><warning-filled /></el-icon> -->
									<!-- <el-icon><info-filled /></el-icon> -->
									<el-icon>
										<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-ba633cb8=""><path fill="currentColor" d="M512 64a448 448 0 1 1 0 896.064A448 448 0 0 1 512 64zm67.2 275.072c33.28 0 60.288-23.104 60.288-57.344s-27.072-57.344-60.288-57.344c-33.28 0-60.16 23.104-60.16 57.344s26.88 57.344 60.16 57.344zM590.912 699.2c0-6.848 2.368-24.64 1.024-34.752l-52.608 60.544c-10.88 11.456-24.512 19.392-30.912 17.28a12.992 12.992 0 0 1-8.256-14.72l87.68-276.992c7.168-35.136-12.544-67.2-54.336-71.296-44.096 0-108.992 44.736-148.48 101.504 0 6.784-1.28 23.68.064 33.792l52.544-60.608c10.88-11.328 23.552-19.328 29.952-17.152a12.8 12.8 0 0 1 7.808 16.128L388.48 728.576c-10.048 32.256 8.96 63.872 55.04 71.04 67.84 0 107.904-43.648 147.456-100.416z"></path></svg>
									</el-icon>
								</el-tooltip>
							</span>
						</template>
						<el-input :disabled='actionModel.actionType==1' type="textarea" v-model="actionModel.warnMsg" placeholder="建议提醒"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="动作设施" prop="devId">
						<el-select :disabled='actionModel.actionType==0' style="width: 100%;" @change="selectChange" v-model="actionModel.devId" placeholder="事件名称">
							<el-option v-for="item in deviceList" :key="item.id"
								:label="item.name" :value="item.devTypeId"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="动作名称" prop="executeId">
						<el-select :disabled='actionModel.actionType==0' style="width: 100%;" v-model="actionModel.executeId" placeholder="事件名称">
							<el-option v-for="item in actionList" :key="item.id"
								:label="item.name" :value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col>
					<el-form-item label="图标名称" prop="icoInfoId">
					<el-select v-model="actionModel.icoInfoId" placeholder="图标名称">
						<el-option v-for="item in icoList" :key="item.id" :label="item.icoName" :value="item.id" style="line-height:34px">
								<img :src="imgServer + item.icoUrl" />
								<div>{{ item.icoName }}</div>
								<div style="flex:1"></div>
							</el-option>
					</el-select>
				</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="弹窗时间(秒)" prop="popTime">
						<!-- <el-input v-model="actionModel.delay" placeholder="延迟时间"></el-input> -->
						<el-input-number
							v-model="actionModel.popTime"
							:min="0"
							:max="1000"
							:step="1"
							controls-position="right"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="推送类型" prop="pushType">
						<el-select multiple style="width: 100%;" v-model="actionModel.pushType" placeholder="推送类型">
							<el-option v-for="item in pushTypeList" :key="item.nameEn" :label="item.nameCn"
							:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="end">
			<el-button type="primary" @click="onActionSubmit">提 交</el-button>
		</el-row>
  </el-dialog>
</template>

<script>
import { warnStrategyExecuteAdd, warnStrategyExecuteEdit } from "@/api/warn/warnStrategy"
import { listDictByNameEn } from "@/api/admin/dict"
import { actionInfoAllList } from "@/api/warn/warn"
import { deviceInfoAllList } from "@/api/device/device"
import mitt from "@/utils/mitt";
export default {
	props: ['statusList', 'tagList', 'icoList'],
	data() {
		return {
			loading: false,
			communityId: localStorage.getItem("communityId"),
			imgServer: import.meta.env.VITE_BASE_API,
			actionModel: {},
			deviceList: [],
			actionList: [],
			pushTypeList: [],
			strategyExecuteList: [],
			dialog: {},
			strategyId: 0,
			eventDetectRule: {},
			strategyExecute: {},
			rules: {
				actionType: [{
					required: true,
					message: '请选择动作类型',
					trigger: 'blur',
				}],
			}
		}
	},
	methods: {
		selectChange(devTypeId) {
			console.log(devTypeId)
			actionInfoAllList(devTypeId)
				.then((res) => {
					this.actionList = res.data.result;
				})
		},
		refresh(type) {
			if (type === 0) {
				this.actionModel.devId = null
				this.actionModel.executeId = null
			} else {
				this.actionModel.warnMsg = null
			}

		},
		onActionSubmit() {
			this.actionModel.warnStrategyId = this.strategyId
			this.actionModel.communityId = this.communityId
			if (this.actionModel.id == 0) {
				warnStrategyExecuteAdd(this.actionModel)
					.then(res => {
						this.$message.success(res.data.msg)
						this.$emit("search")
						this.dialog.show = false
					})
			} else {
				warnStrategyExecuteEdit(this.actionModel)
					.then(res => {
						this.$message.success(res.data.msg)
						this.$emit("search")
						this.dialog.show = false
					})
			}
		},
		loadTriggerType() {
			deviceInfoAllList({
				communityId: this.communityId,
			})
				.then((res) => {
					this.deviceList = res.data.result;
				})
		},
		async init() {
			let res_pushType = await listDictByNameEn('push_type')
			this.pushTypeList = res_pushType.data.result
		}
	},
	created() {
		this.init()
	},
	mounted() {
		this.$nextTick(function () {
			this.loadTriggerType()
			mitt.on('openActionRuleEdit', (person) => {
				// this.loadTriggerType()
				this.actionModel = person.data
				this.strategyId = person.strategyId
				this.dialog.show = true
				this.dialog.title = "修改"
			})
			mitt.on('openActionRuleAdd', (person) => {
				// this.loadTriggerType()
				this.actionModel = {
					id: 0,
					sequence: 1,
					delay: 0,
					actionType: 0,
					popTime: 30
				}
				this.strategyId = person.strategyId
				this.dialog.show = true
				this.dialog.title = "添加"
			})
		})
	}
}
</script>
<style scoped lang="scss">
.el-select-dropdown__item{
	display: flex;align-items: center;text-align:center;
	>img{
		background-color: rgba(136, 186, 255,.3);
		width: 20px;
		margin:0 auto;
		margin-right: 10px;
	}
}
.el-select-dropdown__item:hover{
	background-color: rgba(170, 170, 170,.3);
}

</style>
