<template>
	<warn-device-action-edit :statusList="statusList" :tagList="tagList" @search="search"></warn-device-action-edit>
	<el-dialog draggable width="50%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<el-row :gutter="20">
			<el-col>
				<el-button style="float: right;margin-bottom:10px" type="primary" @click="add()">添 加</el-button>
			</el-col>
		</el-row>
		<el-row>
			<el-table :data="personList" border style="width: 100%">
				<el-table-column prop="name" align="center" label="设施类型名称" />
				<el-table-column prop="name" width="120" align="center" label="预警动作名称" />
				<el-table-column prop="expandParams" align="center" label="运行参数" />
				<el-table-column align="center" width="140" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row.id)">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-row>
	</el-dialog>
</template>

<script>
import { warnDeviceActionDelete, getWarnDeviceAction, warnDeviceActionList } from "@/api/warn/warn";
import warnDeviceActionEdit from "@/componts/warn/warnDeviceActionEdit.vue";
import mitt from "@/utils/mitt";
export default {
	components: { warnDeviceActionEdit },
	data() {
		return {
			loading: false,
			menuModel: {},
			dialog: {},
			devTypeId: 0,
			personList: [],
			communityId: localStorage.getItem("communityId"),
			rules: {
				name: [{
					required: true,
					message: '请输入名称',
					trigger: 'blur',
				}]
			}
		}
	},
	methods: {
		search() {
			warnDeviceActionList({ devTypeId: this.devTypeId })
				.then(res => {
					this.personList = res.data.result.list
				})
		},
		add() {
			mitt.emit('openWarnDeviceActionAdd', this.devTypeId)
		},
		edit(id) {
			getWarnDeviceAction(id)
				.then(res => {
					mitt.emit('openWarnDeviceActionEdit', { data: res.data.result, devTypeId: this.devTypeId })
				})
		},
		deleted(id) {
			this.$confirm('删除动作, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				warnDeviceActionDelete(id)
					.then(res => {
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
		},
	},

	mounted() {
		mitt.on('openWarnActionEdit', (menu) => {
			this.personList = menu.data
			this.devTypeId = menu.devTypeId
			this.dialog.show = true
			this.dialog.title = "预警动作管理"
		})
	},
}
</script>
