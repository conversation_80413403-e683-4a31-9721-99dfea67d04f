<template>
	<div v-show="index == 1">
		<el-row :gutter="20">
			<el-col :span="4">
				<div class="title_des">
					<img style="height: 30px;width: 35px;" src="../../assets/img/house_icon.png" />
					<span>&nbsp;&nbsp;房屋未下发列表</span>
				</div>
			</el-col>

			<el-col :span="6" style="display:flex">
				<el-input style="margin-right:10px;width:50%" @keydown.enter="search" v-model="searchModel.buildingNumber" placeholder="楼房编号" clearable />
				<el-select clearable style="margin-right:10px" v-model="status.value" class="m-2" placeholder="选择状态" >
					<el-option
					v-for="(item, index) in status"
					:key="index"
					:label="item.label"
					:value="item.value"
					/>
				</el-select>
				<el-button type="primary" @click="search">搜 索</el-button>
			</el-col>
			<el-col :span="4">
				<el-button type="primary" @click="back">返回</el-button>
				<el-button type="primary" @click="search('刷新成功')">刷新</el-button>
			</el-col>
			<el-col :span="4" :push="6">
				<el-button style="float: right;" :disabled="ids.length == 0" type="primary" @click="deleted" v-if="hasPerm('base:batch-issue:delete')">删除</el-button>
				<el-button style="float: right; margin-right: 20px;" :disabled="ids.length == 0" type="primary" @click="push" v-if="hasPerm('base:batch-issue:add')">下发</el-button>
			</el-col>
		</el-row>
		<el-row :gutter="20">
			<el-col :span="24">
				<el-table row-key="id" stripe :data="dataList" border height="calc(100vh - 300px)" style="width: 100%;" @selection-change="handleSelectionChange">
					<el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
					<el-table-column prop="id" align="center" label="id"/>
					<el-table-column prop="buildingNumber" align="center" label="楼房编号" />
					<el-table-column prop="communityName" align="center" label="所属小区" />
					<el-table-column prop="dataId" align="center" label="数据ID" />
					<el-table-column prop="dataType" align="center" :formatter="formatDataType" label="数据类型" />
					<el-table-column prop="distributionType" align="center" label="下发类型" />
					<el-table-column prop="createTime" align="center" label="创建时间" />
					<el-table-column prop="failReason" align="center" label="失败原因" />
				</el-table>
			</el-col>
			<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
				<el-pagination background :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
			</el-col>
		</el-row>
	</div>
	
	<div v-show="index == 2">
		<el-row :gutter="20">
			<el-col :span="4">
				<div class="title_des">
					<img style="height: 30px;width: 35px;" src="../../assets/img/person_icon.png" />
					<span>&nbsp;&nbsp;人员未下发列表</span>
				</div>
			</el-col>
			
			<el-col :span="6" style="display:flex">
				<el-input style="margin-right:10px;width:50%" @keydown.enter="search" v-model="searchModel.userName" placeholder="姓名" clearable />
				<el-select clearable style="margin-right:10px" v-model="status.value" class="m-2" placeholder="选择状态" >
					<el-option
					v-for="(item, index) in status"
					:key="index"
					:label="item.label"
					:value="item.value"
					/>
				</el-select>
				<el-button type="primary" @click="search">搜 索</el-button>
			</el-col>
			<el-col  :span="4">
				<el-button type="primary" @click="back">返回</el-button>
				<el-button type="primary" @click="search('刷新成功')">刷新</el-button>
			</el-col>
			<el-col :span="4" :push="6">
				<el-button style="float: right;" :disabled="ids.length == 0" type="primary" @click="deleted" v-if="hasPerm('base:batch-issue:delete')">删除</el-button>
				<el-button style="float: right; margin-right: 20px;" :disabled="ids.length == 0" type="primary" @click="push" v-if="hasPerm('base:batch-issue:add')">下发</el-button>
			</el-col>
		</el-row>
		<el-row :gutter="20">
			<el-col :span="24">
				<el-table row-key="id" stripe :data="dataList" border height="calc(100vh - 300px)" style="width: 100%; " @selection-change="handleSelectionChange">
					<el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
					<el-table-column prop="id" align="center" label="id"/>
					<el-table-column prop="personName" align="center" label="姓名" />
					<el-table-column prop="communityName" align="center" label="所属小区" />
					<el-table-column prop="dataId" align="center" label="数据ID" />
					<el-table-column prop="dataType" align="center" :formatter="formatDataType" label="数据类型" />
					<el-table-column prop="distributionType" align="center" label="下发类型" />
					<el-table-column prop="createTime" align="center" label="创建时间" />
					<el-table-column prop="failReason" align="center" label="失败原因" />
				</el-table>
			</el-col>
			<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
				<el-pagination background :page-sizes="[10, 20, 50, 100, total]" layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
			</el-col>
		</el-row>
	</div>
	
	<div v-show="index == 3">
		<el-row :gutter="20">
			<el-col :span="4">
				<div class="title_des">
					<img style="height: 30px;width: 35px;" src="../../assets/img/car_icon.png" />
					<span>&nbsp;&nbsp;车辆未下发列表</span>
				</div>
			</el-col>
			<el-col :span="6" style="display:flex">
				<el-input style="margin-right:10px;width:50%" @keydown.enter="search" v-model="searchModel.vehicleNumber" placeholder="车牌号" clearable />
				<el-select clearable style="margin-right:10px" v-model="status.value" class="m-2" placeholder="选择状态" >
					<el-option
					v-for="(item, index) in status"
					:key="index"
					:label="item.label"
					:value="item.value"
					/>
				</el-select>
				<el-button type="primary" @click="search">搜 索</el-button>
			</el-col>

			<el-col  :span="4">
				<el-button type="primary" @click="back">返回</el-button>
				<el-button type="primary" @click="refresh('刷新成功')">刷新</el-button>
			</el-col>
			<el-col :span="4" :push="6">
				<el-button style="float: right;" :disabled="ids.length == 0" type="primary" @click="deleted" v-if="hasPerm('base:batch-issue:delete')">删除</el-button>
				<el-button style="float: right; margin-right: 20px;" :disabled="ids.length == 0" type="primary" @click="push" v-if="hasPerm('base:batch-issue:add')">下发</el-button>
			</el-col>
		</el-row>
		<el-row :gutter="20">
			<el-col :span="24">
				<el-table row-key="id" stripe :data="dataList" border height="calc(100vh - 300px)" style="width: 100%;" @selection-change="handleSelectionChange">
					<el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
					<el-table-column prop="id" align="center" label="id"/>
					<el-table-column prop="vehicleNumber" align="center" label="车牌号" />
					<el-table-column prop="communityName" align="center" label="所属小区" />
					<el-table-column prop="dataId" align="center" label="数据ID" />
					<el-table-column prop="dataType" align="center" :formatter="formatDataType" label="数据类型" />
					<el-table-column prop="distributionType" align="center" label="下发类型" />
					<el-table-column prop="createTime" align="center" label="创建时间" />
					<el-table-column prop="failReason" align="center" label="失败原因" />
					<!-- <el-table-column align="center" width="225" label="操作">
						<template #default="scope">
							<el-button type="text" size="default" @click="detail(scope.row.id)">详情</el-button>
						</template>
					</el-table-column> -->
				</el-table>
			</el-col>
			<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
				<el-pagination background :page-sizes="[10, 20, 50, 100, total]" layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
			</el-col>
		</el-row>
	</div>
	<el-dialog width="50%" v-model="dialog.show" :title="dialog.title">

	</el-dialog>
	
</template>

<script>
import { ElLoading } from 'element-plus'
import HouseImg from "@/assets/img/essay.png"
import { issueSta, issuePage, issueBatchIssue, issueBatchDelte } from "@/api/base/lssue.js"
import { listDictByNameEn } from "@/api/admin/dict"
import { pushUser } from "@/api/admin/auth";
import mitt from "@/utils/mitt"
export default {
	components: {},
	data() {
		return {
			searchModel: {
				communityId: localStorage.getItem("communityId")
			},
			index: 0,
			dataList: [],
			ids: [],
			total: 0,
			pageSize: 10,
			status: [{
				value: "ADD",
				label: "ADD"
			}, {
				value: "UPDATE",
				label: "UPDATE"
			}, {
				value: "DELETE",
				label: "DELETE"
			}],
			dialog: {
				show: false
			},
			fullLoading: null
		}
	},
	methods: {
		//用户操作
		pushUserAction(actionName) {
			if (actionName != 'home') {
        let senObj = {
          oper: actionName,
          receiveClient: "all",
          tags: ['manager']
        }
				pushUser(senObj).then(res =>{
					if (res !== null && res.code === 0) {
					} else {
					}
				})
			}
		},
		detail(){
			this.dialog.show = true
			this.dialog.title = "详情"
		},
		// 批量操作
		handleSelectionChange(val) {
			let list = []
			for (let item of val) {
				list.push(item.id)
			}
			this.ids = list
		},
		back() {
			this.$router.go(-1)
		},
		refresh(success) {
			this.search()
			if (success) {
				this.$message.success("刷新成功")
			}
		},
		search() {
			this.searchModel.distributionType = this.status.value
			if (!this.status.value) {
				delete (this.searchModel["distributionType"])
			}
			console.log(this.searchModel);
			if (!this.searchModel.vehicleNumber) {
				// this.searchModel.vehicleNumber=""
				delete (this.searchModel["vehicleNumber"])
			}
			if (!this.searchModel.userName) {
				// this.searchModel.userName=""
				delete (this.searchModel["userName"])
			}
			if (!this.searchModel.buildingNumber) {
				// this.searchModel.userName=""
				delete (this.searchModel["buildingNumber"])
			}
			issuePage(this.searchModel)
				.then(res => {
					this.dataList = res.data.result.list
					this.total = res.data.result.total
				})
		},
		push() {
			this.fullLoading = ElLoading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.7)' , text: '正在执行下发，请不要刷新！！！'});
			issueBatchIssue({ communityId: localStorage.getItem("communityId"), ids: this.ids, dataType: this.searchModel.dataType })
				.then(res => {
					this.fullLoading.close()
					this.search()
					this.$message.success(res.data.msg)
				}).catch(()=> { 
					this.fullLoading.close() 
				})
		},
		deleted() {
			issueBatchDelte({ communityId: localStorage.getItem("communityId"), ids: this.ids, dataType: this.searchModel.dataType })
				.then(res => {
					this.search()
					this.$message.success(res.data.msg)
				})
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num) {
			this.searchModel.pageSize = num
			this.search()
		},
		// formatDataType(row){
		// 	if(row.dataType == 0){
		// 		return "楼栋"
		// 	}else if(row.dataType == 4){
		// 		return "人员"
		// 	}else if(row.dataType == 5){
		// 		return "车辆"
		// 	}
		// },
		formatDataType(row, column, cellValue, index) {
			let result = "";
			for (let item of this.statusList) {
				if (item.nameEn == cellValue) {
					result = item.nameCn;
				}
			}
			return result;
		},
		async init() {
			this.index = this.$route.query.index
			if (this.index == 1) {
				this.searchModel.dataType = 0
				this.pushUserAction("房屋下发")
			} else if (this.index == 2) {
				this.searchModel.dataType = 4
				this.pushUserAction("人员下发")
			} else if (this.index == 3) {
				this.searchModel.dataType = 5
				this.pushUserAction("车辆下发")
			}

			let status_res = await listDictByNameEn('data_type')
			this.statusList = status_res.data.result

			this.search()
		}
	},
	created() {
		this.init()
	}
}
</script>
<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	.title_des{
		font-size: 15px;
		display: flex;
		align-items: flex-end;
	}
</style>
