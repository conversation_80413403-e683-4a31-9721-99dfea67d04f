<template>
  <el-dialog v-model="show" width="46%" :title="title">
    <!-- <div>
      <p>选择时间段</p>
      <el-date-picker
        v-model="beginTime"
        type="datetime"
        value-format="YYYY-MM-DD HH:mm:ss"
        placeholder="开始">
      </el-date-picker>
      <el-date-picker
        v-model="endTime"
        type="datetime"
        value-format="YYYY-MM-DD HH:mm:ss"
        placeholder="结束">
      </el-date-picker>
      <el-button type="porimary" size="small" @click="searchHistoryTime">确定</el-button>
    </div> -->
    <div class="monitor-view-container">
      <div style="width: 961px; height: 460.5px" id="player"></div>
    </div>
  </el-dialog>
</template>

<script>
import { queryMonitorByDevNo } from "@/api/device/device";
import mitt from "@/utils/mitt";

export default {
  data() {
    return {
      backVideo: {},
      baseUrl: import.meta.env.VITE_BASE_API,
      monitorHistoryUrl: "", //历史监控信息
      beginTime: "", // 开始时间
      endTime: "", // 结束时间
      player: null,
      splitNum: 2,
      title: "",
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
      show: true,
      urls: {
        realplay: "",
        talk: "",
        playback: "",
      },
      playback: {
        startTime: "2021-07-26T00:00:00",
        endTime: "2021-07-26T23:59:59",
        valueFormat: "",
        seekStart: "2021-07-26T12:00:00",
        rate: "",
      },
      loading: [],
      muted: true,
      volume: 50,
      performanceLack: false, //浏览器性能不足回调
    };
  },
  watch: {
    show(nV) {
      if (!nV) {
        if (this.player) {
          this.stopAllPlay();
          this.stopPlay();
        }
      }
    },
  },
  created() {
    // mitt.off("openWSVideo");
    this.init();
    mitt.on("openWSVideo", (data) => {
      this.title = `视频监控(${data.name})`;
      console.log(data);
      this.createPlayer();
      this.backVideo = data.video;
      this.treeCheckChange(data.video);
      this.show = true;
    });
  },
  mounted() {
    this.show = false;
    // this.$el.style.setProperty("display", "block");
  },

  beforeUnmount() {
    window.removeEventListener("resize", this.resizeListener);

    if (this.player) {
      try {
        this.player.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
      } catch (error) {}
      try {
        this.player.JS_Disconnect().then(
          () => {
            // 断开与插件服务连接成功
          },
          () => {
            // 断开与插件服务连接失败
          }
        );
      } catch (error) {}
    }

    this.player = null;
  },

  beforeDestroy() {
    window.removeEventListener("resize", this.resizeListener);

    if (this.player != null) {
      this.player.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
      this.player.JS_Disconnect().then(
        () => {
          // 断开与插件服务连接成功
        },
        () => {
          // 断开与插件服务连接失败
        }
      );
    }

    this.player = null;
  },
  methods: {
    //  选择时间段   查看历史监控视频
    searchHistoryTime() {
      if (!!!this.beginTime || !!!this.endTime) {
        this.$message({
          message: "请先选择开始时间或结束时间",
          type: "warning",
        });
        return;
      }
      let t1 = new Date(this.beginTime).getTime();
      let t2 = new Date(this.endTime).getTime();
      if (t2 - t1 <= 0) {
        this.$message({
          message: "开始时间不得大于结束时间！",
          type: "warning",
        });
        return;
      }
      if (t2 - t1 >= 259200000) {
        this.$message({
          message: "时间区间不得超过3天！",
          type: "warning",
        });
        return;
      }
      this.backVideo.beginTime = this.beginTime;
      this.backVideo.endTime = this.endTime;

      queryMonitorByDevNo(this.backVideo).then((res) => {
        this.monitorHistoryUrl = res.data.result.url;
        this.init();
        this.createPlayer();
        this.playbackStart();
      });
    },
    /* 回放 */
    playbackStart() {
      let { player, mode, playback } = this,
        index = player.currentWindowIndex,
        playURL = this.monitorHistoryUrl,
        { startTime, endTime } = playback;
      startTime = this.beginTime.slice(0, 19);
      endTime = this.endTime.slice(0, 19);
      startTime += "Z";
      endTime += "Z";
      console.log("111111111111111");
      console.log(playURL, mode, index, startTime, endTime);
      player
        .JS_Play(playURL, { playURL, mode }, index, startTime, endTime)
        .then(
          () => {
            console.log("playbackStart success");
            this.playback.rate = 1;
          },
          (e) => {
            console.error(e);
          }
        );
    },

    onContextmenu(event) {
      this.$contextmenu({
        items: [
          {
            label: "主码流",
            onClick: function mainCodeStream() {
              // alert('主码流切换')
              that.$emit("mainCodeStream");
            },
          },
          {
            label: "辅码流",
            onClick: function auxiliaryCodeStream() {
              // alert('辅码流切换')
              that.$emit("auxiliaryCodeStream");
            },
          },
        ],
        event,
        customClass: "custom-class",
        zIndex: 999,
        minWidth: 230,
      });
      return false;
    },

    // 监控被选中或取消
    treeCheckChange(obj) {
      this.previewPlay(obj);
    },

    // 监控预览请求地址并播放
    previewPlay(obj) {
      // console.log("监控预览请求地址并播放");
      const dict = obj;
      if (dict.code === null) {
        return;
      }
      var videoSrc = "";
      const param = {
        code: "",
        id: "",
        urlType: "",
        communityId: "",
        playType: "previewURLs",
      };

      param.code = dict.code;
      param.communityId = dict.communityId;
      param.id = dict.id;
      param.urlType = "ws";
      param.playType = "previewURLs";
      // showLoading();
      // console.log("param: " + JSON.stringify(param));

      queryMonitorByDevNo(param)
        .then((res) => {
          if (res.data.result !== null) {
            if (res.data.result !== null) {
              videoSrc = res.data.result.url;

              setTimeout(() => {
                this.realplay(videoSrc, obj);
              }, 100);
            } else {
              videoSrc = "";
              this.$message({
                type: "warning",
                message: "无视频播放资源",
              });
            }
          } else {
            this.$message({
              type: "error",
              message: "获取数据失败",
            });
          }
        })
        .catch((e) => {
          this.$message({
            type: "error",
            message: "获取数据失败",
          });
        });
    },
    resizeListener() {
      console.log("窗口变动了");
      // window.location.reload()
      // if (this.player) this.player.JS_Resize();
      setTimeout(() => {
        if (this.player) this.player.JS_Resize();
      }, 50);
    },
    init() {
      // 设置播放容器的宽高并监听窗口大小变化
      // this.show=false
      // if (this.show) {
      console.log(this.show);
      window.addEventListener("resize", this.resizeListener);
      // }
    },

    createPlayer() {
      var that = this;
      this.player = new window.JSPlugin({
        szId: "player",
        szBasePath: "/lib/h5player",
        // szBasePath: '../../../lib/h5player',
        // iMaxSplit: 16, //分屏播放
        // iCurrentSplit: 1,
        // openDebug: true,
        oStyle: {
          // borderSelect: IS_MOVE_DEVICE ? "#000" : "#FFCC00",
        },
      });

      // 事件回调绑定
      this.player.JS_SetWindowControlCallback({
        performanceLack: function () {
          //性能不足回调
          that.performanceLack = true;
          console.log("performanceLack callback: ");
        },
      });
    },

    /* 预览&对讲 */
    realplay(urls, obj) {
      let { player, mode = 1, config = {} } = this,
        index = player.currentWindowIndex;

      this.player
        .JS_Play(
          urls,
          {
            playURL: urls,
            mode: mode, //解码类型：0=普通模式; 1=高级模式 默认为0,h5高级模式才能播放
            ...config,
          },
          index
        )
        .then(() => {
          var playerObj = {
            currentWindowIndex: index,
            src: urls,
            obj: obj,
          };
        });
    },
    stopPlay() {
      if (this.player)
        this.player.JS_Stop().then(
          () => {
            this.playback.rate = 0;
            console.log("stop realplay success");
          },
          (e) => {
            console.log(e);
          }
        );
    },
    stopAllPlay() {
      if (this.player)
        this.player.JS_StopRealPlayAll().then(
          () => {
            this.playback.rate = 0;
            console.log("stopAllPlay success");
          },
          (e) => {
            console.log(e);
          }
        );
    },
  },
  // beforeDestroy() {

  //   if(this.player)
  //   {
  //     this.stopAllPlay()
  //     this.stopPlay()
  //     this.player.destroy()
  //     console.log(234);
  //   }
  //   this.$store.state.monitorDeviceData = null;
  //   this.ptzShow(false);
  // },
};
</script>

<style scoped lang="less">
/deep/ .sub-wnd {
  width: 100% !important;
}
/deep/ #player_playCanvas0 {
  width: 100% !important;
  height: 100% !important;
}
/deep/ #player-container-0 {
  height: 100% !important;
}
/deep/ #player_playCanvas0 {
  transform: scale(1) !important;
}
.monitor-view-container {
  width: 100%;
  height: 461px;
}
::v-deep .el-loading-mask {
  z-index: 9999 !important;
}
body {
  padding: 8px 8px 32px;
}
#app {
  display: none;
}

.actions {
  padding-left: 8px;
}

.ant-form-item {
  margin-bottom: 8px;
}
.ant-btn {
  margin-right: 2px;
}

.icon-wrapper {
  position: relative;
  padding-left: 20px;
}
.icon-wrapper .anticon {
  position: absolute;
  top: -2px;
  width: 16px;
  height: 16px;
  line-height: 1;
  font-size: 16px;
  left: 0;
}

::-webkit-media-controls {
  display: none !important;
}
#player {
  width: 100% !important;
  // width: 961px!important;
  height: 100% !important;
  height: calc(461px / 100vh) !important;
}

@media screen and (max-width: 991px) {
  #player {
    width: calc(100vw - 16px);
    height: calc((100vw - 16px) * 5 / 8);
  }
}
@media screen and (min-width: 992px) {
  #player {
    width: calc(50vw - 8px);
    height: calc((50vw - 8px) * 5 / 8);
  }
}
</style>
