<template>
	<el-dialog draggable width="32%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="tagModel" label-width="66px">

			<el-form-item label="名称" prop="nameCn">
				<el-input v-model="tagModel.nameCn" placeholder="名称"></el-input>
			</el-form-item>

			<el-form-item label="编码" prop="nameEn">
				<el-input v-model="tagModel.nameEn" placeholder="编码"></el-input>
			</el-form-item>

			<el-form-item label="样式" prop="cssClass">
				<!-- <el-input v-model="tagModel.cssClass" placeholder="样式"></el-input> -->
				<el-input v-model="tagModel.cssClass" placeholder="样式" >
					<!-- <template #prepend>
						<el-button :icon="Search" />
					</template> -->
					<template #append>
						<el-color-picker v-model="tagModel.cssClass" />
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<el-radio-group v-model="tagModel.status">
					<el-radio-button  v-for="item in statusList" :key="item.nameEn"
						:label="parseInt(item.nameEn)">{{ item.nameCn }}</el-radio-button >
				</el-radio-group>
			</el-form-item>

			
			<el-form-item label="排序" prop="sort">
				<!-- <el-input v-model="tagModel.sort" placeholder="排序"></el-input> -->
				<el-input-number v-model="tagModel.sort" :min="1" />
			</el-form-item>

			<el-form-item label="备注" prop="remark">
				<el-input
					v-model="tagModel.remark"
					maxlength="200"
					placeholder="请解释字典项代表的意思"
					show-word-limit
					type="textarea"
				/>
			</el-form-item>
		</el-form>
		
		<template #footer>
			<span class="dialog-footer">
				<!-- <el-button @click="dialogVisible = false">Cancel</el-button> -->
				<el-button type="primary" @click="onSubmit">
					提 交
				</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script>
import { addTagDict, editTagDict } from "@/api/admin/dict"
import mitt from "@/utils/mitt";
export default {
	props: ['statusList'],
	data() {
		return {
			loading: false,
			tagModel: {},
			dialog: {},
			rules: {
				nameCn: [{
					required: true,
					message: '请输入名称',
					trigger: 'blur',
				}],
				nameEn: [{
					required: true,
					message: '请输入编码',
					trigger: 'blur',
				}],
				cssClass: [{
					required: true,
					message: '请输入样式',
					trigger: 'blur',
				}],
			}
		}
	},
	methods: {
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.tagModel.id == 0) {
						addTagDict(this.tagModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						editTagDict(this.tagModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})

		}
	},
	mounted() {
		this.$nextTick(function () {
			mitt.on('openActionEdit', (tag) => {
				this.tagModel = {}
				this.dialog.show = true
				this.dialog.title = "修改信息"
			})
			mitt.on('openTagAdd', () => {
				this.tagModel = {
					id: 0,
					sort: 1,
					status: 1
				}
				this.dialog.show = true
				this.dialog.title = "添加标签"
			})
		})
	}
}
</script>
<style>
.el-input-group__append{
	padding: 0!important;
}
</style>