<template>
	<div style="width: 100%;height: 100%; overflow: hidden;display: flex;flex-direction: column;justify-content: space-between;">
		<!-- 上 -->
		<div style="width: 100%;height: 47%;display: flex;justify-content: space-between;">
			<div style="width: 47%;height: 100%;background: #fff;padding: 20px;">
				<div style="width: 100%;height: 100%;box-sizing: border-box;" id="ps"></div>
			</div>
			<div style="width: 47%;height: 100%;background: #fff;padding: 20px;">
				<div style="width: 100%;height: 100%;box-sizing: border-box;" id="de"></div>
			</div>
		</div>
		<!-- 下 -->
		<div style="width: 100%;height: 46%;display: flex;justify-content: space-between;">
			<div style="width: 47%;height: 100%;background: #fff;padding: 20px;">
				<div style="width: 100%;height: 100%;box-sizing: border-box;" id="vs"></div>
			</div>
			<div style="width: 47%;height: 100%;background: #fff;padding: 20px;">
				<div style="width: 100%;height: 100%;box-sizing: border-box;" id="ev"></div>
			</div>
		</div>
	</div>
	<!-- <div>
		<canvas id="canvas" width="300" height="200" style="margin-top: 100px;border: blue 1px solid;">
			<img src="https://img-prod-cms-rt-microsoft-com.akamaized.net/cms/api/am/imageFileData/RE4wtbz?ver=f0ae" alt="">
		</canvas>
	</div> -->
</template>

<script setup>
import { onMounted,nextTick,ref,onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { personStream,vehicleStream,deviceType,event } from "@/api/home/<USER>"
import * as echarts from 'echarts'

onMounted( () => {
	// var canvasNodes = document.getElementById('canvas')
	// var ctx = canvasNodes.getContext('2d')
	// ctx.beginPath();
	// for (var i = 0; i < 5; i++) {
	// 	ctx.lineTo(Math.cos((18 + i * 72) / 180 * Math.PI) * 50+200,
	// 		- Math.sin((18 + i * 72) / 180 * Math.PI) * 50+80) ;
	// 	ctx.lineTo(Math.cos((54 + i * 72) / 180 * Math.PI) * 25+200,
	// 		- Math.sin((54 + i * 72) / 180 * Math.PI) * 25+80) ;
	// }
	
	// ctx.closePath();
	// ctx.fillStyle = '#f00';
	// ctx.fill();


	// ctx.font = "48px serif";
	// ctx.strokeText("Hello world", 10, 50);

	
	// ctx.fillStyle="rgb(200,0,0)"
	// ctx.fillRect(25, 25, 50, 50);
	// ctx.fillStyle="rgba(0,0,200,0.7)"
	// ctx.fillRect(45, 45, 50, 50);
	// ctx.fillStyle="rgb(0,200,200)"

    // ctx.fillRect(50, 50, 20, 20);
	dispose()
	init()
})

function init(){
	let data = { communityId:localStorage.getItem("communityId")}
	personStream(data).then(res =>{handlerPs(res)})
	vehicleStream(data).then(res =>{handlerVs(res)})
	deviceType(data).then(res =>{handlerDe(res)})
	event(data).then(res =>{handlerEv(res)})
}
function handlerPs(data){
	let list = extractNV(data)
	insterXianEchat(list,"ps","单日人流统计")
}
function handlerVs(data){
	let list = extractNV(data)
	insterZhuEchat(list,"vs","单日车流统计")
}
function handlerDe(data){
	insterBingEchat(data.data.result,"de","设施统计")
}
function handlerEv(data){
	let list = extractNV(data)
	insterXianEchat(list,"ev","事件统计")
}
function extractNV(data){
	let nameList = []
	let valueList = []
	for(let item of data.data.result){
		nameList.push(item.dateTime)
		valueList.push(item.number)
	}
	return [nameList,valueList]
}

function insterZhuEchat(list,docm,title){
	// 基于准备好的dom，初始化echarts实例
	var myChart = echarts.init(document.getElementById(docm))
	// 绘制图表
	myChart.setOption({
		tooltip: {
			trigger: 'item'
		},
		title: {
			text: title
		},
		tooltip: {},
		xAxis: {
			data: list[0]
		},
		yAxis: {},
		series: [{
			type: 'bar',
			data: list[1]
		}]
	})
}
function insterXianEchat(list,docm,title){
	// 基于准备好的dom，初始化echarts实例
	var myChart = echarts.init(document.getElementById(docm))
	// 绘制图表
	myChart.setOption({
		tooltip: {
			trigger: 'item'
		},
		title: {
			text: title
		},
		xAxis: {
			type: 'category',
			data: list[0]
		},
		yAxis: {
			type: 'value'
		},
		series: [{
			data: list[1],
			type: 'line',
			smooth: true
		}]
	})
}
function insterBingEchat(list,docm,title){
	// 基于准备好的dom，初始化echarts实例
	var myChart = echarts.init(document.getElementById(docm))
	// 绘制图表
	myChart.setOption({
		tooltip: {
			trigger: 'item'
		},
		title: {
			text: title
		},
		legend: {
			bottom: "0%"
		},
		series: [{
			emphasis: {
				label: {
					show: true,
					fontWeight: 'bold'
				}
			},
			type: 'pie',
			radius: ['40%', '70%'],
			label: {
				show: false,
				position: 'center'
			},
			data: list
		}]
	})
}
function dispose(){
	var myChart = echarts.init(document.getElementById('ps'))
	myChart.dispose() 
	myChart = echarts.init(document.getElementById('de'))
	myChart.dispose()
	myChart = echarts.init(document.getElementById('vs'))
	myChart.dispose()
	myChart = echarts.init(document.getElementById('ev'))
	myChart.dispose()
}

</script>
