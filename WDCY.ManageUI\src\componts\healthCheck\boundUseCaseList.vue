<template>
    <el-dialog draggable width="70%" top="1vh" v-model="dialog.show" destroy-on-close title="绑定功能">
	<el-row :gutter="20">
		<el-col :span="3">
			<el-input v-model="searchModel.sceneName" @keydown.enter="search" placeholder="功能名称" clearable />
		</el-col>
		<el-col :span="3">
			<el-select style="width: 100%;" v-model="searchModel.status" placeholder="状态" clearable>
				<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
					:value="item.nameEn"></el-option>
			</el-select>
		</el-col>
		<el-col :span="1">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table row-key="id" stripe :data="useCaseScenarioList" border ref="multipleTable" height="calc(100vh - 300px)" style="width: 100%" @selection-change="handleSelectionChange">
        		<el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
				<el-table-column prop="id" align="center" label="ID"/>
				<el-table-column prop="sceneName" align="center" label="场景名称" />
                
				<el-table-column prop="status" align="center" label="状态" width="100">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
						<!-- <el-switch :active-value="1" :inactive-value="0" v-model="scope.row.status" @change="editStatus(scope.row)" /> -->
					</template>
				</el-table-column>
				<el-table-column prop="note" align="left" label="备注"/>
				<el-table-column prop="createTime" align="center" width="170" label="创建时间"/>
				<el-table-column prop="updateTime" align="center" width="170" label="修改时间"/>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
    <el-row justify="center">
        <el-button type="primary" style="width: 100px;height: 30px;margin-top: 5px;" @click="onSubmit">提 交
        </el-button>
    </el-row>
  </el-dialog>
</template>
<script>
import { listCheckScene } from "@/api/healthCheck/useCaseScenario"
import { listHealthExamina, deleteHealthExamina, getHealthExamina, editHealthExamina, toExaminaOne, getProductionAssert } from "@/api/healthCheck/healthExamination"
import { listAllScene, batchCheckCase } from "@/api/healthCheck/useCaseScenario"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"

export default {
	data() {
		return {
			searchModel: {},
			batchModel: {},
			dialog: {},
			useCaseScenarioList: [],
			statusList: [],
			total: 0,
			pageSize: 10,
			boundFunc: '',
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {

			listCheckScene(this.searchModel)
			.then(res => {
				this.useCaseScenarioList = res.data.result.list
				this.total = res.data.result.total
			})
		},
        onSubmit(){
			console.log(this.boundFunc);
            this.$parent.caseTestModel.checkSceneId = this.boundFunc.id
            this.$parent.caseTestModel.note = this.boundFunc.note
            this.$parent.caseTestModel.sceneName = this.boundFunc.sceneName
            this.$parent.jsonVal = JSON.parse(this.boundFunc.expandParams)
            this.dialog.show = false
        },
		handleSelectionChange(val) {
            if (val.length > 1) {
                this.$refs.multipleTable.toggleRowSelection(val[0], false)
                this.$message.error('最多绑定一个')
            } else {
                this.$refs.multipleTable.toggleRowSelection(val[0], true)
                this.boundFunc = val[0]
            }
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			try{
				// let status_res = await listDictByNameEn('user_status')
				// this.statusList = status_res.data.result
				let resStatus = await listDictByNameEn('func_status')
				this.statusList = resStatus.data.result

				let res = await listCheckScene(this.searchModel)
				this.useCaseScenarioList = res.data.result.list
				this.total = res.data.result.total

			}catch(err){
			}
		}
	},
    mounted() {
		this.$nextTick(function () {
			mitt.on('openFuncList', (data) => {
				this.dialog.show = true
				this.dialog.title = "绑定功能"
			})
		})
	},
	created() {
		mitt.on('refreshUserList',()=>{this.search()})
		this.init()

	},
    
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 5px;
		padding: 5px 10px;
	}
	div /deep/ .el-tree-node__content{
		height: 35px;
	}
	div /deep/ .el-tree-node__content:hover{
		color: #fff;
		background-color: var(--el-color-primary-light-5);
	}

	div /deep/ .el-tree-node__expand-icon{
		font-size: 18px;
		margin-right: 15px;
	}
</style>