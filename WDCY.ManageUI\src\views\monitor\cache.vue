<template>
    <div class="app-container">
        <!-- <el-row :gutter="20">
            <el-col :span="6" style="display:flex">
                <el-button type="primary" @click="back">返 回</el-button>
            </el-col>
            <el-col :span="4" :push="14">
                <el-button style="float: right;" :disabled="ids.length == 0" type="primary" @click="deleted">删 除</el-button>
            </el-col>
        </el-row> -->
      <el-row>
        <el-col :span="24" class="card-box">
          <el-card>
            <div slot="header" style="border-bottom: 1px solid #eee; line-height: 44px;"><span>基本信息</span></div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%">
                <tbody>
                  <tr>
                    <td><div class="cell">Redis版本</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.redis_version }}</div></td>
                    <td><div class="cell">运行模式</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.redis_mode == "standalone" ? "单机" : "集群" }}</div></td>
                    <td><div class="cell">端口</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.tcp_port }}</div></td>
                    <td><div class="cell">客户端数</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.connected_clients }}</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">运行时间(天)</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.uptime_in_days }}</div></td>
                    <td><div class="cell">使用内存</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.used_memory_human }}</div></td>
                    <td><div class="cell">使用CPU</div></td>
                    <td><div class="cell" v-if="cache.info">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>
                    <td><div class="cell">内存配置</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.maxmemory_human }}</div></td>
                  </tr>
                  <tr>
                    <td><div class="cell">AOF是否开启</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.aof_enabled == "0" ? "否" : "是" }}</div></td>
                    <td><div class="cell">RDB是否成功</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.rdb_last_bgsave_status }}</div></td>
                    <td><div class="cell">Key数量</div></td>
                    <td><div class="cell" v-if="cache.dbSize">{{ cache.dbSize }} </div></td>
                    <td><div class="cell">网络入口/出口</div></td>
                    <td><div class="cell" v-if="cache.info">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </el-card>
        </el-col>
  
        <el-col :span="12" class="card-box">
          <el-card>
            <div slot="header"><span>命令统计</span></div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <div ref="commandstats" style="height: 420px" />
            </div>
          </el-card>
        </el-col>
  
        <el-col :span="12" class="card-box">
          <el-card>
            <div slot="header">
              <span>内存信息</span>
            </div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <div ref="usedmemory" style="height: 420px" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </template>
<script>
import * as echarts from 'echarts'
import {getCache} from "@/api/monitor/cache"
// import '@/assets/css/index.scss' // global css
import '@/assets/css/ruoyi.scss' // ruoyi css
export default {
    data(){
        return {
            // 加载层信息
            loading: [],
            // 统计命令信息
            commandstats: null,
            // 使用内存
            usedmemory: null,
            // cache信息
            cache: [],
        }
    },
    methods:{
        back(){
            this.$router.go(-1)
        },
    //     handlerDe(data){
    //         console.log(data);
    //         this.insterBingEchat(data.data.result.commandStats,"de","命令统计")
    //     },
    //     extractNV(data){
    //         let nameList = []
    //         let valueList = []
    //         for(let item of data.data.result){
    //             nameList.push(item.dateTime)
    //             valueList.push(item.number)
    //         }
    //         return [nameList,valueList]
    //     },
    //      insterZhuEchat(list,docm,title){
	// // 基于准备好的dom，初始化echarts实例
	// var myChart = echarts.init(document.getElementById(docm))
	// // 绘制图表
	// myChart.setOption({
	// 	tooltip: {
	// 		trigger: 'item'
	// 	},
	// 	title: {
	// 		text: title
	// 	},
	// 	tooltip: {},
	// 	xAxis: {
	// 		data: list[0]
	// 	},
	// 	yAxis: {},
	// 	series: [{
	// 		type: 'bar',
	// 		data: list[1]
	// 	}]
	// })
    //     },
    //     insterXianEchat(list,docm,title){
    //         // 基于准备好的dom，初始化echarts实例
    //         var myChart = echarts.init(document.getElementById(docm))
    //         // 绘制图表
    //         myChart.setOption({
    //             tooltip: {
    //                 trigger: 'item'
    //             },
    //             title: {
    //                 text: title
    //             },
    //             xAxis: {
    //                 type: 'category',
    //                 data: list[0]
    //             },
    //             yAxis: {
    //                 type: 'value'
    //             },
    //             series: [{
    //                 data: list[1],
    //                 type: 'line',
    //                 smooth: true
    //             }]
    //         })
    //     },
    //     insterBingEchat(list,docm,title){
	// // 基于准备好的dom，初始化echarts实例
	// var myChart = echarts.init(document.getElementById(docm))
	// // 绘制图表
	// myChart.setOption({
	// 	tooltip: {
	// 		trigger: 'item'
	// 	},
	// 	title: {
	// 		text: title
	// 	},
	// 	legend: {
    //         // show: false,
	// 		bottom: "0%"
	// 	},
	// 	series: [{
	// 		emphasis: {
	// 			label: {
	// 				show: true,
	// 				fontWeight: 'bold'
	// 			}
	// 		},
	// 		type: 'pie',
	// 		radius: ['40%', '70%'],
	// 		label: {
	// 			show: false,
	// 			position: 'center'
	// 		},
    //         labelLine:{
    //             show:true
    //         },
	// 		data: list
	// 	}]
	// })
    //     },
    //     dispose(){
    //         var myChart = echarts.init(document.getElementById('de'))
    //         myChart.dispose()
    //     },
        init(){
            let data = { communityId:localStorage.getItem("communityId")}
            getCache(data).then(res =>{
                this.handlerDe(res)
            })
        },
        getList(data) {
            getCache(data).then((response) => {
                this.cache = response.data.result;

                this.commandstats = echarts.init(this.$refs.commandstats, "macarons");
                this.commandstats.setOption({
                tooltip: {
                    trigger: "item",
                    formatter: "{a} <br/>{b} : {c} ({d}%)",
                },
                series: [
                    {
                    name: "命令",
                    type: "pie",
                    roseType: "radius",
                    radius: [15, 95],
                    center: ["50%", "38%"],
                    data: response.data.result.commandStats,
                    animationEasing: "cubicInOut",
                    animationDuration: 1000,
                    },
                ],
                });
                this.usedmemory = echarts.init(this.$refs.usedmemory, "macarons");
                this.usedmemory.setOption({
                tooltip: {
                    formatter: "{b} <br/>{a} : " + this.cache.info.used_memory_human,
                },
                series: [
                    {
                    name: "峰值",
                    type: "gauge",
                    min: 0,
                    max: 1000,
                    detail: {
                        formatter: this.cache.info.used_memory_human,
                    },
                    data: [
                        {
                        value: parseFloat(this.cache.info.used_memory_human),
                        name: "内存消耗",
                        },
                    ],
                    },
                ],
                });
            });
        },
    },
    mounted(){
        // this.dispose()
	    this.getList({ip:this.$route.query.ip})
    }
}
</script>
<style scoped>
*{
    box-sizing: border-box;
}
td{
    border-bottom: 1px solid #eee;
}
th{
    border-bottom: 1px solid #eee;
}
.is-leaf{
    text-align: left;
}
.cell{
    line-height: 44px;
}
</style>