<template>
	<!-- <log-detail :typeList="typeList"></log-detail> -->
	<plug-in-edit @search="search" :pluginTypeList="pluginTypeList" :statusList="statusList"></plug-in-edit>
	<el-row :gutter="20">
		<!-- <el-col :span="4">
			<el-select clearable style="width: 100%;" v-model="searchModel.type" placeholder="日志类型">
			    <el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
			</el-select>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col> -->
		<el-col :span="4" :push="20">
			<el-button style="float: right;" type="primary" @click="add">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col>
			<el-table stripe :data="logList" border style="width: 100%">
				<el-table-column prop="pluginName" align="center" label="插件名称" width="100" />
				<el-table-column prop="pluginCode" align="center" label="插件代号" width="90" />
				<!-- <el-table-column prop="request" align="center" label="请求内容" show-overflow-tooltip /> -->
				<el-table-column prop="pluginType" align="center"  label="插件类型" width="95">
					<template #default="scope">
						<el-tag size="default" :type="getDictCss(pluginTypeList, scope.row.pluginType)">{{formatDict(pluginTypeList, scope.row.pluginType)}}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="packagePath" align="center" label="扫描路径" width="168" />
				<el-table-column prop="pluginPath" align="center" label="插件路径" >
					<template #default="scope">
						<el-tooltip :content="scope.row.pluginPath">
							<span style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{ scope.row.pluginPath }}</span>
						</el-tooltip>
					</template>
				</el-table-column >


				<el-table-column prop="version" align="center" label="版本" width="100" />
				
				<el-table-column prop="note" align="center" label="备注" width="125" />
				<el-table-column prop="size" align="center" label="实例化数列" width="125" />
				<el-table-column prop="status" align="center"  label="插件状态" width="88">
					<template #default="scope">
						<el-tag size="default" :type="getDictCss(statusList, scope.row.status)">{{formatDict(statusList, scope.row.status)}}</el-tag>
					</template>
				</el-table-column>


				<el-table-column prop="createTime" align="center" label="创建时间" width="165" />
				<el-table-column align="center" width="200" label="操作">
					<template #default="scope">
						<!-- <el-button type="text" size="default" @click="toDetail(scope.row.id)" v-if="hasPerm('sys:log:detail')">安装</el-button> -->
						<el-button type="text" size="default" v-if="scope.row.status == 0" @click="install(scope.row)">安装</el-button>
						<el-button type="text" size="default" @click="edit(scope.row)">编辑</el-button>
						<el-button type="text" size="default" v-if="scope.row.status == 1" @click="reload(scope.row.pluginCode)">更新</el-button>
						<el-button type="text" size="default" @click="deletePlugIn(scope.row.id)">删除</el-button>
						<el-button type="text" size="default" v-if="scope.row.status == 1" @click="uninstall(scope.row.pluginCode)">卸载</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
	
</template>
<script>
import { listPlugIn,reloadPlugIn,installPlugIn,addPlugIn,uninstallPlugIn, deletePlugIn } from "@/api/admin/plugIn"
import { listDictByNameEn } from "@/api/admin/dict"
import { getDictCss, formatDict } from "@/utils/dict"
import mitt from "@/utils/mitt"
import logDetail from "@/componts/admin/logDetail.vue"
import plugInEdit from "@/componts/admin/plugInEdit.vue"
export default {
	components:{ logDetail,plugInEdit },
	data() {
		return {
			searchModel: {},
			logList: [],
			typeList:[],
			total:0,
			pageSize:10,
			statusList:[],
			pluginTypeList:[]
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			listPlugIn(this.searchModel)
			.then(res => {
				this.logList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		add() {
			mitt.emit('openPlugInAdd')
		},
		edit(data){
			mitt.emit('openPlugInEdit', JSON.parse(JSON.stringify(data)))
		},
		install(data){
			var searchModel = {
				pluginName : data.pluginName,
				pluginCode : data.pluginCode,
				pluginType : data.pluginType,
				packagePath : data.packagePath,
				pluginPath : data.pluginPath,
				version : data.version
			}
			installPlugIn(searchModel).then(res => {
				this.search()
				this.$message.success(res.data.msg)
			})
		},
		reload(pluginCode){
			reloadPlugIn({pluginCode:pluginCode}).then(res => {
				this.search()
				this.$message.success(res.data.msg)
			})
		},
		deletePlugIn(id){

			this.$confirm('删除访客, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deletePlugIn(id).then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		uninstall(id){
			uninstallPlugIn(id).then(res => {
				this.search()
				this.$message.success(res.data.msg)
			})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			try{
				let res = await listPlugIn(this.searchModel)
				this.logList = res.data.result.list
				this.total = res.data.result.total
				
				let plugIn_res = await listDictByNameEn("plugIn_status")
				this.statusList = plugIn_res.data.result

				let plugin_res = await listDictByNameEn('plugin_type')
        		this.pluginTypeList = plugin_res.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
