<template>
  <el-dialog
    draggable
    width="50%"
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-row style="margin-bottom: 50px">
      <span
        style="
          display: flex;
          align-items: center;
          justify-content: flex-start;
          font-size: 15px;
          line-height: 40px;
        "
        v-for="(item, index) in checkRuleList"
        :key="index"
      >
        <span>{{ item }}</span
        >&nbsp;
        <!-- 规则匹配组件生成 -->
        <span
          v-for="warn in this.warnSourceInfoModel.warnEventDetectRules"
          :key="warn.id"
        >
          <el-switch
            v-if="
              warn.sort == checkRuleIndexList[index] &&
              warn.valDataType == 'BOOL'
            "
            v-model="valueList[checkRuleIndexList[index]]"
            inline-prompt
            active-value="1"
            inactive-value="0"
            :active-text="switchOptionVal(warn.optionVal, true)"
            :inactive-text="switchOptionVal(warn.optionVal, false)"
            inactive-color="#13ce66"
          ></el-switch>

          <el-input-number
            style="width: 90px"
            v-else-if="
              warn.sort == checkRuleIndexList[index] &&
              warn.valDataType == 'NUMBER'
            "
            v-model="valueList[checkRuleIndexList[index]]"
            :min="0"
            :max="365"
            controls-position="right"
          ></el-input-number>

          <el-input
            style="width: 100px"
            v-else-if="
              warn.sort == checkRuleIndexList[index] &&
              warn.valDataType == 'TEXT'
            "
            v-model="valueList[checkRuleIndexList[index]]"
          ></el-input>

          <el-date-picker
            style="width: 200px"
            v-else-if="
              warn.sort == checkRuleIndexList[index] &&
              warn.valDataType == 'DATETIME'
            "
            v-model="valueList[checkRuleIndexList[index]]"
            type="datetime"
            placeholder="日期时间"
          ></el-date-picker>
          <el-date-picker
            style="width: 118px"
            v-else-if="
              warn.sort == checkRuleIndexList[index] &&
              warn.valDataType == 'DATE'
            "
            v-model="valueList[checkRuleIndexList[index]]"
            type="date"
            placeholder="选择日期"
          ></el-date-picker>
          <el-time-select
            style="width: 120px"
            v-else-if="
              warn.sort == checkRuleIndexList[index] &&
              warn.valDataType == 'TIME'
            "
            v-model="valueList[checkRuleIndexList[index]]"
            placeholder="选择时间"
            start="0:0"
            end="23:55"
            step="00:05"
          ></el-time-select>
          <el-select
            filterable
            clearable
            class="autoWidth"
            style="width: 160px"
            v-else-if="
              warn.sort == checkRuleIndexList[index] &&
              warn.valDataType == 'SELECT'
            "
            v-model="valueList[checkRuleIndexList[index]]"
            placeholder="选择..."
          >
            <!-- <template #prefix>
              {{
                (
                  warn.optionVal
                    .split("|")
                    .find(
                      (item) =>
                        item.split(":")[1] ===
                        valueList[checkRuleIndexList[index]]
                    ) || ""
                ).split(":")[0]
              }}
       
            </template> -->
            <el-option
              v-for="optval in warn.optionVal.split('|')"
              :key="optval"
              :label="optval.split(':')[0]"
              :value="optval.split(':')[1]"
            ></el-option>
          </el-select>
        </span>
        &nbsp;
      </span>
    </el-row>
    <el-row justify="center">
      <el-button style="width: 100px" type="primary" @click="onSubmit"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script>
import {
  warnEventSourceInfoAdd,
  warnEventSourceInfoEdit,
  deviceTypeList,
  eventTypeList,
} from "@/api/warn/warn";
import mitt from "@/utils/mitt";
export default {
  data() {
    return {
      loading: false,
      checkRuleList: [], //按占位符分解模板
      checkRuleIndexList: [], //占位符索引
      valueList: {}, //占位符值列表
      warnSourceInfoModel: {}, //事件源模型
      dialog: {},
    };
  },
  methods: {
    switchOptionVal(optVal, bool) {
      if (optVal == null || optVal.indexOf("|") == -1) {
        return;
      }
      let optArr = optVal.split("|");
      if (bool) {
        return optArr[0].split(":")[0];
      } else {
        return optArr[1].split(":")[0];
      }
    },
    onSubmit() {
      //组装数据
      for (let item of this.warnSourceInfoModel.warnEventDetectRules) {
        let val = this.valueList[item.sort];
        console.log(val);
        if (val != undefined && val != null) {
          if (item.valDataType == "NUMBER") {
            val = String(val);
          }
          item.thresholdVal = val;
        }
      }
      warnEventSourceInfoEdit(this.warnSourceInfoModel).then((res) => {
        this.$message.success(res.data.msg);
        this.$emit("search");
        this.dialog.show = false;
      });
    },
  },
  mounted() {
    this.valueList = {};
    this.$nextTick(function () {
      mitt.on("openCheckRuleEdit", (data) => {
        this.valueList = {};
        this.warnSourceInfoModel = data;
        //按占位符分解模板
        let checkRuleNote = this.warnSourceInfoModel.note;
        if (checkRuleNote) {
          let ruleList = checkRuleNote.split(/#\d\#/);
          // for(var i=0;i<ruleList.length;i++){
          // 	if(ruleList[i] == undefined || ruleList[i] == null || ruleList[i] == ''){
          // 		ruleList.splice(i,1)
          // 	}
          // }
          this.checkRuleList = ruleList;
          let ruleIndexList = checkRuleNote.match(/#\d\#/gi);
          this.checkRuleIndexList = Array.from(ruleIndexList, (element) => {
            return Number(element.replace(/#/gi, ""));
          });
          // console.log("checkRuleIndexList",this.checkRuleIndexList)
        }
        //按排序占位符值列表
        if (this.warnSourceInfoModel.warnEventDetectRules) {
          for (let item of this.warnSourceInfoModel.warnEventDetectRules) {
            this.valueList[item.sort] = item.thresholdVal;
          }
        }
        // console.log("openCheckRuleEdit",this.checkRuleIndexList,this.valueList)
        this.dialog.show = true;
        this.dialog.title = "检验规则";
      });
    });
  },
};
</script>
<style scoped>
.autoWidth {
  min-width: 40px;
  text-align: center;
}
.autoWidth >>> .el-input__prefix {
  min-width: 40px;
  position: relative;
  box-sizing: border-box;
  border: 1px solid #ffffff00;
  height: 40px;
  line-height: 40px;
  color: #606266;
  left: 0;
  visibility: hidden;
}
.autoWidth >>> .el-input__inner {
  position: absolute;
  padding-left: 10px;
}
</style>