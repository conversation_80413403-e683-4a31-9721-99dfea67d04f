<template>
	<div>
		<el-dialog draggable width="30%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<el-row  :gutter="20">
			<el-col :span="15">
				<div style="font-size:30px;margin-bottom: 10px;color: #6094ff;">预警抓拍</div>
				

				<el-image style="height: 185px" :src="imgServer + extraData.photo" :preview-src-list="[imgServer + extraData.photo]">
					<template #error>
						<!-- <el-image preview-teleported fit="contain" style="height: 120px; width:120px" :src="errorHeadImg" :preview-src-list="[errorHeadImg]"></el-image> -->
					</template>
				</el-image>

			</el-col>
			<el-col :span="9">
				<div style="font-size:30px;margin-bottom: 10px;color: #6094ff;">详情描述</div>
				<div style="height:200px">{{extraData.desc}}</div>
			</el-col>
		</el-row>
	</el-dialog>
	</div>
	
</template>

<script>

	import mitt from "@/utils/mitt";
	import { getDictCss, formatDict } from "@/utils/dict"
	import errImg from "../../assets/img/defaultHead.png"

	export default {
		props: ["statusList", "certificateTypeTagList"],
		data() {
			return {
				loading: false,
				dataList: {},
				dialog: {},
				imgServer: import.meta.env.VITE_BASE_API,
				communityId: localStorage.getItem("communityId"),
				errorHeadImg: errImg,
				extraData:{}
			}
		},
		methods:{
			formatStatus(dicList, cellValue) {
				return formatDict(dicList, cellValue)
			},
			formatcertificateType(dicList, cellValue) {
				return formatDict(dicList, cellValue)
			},
			getDictCss(dicList, cellValue) {
				return getDictCss(dicList, cellValue)
			}, 
			formatName(name){
				if (name) {
					if (name.length == 2) {
						const xing = String(name).substring(0,1)
						return xing + '*'
					} else if ( name.length == 3 ) {
						const xing = String(name).substring(0,1)
						const ming = String(name).substring(2,3)
						return xing + '*' + ming
					} else if(name.length>3){
						return name.substring(0,1)+"*"+'*'+name.substring(3,name.length)
					}	
				}
			},
		},
		mounted() {
			mitt.on("openExtraDataView", (data) => {
				this.extraData = data;
				this.dialog.show = true;
				this.dialog.title = "最新警情";
			});
		},
		created() {
			mitt.off("openExtraDataView");
		},
	};
</script>

<style scoped>
	.el-row {
		margin-bottom: 0px;
		background-color: #fff;
		padding: 0px 10px;
		border-radius: 5px;
	}
	.el-col{
			margin-bottom: 0px;
	}
	/* div /deep/.el-dialog__body {
		background-color: #f1f5ff;
  	} */
</style>
