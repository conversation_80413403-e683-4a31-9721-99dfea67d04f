<template>
	<config-edit :statusList="statusList" @search="search"></config-edit>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.key" @keydown.enter="search" placeholder="键" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="12">
			<el-button style="float: right;" type="primary" @click="add" v-if="hasPerm('sys:config:save')">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="configList" border style="width: 100%">
				<el-table-column prop="key" align="center" label="键" />
				<el-table-column prop="value" align="center" label="值"/>
				<el-table-column prop="remark" align="center" label="备注" />

				<el-table-column prop="status" align="center" label="状态" width="95">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" align="center" label="创建时间" width="168" />
				<el-table-column prop="updateTime" align="center" label="更新时间" width="168" />
				<el-table-column align="center" width="160" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row.id)">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('sys:config:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[12, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>

<script>
import { listConfig, getConfig, deleteConfig } from "@/api/admin/config"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import configEdit from "@/componts/admin/configEdit.vue"
export default {
	components:{ configEdit },
	data() {
		return {
			searchModel: {pageSize:12},
			configList:[],
			total:0,
			pageSize:12,
			statusList:[]
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		},
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			listConfig(this.searchModel)
			.then(res => {
				this.configList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		add(id){
			mitt.emit('openConfigAdd',id)
		},
		edit(id){
			getConfig(id)
			.then(res =>{
				mitt.emit('openConfigEdit',res.data.result)
			})
			
		},
		deleted(id){
			this.$confirm('删除配置, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteConfig(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
					.catch(err =>{
						this.$message.error(err)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			mitt.off('openConfigEdit')
			mitt.off('openConfigAdd')
			try{
				let status_res = await listDictByNameEn('common_status')
				this.statusList = status_res.data.result

				let res = await listConfig(this.searchModel)
				this.configList = res.data.result.list
				this.total = res.data.result.total
			
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
