<template>
  <el-row :gutter="20">
    <el-col :span="4">
      <div class="title_des">
        <img
          style="height: 30px; width: 35px"
          src="../../assets/img/house_icon.png"
        />
        <span>&nbsp;&nbsp;名称</span>
      </div>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-table
        row-key="id"
        stripe
        :data="dataList"
        :row-class-name="tableRowClassName"
        border
        style="width: 100%; height: calc(100vh - 298px)"
      >
        <el-table-column prop="communityName" align="center" label="小区名称" />
        <el-table-column prop="deviceTotal" align="center" label="设施" />
        <el-table-column prop="deviceNormal" align="center" label="正常" />
        <el-table-column prop="deviceAbnormal" align="center" label="异常" />
        <el-table-column prop="roomNum" align="center" label="房间数" />
        <el-table-column prop="personNum" align="center" label="住户" />
        <el-table-column prop="vehicleNum" align="center" label="车辆" />
        <el-table-column prop="status" width="100" align="center" label="是否启用">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>
        <el-table-column prop="enable3D" align="center" label="是否启用3D">
          <template #default="scope">
            <el-tag :type="getDictCss(statusList, scope.row.enable3D)">{{scope.row.enable3D!==null?((scope.row.enable3D == false) ? "否":"是"):""}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="data10" align="center" label="资源占用" />
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination
        background
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :total="Number(total)"
      ></el-pagination>
    </el-col>
  </el-row>
</template>

<script>
import { getDictCss, formatDict } from "@/utils/dict"
import { getCommunityTotalList } from "@/api/dataScreening/dataAllList.js";
import { listDictByNameEn } from "@/api/admin/dict";
import mitt from "@/utils/mitt";
export default {
  components: {},
  data() {
    return {
      searchModel: {},
      index: 0,
      dataList: [],
      total: 0,
      pageSize: 10,
    };
  },
  methods: {
    // 斑马纹样式
    tableRowClassName ({ row, rowIndex }) {
      if (rowIndex % 2 !== 0) {
        return 'el-table__row--striped'
      }
    },
    search() {
			getCommunityTotalList(this.searchModel)
			.then(res => {
				this.dataList = res.data.result.list
				this.total = res.data.result.total
			})
		},
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    handleSizeChange(num) {
      this.searchModel.pageSize = num;
      this.search();
    },
    formatDataType(row, column, cellValue, index) {
      let result = "";
      for (let item of this.statusList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
        }
      }
      return result;
    },
    getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
    formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
    async init() {
      let status_res = await listDictByNameEn("common_status");
      this.statusList = status_res.data.result;

      await getCommunityTotalList().then( res => {
        this.dataList = res.data.result.list
        this.total = res.data.result.total
      })
      console.log(this.dataList);
    },
  },
  created() {
    this.init()
  },
};
</script>
<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
.title_des {
  font-size: 15px;
  display: flex;
  align-items: flex-end;
}
::v-deep .el-table__row--striped{
    background-color: pink !important;
}
</style>
