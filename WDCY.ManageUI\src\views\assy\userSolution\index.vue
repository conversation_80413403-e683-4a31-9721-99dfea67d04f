<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="18" >
        <el-input style="width: 280px; margin-right: 10px;" v-model="queryParams.keyword" placeholder="请输入方案、用户、角色关键字" clearable @keyup.enter.native="handleQuery" />
        
        <el-date-picker
          v-model="daterangeCreateTime"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 200px;margin-right: 10px;"
        />

        <el-button type="primary" @click="handleQuery">搜 索</el-button>
      </el-col>
      <el-col :span="6" >
        <el-button style="float: right;" type="primary" @click="handleAdd" v-if="hasPerm('assy:userSolution:add')">添 加</el-button>
        <el-button style="float: right; margin-right: 10px;" type="primary" :disabled="single" @click="handleUpdate" v-if="hasPerm('assy:userSolution:edit')">修 改</el-button>
        <el-button style="float: right; " type="primary" :disabled="multiple" @click="handleDelete" v-if="hasPerm('assy:userSolution:remove')">删 除</el-button>
      </el-col>
    </el-row>

    <el-container>
      <el-aside width="200px" style="padding-top: var(--el-main-padding);height: calc(100vh - 132px);">
        <el-scrollbar>
          <el-menu
            :default-openeds="['0']"
          >
            <el-sub-menu index="0">
              <template #title><h3>方案列表</h3></template>
              <el-menu-item index="-1" @click="handleMenuClick">全部</el-menu-item>
              <el-menu-item v-for="item in solutionList" :index="item.id" @click="handleMenuClick">
                <el-tooltip v-if="item.solutionNote" :content="item.solutionNote" >
                  <span style="width: 100%;"> {{ item.solutionTitle }} </span> 
                </el-tooltip>
                <span v-else> {{ item.solutionTitle }} </span> 
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-scrollbar>
      </el-aside>
      <el-main style="padding-right: 0;">
        <el-table v-loading="loading" :data="userSolutionList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="ID" align="center" prop="id" />
          <el-table-column label="方案" align="center" prop="solutionTitle" />
          <!-- <el-table-column label="方案" align="center" prop="solutionId">
            <template #default="scope">
              {{ showValueKey(solutionList, scope.row.solutionId, "id", "solutionTitle") }}
            </template>
          </el-table-column> -->
          <el-table-column label="用户" align="center" prop="userId">
            <template #default="scope">
              {{ showValueKey(userList, scope.row.userId, "id", "nickName") }}
            </template>
          </el-table-column>
          <el-table-column label="角色" align="center" prop="roleId">
            <template #default="scope">
              {{ showValueKey(roleList, scope.row.roleId, "id", "roleName") }}
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center" prop="createUser" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="修改人" align="center" prop="updateUser" />
          <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['assy:userSolution:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['assy:userSolution:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div style="float: right; margin-top: 20px;">
          <el-pagination background :total="Number(total)" v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" @change="getList"
            layout="total, sizes, prev, pager, next, jumper" />
        </div>
      </el-main>
    </el-container>

    <!-- 添加或修改用户方案对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="组件方案" prop="solutionId">
          <el-select
            v-model="form.solutionId"
            clearable
            placeholder="选择方案"
          >
            <el-option
              v-for="item in solutionList"
              :key="item.id"
              :label="item.solutionTitle"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="关联账号" prop="userId">
          <el-select v-model="form.userId" placeholder="选择关联用户账号" clearable>
            <el-option v-for="item in userList" :key="item.id" :label="item.nickName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="关联角色" prop="roleId">
          <el-select v-model="form.roleId" placeholder="选择关联角色" clearable>
            <el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pagingUserSolution, listUserSolution, getUserSolution, delUserSolution, addUserSolution, updateUserSolution } from "@/api/assy/userSolution";
import { listSolution } from "@/api/assy/solution";
import { listRole } from "@/api/admin/role"
import { listUser } from "@/api/admin/user"

export default {
  name: "UserSolution",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户方案表格数据
      userSolutionList: [],
      solutionList: [],
      userList: [],
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 修改人时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: null,
        solutionId: null,
        userId: null,
        roleId: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        solutionId: [
          { required: true, message: "方案不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    listSolution({status:1}).then(res => {
      this.solutionList = res.data.result
    });
    listUser({status:0}).then(res => {
      this.userList = res.data.result
    });
    listRole({status:0}).then(res => {
      this.roleList = res.data.result
    });
    this.getList();
  },
  methods: {
    /** 查询用户方案列表 */
    getList() {
      this.loading = true;
      this.queryParams.map = null;
      //console.info("daterange", this.daterangeCreateTime);
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.map = {};
        this.queryParams.map["beginTime"] = this.daterangeCreateTime[0];
        this.queryParams.map["endTime"] = this.daterangeCreateTime[1];
      }
      pagingUserSolution(this.queryParams).then(response => {
        let data = response.data;
        if (data.code == 0) {
          this.userSolutionList = data.result.list;
          this.total = data.result.total;
        } else {
          this.$modal.msgError(data.msg);
        }
        this.loading = false;
      }).catch(res => {        
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        solutionId: null,
        userId: null,
        roleId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户方案";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getUserSolution(id).then(response => {
        let data = response.data;
        if (data.code == 0) {
          this.form = data.result;
          this.open = true;
          this.title = "修改用户方案";
        } else {
          this.$modal.msgSuccess(data.msg);
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          //console.log(this.form.userId,this.form.roleId);
          if (!this.form.userId && !this.form.roleId) {
            this.$modal.msgWarning('请选择关联用户账号或角色!');
            return
          }
          if (this.form.id != null) {
            updateUserSolution(this.form).then(response => {
              let data = response.data;
              if (data.code == 0) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
              } else {
                this.$modal.msgSuccess(data.msg);
              }
              this.getList();
            });
          } else {
            addUserSolution(this.form).then(response => {
              let data = response.data;
              if (data.code == 0) {
                this.$modal.msgSuccess("添加成功");
                this.open = false;
              } else {
                this.$modal.msgSuccess(data.msg);
              }
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除用户方案编号为"' + ids + '"的数据项？').then(function() {
        return delUserSolution(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /**
     * 在列表中查找指定key对应的值，并输出指定key的值
     * @param list 要查询的列表
     * @param val 要查询的值
     * @param key 要查询的KEY值
     * @param outKey 要输出的KEY值
     */
    showValueKey(list, val, key, outKey) {
      if (list) {
        let find = list.find(m => m[key] == val);
        if (find)
          return find[outKey];
      }
      return val;
    },
    handleMenuClick(item) {
      //console.log('handleMenuClick', item);
      if (item.index == -1) {
        this.queryParams.solutionId = null;
      } else {
        this.queryParams.solutionId = item.index;
      }
      this.getList();
    }
  }
};
</script>

<style scoped>
/* 隐藏滚动条 */
/* ::-webkit-scrollbar {
	display: none;
} */
.dialog-footer{
  text-align: right;
}
</style>