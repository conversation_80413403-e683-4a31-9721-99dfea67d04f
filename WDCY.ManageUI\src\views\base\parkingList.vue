<template>
  <mars-map @point="point"></mars-map>
  <parking-edit
    :statusList="statusList"
    :typeList="typeList"
    :parkingList="parkingList"
    @search="search"
  ></parking-edit>
  <map-select-point @point="point" @addressCall="setAddress"></map-select-point>
  <el-row :gutter="20">
    <el-col :span="3" style="display: flex; width: 500px">
      <el-input
        v-model="searchModel.name"
        @keydown.enter="search"
        placeholder="停车场名称"
        clearable
      />
    </el-col>
    <el-col :span="2">
      <el-select
        style="width: 100%"
        v-model="searchModel.status"
        placeholder="状态"
        clearable
      >
        <el-option
          v-for="item in statusList"
          :key="item.nameEn"
          :label="item.nameCn"
          :value="parseInt(item.nameEn)"
        ></el-option>
      </el-select>
    </el-col>
    <el-col :span="4">
      <el-button type="primary" @click="search">搜 索</el-button>
    </el-col>
    <el-col :span="4" :push="11">
      <el-button
        style="float: right"
        type="primary"
        @click="add"
        v-if="hasPerm('base:carParkInfo:add')"
        >添 加</el-button
      >
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-table
        stripe
        :data="parkingList"
        border
        height="calc(100vh - 300px)"
        style="width: 100%"
      >
        <!-- <el-table-column label="图标" align="center" prop="icoUrl" width="75">
                    <template #default="scope">
                        <el-image style="width:45px;height:45px;background: rgba(136, 186, 255,.3);" :src="imgServer+scope.row.icoUrl" fit="contain"></el-image>
                    </template>
                </el-table-column> -->
        <el-table-column prop="code" align="center" label="标识" />
        <el-table-column prop="name" align="center" label="停车场名称" />
        <el-table-column prop="address" align="center" label="停车场地址">
          <template #default="scope">
            <el-col
              :span="24"
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <el-tooltip :content="scope.row.address">
                <span
                  style="
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  "
                  >{{ scope.row.address }}</span
                >
              </el-tooltip>
              <el-button
                v-show="isShow(scope.row)"
                type="text"
                style="font-size: 30px; padding: 0"
                @click="selectPoint(scope.row)"
              >
                <el-icon :size="30">
                  <location-filled></location-filled>
                </el-icon>
              </el-button>
            </el-col>
          </template>
        </el-table-column>
        <el-table-column prop="totalCount" align="center" label="总停车位" />
        <el-table-column prop="status" align="center" label="状态" width="95">
          <template #default="scope">
            <el-tag :type="getDictCss(statusList, scope.row.status)">{{
              formatDict(statusList, scope.row.status)
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          align="center"
          label="创建时间"
          width="168"
        />
        <el-table-column prop="note" align="center" label="描述" />

        <!-- <el-table-column prop="status" width="100" :formatter="formatStatus" align="center" label="状态" /> -->

        <el-table-column align="center" width="100" label="操作">
          <template #default="scope">
            <el-button
              type="text"
              size="default"
              @click="edit(scope.row)"
              v-if="hasPerm('base:carParkInfo:edit')"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="deleted(scope.row.id)"
              v-if="hasPerm('base:carParkInfo:delete')"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination
        background
        v-model:page-size="searchModel.pageSize"
        :page-sizes="[12, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :total="Number(total)"
      ></el-pagination>
    </el-col>
  </el-row>
</template>

<script>
import { ElLoading } from "element-plus";
import { listParking, deleteParking } from "@/api/base/parking";
import mapSelectPoint from "@/componts/map/mapSelectPoint.vue";
import { listDictByNameEn } from "@/api/admin/dict";
import { openMap, openMarsMap } from "@/utils/myUtils";
import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict";
import parkingEdit from "@/componts/base/parkingEdit.vue";
import marsMap from "@/componts/map/marsMap.vue";
import { getCommunity } from "@/api/base/community";
export default {
  components: { parkingEdit, mapSelectPoint, marsMap },
  data() {
    return {
      searchModel: {
        pageSize: 12,
        communityId: localStorage.getItem("communityId"),
      },
      parkingList: [],
      statusList: [],
      typeList: [],
      total: 0,
      imgServer: import.meta.env.VITE_BASE_API,
    };
  },
  methods: {
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    formatType(row, column, cellValue, index) {
      return formatDict(this.typeList, cellValue);
    },
    isShow(row) {
      if (
        row.lng != undefined &&
        row.lng != null &&
        row.lng != "" &&
        row.lat != undefined &&
        row.lat != null &&
        row.lat != ""
      ) {
        return true;
      }
      return false;
    },
    point(e) {
      mitt.emit("setPointValue", e);
    },
    setAddress(e) {
      mitt.emit("setAddress", e);
    },
    selectPoint(row) {
      // openMap(mitt, false, [row.lng, row.lat], row.address);

      var communityId = localStorage.getItem("communityId");
      getCommunity(communityId)
        .then((res) => {
          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map.mode;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          if (result.enabled3d && mode == "mars3d") {
            position = config.map.sdgis.position;

            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (result.lng && result.lat) {
            center = {
              lng: result.lng,
              lat: result.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }

          var data = {
            enabled3d: result.enabled3d,
            edit: false,
            point: [row.lng, row.lat, 0],
            position: position,
            center: center,
            modeUrl: modeUrl,
            title: "查看点位",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);

          openMarsMap(mitt, data);
        })
        .catch((err) => {});
    },
    search() {
      listParking(this.searchModel).then((res) => {
        this.parkingList = res.data.result.list;
        this.total = res.data.result.total;
      });
    },
    edit(row) {
      mitt.emit("openParkingEdit", JSON.parse(JSON.stringify(row)));
    },
    add() {
      mitt.emit("openParkingAdd");
    },
    deleted(id) {
      this.$confirm("删除图标, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteParking(id).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
          });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    handleSizeChange(num) {
      this.searchModel.pageSize = num;
      this.search();
    },
    async init() {
      mitt.off("openVehicleEdit");
      mitt.off("openVehicleAdd");
      try {
        let res = await listParking(this.searchModel);
        this.parkingList = res.data.result.list;
        this.total = res.data.result.total;
        let vehicle_status = await listDictByNameEn("parking_status");
        this.statusList = vehicle_status.data.result;
        // let parking_type = await listDictByNameEn('parking_type')
        // this.parkingList = parking_type.data.result
      } catch (err) {}
    },
  },
  created() {
    this.init();
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
div /deep/ .diaLogClass {
  height: 780px;
  width: 1124px;
  background-color: rgb(241, 245, 255);
  overflow-x: auto;
  position: relative;
  border-radius: 16px;
}
div /deep/ .el-dialog__body {
  padding: 0 34px;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
</style>
