<template>
  <div>
    <mars-map @point="point"></mars-map>
    <user-group-edit
      :nodeTypeList="nodeTypeList"
      :statusList="statusList"
      :userGroupList="userGroupList"
      @search="search"
    ></user-group-edit>
    <map-select-point @point="point"></map-select-point>
    <el-row :gutter="20">
      <el-col :span="4">
        <el-input
          v-model="searchModel.groupName"
          @keydown.enter="search"
          placeholder="组名"
          clearable
        />
      </el-col>
      <el-col :span="4">
        <el-button type="primary" @click="search">搜 索</el-button>
        <el-button type="primary" @click="toggle">
          {{ isExpandAll ? "全部收起" : "全部展开" }}
        </el-button>
      </el-col>
      <el-col :span="4" :push="12">
        <el-button
          style="float: right"
          type="primary"
          @click="add({ id: 0, nodeType: -1 })"
          v-if="hasPerm('sys:userGroup:add')"
          >添 加</el-button
        >
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-table
          ref="tableTree"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          stripe
          :data="userGroupList"
          row-key="id"
          border
          height="calc(100vh - 300px)"
        >
          <!-- <el-table-column /> -->
          <el-table-column
            prop="groupName"
            label="组名"
            header-align="center"
            sortable
          />
          <el-table-column
            prop="nodeType"
            align="center"
            label="级别"
            :formatter="levelFormat"
            width="86"
          />
          <el-table-column prop="sort" align="center" label="排序" width="60" />
          <el-table-column header-align="center" label="位置">
            <template #default="scope">
              <el-button
                v-show="scope.row.lng"
                type="text"
                @click="selectPoint(scope.row)"
              >
                <el-icon :size="30">
                  <location-filled></location-filled>
                </el-icon>
              </el-button>
              <span v-show="scope.row.lng"
                >({{ scope.row.lng }},{{ scope.row.lat }})</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="remark" header-align="center" label="备注" />
          <!-- <el-table-column prop="status" align="center" :formatter="formatStatus" label="状态" sortable width="82" /> -->
          <el-table-column prop="status" align="center" label="状态" width="95">
            <template #default="scope">
              <el-tag :type="getDictCss(statusList, scope.row.status)">{{
                formatDict(statusList, scope.row.status)
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="updateTime"
            align="center"
            label="更新时间"
            sortable
            width="168"
          />
          <el-table-column
            prop="createBy"
            align="center"
            label="创建用户"
            sortable
            width="108"
          />
          <el-table-column
            align="center"
            width="200"
            label="操作"
            v-if="
              hasPerm('sys:userGroup:delete') ||
              hasPerm('sys:userGroup:edit') ||
              hasPerm('sys:userGroup:add')
            "
          >
            <template #default="scope">
              <el-button
                type="text"
                size="default"
                v-show="scope.row.nodeType != 10"
                @click="add(scope.row)"
                v-if="hasPerm('sys:userGroup:add')"
                >添加子级</el-button
              >
              <el-button
                type="text"
                size="default"
                @click="edit(scope.row)"
                v-if="hasPerm('sys:userGroup:edit')"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="default"
                @click="deleted(scope.row.id)"
                v-if="hasPerm('sys:userGroup:delete')"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col
        style="display: flex; justify-content: flex-end; margin-top: 10px"
      >
        <el-pagination
          background
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="currentChange"
          @size-change="handleSizeChange"
          :total="Number(total)"
        ></el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { LocationFilled } from "@element-plus/icons-vue";
import mapSelectPoint from "@/componts/map/mapSelectPoint.vue";
import { listUserGroup, deleteUserGroup } from "@/api/admin/userGroup";
import { listDictByNameEn } from "@/api/admin/dict";
import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict";
import { openMap, openMarsMap } from "@/utils/myUtils";
import userGroupEdit from "@/componts/admin/userGroup/userGroupEdit.vue";
import marsMap from "@/componts/map/marsMap.vue";

export default {
  components: { userGroupEdit, mapSelectPoint, marsMap },
  data() {
    return {
      searchModel: {},
      userGroupList: [],
      statusList: [],
      nodeTypeList: [],
      total: 0,
      expand: true,
      isExpandAll: false, // 全部展开
      pageSize: 10,
    };
  },
  methods: {
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    levelFormat(row, column, cellValue, index) {
      let findInfo = this.nodeTypeList.find((m) => m.nameEn == cellValue);
      if (findInfo) return findInfo.nameCn;
      return "";
    },
    search() {
      listUserGroup(this.searchModel).then((res) => {
        this.userGroupList = res.data.result.list;
        this.total = res.data.result.total;
      });
    },
    // 点击切换
    toggle() {
      this.isExpandAll = !this.isExpandAll;
      this.toggleExpand(this.userGroupList, this.isExpandAll);
    },

    /** 展开/收起方法
     * @param {data} array 绑定table中的data
     * @param {isExpand} boolean 是否展开
     */
    toggleExpand(data, isExpand) {
      data.forEach((item) => {
        this.$refs["tableTree"].toggleRowExpansion(item, isExpand);
        if (item.children != null) {
          this.toggleExpand(item.children, isExpand);
        }
      });
    },
    // 地图选点
    point(e) {
      mitt.emit("setPointValue", e);
    },
    edit(user) {
      mitt.emit("openUserGroupEdit", user);
    },
    add(row) {
      mitt.emit("openUserGroupAdd", row);
    },
    deleted(id) {
      this.$confirm("删除用户组, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteUserGroup(id).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
          });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    handleSizeChange(num) {
      this.searchModel.pageSize = num;
      this.search();
    },
    // 地图选点
    selectPoint(row) {
      // openMap(mitt, false, [row.lng, row.lat], row.groupName);

      var center;
      var rotationSet = { x: 0, y: 0, z: 0 };
      var scaleSet = 1;
      var showBaseMap = false;
      var position;
      var modeUrl;

      center = {
        lng: row.lng,
        lat: row.lat,
        alt: 500,
        heading: 0,
        pitch: -90,
      };

      var data = {
        enabled3d: false,
        edit: false,
        point: [row.lng, row.lat, 0],
        position: position,
        center: center,
        modeUrl: modeUrl,
        title: "查看点位",
        rotationSet,
        scaleSet,
        showBaseMap: showBaseMap,
      };
      console.log(data);

      openMarsMap(mitt, data);
    },
    async init() {
      mitt.off("openUserGroupEdit");
      mitt.off("openUserGroupAdd");
      mitt.off("setPointValue");
      mitt.off("openMarsMap");

      try {
        let res = await listUserGroup(this.searchModel);
        let resStatus = await listDictByNameEn("user_group_status");
        let level_res = await listDictByNameEn("user_group_level");

        this.statusList = resStatus.data.result;
        this.nodeTypeList = level_res.data.result;
        this.userGroupList = res.data.result.list;
        this.total = res.data.result.total;
      } catch (err) {}
    },
  },
  created() {
    this.init();
    this.userGroupList = [
      {
        id: 1,
        groupName: "测试数据1",
      },
      {
        id: 2,
        groupName: "测试数据2",
        children: [
          {
            id: 21,
            groupName: "测试数据21",
          },
        ],
      },
    ];
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
</style>
