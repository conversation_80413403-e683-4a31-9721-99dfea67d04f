/**
 * Mars3D平台插件,结合echarts可视化功能插件  mars3d-echarts
 *
 * 版本信息：v3.8.13
 * 编译日期：2025-01-09 16:08
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：火星科技免费公开版 ，2024-08-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.echarts || require('echarts')), (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'echarts', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-echarts"] = {}, global.echarts, global.mars3d));
})(this, (function (exports, echarts, mars3d) { 
'use strict';(function(_0xde9858,_0x4a479c){const _0x327d34={_0x21855a:0x25d,_0x189e70:0x233,_0x2317ed:0x1ef,_0x650588:0x1f8,_0x3291d8:0x1e5,_0x3cb88e:0x1da,_0x591fa5:0x24d,_0x548665:0x252},_0x5b0496={_0x36b521:0x334};function _0x1b95d3(_0x2daf3f,_0x1bb456){return _0x4f76(_0x1bb456- -0x38d,_0x2daf3f);}function _0x223592(_0x4f3895,_0x46fcc1){return _0x4f76(_0x4f3895- -_0x5b0496._0x36b521,_0x46fcc1);}const _0x301984=_0xde9858();while(!![]){try{const _0x20c6dc=parseInt(_0x1b95d3(-0x25b,-_0x327d34._0x21855a))/0x1*(-parseInt(_0x1b95d3(-0x231,-0x257))/0x2)+-parseInt(_0x1b95d3(-_0x327d34._0x189e70,-0x238))/0x3+-parseInt(_0x223592(-_0x327d34._0x2317ed,-_0x327d34._0x650588))/0x4*(parseInt(_0x1b95d3(-0x215,-0x22b))/0x5)+-parseInt(_0x223592(-0x20b,-_0x327d34._0x3291d8))/0x6*(-parseInt(_0x223592(-0x1e8,-0x1dd))/0x7)+parseInt(_0x1b95d3(-0x230,-0x256))/0x8+-parseInt(_0x223592(-_0x327d34._0x3cb88e,-0x1b9))/0x9+parseInt(_0x1b95d3(-_0x327d34._0x591fa5,-_0x327d34._0x548665))/0xa;if(_0x20c6dc===_0x4a479c)break;else _0x301984['push'](_0x301984['shift']());}catch(_0x1d0025){_0x301984['push'](_0x301984['shift']());}}}(_0x1af6,0xcf64f));function _0x1af6(){const _0x5d4a46=['x3bVAw50zxjfDMvUDhm','BwfYCZnKtwfW','BM9Uzq','tgf5zxjvDgLS','q2fYDg9NCMfWAgLJ','x2vJAgfYDhnjBNn0yw5Jzq','AgvPz2H0','mhb4','C3r5Bgu','C2vYAwvZ','Cg9ZDfjLBMrLCG','y2fUDMfZ','zNjVBurLz3jLzxm','x19TyxbpzMzZzxq','zgf0yq','BwfYCZnKtwfWuM9HBq','x21HCNmZzf9Zy2vUzq','Dg9xAw5KB3DdB29YzgLUyxrLCW','Bgf0','zwnOyxj0C0rLChrOvgvZDa','nJC4rgH6Bg1R','x21VDw50zwriB29R','zML4zwrizwLNAhq','C2v0rwnOyxj0C09WDgLVBG','vxrPBa','z3jHCgHPyW','zwnOyxj0C0f1Dg9izwLNAhq','nZvQC255z3e','Aw5PDa','DMfSDwu','rwnOyxj0C0XHEwvY','x21HCe9MzNnLDa','x21HCa','mJq4mdj6DujOBuu','mJm4nJmYmgLSsezdwq','CMvNAxn0zxi','zgvMAw5LuhjVCgvYDhK','y2XPzw50sgvPz2H0','mJCXntu1ntbKA3f5BvG','ug9PBNruCMfUCW','zM9YrwfJAa','x3nLDe9WDgLVBNniB29R','zgvWDgHuzxn0','x2vJAgfYDhndB250ywLUzxi','Bwf4','zwnOyxj0CW','Bg5N','zgLTzw5ZAw9UCW','odG0wenUtxfQ','DxbKyxrL','Bwf0CML4','ywjZB2X1Dgu','z2v0wNi','CMvZAxPL','zxH0zw5Kq29TCg9Uzw50tw9KzwW','nJiXntnbEvvluLK','rwXSAxbZB2LKywXpy2nSDwrLCG','B3b0Aw9UCW','z2v0qK1HCa','y2fTzxjH','Cg9PBNrLCKv2zw50CW','C2nLBMu','zgvMyxvSDa','zwnjBNn0yw5Jzq','mZi2ntm1owrdA2DKzG','z2v0sgvPz2H0','y29VCMrPBMf0zvn5C3rLBq','AxnqB2LUDfzPC2LIBgu','A2v5CW','nZuZntKZnfbRBNrvrq','C2v0twfWt2zMC2v0','EKLUzgv4','x19LC01VzhvSzq','zxzLBNrqyxjLBNq','Bgf5zxi','zgLZCg9Zzq','AxngB3jTyxq','nZa1nxrkBNb1za','q2vZAxvT','zgLZCgf0y2Hby3rPB24','ywXS','yxbP','DhLWzq','CMvTB3zLrxzLBNrmAxn0zw5LCG'];_0x1af6=function(){return _0x5d4a46;};return _0x1af6();}function _0x4f76(_0x13e202,_0x260707){const _0x1af63e=_0x1af6();return _0x4f76=function(_0x4f76e0,_0x58ff04){_0x4f76e0=_0x4f76e0-0x120;let _0x28ec03=_0x1af63e[_0x4f76e0];if(_0x4f76['Nguwdg']===undefined){var _0x239682=function(_0x274f23){const _0xaec81d='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';let _0x3b69e9='',_0xcb318d='';for(let _0x241943=0x0,_0x5102ea,_0x97e686,_0x1523ee=0x0;_0x97e686=_0x274f23['charAt'](_0x1523ee++);~_0x97e686&&(_0x5102ea=_0x241943%0x4?_0x5102ea*0x40+_0x97e686:_0x97e686,_0x241943++%0x4)?_0x3b69e9+=String['fromCharCode'](0xff&_0x5102ea>>(-0x2*_0x241943&0x6)):0x0){_0x97e686=_0xaec81d['indexOf'](_0x97e686);}for(let _0x3efc08=0x0,_0x55b4bb=_0x3b69e9['length'];_0x3efc08<_0x55b4bb;_0x3efc08++){_0xcb318d+='%'+('00'+_0x3b69e9['charCodeAt'](_0x3efc08)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0xcb318d);};_0x4f76['tlkbOD']=_0x239682,_0x13e202=arguments,_0x4f76['Nguwdg']=!![];}const _0x379188=_0x1af63e[0x0],_0x1cea7d=_0x4f76e0+_0x379188,_0x8f8c4b=_0x13e202[_0x1cea7d];return!_0x8f8c4b?(_0x28ec03=_0x4f76['tlkbOD'](_0x28ec03),_0x13e202[_0x1cea7d]=_0x28ec03):_0x28ec03=_0x8f8c4b,_0x28ec03;},_0x4f76(_0x13e202,_0x260707);}function _interopNamespace(_0x1523ee){const _0x4b88ee={_0x20e136:0x1ec,_0x15854d:0xe2},_0x110f65={_0x56cf25:0x21d},_0xa4c45b={_0x43f8e5:0x349};function _0x12590f(_0x4176e6,_0x4918ed){return _0x4f76(_0x4176e6- -_0xa4c45b._0x43f8e5,_0x4918ed);}if(_0x1523ee&&_0x1523ee[_0x12590f(-_0x4b88ee._0x20e136,-0x1c3)])return _0x1523ee;var _0x3efc08=Object['create'](null);function _0x9dc7b9(_0x3c4df5,_0x4faa55){return _0x4f76(_0x3c4df5- -_0x110f65._0x56cf25,_0x4faa55);}return _0x1523ee&&Object[_0x9dc7b9(-0xc4,-_0x4b88ee._0x15854d)](_0x1523ee)['forEach'](function(_0x55b4bb){if(_0x55b4bb!=='default'){var _0x3b5677=Object['getOwnPropertyDescriptor'](_0x1523ee,_0x55b4bb);Object['defineProperty'](_0x3efc08,_0x55b4bb,_0x3b5677['get']?_0x3b5677:{'enumerable':!![],'get':function(){return _0x1523ee[_0x55b4bb];}});}}),_0x3efc08['default']=_0x1523ee,_0x3efc08;}function _0x48a104(_0x31d28f,_0x394784){const _0x2f5309={_0x142d3f:0x1f};return _0x4f76(_0x394784-_0x2f5309._0x142d3f,_0x31d28f);}function _0x4ec83c(_0x1b3d20,_0x2c7951){const _0x419ea8={_0x547f54:0x12};return _0x4f76(_0x2c7951-_0x419ea8._0x547f54,_0x1b3d20);}var echarts__namespace=_interopNamespace(echarts),mars3d__namespace=_interopNamespace(mars3d);const Cesium$1=mars3d__namespace[_0x48a104(0x1a2,0x182)];class CompositeCoordinateSystem{constructor(_0x1fcb02,_0x3f399f){const _0x1557c5={_0xff84db:0x18a,_0x396483:0x1bc},_0x127e8c={_0x4734b9:0x119};function _0x9e6316(_0x481a75,_0x578fbb){return _0x48a104(_0x481a75,_0x578fbb-_0x127e8c._0x4734b9);}this['_mars3d_scene']=_0x1fcb02;function _0x426eb4(_0x40a9d2,_0x24b697){return _0x48a104(_0x40a9d2,_0x24b697- -0x30f);}this[_0x426eb4(-_0x1557c5._0xff84db,-0x1ac)]=['lng','lat'],this[_0x426eb4(-0x1a1,-_0x1557c5._0x396483)]=[0x0,0x0],this['_api']=_0x3f399f;}['setMapOffset'](_0x358aa8){this['_mapOffset']=_0x358aa8;}[_0x4ec83c(0x15d,0x161)](){return this['_mars3d_scene'];}['dataToPoint'](_0x59786f){const _0x565b56={_0x342094:0x82,_0x31ad3a:0xa7,_0x379f6e:0xb1,_0x443d12:0x8d,_0x5783ec:0x413,_0x20e1de:0x400,_0x2d4535:0x412,_0x464450:0x41a},_0x37aad6={_0x297ae0:0x2a7},_0x3ea647=this[_0x429c34(_0x565b56._0x342094,0x8c)],_0x44e147=[NaN,NaN];function _0x3e3182(_0x309d9c,_0x52bb8a){return _0x48a104(_0x52bb8a,_0x309d9c-_0x37aad6._0x297ae0);}let _0x439e61=_0x3ea647['echartsFixedHeight'];_0x3ea647[_0x3e3182(0x3f5,0x3d2)]&&(_0x439e61=_0x3ea647[_0x3e3182(0x41c,0x3f9)](Cesium$1[_0x429c34(0xad,0xd4)]['fromDegrees'](_0x59786f[0x0],_0x59786f[0x1])));function _0x429c34(_0x1dcb62,_0x2a9743){return _0x4ec83c(_0x1dcb62,_0x2a9743- -0xab);}const _0xfb314d=Cesium$1['Cartesian3'][_0x429c34(0xa0,0x88)](_0x59786f[0x0],_0x59786f[0x1],_0x439e61);if(!_0xfb314d)return _0x44e147;const _0x122f86=mars3d__namespace[_0x429c34(_0x565b56._0x31ad3a,0xa3)][_0x429c34(_0x565b56._0x379f6e,_0x565b56._0x443d12)](_0x3ea647,_0xfb314d);if(!_0x122f86)return _0x44e147;if(_0x3ea647['echartsDepthTest']&&_0x3ea647['mode']===Cesium$1['SceneMode']['SCENE3D']){const _0x531985=new Cesium$1[(_0x3e3182(_0x565b56._0x5783ec,_0x565b56._0x20e1de))](_0x3ea647['globe']['ellipsoid'],_0x3ea647[_0x3e3182(0x416,_0x565b56._0x2d4535)]['positionWC']),_0x4db3ed=_0x531985[_0x3e3182(0x41e,_0x565b56._0x464450)](_0xfb314d);if(!_0x4db3ed)return _0x44e147;}return[_0x122f86['x']-this['_mapOffset'][0x0],_0x122f86['y']-this['_mapOffset'][0x1]];}['getViewRect'](){const _0x8444e5={_0x3a1497:0x3e4};function _0x31f040(_0x3bd0cc,_0x149a77){return _0x4ec83c(_0x149a77,_0x3bd0cc- -_0x8444e5._0x3a1497);}const _0x4f2716=this['_api'];return new echarts__namespace[(_0x31f040(-0x2a4,-0x29d))]['BoundingRect'](0x0,0x0,_0x4f2716['getWidth'](),_0x4f2716['getHeight']());}['getRoamTransform'](){const _0x5e1d7a={_0xa76943:0x3ae};function _0x232f3e(_0x3eb6ab,_0x5ba920){return _0x4ec83c(_0x3eb6ab,_0x5ba920- -_0x5e1d7a._0xa76943);}return echarts__namespace[_0x232f3e(-0x279,-0x255)]['create']();}}CompositeCoordinateSystem['dimensions']=[_0x4ec83c(0x16c,0x155),_0x4ec83c(0x160,0x139)],CompositeCoordinateSystem['create']=function(_0x2deefe,_0x3397ab){const _0x3d858b={_0x1321ba:0x416},_0x12a2db={_0x522f92:0x28b,_0x2c0dff:0x27b,_0x34344a:0x305,_0x252fe1:0x264};let _0x1e3665;const _0x1cfe10=_0x2deefe['scheduler'][_0x2afd16(0x3ee,0x3cc)]['_mars3d_scene'];function _0x1fa6cb(_0x44db82,_0x366bcc){return _0x4ec83c(_0x366bcc,_0x44db82-0x1c4);}function _0x2afd16(_0x3251fe,_0x270830){return _0x48a104(_0x3251fe,_0x270830-0x259);}_0x2deefe['eachComponent'](_0x1fa6cb(0x340,0x349),function(_0x20c531){const _0xb982f={_0x5ac11f:0x136},_0x50fa98=_0x3397ab[_0x17e775(0x290,_0x12a2db._0x522f92)]()['painter'];function _0x17e775(_0x2aad91,_0x3877c2){return _0x2afd16(_0x2aad91,_0x3877c2- -_0xb982f._0x5ac11f);}function _0x221e45(_0x541cdf,_0x4ff98c){return _0x2afd16(_0x4ff98c,_0x541cdf- -0xce);}if(!_0x50fa98)return;!_0x1e3665&&(_0x1e3665=new CompositeCoordinateSystem(_0x1cfe10,_0x3397ab)),_0x20c531[_0x17e775(_0x12a2db._0x2c0dff,0x299)]=_0x1e3665,_0x1e3665[_0x221e45(_0x12a2db._0x34344a,0x2eb)](_0x20c531[_0x17e775(0x284,_0x12a2db._0x252fe1)]||[0x0,0x0]);}),_0x2deefe['eachSeries'](function(_0x1d20be){const _0x38fc39={_0x18f917:0x32};function _0x123e4d(_0x2e7732,_0x7da313){return _0x1fa6cb(_0x7da313-0xeb,_0x2e7732);}function _0x5199e2(_0x3680a6,_0x450268){return _0x1fa6cb(_0x3680a6- -_0x38fc39._0x18f917,_0x450268);}_0x1d20be['get']('coordinateSystem')===_0x123e4d(0x437,0x42b)&&(!_0x1e3665&&(_0x1e3665=new CompositeCoordinateSystem(_0x1cfe10,_0x3397ab)),_0x1d20be[_0x123e4d(_0x3d858b._0x1321ba,0x418)]=_0x1e3665);});};if(echarts__namespace!==null&&echarts__namespace!==void 0x0&&echarts__namespace[_0x4ec83c(0x122,0x143)]){echarts__namespace['registerCoordinateSystem']('mars3dMap',CompositeCoordinateSystem);const _0x274f23={};_0x274f23[_0x48a104(0x17b,0x186)]=_0x48a104(0x143,0x143),_0x274f23['event']='mars3dMapRoam',_0x274f23[_0x48a104(0x149,0x165)]='updateLayout',echarts__namespace['registerAction'](_0x274f23,function(_0x4f69f5,_0x345086){});const _0xaec81d={};_0xaec81d['roam']=![];const _0x3b69e9={};_0x3b69e9[_0x48a104(0x1b0,0x186)]=_0x48a104(0x19a,0x189),_0x3b69e9[_0x48a104(0x167,0x16e)]=function(){return this['_mars3d_scene'];},_0x3b69e9['defaultOption']=_0xaec81d,echarts__namespace[_0x48a104(0x15f,0x16a)](_0x3b69e9),echarts__namespace['extendComponentView']({'type':_0x4ec83c(0x16d,0x17c),'init':function(_0x901a1f,_0x454feb){const _0xeb2b16={_0x354f4b:0x16,_0x3e183f:0x6};this[_0x55763(0x6,0xe)]=_0x454feb,this['scene']=_0x901a1f['scheduler'][_0x164f71(0x45b,0x45b)]['_mars3d_scene'];function _0x55763(_0x227c09,_0x1fc2c8){return _0x48a104(_0x227c09,_0x1fc2c8- -0x177);}function _0x164f71(_0x53e212,_0x31fe3f){return _0x48a104(_0x53e212,_0x31fe3f-0x2e8);}this[_0x55763(_0xeb2b16._0x354f4b,-_0xeb2b16._0x3e183f)][_0x164f71(0x49c,0x47a)]['addEventListener'](this['moveHandler'],this);},'moveHandler':function(_0x11dc86,_0x393143){const _0x7b4b00={_0x1bf34d:0x287},_0x209cab={};function _0x271ae5(_0x555d39,_0x32d2ca){return _0x4ec83c(_0x32d2ca,_0x555d39- -0x17e);}_0x209cab['type']='mars3dMapRoam';function _0x3c9c10(_0x58ba04,_0x432389){return _0x48a104(_0x432389,_0x58ba04-_0x7b4b00._0x1bf34d);}this[_0x3c9c10(0x40c,0x3f0)][_0x271ae5(-0x8,-0x14)](_0x209cab);},'render':function(_0x39339a,_0xe8743,_0x2c7dac){},'dispose':function(_0x4f50a8){const _0x5bfa1e={_0x142619:0x2c};function _0x2000de(_0x14dc6b,_0x45345b){return _0x4ec83c(_0x45345b,_0x14dc6b- -0x11f);}function _0x3e08d1(_0x1b9292,_0x1fd3a3){return _0x48a104(_0x1b9292,_0x1fd3a3- -0x147);}this[_0x2000de(0x45,0x4a)]['postRender'][_0x3e08d1(_0x5bfa1e._0x142619,0x40)](this['moveHandler'],this);}});}else throw new Error('请引入\x20echarts\x20库\x20');const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace[_0x4ec83c(0x156,0x171)]['BaseLayer'];class EchartsLayer extends BaseLayer{constructor(_0x507bb2={}){function _0x2da288(_0x54c088,_0xe1b475){return _0x4ec83c(_0xe1b475,_0x54c088- -0x14);}super(_0x507bb2),this['_pointerEvents']=this['options'][_0x2da288(0x14f,0x128)];}get[_0x4ec83c(0x165,0x171)](){return this['_echartsInstance'];}get['pointerEvents'](){return this['_pointerEvents'];}set[_0x4ec83c(0x170,0x163)](_0x12edef){const _0x208d47={_0x10f710:0x366,_0x3d2337:0x4d,_0x1be3ce:0x69},_0x4661ef={_0x445239:0x1bd},_0x30b612={_0x23eea0:0x216};function _0xe325d6(_0x37b5cc,_0x2cf5a1){return _0x4ec83c(_0x2cf5a1,_0x37b5cc-_0x30b612._0x23eea0);}function _0x2da74a(_0x57477f,_0x1917c0){return _0x48a104(_0x1917c0,_0x57477f- -_0x4661ef._0x445239);}this['_pointerEvents']=_0x12edef,this['_echartsContainer']&&(_0x12edef?this['_echartsContainer']['style']['pointerEvents']=_0xe325d6(0x38d,_0x208d47._0x10f710):this['_echartsContainer']['style'][_0x2da74a(-_0x208d47._0x3d2337,-_0x208d47._0x1be3ce)]=_0x2da74a(-0x33,-0x2d));}[_0x48a104(0x13a,0x15d)](_0x40befe,_0x56a015){this['setEchartsOption'](_0x40befe);}['_showHook'](_0x6f5a8f){_0x6f5a8f?this['_echartsContainer']['style']['visibility']='visible':this['_echartsContainer']['style']['visibility']='hidden';}[_0x48a104(0x173,0x149)](){const _0x10e828={_0x3cc147:0x67,_0x3565ca:0x70,_0x22f298:0xac,_0x351542:0xaf},_0x1d039a={_0x7e97ab:0x1ec};function _0x1cdcbc(_0x4aeee9,_0x1882b1){return _0x4ec83c(_0x4aeee9,_0x1882b1- -_0x1d039a._0x7e97ab);}this[_0x4d750b(0x74,0x4b)]['scene'][_0x4d750b(_0x10e828._0x3cc147,0x6d)]=this[_0x1cdcbc(-_0x10e828._0x3565ca,-0x8c)][_0x1cdcbc(-0xb6,-0x9b)]??!![];function _0x4d750b(_0x382246,_0xab718){return _0x4ec83c(_0xab718,_0x382246- -0xd3);}this['_map']['scene']['echartsAutoHeight']=this['options']['clampToGround']??![],this['_map']['scene']['echartsFixedHeight']=this['options'][_0x1cdcbc(-_0x10e828._0x22f298,-_0x10e828._0x351542)]??0x0;}['_addedHook'](){const _0x5e56b6={_0x3b4c17:0x238,_0x3c23a4:0x289,_0x55ae67:0x283,_0x127d9e:0x2c8,_0x368777:0x2ad},_0x41c1d7={_0x1ee6ff:0xb8};this['_echartsContainer']=this['_createChartOverlay']();function _0x1b133c(_0xf6598c,_0x594ece){return _0x4ec83c(_0xf6598c,_0x594ece- -0x3eb);}this[_0x1308a3(0x25d,_0x5e56b6._0x3b4c17)]=echarts__namespace[_0x1308a3(0x1e6,0x1fb)](this[_0x1b133c(-_0x5e56b6._0x3c23a4,-0x299)]);function _0x1308a3(_0x15e249,_0x2ebcb9){return _0x4ec83c(_0x15e249,_0x2ebcb9-_0x41c1d7._0x1ee6ff);}this['_echartsInstance']['_mars3d_scene']=this['_map'][_0x1b133c(-_0x5e56b6._0x55ae67,-0x287)],this[_0x1b133c(-_0x5e56b6._0x127d9e,-_0x5e56b6._0x368777)](this[_0x1b133c(-0x26f,-0x28b)]);}['_removedHook'](){const _0x4953e3={_0x7c3bb3:0x23,_0x2b1a76:0xfc,_0x25a295:0xf},_0x4be507={_0x5de03b:0x2a5};function _0x53ed61(_0x101e38,_0x5821a5){return _0x48a104(_0x5821a5,_0x101e38- -0x186);}this['_echartsInstance']&&(this[_0x53ed61(0x7,-_0x4953e3._0x7c3bb3)]['clear'](),this[_0x2a93bf(-0x118,-_0x4953e3._0x2b1a76)][_0x53ed61(-0x7,-_0x4953e3._0x25a295)](),delete this['_echartsInstance']);function _0x2a93bf(_0x4862ae,_0x19f901){return _0x48a104(_0x19f901,_0x4862ae- -_0x4be507._0x5de03b);}this['_echartsContainer']&&(this['_map']['container']['removeChild'](this['_echartsContainer']),delete this[_0x2a93bf(-0x146,-0x12f)]);}['_createChartOverlay'](){const _0x25bf31={_0x41ac73:0x1c8,_0x18b75e:0x1b0,_0x214d84:0x1c1,_0xce2cc4:0xfb,_0x34af22:0xfc,_0x223006:0xfc,_0x3e083b:0x1ce,_0x16668d:0xc5,_0x1f97aa:0xf4,_0x26fa92:0xeb,_0xd727c3:0x1e9};function _0x2582ee(_0xe2b19,_0x1b07dc){return _0x48a104(_0xe2b19,_0x1b07dc- -0x340);}const _0x1493fd=mars3d__namespace['DomUtil']['create']('div','mars3d-echarts',this['_map']['container']);function _0x1ee4a3(_0x5f04bc,_0x113f8b){return _0x48a104(_0x5f04bc,_0x113f8b- -0x94);}return _0x1493fd['id']=this['id'],_0x1493fd[_0x2582ee(-_0x25bf31._0x41ac73,-_0x25bf31._0x18b75e)]['position']=_0x1ee4a3(0xec,0xd3),_0x1493fd[_0x2582ee(-_0x25bf31._0x214d84,-0x1b0)]['top']='0px',_0x1493fd['style']['left']=_0x1ee4a3(0xdb,_0x25bf31._0xce2cc4),_0x1493fd[_0x1ee4a3(0xdf,_0x25bf31._0x34af22)]['width']=this[_0x1ee4a3(0xb2,0xc0)][_0x2582ee(-0x1d9,-0x1cf)]['canvas']['clientWidth']+'px',_0x1493fd[_0x1ee4a3(0xd3,_0x25bf31._0x223006)][_0x2582ee(-_0x25bf31._0x3e083b,-0x1b2)]=this['_map']['scene']['canvas'][_0x1ee4a3(0xcf,_0x25bf31._0x16668d)]+'px',_0x1493fd['style']['pointerEvents']=this[_0x1ee4a3(0x11c,_0x25bf31._0x1f97aa)]?'all':_0x1ee4a3(_0x25bf31._0x26fa92,0xf6),_0x1493fd['style']['zIndex']=this['options'][_0x2582ee(-_0x25bf31._0xd727c3,-0x1c5)]??0x9,_0x1493fd;}['resize'](){const _0x3dd3d0={_0x882a10:0x1de,_0x598891:0x1be,_0x4b3d65:0x1b4,_0x3cf85e:0x1bf,_0x268f83:0x121,_0x24d32e:0xd0,_0x5e1751:0x1bf,_0x25df7a:0x121,_0x2f336f:0x128,_0x1b1f32:0x194},_0x52ea68={_0x16cb7c:0x5b};function _0x2ba03e(_0x1d8888,_0xd5056b){return _0x4ec83c(_0xd5056b,_0x1d8888- -0x253);}if(!this['_echartsInstance'])return;this['_echartsContainer'][_0x5b8858(0x1d2,_0x3dd3d0._0x882a10)]['width']=this[_0x5b8858(_0x3dd3d0._0x598891,0x1a2)][_0x5b8858(_0x3dd3d0._0x4b3d65,_0x3dd3d0._0x3cf85e)][_0x2ba03e(-_0x3dd3d0._0x268f83,-0x13d)]['clientWidth']+'px',this['_echartsContainer'][_0x2ba03e(-_0x3dd3d0._0x24d32e,-0xe0)]['height']=this['_map'][_0x5b8858(0x1a5,_0x3dd3d0._0x5e1751)][_0x2ba03e(-_0x3dd3d0._0x25df7a,-_0x3dd3d0._0x2f336f)][_0x5b8858(_0x3dd3d0._0x1b1f32,0x1a7)]+'px';function _0x5b8858(_0x17cc52,_0x3d4431){return _0x4ec83c(_0x17cc52,_0x3d4431-_0x52ea68._0x16cb7c);}this['_echartsInstance'][_0x5b8858(0x1ae,0x1b7)]();}['setEchartsOption'](_0x1eebf8,_0x4e6f63,_0x3a1e11){const _0x40084c={_0x5112e1:0xa3,_0xba35b3:0x93},_0x8a2c52={_0x4cb197:0xdd};function _0x50d051(_0x464f02,_0x9e3b5a){return _0x48a104(_0x9e3b5a,_0x464f02- -0x2);}function _0x3b6d71(_0x440ebe,_0x5bc9ae){return _0x4ec83c(_0x5bc9ae,_0x440ebe- -_0x8a2c52._0x4cb197);}if(this[_0x3b6d71(_0x40084c._0x5112e1,0x9f)]){const _0xa7eb33={};_0xa7eb33['onlySimpleType']=!![],_0x1eebf8={'mars3dMap':{},...mars3d__namespace[_0x50d051(0x14a,0x150)]['getAttrVal'](_0x1eebf8,_0xa7eb33)},delete _0x1eebf8[_0x3b6d71(_0x40084c._0xba35b3,0x82)],this['_echartsInstance']['setOption'](_0x1eebf8,_0x4e6f63,_0x3a1e11);}}['getRectangle'](_0x279b5c){const _0xd13624={_0x5560cc:0x2d2,_0x35ff72:0x2c0,_0x274e5b:0x2aa,_0x1fe49d:0x2af},_0x26522b={_0xed22b5:0x4b8},_0x47c91d={_0x92ae6b:0x29a};function _0x2a69c5(_0x5f3fe2,_0x54181e){return _0x4ec83c(_0x5f3fe2,_0x54181e-0x13c);}let _0x2bf05d,_0x59bcd1,_0x30f9a0,_0x4f555b;function _0x21c5ee(_0x3a2cb9,_0x457ef5){return _0x4ec83c(_0x3a2cb9,_0x457ef5- -0x24c);}function _0x2b32e7(_0x375455){if(!Array['isArray'](_0x375455))return;function _0x355737(_0x4235ff,_0x23d5c1){return _0x4f76(_0x4235ff- -0x3db,_0x23d5c1);}const _0x518721=_0x375455[0x0]||0x0,_0x31728b=_0x375455[0x1]||0x0;_0x518721!==0x0&&_0x31728b!==0x0&&(_0x2bf05d===undefined?(_0x2bf05d=_0x518721,_0x59bcd1=_0x518721,_0x30f9a0=_0x31728b,_0x4f555b=_0x31728b):(_0x2bf05d=Math['min'](_0x2bf05d,_0x518721),_0x59bcd1=Math['max'](_0x59bcd1,_0x518721),_0x30f9a0=Math['min'](_0x30f9a0,_0x31728b),_0x4f555b=Math[_0x355737(-_0x47c91d._0x92ae6b,-0x2b4)](_0x4f555b,_0x31728b)));}const _0x87ff98=this['options'][_0x2a69c5(_0xd13624._0x5560cc,_0xd13624._0x35ff72)];_0x87ff98&&_0x87ff98['forEach'](_0x604bc0=>{const _0x30f00e={_0x4f3733:0xce};function _0x50fdad(_0x5873a9,_0x3b59a0){return _0x2a69c5(_0x5873a9,_0x3b59a0-0x261);}_0x604bc0[_0x50fdad(_0x26522b._0xed22b5,0x4d2)]&&_0x604bc0['data']['forEach'](_0x4f3aae=>{function _0xf3d1ca(_0x34d68f,_0x4f78c0){return _0x50fdad(_0x4f78c0,_0x34d68f- -0x42b);}function _0x49ccc5(_0x36f2dd,_0x41fcd1){return _0x50fdad(_0x36f2dd,_0x41fcd1- -0x9c);}if(_0x4f3aae['value'])_0x2b32e7(_0x4f3aae[_0xf3d1ca(0xb6,_0x30f00e._0x4f3733)]);else _0x4f3aae['coords']&&_0x4f3aae['coords'][_0xf3d1ca(0xc1,0xdd)](_0x444c2b=>{_0x2b32e7(_0x444c2b);});});});if(_0x2bf05d===0x0&&_0x30f9a0===0x0&&_0x59bcd1===0x0&&_0x4f555b===0x0)return null;if(_0x279b5c!==null&&_0x279b5c!==void 0x0&&_0x279b5c[_0x2a69c5(_0xd13624._0x274e5b,_0xd13624._0x1fe49d)]){const _0x457ab9={};return _0x457ab9['xmin']=_0x2bf05d,_0x457ab9['xmax']=_0x59bcd1,_0x457ab9['ymin']=_0x30f9a0,_0x457ab9['ymax']=_0x4f555b,_0x457ab9;}else return Cesium['Rectangle']['fromDegrees'](_0x2bf05d,_0x30f9a0,_0x59bcd1,_0x4f555b);}['on'](_0x3e73c9,_0x4f4eaa,_0x22681f){return this['_echartsInstance']['on'](_0x3e73c9,_0x4f4eaa,_0x22681f||this),this;}['onByQuery'](_0x1baadc,_0x58aa4d,_0x8c6179,_0x484006){return this['_echartsInstance']['on'](_0x1baadc,_0x58aa4d,_0x8c6179,_0x484006||this),this;}['off'](_0x34dcbe,_0x84d121,_0x1ab89c){return this['_echartsInstance']['off'](_0x34dcbe,_0x84d121,_0x1ab89c||this),this;}}mars3d__namespace[_0x48a104(0x185,0x18b)][_0x48a104(0x166,0x157)]('echarts',EchartsLayer),mars3d__namespace['layer'][_0x4ec83c(0x147,0x145)]=EchartsLayer,mars3d__namespace[_0x4ec83c(0x165,0x154)]=echarts__namespace,exports['EchartsLayer']=EchartsLayer,Object['keys'](echarts)['forEach'](function(_0x560767){const _0x3c49d5={_0x329b3e:0x160};function _0x5673fd(_0x2eabc0,_0x412e61){return _0x4ec83c(_0x2eabc0,_0x412e61- -0x1aa);}function _0xaad262(_0x13293f,_0x4d801a){return _0x48a104(_0x4d801a,_0x13293f-_0x3c49d5._0x329b3e);}if(_0x560767!==_0xaad262(0x2d2,0x2dd)&&!exports['hasOwnProperty'](_0x560767))Object[_0x5673fd(-0x35,-0x5f)](exports,_0x560767,{'enumerable':!![],'get':function(){return echarts[_0x560767];}});});const _0x97e686={};_0x97e686[_0x4ec83c(0x136,0x144)]=!![],Object['defineProperty'](exports,_0x48a104(0x168,0x17c),_0x97e686);
}));
