<template>
	<use-case-scenario-edit :statusList="statusList" @search="search" ></use-case-scenario-edit>
	<use-case-scenario-check-list :statusList="statusList"></use-case-scenario-check-list>
	<use-case-examine-list :statusList="statusList"></use-case-examine-list>
	<el-row :gutter="20">
		<el-col :span="3">
			<el-input v-model="searchModel.sceneName" @keydown.enter="search" placeholder="场景名称" clearable />
		</el-col>
		<el-col :span="2">
			<el-select style="width: 100%;" v-model="searchModel.status" placeholder="状态" clearable>
				<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
					:value="item.nameEn"></el-option>
			</el-select>
		</el-col>
		<el-col :span="3">
			<el-input v-model="searchModel.note" @keydown.enter="search" placeholder="备注" clearable />
		</el-col>
		<el-col :span="1">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="5" :push="10">
			<el-button style="float: right;" type="primary" @click="add">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="useCaseScenarioList" border height="calc(100vh - 300px)" style="width: 100%">
				<el-table-column prop="id" align="center" label="ID"/>
				<el-table-column prop="sceneName" align="center" label="场景名称" />
				<el-table-column prop="status" align="center" label="状态" width="100">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
						<!-- <el-switch :active-value="1" :inactive-value="0" v-model="scope.row.status" @change="editStatus(scope.row)" /> -->
					</template>
				</el-table-column>
				<el-table-column prop="createTime" align="center" width="170" label="创建时间"/>
				<el-table-column prop="updateTime" align="center" width="170" label="修改时间"/>
				<el-table-column prop="note" align="center" label="备注"/>

				<el-table-column align="center" width="210" label="操作">
					<template #default="scope">
						
						<el-button type="text" size="default" @click="examineSelf(scope.row)">步骤</el-button>
						<el-button type="text" size="default" @click="examine(scope.row)">检查</el-button>
						<el-button type="text" size="default" @click="edit(scope.row)">编辑</el-button>
						<el-button style="margin-right: 10px;" type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('sys:healthCheck:delete')">删除</el-button>
                    </template>
				</el-table-column>
			</el-table>
		</el-col>

		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>
<script>
import { listCheckScene, deleteCheckScene, getCheckScene, checkSceneTest, listTestCase } from "@/api/healthCheck/useCaseScenario"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import useCaseScenarioEdit from "@/componts/healthCheck/useCaseScenarioEdit.vue"
import useCaseScenarioCheckList from "@/componts/healthCheck/useCaseScenarioCheckList.vue"
import useCaseExamineList from "@/componts/healthCheck/useCaseExamineList.vue"

export default {
	components:{ useCaseScenarioEdit, useCaseScenarioCheckList, useCaseExamineList },
	data() {
		return {
			searchModel: {},
			useCaseScenarioList: [],
			statusList: [],
			total: 0,
			pageSize: 10,
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			listCheckScene(this.searchModel)
			.then(res => {
				this.useCaseScenarioList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		examineSelf(row){
			const sendData = {
				checkSceneId: row.id
			}
			listTestCase(sendData).then( res => {
				const data  = {...sendData,...{list: res.data.result.list},...{total:res.data.result.total},...{title:row.sceneName}}
				console.log(data);
				mitt.emit('openExamineSelf',data)
			})
		},
		// async editStatus(data) {
		// 	let searchModel = {
		// 		id: data.id,
		// 		status: data.status
		// 	}
		// 	editHealthExamina(searchModel)
		// },
		edit(row){
            getCheckScene(row.id).then(res => {
				mitt.emit('openUseCaseScenarioEdit',res.data.result)
			})
		},
		examine(row){
			this.$confirm('正在执行检查, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					checkSceneTest(row.id)
					.then(res =>{
						mitt.emit('openExamineEdit',res.data.result)
						this.search()
					})
				}).catch(()=>{})
		},
		deleted(id){
			this.$confirm('删除区域系统, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteCheckScene(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		add() {
			mitt.emit('openUseCaseScenarioAdd',this.searchModel)
		},
		async init(){
			try{
				let resStatus = await listDictByNameEn('common_status')
				this.statusList = resStatus.data.result
				let res = await listCheckScene(this.searchModel)
				this.useCaseScenarioList = res.data.result.list
				this.total = res.data.result.total
			}catch(err){
			}
		}
	},
	created() {
		mitt.off('openUserEdit')
		mitt.off('openUserRegister')
		mitt.on('refreshUserList',()=>{this.search()})
		this.init()
		// this.search()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	div /deep/ .el-tree-node__content{
		height: 35px;
	}
	div /deep/ .el-tree-node__content:hover{
		color: #fff;
		background-color: var(--el-color-primary-light-5);
	}

	div /deep/ .el-tree-node__expand-icon{
		font-size: 18px;
		margin-right: 15px;
	}
</style>