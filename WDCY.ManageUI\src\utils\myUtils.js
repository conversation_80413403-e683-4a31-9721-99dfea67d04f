/**
 * 打开地图
 * @param {*} mitt  functional event emitter / pubsub.
 * @param {*} editMode  是否编辑模式：true or false
 * @param {*} position  锚点经纬度，可null
 * @param {*} pointName 锚点名称，可null
 */
export const openMap = (mitt, editMode, position, pointName) => {
  let centerPosition = [116.405005, 39.914902];
  let communityPosition = JSON.parse(localStorage.getItem("lnglat"));
  if (communityPosition && communityPosition.length >= 2) {
    centerPosition = communityPosition;
  } else {
    let userInfo = JSON.parse(localStorage.getItem("userInfo"));

    if (userInfo.groupLng && userInfo.groupLat) {
      centerPosition = [userInfo.groupLng, userInfo.groupLat];
    }
  }
  console.log(mitt, editMode, position, pointName);
  mitt.emit("openMap", {
    editMode: editMode,
    center: centerPosition,
    anchor: position,
    anchorName: pointName
  });
}

export const openMarsMap = (mitt, data) => {
  mitt.emit("openMarsMap", data);
}
/**
 * 打开地理围栏编辑器
 * @param {*} mitt 
 * @param {*} data 包含业务id、地理围栏、地图配置
 */
export const openGeofencingMap = (mitt, data) => {
  var config = JSON.parse(data.mapConfig);
  var mode = config.map.mode;
  //模型位置
  var titlePosition;
  //地图中心，用于地图定位
  var center = {
    alt: 500,
    heading: 0,
    pitch: -90
  };
  var rotationSet = { x: 0, y: 0, z: 0 };
  var scaleSet = 1;
  var showBaseMap = false;
  var modeUrl;

  var polyCoords = [];

  if (data.polyCoords) {
    polyCoords = JSON.parse(data.polyCoords);
  }
  //3DGIS参数
  if (data.enabled3d && mode == "mars3d") {
    let sdgis = config.map.sdgis;
    titlePosition = sdgis.position;
    modeUrl = sdgis.tdtile;

    try {
      rotationSet = sdgis.rotation;
    } catch (error) { }

    try {
      scaleSet = sdgis.scale;
    } catch (error) { }

    try {
      showBaseMap = sdgis.showBaseMap;
    } catch (error) { }
    //地图中心默认设置为模型位置
    center = {
      lng: titlePosition.lng,
      lat: titlePosition.lat,
      alt: sdgis.view.alt,
      heading: sdgis.view.heading,
      pitch: sdgis.view.pitch,
    };
  }
  //如有锚点位置，则将地图中心移至锚点
  var point = [center.lng, center.lat, 20];
  if (data.position) {
    center.lng = data.position.lng;
    center.lat = data.position.lat;
    point = [center.lng, center.lat, data.position.alt || 20];
  }

  var eventData = {
    title: data.title,
    edit: true,
    polyCoordsEdit: true,
    modeUrl: modeUrl,
    enabled3d: data.enabled3d,
    id: data.id,
    polyCoords: polyCoords,
    point: point, //锚点位置
    position: titlePosition, //模型位置
    center: center, //地图中心
    rotationSet,
    scaleSet,
    showBaseMap: showBaseMap,
  };
  console.info("editGeofencingMap", eventData);
  mitt.emit("editGeofencingMap", eventData);
}

/**
 * 获取预警规则描述字符串
 * @param {String} checkRuleNote 检测规则描述模板
 * @param {Object} detectRules 检测规则列表项
 */
export const getWarnDetectRuleNote = (checkRuleNote, detectRules) => {
  //分离模板以及点位索引
  if (!checkRuleNote) return "";
  let tplRuleList = checkRuleNote.split(/#\d\#/);
  let ruleIndexList = checkRuleNote.match(/#\d\#/gi);
  ruleIndexList = Array.from(ruleIndexList, (element) => {
    return Number(element.replace(/#/gi, ""));
  });

  let list = [];
  let index = 1;
  for (let tpl of tplRuleList) {
    // 检索点位规则项
    let ruleItem = detectRules.find(
      (m) => m.sort == ruleIndexList[index - 1]
    );
    let value = "";
    if (ruleItem) {
      if (ruleItem.valDataType == "BOOL") {
        // value = ruleItem.optionVal.split("|")[Math.abs(parseInt(ruleItem.thresholdVal) - 1)];
        // value = ruleItem.optionVal.split("|").reverse()[parseInt(ruleItem.thresholdVal)];
        let kv = ruleItem.optionVal.split("|").find(kv => ruleItem.thresholdVal == '1' && kv.includes('true') || ruleItem.thresholdVal == '0' && kv.includes('false'))
        value = kv ? kv.split(':')[0] : ''
      } else if (ruleItem.valDataType == "SELECT") {
        let kv = ruleItem.optionVal.split("|").find(kv => kv.includes(ruleItem.thresholdVal))
        value = kv ? kv.split(':')[0] : ''
      } else {
        value = ruleItem.thresholdVal;
      }
    }
    list.push({ text: tpl, value: value });
    index++;
    // console.log("warnEventDetectRules.find:", ruleItem);
  }
  let note = "";
  list.forEach(
    (item) =>
      (note += item.text + (item.value ? "『" + item.value + "』" : ""))
  );
  // console.log("getWarnDetectRuleNote.list:", list, note);
  return note;
}

/**
 * 计算两个事件
 */
export function getTwoTime(showTime) {

  const currentTime = new Date()
  const targetTime = new Date(showTime)

  const timeDiff = currentTime.getTime() - targetTime.getTime()

  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24)) <= 0 ? "" : Math.floor(timeDiff / (1000 * 60 * 60 * 24)) + "天"
  const hours = Math.floor(timeDiff / (1000 * 60 * 60) % 24) <= 0 ? "" : Math.floor(timeDiff / (1000 * 60 * 60) % 24) + "小时"
  const minutes = Math.floor((timeDiff / (1000 * 60)) % 60) <= 0 ? "" : Math.floor((timeDiff / (1000 * 60)) % 60) + "分"
  let ret = `${days}${hours}${minutes}`
  let time = ret.includes("-") ? "--" : ret
  return time

}

/**
 * 通用js方法封装处理
 * Copyright (c) 2019 ruoyi
 */

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}
//格式化时间 YYYY-MM-DD HH:mm:ss
export function formatDate(date) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  console.log(`${year}-${month}-${day} ${hours}:${minutes}:${seconds}`);
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  let search = params;
  search.params = typeof (search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
  dateRange = Array.isArray(dateRange) ? dateRange : [];
  // debugger
  if (typeof (propName) === 'undefined') {
    search.params['beginTime'] = dateRange[0]; //dateRange.length>0?dateRange[0]:undefined;
    search.params['endTime'] = dateRange[1];   //dateRange.length>1?dateRange[1]:undefined;
  } else {
    search.params['begin' + propName] = dateRange[0];
    search.params['end' + propName] = dateRange[1];
  }
  //仅post body模式识别
  return search;
}
let CROPPER;
export default CROPPER


// 前台按钮权限控制
import store from '../store/index.js'
export function hasPermission(permission) {
  const myPermissions = store.getters.permissions
  return myPermissions.indexOf(permission) > -1
}

//加密身份证手机号
export function encryptData(text) {
  var oldVal, newVal;
  oldVal = String(text)
  if (oldVal.length === 11) {
    newVal = oldVal.replace(/^(\d{3})\d+(\d{4})$/, "$1****$2");
  }
  if (oldVal.length === 18 || oldVal.length === 15) {
    newVal = oldVal.replace(/^(\d{6})\d+(\d{4})$/, "$1******$2");
  }
  return newVal
}


import { listDictByNameEn } from "@/api/admin/dict";
export function saveDict(text) {
  if (!localStorage.getItem("distList")) {
    listDictByNameEn(text).then(res => {
      let data = {}
      data[text] = res.data.result
      localStorage.setItem("distList", JSON.stringify(data))
    })
  } else {
    if (!JSON.stringify(localStorage.getItem("distList")).includes(text)) {
      listDictByNameEn(text).then(res => {

        let obj = JSON.parse(localStorage.getItem("distList"))
        obj[text] = res.data.result
        delete obj.undefined

        localStorage.setItem("distList", JSON.stringify(obj))
      })
    }
  }
  // if (!JSON.stringify(localStorage.getItem("distList")).includes(text))
  // {
  //   listDictByNameEn(text).then(res => {
  //     let data = {}
  //     data[text] = res.data.result
  //     console.log(data);
  //     localStorage.setItem("distList", (JSON.stringify(localStorage.getItem("distList"))?(JSON.stringify(localStorage.getItem("distList"))+JSON.stringify(data)):JSON.stringify(data)))
  //   })
  // }
}

export function randomString(len) {
  var lenth = len || 32;
  var str = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678!@#$%^&';    /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
  var maxPos = str.length;
  var pwd = '';
  for (var i = 0; i < lenth; i++) {
    pwd += str.charAt(Math.floor(Math.random() * maxPos));
  }
  return pwd;
}


// import JSEncrypt from 'jsencrypt'
import { Base64 } from 'js-base64';
// 加密
export function encrypt(randomStr32, userInfo) {
  // console.log(userInfo);
  var result = Base64.encode(userInfo + '');
  const txt = randomStr32 + '' + result
  // console.log(txt);
  return txt
}
// 解密
export function decrypt(txt) {
  // console.log(Base64.decode(txt));
  return Base64.decode(txt);
}