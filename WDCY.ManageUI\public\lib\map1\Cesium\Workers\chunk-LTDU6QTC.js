/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.124
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as E}from"./chunk-RPAJTIFB.js";import{a as Q}from"./chunk-EQBXX45H.js";import{a as ae}from"./chunk-E2XIHPTZ.js";import{b as oe,c as re,d as J}from"./chunk-T6R736YE.js";import{b as A,d as ie}from"./chunk-V7TDVUVX.js";import{a as b,b as F,f as N}from"./chunk-T5JXF4O6.js";import{a as H}from"./chunk-GNTVNTUY.js";import{a as r,e as G}from"./chunk-HWH6BBH6.js";import{a as P}from"./chunk-DW4EQ6YI.js";import{a as m}from"./chunk-CJFJKB5V.js";import{a as w,b as O}from"./chunk-4BGNE6LH.js";import{e as a}from"./chunk-OX5CTLXY.js";function T(e){this.planes=m(e,[])}var X=[new r,new r,new r];r.clone(r.UNIT_X,X[0]);r.clone(r.UNIT_Y,X[1]);r.clone(r.UNIT_Z,X[2]);var V=new r,_e=new r,fe=new Q(new r(1,0,0),0);T.fromBoundingSphere=function(e,t){if(!a(e))throw new w("boundingSphere is required.");a(t)||(t=new T);let n=X.length,f=t.planes;f.length=2*n;let p=e.center,d=e.radius,s=0;for(let y=0;y<n;++y){let i=X[y],c=f[s],_=f[s+1];a(c)||(c=f[s]=new b),a(_)||(_=f[s+1]=new b),r.multiplyByScalar(i,-d,V),r.add(p,V,V),c.x=i.x,c.y=i.y,c.z=i.z,c.w=-r.dot(i,V),r.multiplyByScalar(i,d,V),r.add(p,V,V),_.x=-i.x,_.y=-i.y,_.z=-i.z,_.w=-r.dot(r.negate(i,_e),V),s+=2}return t};T.prototype.computeVisibility=function(e){if(!a(e))throw new w("boundingVolume is required.");let t=this.planes,n=!1;for(let f=0,p=t.length;f<p;++f){let d=e.intersectPlane(Q.fromCartesian4(t[f],fe));if(d===A.OUTSIDE)return A.OUTSIDE;d===A.INTERSECTING&&(n=!0)}return n?A.INTERSECTING:A.INSIDE};T.prototype.computeVisibilityWithPlaneMask=function(e,t){if(!a(e))throw new w("boundingVolume is required.");if(!a(t))throw new w("parentPlaneMask is required.");if(t===T.MASK_OUTSIDE||t===T.MASK_INSIDE)return t;let n=T.MASK_INSIDE,f=this.planes;for(let p=0,d=f.length;p<d;++p){let s=p<31?1<<p:0;if(p<31&&!(t&s))continue;let y=e.intersectPlane(Q.fromCartesian4(f[p],fe));if(y===A.OUTSIDE)return T.MASK_OUTSIDE;y===A.INTERSECTING&&(n|=s)}return n};T.MASK_OUTSIDE=4294967295;T.MASK_INSIDE=0;T.MASK_INDETERMINATE=2147483647;var $=T;function k(e){e=m(e,m.EMPTY_OBJECT),this.left=e.left,this._left=void 0,this.right=e.right,this._right=void 0,this.top=e.top,this._top=void 0,this.bottom=e.bottom,this._bottom=void 0,this.near=m(e.near,1),this._near=this.near,this.far=m(e.far,5e8),this._far=this.far,this._cullingVolume=new $,this._orthographicMatrix=new F}function ce(e){if(!a(e.right)||!a(e.left)||!a(e.top)||!a(e.bottom)||!a(e.near)||!a(e.far))throw new w("right, left, top, bottom, near, or far parameters are not set.");if(e.top!==e._top||e.bottom!==e._bottom||e.left!==e._left||e.right!==e._right||e.near!==e._near||e.far!==e._far){if(e.left>e.right)throw new w("right must be greater than left.");if(e.bottom>e.top)throw new w("top must be greater than bottom.");e.near<=0||e.near>e.far,e._left=e.left,e._right=e.right,e._top=e.top,e._bottom=e.bottom,e._near=e.near,e._far=e.far,e._orthographicMatrix=F.computeOrthographicOffCenter(e.left,e.right,e.bottom,e.top,e.near,e.far,e._orthographicMatrix)}}Object.defineProperties(k.prototype,{projectionMatrix:{get:function(){return ce(this),this._orthographicMatrix}}});var me=new r,ye=new r,ge=new r,ee=new r;k.prototype.computeCullingVolume=function(e,t,n){if(!a(e))throw new w("position is required.");if(!a(t))throw new w("direction is required.");if(!a(n))throw new w("up is required.");let f=this._cullingVolume.planes,p=this.top,d=this.bottom,s=this.right,y=this.left,i=this.near,c=this.far,_=r.cross(t,n,me);r.normalize(_,_);let g=ye;r.multiplyByScalar(t,i,g),r.add(e,g,g);let l=ge;r.multiplyByScalar(_,y,l),r.add(g,l,l);let o=f[0];return a(o)||(o=f[0]=new b),o.x=_.x,o.y=_.y,o.z=_.z,o.w=-r.dot(_,l),r.multiplyByScalar(_,s,l),r.add(g,l,l),o=f[1],a(o)||(o=f[1]=new b),o.x=-_.x,o.y=-_.y,o.z=-_.z,o.w=-r.dot(r.negate(_,ee),l),r.multiplyByScalar(n,d,l),r.add(g,l,l),o=f[2],a(o)||(o=f[2]=new b),o.x=n.x,o.y=n.y,o.z=n.z,o.w=-r.dot(n,l),r.multiplyByScalar(n,p,l),r.add(g,l,l),o=f[3],a(o)||(o=f[3]=new b),o.x=-n.x,o.y=-n.y,o.z=-n.z,o.w=-r.dot(r.negate(n,ee),l),o=f[4],a(o)||(o=f[4]=new b),o.x=t.x,o.y=t.y,o.z=t.z,o.w=-r.dot(t,g),r.multiplyByScalar(t,c,l),r.add(e,l,l),o=f[5],a(o)||(o=f[5]=new b),o.x=-t.x,o.y=-t.y,o.z=-t.z,o.w=-r.dot(r.negate(t,ee),l),this._cullingVolume};k.prototype.getPixelDimensions=function(e,t,n,f,p){if(ce(this),!a(e)||!a(t))throw new w("Both drawingBufferWidth and drawingBufferHeight are required.");if(e<=0)throw new w("drawingBufferWidth must be greater than zero.");if(t<=0)throw new w("drawingBufferHeight must be greater than zero.");if(!a(n))throw new w("distance is required.");if(!a(f))throw new w("pixelRatio is required.");if(f<=0)throw new w("pixelRatio must be greater than zero.");if(!a(p))throw new w("A result object is required.");let d=this.right-this.left,s=this.top-this.bottom,y=f*d/e,i=f*s/t;return p.x=y,p.y=i,p};k.prototype.clone=function(e){return a(e)||(e=new k),e.left=this.left,e.right=this.right,e.top=this.top,e.bottom=this.bottom,e.near=this.near,e.far=this.far,e._left=void 0,e._right=void 0,e._top=void 0,e._bottom=void 0,e._near=void 0,e._far=void 0,e};k.prototype.equals=function(e){return a(e)&&e instanceof k&&this.right===e.right&&this.left===e.left&&this.top===e.top&&this.bottom===e.bottom&&this.near===e.near&&this.far===e.far};k.prototype.equalsEpsilon=function(e,t,n){return e===this||a(e)&&e instanceof k&&P.equalsEpsilon(this.right,e.right,t,n)&&P.equalsEpsilon(this.left,e.left,t,n)&&P.equalsEpsilon(this.top,e.top,t,n)&&P.equalsEpsilon(this.bottom,e.bottom,t,n)&&P.equalsEpsilon(this.near,e.near,t,n)&&P.equalsEpsilon(this.far,e.far,t,n)};var he=k;function z(e){e=m(e,m.EMPTY_OBJECT),this._offCenterFrustum=new he,this.width=e.width,this._width=void 0,this.aspectRatio=e.aspectRatio,this._aspectRatio=void 0,this.near=m(e.near,1),this._near=this.near,this.far=m(e.far,5e8),this._far=this.far}z.packedLength=4;z.pack=function(e,t,n){return O.typeOf.object("value",e),O.defined("array",t),n=m(n,0),t[n++]=e.width,t[n++]=e.aspectRatio,t[n++]=e.near,t[n]=e.far,t};z.unpack=function(e,t,n){return O.defined("array",e),t=m(t,0),a(n)||(n=new z),n.width=e[t++],n.aspectRatio=e[t++],n.near=e[t++],n.far=e[t],n};function j(e){if(!a(e.width)||!a(e.aspectRatio)||!a(e.near)||!a(e.far))throw new w("width, aspectRatio, near, or far parameters are not set.");let t=e._offCenterFrustum;if(e.width!==e._width||e.aspectRatio!==e._aspectRatio||e.near!==e._near||e.far!==e._far){if(e.aspectRatio<0)throw new w("aspectRatio must be positive.");if(e.near<0||e.near>e.far)throw new w("near must be greater than zero and less than far.");e._aspectRatio=e.aspectRatio,e._width=e.width,e._near=e.near,e._far=e.far;let n=1/e.aspectRatio;t.right=e.width*.5,t.left=-t.right,t.top=n*t.right,t.bottom=-t.top,t.near=e.near,t.far=e.far}}Object.defineProperties(z.prototype,{projectionMatrix:{get:function(){return j(this),this._offCenterFrustum.projectionMatrix}},offCenterFrustum:{get:function(){return j(this),this._offCenterFrustum}}});z.prototype.computeCullingVolume=function(e,t,n){return j(this),this._offCenterFrustum.computeCullingVolume(e,t,n)};z.prototype.getPixelDimensions=function(e,t,n,f,p){return j(this),this._offCenterFrustum.getPixelDimensions(e,t,n,f,p)};z.prototype.clone=function(e){return a(e)||(e=new z),e.aspectRatio=this.aspectRatio,e.width=this.width,e.near=this.near,e.far=this.far,e._aspectRatio=void 0,e._width=void 0,e._near=void 0,e._far=void 0,this._offCenterFrustum.clone(e._offCenterFrustum),e};z.prototype.equals=function(e){return!a(e)||!(e instanceof z)?!1:(j(this),j(e),this.width===e.width&&this.aspectRatio===e.aspectRatio&&this._offCenterFrustum.equals(e._offCenterFrustum))};z.prototype.equalsEpsilon=function(e,t,n){return!a(e)||!(e instanceof z)?!1:(j(this),j(e),P.equalsEpsilon(this.width,e.width,t,n)&&P.equalsEpsilon(this.aspectRatio,e.aspectRatio,t,n)&&this._offCenterFrustum.equalsEpsilon(e._offCenterFrustum,t,n))};var B=z;function S(e){e=m(e,m.EMPTY_OBJECT),this.left=e.left,this._left=void 0,this.right=e.right,this._right=void 0,this.top=e.top,this._top=void 0,this.bottom=e.bottom,this._bottom=void 0,this.near=m(e.near,1),this._near=this.near,this.far=m(e.far,5e8),this._far=this.far,this._cullingVolume=new $,this._perspectiveMatrix=new F,this._infinitePerspective=new F}function te(e){if(!a(e.right)||!a(e.left)||!a(e.top)||!a(e.bottom)||!a(e.near)||!a(e.far))throw new w("right, left, top, bottom, near, or far parameters are not set.");let{top:t,bottom:n,right:f,left:p,near:d,far:s}=e;if(t!==e._top||n!==e._bottom||p!==e._left||f!==e._right||d!==e._near||s!==e._far){if(e.near<=0||e.near>e.far)throw new w("near must be greater than zero and less than far.");e._left=p,e._right=f,e._top=t,e._bottom=n,e._near=d,e._far=s,e._perspectiveMatrix=F.computePerspectiveOffCenter(p,f,n,t,d,s,e._perspectiveMatrix),e._infinitePerspective=F.computeInfinitePerspectiveOffCenter(p,f,n,t,d,e._infinitePerspective)}}Object.defineProperties(S.prototype,{projectionMatrix:{get:function(){return te(this),this._perspectiveMatrix}},infiniteProjectionMatrix:{get:function(){return te(this),this._infinitePerspective}}});var Ce=new r,be=new r,Oe=new r,Pe=new r;S.prototype.computeCullingVolume=function(e,t,n){if(!a(e))throw new w("position is required.");if(!a(t))throw new w("direction is required.");if(!a(n))throw new w("up is required.");let f=this._cullingVolume.planes,p=this.top,d=this.bottom,s=this.right,y=this.left,i=this.near,c=this.far,_=r.cross(t,n,Ce),g=be;r.multiplyByScalar(t,i,g),r.add(e,g,g);let l=Oe;r.multiplyByScalar(t,c,l),r.add(e,l,l);let o=Pe;r.multiplyByScalar(_,y,o),r.add(g,o,o),r.subtract(o,e,o),r.normalize(o,o),r.cross(o,n,o),r.normalize(o,o);let h=f[0];return a(h)||(h=f[0]=new b),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,e),r.multiplyByScalar(_,s,o),r.add(g,o,o),r.subtract(o,e,o),r.cross(n,o,o),r.normalize(o,o),h=f[1],a(h)||(h=f[1]=new b),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,e),r.multiplyByScalar(n,d,o),r.add(g,o,o),r.subtract(o,e,o),r.cross(_,o,o),r.normalize(o,o),h=f[2],a(h)||(h=f[2]=new b),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,e),r.multiplyByScalar(n,p,o),r.add(g,o,o),r.subtract(o,e,o),r.cross(o,_,o),r.normalize(o,o),h=f[3],a(h)||(h=f[3]=new b),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,e),h=f[4],a(h)||(h=f[4]=new b),h.x=t.x,h.y=t.y,h.z=t.z,h.w=-r.dot(t,g),r.negate(t,o),h=f[5],a(h)||(h=f[5]=new b),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,l),this._cullingVolume};S.prototype.getPixelDimensions=function(e,t,n,f,p){if(te(this),!a(e)||!a(t))throw new w("Both drawingBufferWidth and drawingBufferHeight are required.");if(e<=0)throw new w("drawingBufferWidth must be greater than zero.");if(t<=0)throw new w("drawingBufferHeight must be greater than zero.");if(!a(n))throw new w("distance is required.");if(!a(f))throw new w("pixelRatio is required");if(f<=0)throw new w("pixelRatio must be greater than zero.");if(!a(p))throw new w("A result object is required.");let d=1/this.near,s=this.top*d,y=2*f*n*s/t;s=this.right*d;let i=2*f*n*s/e;return p.x=i,p.y=y,p};S.prototype.clone=function(e){return a(e)||(e=new S),e.right=this.right,e.left=this.left,e.top=this.top,e.bottom=this.bottom,e.near=this.near,e.far=this.far,e._left=void 0,e._right=void 0,e._top=void 0,e._bottom=void 0,e._near=void 0,e._far=void 0,e};S.prototype.equals=function(e){return a(e)&&e instanceof S&&this.right===e.right&&this.left===e.left&&this.top===e.top&&this.bottom===e.bottom&&this.near===e.near&&this.far===e.far};S.prototype.equalsEpsilon=function(e,t,n){return e===this||a(e)&&e instanceof S&&P.equalsEpsilon(this.right,e.right,t,n)&&P.equalsEpsilon(this.left,e.left,t,n)&&P.equalsEpsilon(this.top,e.top,t,n)&&P.equalsEpsilon(this.bottom,e.bottom,t,n)&&P.equalsEpsilon(this.near,e.near,t,n)&&P.equalsEpsilon(this.far,e.far,t,n)};var pe=S;function R(e){e=m(e,m.EMPTY_OBJECT),this._offCenterFrustum=new pe,this.fov=e.fov,this._fov=void 0,this._fovy=void 0,this._sseDenominator=void 0,this.aspectRatio=e.aspectRatio,this._aspectRatio=void 0,this.near=m(e.near,1),this._near=this.near,this.far=m(e.far,5e8),this._far=this.far,this.xOffset=m(e.xOffset,0),this._xOffset=this.xOffset,this.yOffset=m(e.yOffset,0),this._yOffset=this.yOffset}R.packedLength=6;R.pack=function(e,t,n){return O.typeOf.object("value",e),O.defined("array",t),n=m(n,0),t[n++]=e.fov,t[n++]=e.aspectRatio,t[n++]=e.near,t[n++]=e.far,t[n++]=e.xOffset,t[n]=e.yOffset,t};R.unpack=function(e,t,n){return O.defined("array",e),t=m(t,0),a(n)||(n=new R),n.fov=e[t++],n.aspectRatio=e[t++],n.near=e[t++],n.far=e[t++],n.xOffset=e[t++],n.yOffset=e[t],n};function q(e){if(!a(e.fov)||!a(e.aspectRatio)||!a(e.near)||!a(e.far))throw new w("fov, aspectRatio, near, or far parameters are not set.");if(!(e.fov!==e._fov||e.aspectRatio!==e._aspectRatio||e.near!==e._near||e.far!==e._far||e.xOffset!==e._xOffset||e.yOffset!==e._yOffset))return;if(O.typeOf.number.greaterThanOrEquals("fov",e.fov,0),O.typeOf.number.lessThan("fov",e.fov,Math.PI),O.typeOf.number.greaterThanOrEquals("aspectRatio",e.aspectRatio,0),O.typeOf.number.greaterThanOrEquals("near",e.near,0),e.near>e.far)throw new w("near must be less than far.");e._aspectRatio=e.aspectRatio,e._fov=e.fov,e._fovy=e.aspectRatio<=1?e.fov:Math.atan(Math.tan(e.fov*.5)/e.aspectRatio)*2,e._near=e.near,e._far=e.far,e._sseDenominator=2*Math.tan(.5*e._fovy),e._xOffset=e.xOffset,e._yOffset=e.yOffset;let n=e._offCenterFrustum;n.top=e.near*Math.tan(.5*e._fovy),n.bottom=-n.top,n.right=e.aspectRatio*n.top,n.left=-n.right,n.near=e.near,n.far=e.far,n.right+=e.xOffset,n.left+=e.xOffset,n.top+=e.yOffset,n.bottom+=e.yOffset}Object.defineProperties(R.prototype,{projectionMatrix:{get:function(){return q(this),this._offCenterFrustum.projectionMatrix}},infiniteProjectionMatrix:{get:function(){return q(this),this._offCenterFrustum.infiniteProjectionMatrix}},fovy:{get:function(){return q(this),this._fovy}},sseDenominator:{get:function(){return q(this),this._sseDenominator}},offCenterFrustum:{get:function(){return q(this),this._offCenterFrustum}}});R.prototype.computeCullingVolume=function(e,t,n){return q(this),this._offCenterFrustum.computeCullingVolume(e,t,n)};R.prototype.getPixelDimensions=function(e,t,n,f,p){return q(this),this._offCenterFrustum.getPixelDimensions(e,t,n,f,p)};R.prototype.clone=function(e){return a(e)||(e=new R),e.aspectRatio=this.aspectRatio,e.fov=this.fov,e.near=this.near,e.far=this.far,e._aspectRatio=void 0,e._fov=void 0,e._near=void 0,e._far=void 0,this._offCenterFrustum.clone(e._offCenterFrustum),e};R.prototype.equals=function(e){return!a(e)||!(e instanceof R)?!1:(q(this),q(e),this.fov===e.fov&&this.aspectRatio===e.aspectRatio&&this._offCenterFrustum.equals(e._offCenterFrustum))};R.prototype.equalsEpsilon=function(e,t,n){return!a(e)||!(e instanceof R)?!1:(q(this),q(e),P.equalsEpsilon(this.fov,e.fov,t,n)&&P.equalsEpsilon(this.aspectRatio,e.aspectRatio,t,n)&&this._offCenterFrustum.equalsEpsilon(e._offCenterFrustum,t,n))};var L=R;var I=0,Fe=1;function U(e){O.typeOf.object("options",e),O.typeOf.object("options.frustum",e.frustum),O.typeOf.object("options.origin",e.origin),O.typeOf.object("options.orientation",e.orientation);let t=e.frustum,n=e.orientation,f=e.origin,p=m(e.vertexFormat,E.DEFAULT),d=m(e._drawNearPlane,!0),s,y;t instanceof L?(s=I,y=L.packedLength):t instanceof B&&(s=Fe,y=B.packedLength),this._frustumType=s,this._frustum=t.clone(),this._origin=r.clone(f),this._orientation=N.clone(n),this._drawNearPlane=d,this._vertexFormat=p,this._workerName="createFrustumGeometry",this.packedLength=2+y+r.packedLength+N.packedLength+E.packedLength}U.pack=function(e,t,n){O.typeOf.object("value",e),O.defined("array",t),n=m(n,0);let f=e._frustumType,p=e._frustum;return t[n++]=f,f===I?(L.pack(p,t,n),n+=L.packedLength):(B.pack(p,t,n),n+=B.packedLength),r.pack(e._origin,t,n),n+=r.packedLength,N.pack(e._orientation,t,n),n+=N.packedLength,E.pack(e._vertexFormat,t,n),n+=E.packedLength,t[n]=e._drawNearPlane?1:0,t};var xe=new L,ve=new B,ze=new N,Re=new r,Te=new E;U.unpack=function(e,t,n){O.defined("array",e),t=m(t,0);let f=e[t++],p;f===I?(p=L.unpack(e,t,xe),t+=L.packedLength):(p=B.unpack(e,t,ve),t+=B.packedLength);let d=r.unpack(e,t,Re);t+=r.packedLength;let s=N.unpack(e,t,ze);t+=N.packedLength;let y=E.unpack(e,t,Te);t+=E.packedLength;let i=e[t]===1;if(!a(n))return new U({frustum:p,origin:d,orientation:s,vertexFormat:y,_drawNearPlane:i});let c=f===n._frustumType?n._frustum:void 0;return n._frustum=p.clone(c),n._frustumType=f,n._origin=r.clone(d,n._origin),n._orientation=N.clone(s,n._orientation),n._vertexFormat=E.clone(y,n._vertexFormat),n._drawNearPlane=i,n};function W(e,t,n,f,p,d,s,y){let i=e/3*2;for(let c=0;c<4;++c)a(t)&&(t[e]=d.x,t[e+1]=d.y,t[e+2]=d.z),a(n)&&(n[e]=s.x,n[e+1]=s.y,n[e+2]=s.z),a(f)&&(f[e]=y.x,f[e+1]=y.y,f[e+2]=y.z),e+=3;p[i]=0,p[i+1]=0,p[i+2]=1,p[i+3]=0,p[i+4]=1,p[i+5]=1,p[i+6]=0,p[i+7]=1}var qe=new G,Me=new F,ne=new F,se=new r,le=new r,we=new r,ke=new r,Se=new r,De=new r,u=new Array(3),Z=new Array(4);Z[0]=new b(-1,-1,1,1);Z[1]=new b(1,-1,1,1);Z[2]=new b(1,1,1,1);Z[3]=new b(-1,1,1,1);var de=new Array(4);for(let e=0;e<4;++e)de[e]=new b;U._computeNearFarPlanes=function(e,t,n,f,p,d,s,y){let i=G.fromQuaternion(t,qe),c=m(d,se),_=m(s,le),g=m(y,we);c=G.getColumn(i,0,c),_=G.getColumn(i,1,_),g=G.getColumn(i,2,g),r.normalize(c,c),r.normalize(_,_),r.normalize(g,g),r.negate(c,c);let l=F.computeView(e,g,_,c,Me),o,h,M=f.projectionMatrix;if(n===I){let x=F.multiply(M,l,ne);h=F.inverse(x,ne)}else o=F.inverseTransformation(l,ne);a(h)?(u[0]=f.near,u[1]=f.far):(u[0]=0,u[1]=f.near,u[2]=f.far);for(let x=0;x<2;++x)for(let v=0;v<4;++v){let C=b.clone(Z[v],de[v]);if(a(h)){C=F.multiplyByVector(h,C,C);let D=1/C.w;r.multiplyByScalar(C,D,C),r.subtract(C,e,C),r.normalize(C,C);let Y=r.dot(g,C);r.multiplyByScalar(C,u[x]/Y,C),r.add(C,e,C)}else{let D=f.offCenterFrustum;a(D)&&(f=D);let Y=u[x],K=u[x+1];C.x=(C.x*(f.right-f.left)+f.left+f.right)*.5,C.y=(C.y*(f.top-f.bottom)+f.bottom+f.top)*.5,C.z=(C.z*(Y-K)-Y-K)*.5,C.w=1,F.multiplyByVector(o,C,C)}p[12*x+v*3]=C.x,p[12*x+v*3+1]=C.y,p[12*x+v*3+2]=C.z}};U.createGeometry=function(e){let t=e._frustumType,n=e._frustum,f=e._origin,p=e._orientation,d=e._drawNearPlane,s=e._vertexFormat,y=d?6:5,i=new Float64Array(3*4*6);U._computeNearFarPlanes(f,p,t,n,i);let c=3*4*2;i[c]=i[3*4],i[c+1]=i[3*4+1],i[c+2]=i[3*4+2],i[c+3]=i[0],i[c+4]=i[1],i[c+5]=i[2],i[c+6]=i[3*3],i[c+7]=i[3*3+1],i[c+8]=i[3*3+2],i[c+9]=i[3*7],i[c+10]=i[3*7+1],i[c+11]=i[3*7+2],c+=3*4,i[c]=i[3*5],i[c+1]=i[3*5+1],i[c+2]=i[3*5+2],i[c+3]=i[3],i[c+4]=i[4],i[c+5]=i[5],i[c+6]=i[0],i[c+7]=i[1],i[c+8]=i[2],i[c+9]=i[3*4],i[c+10]=i[3*4+1],i[c+11]=i[3*4+2],c+=3*4,i[c]=i[3],i[c+1]=i[4],i[c+2]=i[5],i[c+3]=i[3*5],i[c+4]=i[3*5+1],i[c+5]=i[3*5+2],i[c+6]=i[3*6],i[c+7]=i[3*6+1],i[c+8]=i[3*6+2],i[c+9]=i[3*2],i[c+10]=i[3*2+1],i[c+11]=i[3*2+2],c+=3*4,i[c]=i[3*2],i[c+1]=i[3*2+1],i[c+2]=i[3*2+2],i[c+3]=i[3*6],i[c+4]=i[3*6+1],i[c+5]=i[3*6+2],i[c+6]=i[3*7],i[c+7]=i[3*7+1],i[c+8]=i[3*7+2],i[c+9]=i[3*3],i[c+10]=i[3*3+1],i[c+11]=i[3*3+2],d||(i=i.subarray(3*4));let _=new ae({position:new J({componentDatatype:H.DOUBLE,componentsPerAttribute:3,values:i})});if(a(s.normal)||a(s.tangent)||a(s.bitangent)||a(s.st)){let l=a(s.normal)?new Float32Array(12*y):void 0,o=a(s.tangent)?new Float32Array(3*4*y):void 0,h=a(s.bitangent)?new Float32Array(3*4*y):void 0,M=a(s.st)?new Float32Array(2*4*y):void 0,x=se,v=le,C=we,D=r.negate(x,ke),Y=r.negate(v,Se),K=r.negate(C,De);c=0,d&&(W(c,l,o,h,M,K,x,v),c+=3*4),W(c,l,o,h,M,C,D,v),c+=3*4,W(c,l,o,h,M,D,K,v),c+=3*4,W(c,l,o,h,M,Y,K,D),c+=3*4,W(c,l,o,h,M,x,C,v),c+=3*4,W(c,l,o,h,M,v,C,D),a(l)&&(_.normal=new J({componentDatatype:H.FLOAT,componentsPerAttribute:3,values:l})),a(o)&&(_.tangent=new J({componentDatatype:H.FLOAT,componentsPerAttribute:3,values:o})),a(h)&&(_.bitangent=new J({componentDatatype:H.FLOAT,componentsPerAttribute:3,values:h})),a(M)&&(_.st=new J({componentDatatype:H.FLOAT,componentsPerAttribute:2,values:M}))}let g=new Uint16Array(6*y);for(let l=0;l<y;++l){let o=l*6,h=l*4;g[o]=h,g[o+1]=h+1,g[o+2]=h+2,g[o+3]=h,g[o+4]=h+2,g[o+5]=h+3}return new re({attributes:_,indices:g,primitiveType:oe.TRIANGLES,boundingSphere:ie.fromVertices(i)})};var Vt=U;export{B as a,L as b,Vt as c};
