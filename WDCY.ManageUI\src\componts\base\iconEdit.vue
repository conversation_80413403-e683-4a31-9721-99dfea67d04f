<template>
	<el-dialog draggable width="40%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="iconModel" label-width="80px">
			<el-row>
				<el-col :span="7">
					<el-form-item label="图标名称" prop="icoName">
						<el-input v-model="iconModel.icoName" placeholder="图标名称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="7">
					<el-form-item label="图标代号" prop="icoCode">
						<el-input v-model="iconModel.icoCode" placeholder="图标代号"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="7">
					<el-form-item label="状态" prop="status">
						<el-select style="width: 100%;" v-model="iconModel.status" clearable placeholder="状态">
							<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="7">
					<el-form-item label="图标分类" prop="icoCategory">
						<el-select style="width: 100%;" v-model="iconModel.icoCategory" clearable placeholder="图标分类"
							@change="icoCategoryChange">
							<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row>
				<el-col :span="10">
					<el-form-item label="导入图标" prop="icoUrl">
						<!-- <el-input v-model="iconModel.icoUrl" placeholder="图标url"></el-input> -->
						<el-upload :class="[iconModel.icoUrl ? '' : 'upload']" class="avatar-uploader"
							:action="imgServer + iconModel.icoUrl" :show-file-list="false" :http-request="loadingImg">
							<img v-if="iconModel.icoUrl" :src="imgServer + iconModel.icoUrl" @mouseenter="mask = true"
								@mouseleave="mask = false" class="avatar" />
							<el-icon v-else class="avatar-uploader-icon">
								<Plus />
							</el-icon>
							<div @mouseenter="mask = true" @mouseleave="mask = false" v-if="mask"
								style="background:rgba(0, 0, 0, .2);width: 178px; height: 178px;position: absolute;">
							</div>
						</el-upload>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<div style="display: flex;align-items: center;">
						<span style="margin-right: 16px;">预览图标底色</span>
						<el-color-picker v-model="icoColor" />
					</div>
				</el-col>
			</el-row>

			<el-row>
				<el-col>
					<el-form-item label="描述" prop="remark">
						<el-input type="textarea" :rows="2" v-model="iconModel.note" placeholder="描述"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交
			</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { editIcon, addIcon } from "@/api/base/icon"
import { fileUpload } from "@/api/admin/file";
import mitt from "@/utils/mitt";
export default {
	props: ['statusList', 'tagList', 'iconCateGoryList', 'typeList'],
	data() {
		return {
			loading: false,
			iconModel: {},
			dialog: {},
			personList: [],
			personId: "",
			icoColor: "",
			imgServer: import.meta.env.VITE_BASE_API,
			rules: {
				status: [
					{
						required: true,
						message: '请选择状态',
						trigger: "change"
					}
				],
				icoUrl: [
					{
						required: true,
						message: '请选择导入图标',
						trigger: "change"
					}
				],
				icoCategory: [
					{
						required: true,
						message: '请选择图标分类',
						trigger: "change"
					}
				],
				icoName: [
					{
						required: true,
						message: '请输入图标名称',
						trigger: "blur"
					}
				],
				sysName: [
					{
						required: true,
						message: '请输入系统名称',
						trigger: "blur"
					}
				],
				icoCode: [
					{
						required: true,
						message: '请输入图标代号',
						trigger: "blur"
					}
				],
			},
		}
	},
	methods: {
		icoCategoryChange() {
			this.iconModel.icoUrl = null
		},
		// 上传图片
		loadingImg(files) {
			if (!this.iconModel.icoCategory) {
				this.$message.error("请选择图标分类")
				return
			}
			let funcName = this.iconModel.icoCategory.split("_").join("");
			let form = new FormData();
			form.append("file", files.file);
			form.append("modulesName", 'icon');
			form.append("functionName", funcName);
			//form.append("communityId", localStorage.getItem('communityId'));
			fileUpload(form).then((res) => {
				this.iconModel.icoUrl = res.data.result.url;
				if (res.data.code == 0) {
					this.$message.success("上传成功");
				}
			});
		},
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.iconModel.id == 0) {
						addIcon(this.iconModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						// this.iconModel.personId = this.iconModel.personId.personId
						editIcon(this.iconModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		}
	},
	mounted() {
		this.$nextTick(function () {
			mitt.on('openIconEdit', (vehicle) => {
				this.icoColor = ""
				this.iconModel = vehicle
				this.dialog.show = true
				this.dialog.title = "修改图标"
				this.personList = [{ id: this.iconModel.personId, name: this.iconModel.personName }]
			})
			mitt.on('openIconAdd', () => {
				this.icoColor = ""
				this.iconModel = {
					id: 0,
					status: 1
				}
				this.dialog.show = true
				this.dialog.title = "添加图标"
			})
			mitt.on("carNum", (data) => {
				console.log(data, 'yesyes');
			})
		})
	},
	watch: {
		icoColor(newVal) {
			const icoPicNode = document.getElementsByClassName("avatar")
			if (icoPicNode[0]) {
				icoPicNode[0].style.backgroundColor = newVal
			}
		}
	}
}
</script>
<style scoped>
.avatar-uploader .avatar {
	width: 150px;
	height: 150px;
	display: block;
	border: #ddd 1px solid;
}

.avatar:hover {
	background-color: #eee;
}

.upload {
	border: 1px dashed #ddd;
	border-radius: 2px;
}

div /deep/.avatar-uploader .el-upload {
	/* border: 1px dashed #ddd; */
	border-radius: 2px;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
	border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 150px;
	height: 150px;
	text-align: center;
}
</style>
