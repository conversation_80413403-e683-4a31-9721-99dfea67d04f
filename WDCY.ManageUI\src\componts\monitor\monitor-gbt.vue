<template>
  <div
    class="monitor-box"
    :style="{ width: width + 'px', height: height + 'px' }"
  >
    <div
      :style="{ width: width + 'px', height: height + 'px' }"
      id="gbtcontainer"
      ref="gbtcontainer"
    ></div>

    <div v-if="playMode == 1">
      <div
        class="time-view"
        :style="{ width: width + 'px' }"
        id="progress-bar"
        v-show="isPlayerBackSuccess"
        @click="clickOnProgress($event)"
      >
        <img
          class="time-ruler"
          :style="{ width: width + 'px' }"
          src="@/assets/img/time-ruler.png"
        />
        <div class="progress-bar" :style="{ width: width + 'px' }"></div>
        <div class="pointer" id="pointer" ref="pointer"></div>
      </div>
    </div>
  </div>
</template>
  
  <script>
import dateUtil from "@/utils/dateUtil.js";

// import $ from "@/utils/jquery.min.js";
import { queryMonitorByDevNo } from "@/api/device/device";

export default {
  computed: {
    mode: function () {
      return 1;
    },
  },

  props: {
    communityId: {
      type: String,
      default: "",
    },

    width: {
      type: Number,
      default: 850,
    },
    height: {
      type: Number,
      default: 550,
    },

    playMode: {
      type: Number,
      default: 0,
    },

    videoMode: {
      type: String,
      default: "GBT",
    },

    devId: {
      type: String,
      default: 0,
    },

    devNo: {
      type: String,
      default: "",
    },

    backStartTime: {
      type: String,
      default: "",
    },
    backEndTime: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      jessibuca: null, // gbt播放器对象
      performance: "",
      videoSrc: null,
      videoBackSrc: null,
      urlType: "flv",
      //定时器，更新回放进度条
      datatimer: null,
      playback_date: "", // 录像回放时间

      cameraIndexCode: "",
      isPlayerBackSuccess: false,
      rate: 1,
      currentTime: "",
      wasm: false,
      vc: "ff",
      playing: false,
      quieting: true,
      loaded: false, // mute
      showOperateBtns: false,
      showBandwidth: false,
      err: "",
      speed: 0,
      performance: "",
      volume: 1,
      rotate: 0,
      useWCS: false,
      useMSE: true,
      useOffscreen: false,
      recording: false,
      recordType: "mp4",
      scale: 1,
    };
  },

  mounted() {},
  created() {},

  methods: {
    initMonitorGBT() {
      // console.log("initMonitorGBT");

      var that = this;
      // console.log("devId", this.devId);
      setTimeout(() => {
        var options = {};

        that.jessibuca = new window.Jessibuca(
          Object.assign(
            {
              container: this.$refs.gbtcontainer,
              videoBuffer: 0.2, // 缓存时长
              isResize: false,
              useWCS: this.useWCS,
              useMSE: this.useMSE,
              text: "",
              // background: "bg.jpg",
              loadingText: "加载中...",
              // hasAudio:false,
              debug: true,
              supportDblclickFullscreen: true,
              showBandwidth: this.showBandwidth, // 显示网速
              operateBtns: {
                fullscreen: this.showOperateBtns,
                screenshot: this.showOperateBtns,
                play: this.showOperateBtns,
                audio: this.showOperateBtns,
                scale: 1,
              },
              vod: this.vod,
              forceNoOffscreen: !this.useOffscreen,
              isNotMute: true,
              timeout: 10,
            },
            options
          )
        );

        that.jessibuca.on("load", function () {
          // console.log("on load");
        });

        that.jessibuca.on("log", function (msg) {
          // console.log("on log", msg);
        });
        that.jessibuca.on("record", function (msg) {
          // console.log("on record:", msg);
        });
        that.jessibuca.on("pause", function () {
          // console.log("on pause");
          that.playing = false;
        });
        that.jessibuca.on("play", function () {
          // console.log("on play");
          that.playing = true;
        });
        that.jessibuca.on("fullscreen", function (msg) {
          // console.log("on fullscreen", msg);
        });

        that.jessibuca.on("mute", function (msg) {
          // console.log("on mute", msg);
          that.quieting = msg;
        });

        that.jessibuca.on("mute", function (msg) {
          // console.log("on mute2", msg);
        });

        that.jessibuca.on("audioInfo", function (msg) {
          // console.log("audioInfo", msg);
        });

        that.jessibuca.on("videoInfo", function (info) {
          // console.log("videoInfo", info);
        });

        that.jessibuca.on("error", function (error) {
          // console.log("error", error);
        });

        that.jessibuca.on("timeout", function () {
          // console.log("timeout");
        });

        that.jessibuca.on("start", function () {
          // console.log("frame start");
        });

        that.jessibuca.on("performance", function (performance) {
          var show = "卡顿";
          if (performance === 2) {
            show = "非常流畅";
          } else if (performance === 1) {
            show = "流畅";
          }
          that.performance = show;
        });
        that.jessibuca.on("buffer", function (buffer) {
          // console.log("buffer", buffer);
        });

        that.jessibuca.on("stats", function (stats) {
          // console.log("stats", stats);
        });

        that.jessibuca.on("kBps", function (kBps) {
          // console.log("kBps", kBps);
        });

        that.jessibuca.on("play", () => {
          that.playing = true;
          that.loaded = true;
          that.quieting = that.jessibuca.isMute();
        });

        that.jessibuca.on("recordingTimestamp", (ts) => {
          // console.log("recordingTimestamp", ts);
        });
      }, 500);
      if (that.playMode == 0) {
        that.getVideoInfo();
      } else {
        console.log("that.backStartTime", that.backStartTime);
        console.log("that.backEndTime", that.backEndTime);

        var startTime =
          that.backStartTime != ""
            ? that.backStartTime
            : dateUtil.getCurrentTime() + " 00:00:00";

        var endTime =
          that.backEndTime != ""
            ? that.backEndTime
            : dateUtil.getCurrentTime() + " 23:59:59";

        that.playback_date =
          that.backStartTime != ""
            ? that.backStartTime.split(" ")[0]
            : dateUtil.getCurrentTime();

        that.getRecordByTime(startTime, endTime);
      }
    },
    getVideoInfo() {
      var that = this;
      var videoSrc = "";

      if (!this.devId) return;
      const param = {
        id: this.devId,
        urlType: "flv",
        communityId: this.communityId,
        playType: "previewURLs",
      };

      queryMonitorByDevNo(param)
        .then((res) => {
          if (res !== null && res.data.code === 0) {
            if (res.data.result !== null && res.data.result.url != null) {
              var result = res.data.result;

              videoSrc = result.url; // 地址
              if (videoSrc) {
                setTimeout(() => {
                  that.play(videoSrc);
                }, 500);
              } else {
                that.$message({
                  type: "error",
                  message: "无视频播放资源",
                });
              }
            } else {
              this.$message({
                type: "error",
                message: "获取数据失败",
              });
            }
          } else {
            that.$message({
              type: "error",
              message: "获取数据失败",
            });
          }
        })
        .catch((e) => {
          this.$message({
            type: "error",
            message: "获取数据失败",
          });
        });
      // http
      //   .queryMonitorInfo(param)
      //   .then((res) => {
      //     if (res !== null && res.code === 0) {
      //       if (res.result !== null) {
      //         videoSrc = res.result.url;

      //         if (videoSrc) {
      //           setTimeout(() => {
      //             that.play(videoSrc);
      //           }, 500);
      //         } else {
      //           that.$message({
      //             type: "error",
      //             message: "无视频播放资源",
      //           });
      //         }
      //       } else {
      //         videoSrc = "";
      //         this.$message({
      //           type: "warning",
      //           message: "无视频播放资源",
      //         });
      //       }
      //     } else {
      //       this.$message({
      //         type: "error",
      //         message: "暂无回放列表",
      //       });
      //     }
      //   })
      //   .catch((e) => {
      //     console.log(e);

      //     this.$message({
      //       type: "error",
      //       message: "获取数据失败",
      //     });
      //   });
    },

    //清空定时器 用于回放底部时间条更新
    clearTimer() {
      if (this.datatimer) {
        clearInterval(this.datatimer);
        this.datatimer = null;
      }
    },

    // 录像回放点击进度条
    clickOnProgress: function (e) {
      //选中的播放时间
      var base = new Date(this.playback_date + " 00:00:00").getTime() / 1000;

      //时间div
      var rect = document
        .getElementById("progress-bar")
        .getBoundingClientRect();
      //时间div起点X位置
      var progressBarLeftX = rect.x;
      // 鼠标点击的x位置
      var mouseX = e.clientX + document.body.scrollLeft;
      // 计算点击的相对位置
      var objX = mouseX - progressBarLeftX;
      const start = parseInt((base + (objX / rect.width) * 86400) * 1000);
      const startTime = dateUtil.formatTimeSeconds(new Date(start));

      if (dateUtil.getTwoTimeSeconds(startTime) < 0) {
        this.$message({
          type: "error",
          message: "超出当前时间",
        });
        return;
      }
      this.getRecordByTime(startTime, this.playback_date + " 23:59:59");
    },

    // 录像回放播放回调
    playbackUpdate(currentTime) {
      var time = new Date(currentTime).getTime() / 1000;

      const base = new Date(this.playback_date + " 00:00:00").getTime() / 1000;

      var percentage = (time - base) / 86400;

      // $("#pointer").css({ left: this.width * percentage + "px" });
      this.$refs.pointerWs.style.left = `${this.width * percentage}px`;
    },

    //选择回放时间
    getRecordByTime(startTime, endTime) {
      var that = this;
      this.clearTimer();
      const param = {
        beginTime: startTime,
        communityId: this.communityId,
        endTime: endTime,
        id: this.devId,
        playType: "playbackURLs",
        urlType: "flv",
      };

      queryMonitorByDevNo(param)
        .then((res) => {
          if (res !== null && res.code === 0) {
            if (res.data.result !== null && res.data.result.url != null) {
              var result = res.data.result;

              that.videoBackSrc = result.url; // 回放地址flv

              that.record_list = result.list;
              that.isPlayerBackSuccess = true;
              that.play(this.videoBackSrc);

              that.playbackUpdate(startTime);
            } else {
              that.videoBackSrc = "";
              that.$message({
                type: "error",
                message: "无回放资源",
              });
            }
          } else {
            this.$message({
              type: "error",
              message: "获取数据失败",
            });
          }
        })
        .catch((e) => {
          this.$message({
            type: "error",
            message: "获取数据失败",
          });
        });

      // http.queryMonitorInfo(param).then((res) => {
      //   if (res !== null && res.code === 0) {
      //     if (res.result !== null && res.result.url != null) {
      //       var result = res.result;

      //       that.videoBackSrc = result.url; // 回放地址flv

      //       that.record_list = result.list;
      //       that.isPlayerBackSuccess = true;
      //       that.play(this.videoBackSrc);

      //       that.playbackUpdate(startTime);
      //     } else {
      //       that.videoBackSrc = "";
      //       that.$message({
      //         type: "error",
      //         message: "无回放资源",
      //       });
      //     }
      //   } else {
      //     that.$message({
      //       type: "error",
      //       message: "获取数据失败",
      //     });
      //   }
      // });
    },

    play(url) {
      this.jessibuca.play(url);
      this.jessibuca.setScaleMode(this.scale);
    },

    mute() {
      this.jessibuca.mute();
    },
    cancelMute() {
      this.jessibuca.cancelMute();
    },

    pause() {
      this.jessibuca.pause();
      this.playing = false;
      this.err = "";
      this.performance = "";
    },
    volumeChange() {
      this.jessibuca.setVolume(this.volume);
    },
    rotateChange() {
      this.jessibuca.setRotate(this.rotate);
    },

    fullscreen() {
      this.jessibuca.setFullscreen(true);
    },

    clearView() {
      this.jessibuca.clearView();
    },

    startRecord() {
      const time = new Date().getTime();
      this.recording = true;
      var fileName =
        this.monitorDeviceData.devName + dateUtil.getCurrentFullTime();

      this.jessibuca.startRecord(fileName, this.recordType);
    },

    stopAndSaveRecord() {
      this.recording = false;
      this.jessibuca.stopRecordAndSave();
    },

    screenShot() {
      this.jessibuca.screenshot();
    },

    restartPlay(type) {
      if (type === "mse") {
        this.useWCS = false;
        this.useOffscreen = false;
      } else if (type === "wcs") {
        this.useMSE = false;
      } else if (type === "offscreen") {
        this.useMSE = false;
      }

      this.destroyMonitorGBT();
      setTimeout(() => {
        this.play();
      }, 100);
    },

    changeBuffer() {
      this.jessibuca.setBufferTime(Number(this.$refs.buffer.value));
    },

    scaleChange() {
      this.jessibuca.setScaleMode(this.scale);
    },

    destroyMonitorGBT() {
      if (this.jessibuca) {
        this.jessibuca.destroy();
      }
      this.clearTimer();
      this.jessibuca = null;
      // this.create();
      this.playing = false;
      this.loaded = false;
      this.performance = "";

      this.playback_date = "";
    },
  },

  unmounted() {
    this.destroyMonitorGBT();
  },
};
</script>
  
  <style scoped lang="scss">
.monitor-box {
  background: #000;
}

.time-view {
  height: 45px;
  cursor: pointer;

  bottom: 0;
  position: relative;

  .time-ruler {
    height: 45px;
  }

  .progress-bar {
    height: 15px;
    background: #44817b;
    position: absolute;
    bottom: -5px;
    overflow: hidden;
    left: 0;
  }

  .pointer {
    width: 2px;
    height: 15px;
    background: red;
    position: absolute;
    bottom: -5px;
    left: 0;
  }
}
</style>
  