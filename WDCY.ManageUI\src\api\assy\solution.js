import request from '@/utils/request'

// 查询方案列表
export function listSolution(query) {
  return request({
    url: '/mapi/v3_8_0/assy/solution/list',
    method: 'get',
    params: query
  })
}

// 查询方案详细
export function getSolution(id) {
  return request({
    url: '/sapi/v3_8_0/assy/solution/' + id,
    method: 'get'
  })
}

// 新增方案
export function addSolution(data) {
  return request({
    url: '/sapi/v3_8_0/assy/solution',
    method: 'post',
    data: data
  })
}

// 修改方案
export function updateSolution(data) {
  return request({
    url: '/sapi/v3_8_0/assy/solution',
    method: 'put',
    data: data
  })
}

// 删除方案
export function delSolution(id) {
  return request({
    url: '/sapi/v3_8_0/assy/solution/' + id,
    method: 'delete'
  })
}
