import request from '@/utils/request'
// 健康检查 分页
export const listHealthExamina = (data) =>
	request({
		url: '/func/page',
		method: 'get',
		params: data
	})
// 健康检查 详情
export const getHealthExamina = (id) =>
	request({
		url: '/func/'+id,
		method: 'get'
	})
	//健康检查 添加
export const addHealthExamina = (data) =>
	request({
		url: '/func/insert',
		method: 'post',
		data: data
	})
	// 健康检查 编辑
export const editHealthExamina = (data) =>
	request({
		url: '/func',
		method: 'put',
		data: data
	})
	// 健康检查 删除
export const deleteHealthExamina = (id) =>
	request({
		url: '/func/'+id,
		method: 'delete'
	})

// 健康检查全部
export const initScanAdd = (id) =>
	request({
		url: '/func/init',
		method: 'get',
		params: {
			serviceId: id || ""
		}
	})
// 健康检查单个
export const toExaminaOne = (id) =>
	request({
		url: '/func/check-interface/'+id,
		method: 'get'
	})

	//健康检查 添加
	export const getProductionAssert = (data) =>
	request({
		url: '/func/production-assert',
		method: 'post',
		data: data
	})
