<template>
  <el-dialog
    draggable
    width="50%"
    v-loading="loading"
    destroy-on-close
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form
      :rules="rules"
      ref="form"
      :model="userGroupModel"
      label-width="120px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="组名" prop="groupName">
            <el-input
              v-model="userGroupModel.groupName"
              placeholder="组名"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <!-- <el-input onkeyup = "value=value.replace(/[^\d]/g,'')" v-model="userGroupModel.sort" placeholder="排序"></el-input> -->
            <el-input-number
              v-model="userGroupModel.sort"
              :min="0"
              :max="1000"
              :tep="1"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="级别" prop="nodeType">
            <el-select
              style="width: 100%"
              v-model="userGroupModel.nodeType"
              clearable
              placeholder="级别"
            >
              <el-option
                v-for="item in nodeList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="parseInt(item.nameEn)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select
              style="width: 100%"
              v-model="userGroupModel.status"
              clearable
              placeholder="状态"
            >
              <el-option
                v-for="item in statusList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="parseInt(item.nameEn)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="10">
          <el-form-item label="经纬度" prop="lng">
            <el-input
              style="width: 45%"
              v-model="userGroupModel.lng"
              placeholder="经度"
            ></el-input>
            &nbsp;-&nbsp;<el-input
              style="width: 45%"
              v-model="userGroupModel.lat"
              placeholder="纬度"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-form-item label-width="0">
            <el-button
              type="text"
              style="font-size: 30px; padding: 0"
              @click="selectPoint"
            >
              <el-icon :size="30">
                <location-filled></location-filled>
              </el-icon>
            </el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="行政区" prop="personId">
            <el-select
              style="width: 100%"
              v-model="userGroupModel.districtCodeId"
              filterable
              remote
              reserve-keyword
              placeholder="输入关键字搜索"
              :remote-method="remoteMethod"
              :loading="loading"
            >
              <el-option
                v-for="item in districtList"
                :key="item.id"
                :label="item.name + '(' + item.code + ')'"
                :value="item.id"
              >
                <span>{{ item.name }}</span>
                <span
                  v-if="item.code"
                  style="color: var(--el-text-color-secondary); font-size: 13px"
                  >({{ item.code }})</span
                >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="userGroupModel.parentId != 0">
        <el-col :span="12">
          <el-form-item label="父级" prop="parentId">
            <!-- <el-cascader style="width: 100%;" :props="{ checkStrictly: true }" :options="buildingNodeList"
							@change="handleChange" clearable placeholder="选择父级" /> -->
            <el-tree-select
              style="width: 100%"
              v-model="userGroupModel.parentId"
              :data="newuserGroupList"
              check-strictly
              :render-after-expand="false"
              placeholder="选择父级"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :rows="2"
              v-model="userGroupModel.remark"
              placeholder="备注内容"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        >提 交
      </el-button>
    </el-row>
  </el-dialog>
</template>


<script>
import { LocationFilled } from "@element-plus/icons-vue";
import {
  editUserGroup,
  addUserGroup,
  getUserGroup,
} from "@/api/admin/userGroup";
import { queryDistrict } from "@/api/base/person";
import mitt from "@/utils/mitt";
import { openMap, openMarsMap } from "@/utils/myUtils";

export default {
  emits: ["search", "openMap"],
  props: ["statusList", "nodeTypeList", "userGroupList"],
  data() {
    return {
      loading: false,
      userGroupModel: {},
      dialog: { show: false },
      nodeList: [],
      districtList: [],
      rules: {
        groupName: [
          {
            required: true,
            message: "请输入组名",
            trigger: "blur",
          },
        ],
        nodeType: [
          {
            required: true,
            message: "请选择级别",
            trigger: "change",
          },
        ],
      },
      newuserGroupList: [],
    };
  },
  methods: {
    remoteMethod(val) {
      // 编辑时候调用获取输入的姓，更改所属人员列表
      queryDistrict({ keyWord: val }).then((res) => {
        this.districtList = res.data.result;
      });
    },
    selectPoint() {
      var center;
      var rotationSet = { x: 0, y: 0, z: 0 };
      var scaleSet = 1;
      var showBaseMap = false;
      var position;
      var modeUrl;

      center = {
        lng: this.userGroupModel.lng,
        lat: this.userGroupModel.lat,
        alt: 500,
        heading: 0,
        pitch: -90,
      };

      var data = {
        enabled3d: false,
        edit: true,
        point: [center.lng, center.lat, 0],
        position: position,
        center: center,
        modeUrl: modeUrl,
        title: this.userGroupModel.groupName,
        rotationSet,
        scaleSet,
        showBaseMap: showBaseMap,
      };
      console.log(data);
      openMarsMap(mitt, data);
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.userGroupModel.id == 0) {
            addUserGroup(this.userGroupModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          } else {
            editUserGroup(this.userGroupModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          }
        }
      });
    },
  },
  created() {
    mitt.off("openMarsMap");
  },
  mounted() {
    mitt.on("openUserGroupEdit", (userGroup) => {
      let groupStr = JSON.stringify(this.userGroupList)
        .replaceAll("groupName", "label")
        .replaceAll("id", "value");
      this.newuserGroupList = JSON.parse(groupStr);
      this.userGroupModel = userGroup;
      if (userGroup.parentId != 0) {
        getUserGroup(userGroup.parentId).then((res) => {
          let list = [];
          for (let item of this.nodeTypeList) {
            if (Number(item.nameEn) > res.data.result.nodeType) {
              list.push(item);
            }
          }
          this.nodeList = list;
        });
      } else {
        this.nodeList = this.nodeTypeList;
      }
      this.districtList = [
        {
          code: this.userGroupModel.code,
          name: this.userGroupModel.name,
          id: this.userGroupModel.districtCodeId,
        },
      ];
      if (!this.userGroupModel.code) {
        this.districtList = [];
      }
      this.dialog.show = true;
      this.dialog.title = "修改信息";
    });
    mitt.on("openUserGroupAdd", (userGroup) => {
      let groupStr = JSON.stringify(this.userGroupList)
        .replaceAll("groupName", "label")
        .replaceAll("id", "value");
      this.newuserGroupList = JSON.parse(groupStr);
      console.log(userGroup);
      this.userGroupModel = {
        id: 0,
        parentId: userGroup.id,
        sort: userGroup.children
          ? userGroup.children.length
          : this.userGroupList.length,
      };
      console.log(userGroup);
      console.log(this.nodeTypeList);
      if (userGroup.nodeType != -1) {
        let list = [];
        for (let item of this.nodeTypeList) {
          console.log(Number(item.nameEn));
          console.log(Number(userGroup.nodeType));
          if (Number(item.nameEn) > userGroup.nodeType) {
            list.push(item);
          }
        }
        this.nodeList = list;
      } else {
        this.nodeList = this.nodeTypeList;
      }
      if (!this.userGroupModel.code) {
        this.districtList = [];
      }
      this.dialog.show = true;
      this.dialog.title = "添加组";
    });
    mitt.on("setPointValue", (e) => {
      this.userGroupModel.lng = e[0];
      this.userGroupModel.lat = e[1];
    });
  },
};
</script>
