<template>
	<el-dialog draggable width="25%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="initScanModel" label-width="100px">
			<el-row>
				<el-col :span="21">
					<el-form-item label="功能分类" prop="funcCategory">
                        <el-select style="width: 100%" v-model="initScanModel.funcCategory" clearable placeholder="功能分类">
							<el-option v-for="item in categoryList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交
			</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { initScanAdd } from "@/api/healthCheck/healthExamination"
import mitt from "@/utils/mitt";
export default {
	props: ['statusList', 'categoryList', 'hasRightList', 'methodList'],
	data() {
		return {
			loading: false,
			initScanModel: {},
			dialog: {},
			imgServer: import.meta.env.VITE_BASE_API,
            loading: [],
		}
	},
	methods: {
        // 打开加载层
        openLoading() {
          this.loading = this.$loading({
              lock: true,
              text: "拼命扫描中...",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)"
          });
        },
		onSubmit() {
			// this.dialog.show = false
            this.openLoading();
			this.$refs['form'].validate((valid) => {
				if (valid) {
					// if (this.initScanModel.funcCategory) {
						initScanAdd(this.initScanModel.funcCategory)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
                                this.loading.close()
							})
					// }
				}
			})
		},
        init(){
        }
	},
	mounted() {
		this.$nextTick(function () {
			mitt.on('openInitScan', () => {
                this.initScanModel = {}
				this.dialog.show = true
				this.dialog.title = "初始扫描"
			})
		})
	},
    created(){
        this.init()
    }
}
</script>
<style scoped>

.editor {
  width: 805px;
}
.avatar-uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}
</style>
