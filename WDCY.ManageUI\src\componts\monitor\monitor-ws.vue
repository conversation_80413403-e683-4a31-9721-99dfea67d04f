<template>
  <div
    class="monitor-box"
    :style="{ width: width + 'px', height: height + 'px' }"
  >
    <div
      :style="{ width: width + 'px', height: height + 'px' }"
      id="monitorPlayerWs"
    ></div>

    <div v-if="playMode == 1">
      <div
        class="time-view"
        :style="{ width: width + 'px' }"
        id="progress-bar"
        v-if="isPlayerBackSuccess"
        @click="clickOnProgress($event)"
      >
        <img
          class="time-ruler"
          :style="{ width: width + 'px' }"
          src="@/assets/img/time-ruler.png"
        />
        <div class="progress-bar" :style="{ width: width + 'px' }"></div>
        <div class="pointer" id="pointerWs" ref="pointerWs"></div>
      </div>
    </div>
  </div>
</template>

<script>
import dateUtil from "@/utils/dateUtil.js";
// import $ from "@/utils/jquery.min.js";

import { queryMonitorByDevNo } from "@/api/device/device";

export default {
  computed: {
    mode: function () {
      return 1;
    },
  },

  props: {
    communityId: {
      type: String,
      default: "",
    },

    width: {
      type: Number,
      default: 850,
    },
    height: {
      type: Number,
      default: 550,
    },

    playMode: {
      type: Number,
      default: 0,
    },

    videoMode: {
      type: String,
      default: "WS",
    },

    devId: {
      type: String,
      default: 0,
    },

    devNo: {
      type: String,
      default: "",
    },

    backStartTime: {
      type: String,
      default: "",
    },
    backEndTime: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      videoPlayerWsMonitor: null, // 监视器播放对象
      isPlayerBackSuccess: false,
      currentTime: "", // 一段录像开始的时间
      videoSrc: "",
      videoBackSrc: null,
      urlType: "ws",
      //定时器，更新回放进度条
      datatimer: null,
      playback_date: "", // 录像回放时间
    };
  },

  mounted() {},
  created() {},

  methods: {
    initMonitorWS() {
      // console.log("initMonitorWS");
      if (this.playMode == 0) {
        this.getVideoInfo();
      } else {
        this.playback_date = this.backStartTime.split(" ")[0];

        this.getRecordByTime(this.backStartTime, this.backEndTime);
      }
    },

    getVideoInfo() {
      var that = this;
      if (!this.devId) return;
      const param = {
        id: this.devId,
        urlType: this.urlType,
        communityId: this.communityId,
        beginTime: this.backStartTime,
        endTime: this.backEndTime,
        playType: this.playMode == 0 ? "previewURLs" : "playbackURLs",
      };

      queryMonitorByDevNo(param)
        .then((res) => {
          if (res !== null && res.data.code === 0) {
            if (res.data.result !== null && res.data.result.url != null) {
              this.videoSrc = res.data.result.url;
              this.previewPlay();
            } else {
              this.videoSrc = null;
              this.$warning({
                type: "error",
                message: "无视频播放资源",
              });
            }
          } else {
            this.$message({
              type: "error",
              message: "获取数据失败",
            });
          }
        })
        .catch(function (e) {
          that.$message({
            type: "error",
            message: e.data && e.data.msg ? e.data.msg : "获取数据失败",
          });
        });
    },

    // 监控预览请求获取监控地址
    previewPlay: function () {
      // console.log("监控预览请求地址并播放");
      var that = this;

      try {
        if (!this.videoPlayerWsMonitor)
          this.videoPlayerWsMonitor = new window.JSPlugin({
            szId: "monitorPlayerWs",
            szBasePath: "/public/lib/h5player",
            iMaxSplit: 1,
            iCurrentSplit: 2,
            openDebug: true,
            oStyle: {
              borderSelect: "#000000",
            },
          });

        var index = 1;
        let { mode, config = {} } = this;

        this.videoPlayerWsMonitor
          .JS_Play(
            that.videoSrc,
            {
              playURL: that.videoSrc,
              mode: mode, //解码类型：0=普通模式; 1=高级模式 默认为0,h5高级模式才能播放
              ...config,
            },
            index
          )
          .then(
            () => {
              console.log("realplay success");
            },

            (e) => {
              console.log(e);
            }
          )
          .catch((e) => {
            console.log(e);
          });
      } catch (e) {
        console.log(e);
      }
    },

    destroyMonitorWS() {
      console.log("destroyMonitorWS");
      if (this.videoPlayerWsMonitor)
        this.videoPlayerWsMonitor.JS_StopRealPlayAll().then(
          () => {
            // console.log("stopAllPlay success");
          },
          (e) => {
            console.log(e);
          }
        );

      this.clearTimer();
      this.videoPlayerWsMonitor = null;
      this.isPlayerBackSuccess = false;
      this.currentTime = ""; // 一段录像开始的时间
      this.videoSrc = "";
      this.videoBackSrc = null;
      this.urlType = "ws";
      //定时器，更新回放进度条
      this.datatimer = null;
      this.playback_date = ""; // 录像回放时间
    },

    //重置定时器 用于回放底部时间条更新
    resetTimer() {
      var that = this;
      //将定时器名字赋值到变量中
      this.datatimer = setInterval(() => {
        let player = that.videoPlayerWsMonitor,
          index = player.currentWindowIndex;

        player.JS_GetOSDTime(index).then(function (time) {
          //  // console.log("osdTime:", new Date(time));

          //  // console.log("time:", util.formatTime(new Date(time)));

          that.currentTime = time;

          that.playbackUpdate(that.currentTime);
        });
      }, 1000);
    },
    //清空定时器 用于回放底部时间条更新
    clearTimer() {
      if (this.datatimer) {
        clearInterval(this.datatimer);
        this.datatimer = null;
      }
    },

    // 录像回放点击进度条
    clickOnProgress: function (e) {
      //选中的播放时间
      var base = new Date(this.playback_date + " 00:00:00").getTime() / 1000;

      //时间div
      var rect = document
        .getElementById("progress-bar")
        .getBoundingClientRect();
      //时间div起点X位置
      var progressBarLeftX = rect.x;
      // 鼠标点击的x位置
      var mouseX = e.clientX + document.body.scrollLeft;
      // 计算点击的相对位置
      var objX = mouseX - progressBarLeftX;
      const start = parseInt((base + (objX / rect.width) * 86400) * 1000);
      const startTime = dateUtil.formatTimeSeconds(new Date(start));

      if (dateUtil.getTwoTimeSeconds(startTime) < 0) {
        this.$message({
          type: "error",
          message: "超出当前时间",
        });
        return;
      }
      this.getRecordByTime(startTime, this.playback_date + " 23:59:59");
    },

    // 录像回放播放回调
    playbackUpdate(currentTime) {
      var time = new Date(currentTime).getTime() / 1000;

      const base = new Date(this.playback_date + " 00:00:00").getTime() / 1000;

      var percentage = (time - base) / 86400;

      // $("#pointerWs").css({ left: this.width * percentage + "px" });
      this.$refs.pointerWs.style.left = `${this.width * percentage}px`;
    },

    //选择回放时间
    getRecordByTime(startTime, endTime) {
      var that = this;
      this.clearTimer();

      const param = {
        id: this.devId,
        urlType: this.urlType,
        communityId: this.communityId,
        beginTime: startTime,
        endTime: endTime,
        playType: this.playMode == 0 ? "previewURLs" : "playbackURLs",
      };

      queryMonitorByDevNo(param)
        .then((res) => {
          if (res !== null && res.code === 0) {
            if (res.result !== null && res.result.url != null) {
              var result = res.result;

              that.videoBackSrc = result.url; // 回放地址flv
              if (that.videoBackSrc) {
                that.isPlayerBackSuccess = true;

                that.playbackStart(startTime, endTime);
              } else {
                that.$message({
                  type: "error",
                  message: "暂无回放列表",
                });
              }
            } else {
              that.videoBackSrc = "";
              that.$message({
                type: "error",
                message: "暂无回放列表",
              });
            }
          } else {
            that.$message({
              type: "error",
              message: res.msg ? res.msg : "获取数据失败",
            });
          }
        })
        .catch(function (e) {
          that.$message({
            type: "error",
            message: e.data && e.data.msg ? e.data.msg : "获取数据失败",
          });
        });
    },

    /* 回放 */
    playbackStart(startTime, endTime) {
      this.rate = 1;
      // console.log("playURL", playURL);

      var playURL = this.videoBackSrc;

      if (!this.videoPlayerWsMonitor)
        this.videoPlayerWsMonitor = new window.JSPlugin({
          szId: "monitorPlayerWs",
          szBasePath: "/",
          iMaxSplit: 1,
          iCurrentSplit: 2,
          openDebug: true,
          oStyle: {
            borderSelect: "#000000",
          },
        });

      const { mode } = this;
      var index = 1;

      // console.log("playbackStart");
      this.videoPlayerWsMonitor
        .JS_Play(
          playURL,
          { playURL, mode },
          index,
          startTime.split(" ")[0] + "T" + startTime.split(" ")[1] + "Z",
          endTime.split(" ")[0] + "T" + endTime.split(" ")[1] + "Z"
        )
        .then(
          () => {
            // 关闭加载中
            // console.log("playbackStart success");

            this.resetTimer();
          },
          (e) => {
            // 关闭加载中
            this.$message({
              type: "error",
              message: "获取回放失败",
            });
            console.error(e);
          }
        );
    },
  },

  unmounted() {
    this.destroyMonitorWS();
  },
};
</script>
  
  <style scoped lang="scss">
.monitor-box {
}

.time-view {
  height: 45px;
  cursor: pointer;

  bottom: 0;
  position: relative;

  .time-ruler {
    height: 45px;
  }

  .progress-bar {
    height: 15px;
    background: #44817b;
    position: absolute;
    bottom: -5px;
    overflow: hidden;
    left: 0;
  }

  .pointer {
    width: 2px;
    height: 15px;
    background: red;
    position: absolute;
    bottom: -5px;
    left: 0;
  }
}
</style>
  