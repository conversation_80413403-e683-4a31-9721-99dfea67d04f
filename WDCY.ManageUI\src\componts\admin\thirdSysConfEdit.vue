<template>
	<el-dialog draggable width="25%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="confModel" label-width="100px">
			<el-row>
				<el-col :span="24">
					<el-form-item label="三方系统" prop="thirdSystemId">
						<el-select style="width: 100%;" v-model="confModel.thirdSystemId" placeholder="三方系统" clearable>
							<el-option v-for="item in thirdSystemList" :key="item.id" :label="item.sysName"
							:value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="自定名称" prop="sysName">
						<el-input v-model="confModel.sysName" placeholder="自定名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="系统图标" prop="icoInfoId">
						<el-select style="width: 100%;" v-model="confModel.icoInfoId" placeholder="系统图标">
							<el-option v-for="item in icoList" :key="item.id" :label="item.icoName" :value="item.id" style="line-height:34px">
								<img :src="imgServer + item.icoUrl" />
								<div>{{ item.icoName }}</div>
								<div style="flex:1"></div>
							</el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="归属账号" prop="userId">
						<el-select style="width: 100%;" v-model="confModel.userId" placeholder="归属账号" clearable>
							<el-option v-for="item in userList" :key="item.id" :label="item.nickName"
							:value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="允许推送" prop="allowPush">
						<el-radio-group v-model="confModel.allowPush">
                            <el-radio-button  v-for="item in allowPushList" :key="item.nameEn"
                                :label="parseInt(item.nameEn)">{{ item.nameCn }}</el-radio-button >
                        </el-radio-group>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="排序" prop="sort">
						<el-input-number v-model="confModel.sort" :min="1" @change="handleChange" />
					</el-form-item>
				</el-col>
			</el-row>
			
			<!-- <el-row>
				<el-col :span="7">
					<el-form-item label="图标代号" prop="icoCode">
						<el-input v-model="confModel.icoCode" placeholder="图标代号"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="7">
					<el-form-item label="状态" prop="status">
						<el-select style="width: 100%;" v-model="confModel.status" placeholder="状态">
							<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="7">
					<el-form-item label="图标分类" prop="icoUrl">
						<el-select style="width: 100%;" v-model="confModel.icoCategory" placeholder="图标分类">
							<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="图标url" prop="icoUrl">


						<el-upload :class="[confModel.icoUrl ? '' : 'upload']" class="avatar-uploader"
						:action="imgServer + confModel.icoUrl" :show-file-list="false" :http-request="loadingImg">
						<img v-if="confModel.icoUrl" :src="imgServer + confModel.icoUrl" @mouseenter="mask=true" @mouseleave="mask=false" class="avatar" />
						<el-icon v-else class="avatar-uploader-icon">
							<Plus />
						</el-icon>
						<div @mouseenter="mask=true" @mouseleave="mask=false" v-if="mask" style="background:rgba(0, 0, 0, .2);width: 178px; height: 178px;position: absolute;"></div>
						</el-upload>


					</el-form-item>
				</el-col>
			</el-row>

			<el-row>
				<el-col>
					<el-form-item label="描述" prop="remark">
						<el-input type="textarea" :rows="2" v-model="confModel.note" placeholder="描述"></el-input>
					</el-form-item>
				</el-col>
			</el-row> -->
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交
			</el-button>
		</el-row>
		<!-- <vehicle-number></vehicle-number> -->
	</el-dialog>
</template>

<script>
import { addThirdSysConf, editThirdSysConf, addThirdSysAccount } from "@/api/admin/thirdSystem"
import { fileUpload } from "@/api/admin/file";
import { queryList } from "@/api/base/person"
import mitt from "@/utils/mitt";
export default {
	props: ['statusList', 'userList', 'thirdSystemList', 'allowPushList', 'icoList'],
	data() {
		return {
			loading: false,
			confModel: {},
			dialog: {},
			personList: [],
			personId: "",
			startToEndTime: [],
			imgServer: import.meta.env.VITE_BASE_API,
			userId: ""
		}
	},
	methods: {
		onSubmit() {
			this.dialog.show = false
			this.$parent.dialog.show = true
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.confModel.id == 0) {
						delete this.confModel.id
						addThirdSysConf(this.confModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						// this.confModel.personId = this.confModel.personId.personId
						editThirdSysConf(this.confModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		}
	},
	mounted() {
		this.$nextTick(function () {
			mitt.on('openThirdSysConfEdit', (data) => {
				this.confModel = data
				this.dialog.show = true
				this.dialog.title = "修改配置"
			})
			mitt.on('openThirdSysConfAdd', (id) => {
				this.confModel = {
					id: 0,
					sort:1,
					thirdSystemId: id,
					allowPush: 1
				}
				console.log(this.icoList);
				this.dialog.show = true
				this.dialog.title = "添加配置"
			})
		})
	},
	watch:{
		"dialog.show"(newVal,oldVal){
			if (newVal == false) {
				this.$parent.dialog.show = true
			}
		}
	}
}
</script>
<style scoped lang="less">
.avatar-uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}

.el-select-dropdown__item{
	display: flex;align-items: center;text-align:center;
	>img{
		background-color: rgba(136, 186, 255,.3);
		width: 20px;
		margin:0 auto;
		margin-right: 10px;
	}
}
.el-select-dropdown__item:hover{
	background-color: rgba(170, 170, 170,.3);
}
</style>
