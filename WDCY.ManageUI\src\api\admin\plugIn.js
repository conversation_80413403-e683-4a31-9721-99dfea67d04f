import request from '@/utils/request'

export const listPlugIn = (data) =>
	request({
		url: '/plugin-manager/queryByPage',
		method: 'get',
		params:data
	})
export const reloadPlugIn = (data) =>
	request({
		url: '/plugin-manager/reload',
		method: 'get',
		params:data
	})
export const installPlugIn = (data) =>
	request({
		url: '/plugin-manager/install',
		method: 'post',
		data:data
	})
export const addPlugIn = (data) =>
	request({
		url: '/plugin-manager/add',
		method: 'post',
		data:data
	})
export const editPlugIn = (data) =>
	request({
		url: '/plugin-manager/edit',
		method: 'post',
		data:data
	})

export const uninstallPlugIn = (id) =>
	request({
		url: '/plugin-manager/uninstall',
		method: 'get',
		params: {
			pluginCode: id
		}
	})
export const deletePlugIn = (id) =>
	request({
		url: '/plugin-manager/deleteById',
		method: 'get',
		params: {
			id: id
		}
	})
