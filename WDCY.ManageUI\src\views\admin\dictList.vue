<template>
	<div>
	<dict-edit @search="search" :statusList="statusList"></dict-edit>
	<el-row :gutter="20">
		<el-col :span="8"  style="display:flex">
			<el-input style="margin-right:10px" v-model="searchModel.nameCn" placeholder="字典名称" @keydown.enter="search" clearable />
			<el-input v-model="searchModel.nameEn" placeholder="字典编码" @keydown.enter="search" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="8">
			<el-button style="float: right;" type="primary" @click="add(0)" v-if="hasPerm('sys:dict:add')">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="dictList" row-key="id" border height="calc(100vh - 300px)" style="width: 100%">
				<el-table-column prop="nameCn" sortable header-align="center" label="名称" />
				<el-table-column prop="status" width="100" align="center" label="状态">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="nameEn" sortable align="center" label="编码" width="200" />
				<el-table-column prop="cssClass" align="center" label="样式" width="168" />
				<el-table-column prop="sort" sortable align="center" label="排序" width="88" />
				<el-table-column prop="remark" align="center" label="备注" />
				<el-table-column prop="createTime" sortable align="center" label="创建时间" width="168" />
				<el-table-column prop="updateTime" sortable align="center" label="更新时间" width="168" />
				<el-table-column align="center" width="200" label="操作" v-if="hasPerm('sys:dict:edit') || hasPerm('sys:dict:delete') || hasPerm('sys:dict:add')">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row)" v-if="hasPerm('sys:dict:edit')">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('sys:dict:delete')">删除</el-button>
						<el-button type="text" size="default" v-if="(scope.row.parentId == 0 ) && hasPerm('sys:dict:add')" @click="add(scope.row.id)">添加子级</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</div>
</template>
<script>
import { getDictCss, formatDict } from "@/utils/dict"
import { listDictByNameEn } from "@/api/admin/dict"
import { listDict,deleteDict } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import dictEdit from "@/componts/admin/dict/dictEdit.vue"
export default {
	emits: ['openDictEdit','openDictAdd'],
	components:{ dictEdit },
	data() {
		return {
			searchModel: {},
			dictList: [],
			total:0,
			pageSize:10,
			statusList:[]
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			listDict(this.searchModel)
			.then(res => {
				this.dictList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		edit(dict){
			mitt.emit('openDictEdit',dict)
		},
		add(id){
			mitt.emit('openDictAdd',id)
		},
		deleted(id){
			this.$confirm('删除字典, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteDict(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			mitt.off('openDictEdit')
			mitt.off('openDictAdd')
			try{
				let res = await listDict(this.searchModel)
				this.dictList = res.data.result.list
				this.total = res.data.result.total

				let status_res = await listDictByNameEn('common_status')
				this.statusList = status_res.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
