!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("mars3d")):"function"==typeof define&&define.amd?define(["exports","mars3d"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self)["mars3d-supermap"]={},e.mars3d)}(this,(function(e,t){"use strict";function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,t}var s=r(t);const o=s.Cesium,i=s.layer.BaseLayer;class a extends i{get layer(){return this._layerArr}get s3mOptions(){return this.options.s3mOptions}set s3mOptions(e){for(const t in e){let r=e[t];this.options.s3mOptions[t]=r,"transparentBackColor"===t?r=o.Color.fromCssColorString(r):"transparentBackColorTolerance"===t&&(r=Number(r));for(let e=0;e<this._layerArr.length;e++){const s=this._layerArr[e];null!=s&&(s[t]=r)}}}_showHook(e){this.eachLayer((t=>{t.visible=e,t.show=e}),this)}_mountedHook(){if(!this._map.scene.open)throw new Error("请引入 超图版本Cesium库 或 超图S3M插件 ");const e=this._map.getCameraView();let t;t=this.options.layername?this._map.scene.addS3MTilesLayerByScp(this.options.url,{name:this.options.layername,autoSetView:this.options.flyTo,cullEnabled:this.options.cullEnabled}):this._map.scene.open(this.options.url,this.options.sceneName,{autoSetView:this.options.flyTo}),t.then((t=>{if(Array.isArray(t)?this._layerArr=t:this._layerArr=[t],this.isAdded){for(let e=0;e<this._layerArr.length;e++){const t=this._layerArr[e];if(t)try{this._initModelItem(t)}catch(e){s.Log.logError("s3m图层初始化出错",e)}}this._showHook(this.show),this.options.flyTo?this.flyTo():!1===this.options.flyTo&&this._map.setCameraView(e,{duration:0}),this._readyPromise.resolve(this),this.fire(s.EventType.load,{layers:this._layerArr})}else this._removedHook()}),(e=>{var t;null!==(t=this._readyPromise)&&void 0!==t&&t.reject&&this._readyPromise.reject(e)}))}_initModelItem(e){var t;if(this.options.s3mOptions)for(const t in this.options.s3mOptions){const r=this.options.s3mOptions[t];e[t]="transparentBackColor"===t?o.Color.fromCssColorString(r):"transparentBackColorTolerance"===t?Number(r):r}this.options.highlight&&(e.selectedColor=s.Util.getColorByStyle(this.options.highlight)),null!==(t=this.options)&&void 0!==t&&null!==(t=t.position)&&void 0!==t&&t.alt&&(e.style3D.altitudeMode=o.HeightReference.NONE,e.style3D.bottomAltitude=this.options.position.alt,e.refresh&&e.refresh())}_addedHook(){this.eachLayer((e=>{this._map.scene.layers.add(e)}))}_removedHook(){try{this.eachLayer((e=>{this._map.scene.layers.remove(e.name,!0)}))}catch(e){s.Log.logWarn("s3m移除中空值异常(插件本身问题，需改超图插件)",e)}}eachLayer(e,t){if(this._layerArr)return this._layerArr.forEach((r=>{e.call(t,r)})),this}setOpacity(e){this.eachLayer((t=>{t.style3D.fillForeColor.alpha=e}),this)}flyTo(e={}){return this.options.center?this._map.setCameraView(this.options.center,e):this.options.extent?this._map.flyToExtent(this.options.extent,e):void 0}}s.layer.S3MLayer=a,s.LayerUtil.register("supermap_s3m",a);const n=s.Cesium,l=s.layer.BaseTileLayer;class h extends l{async _createImageryProvider(e){return await p(e)}_addedHook(){super._addedHook(),n.defined(this.options.transparentBackColor)&&(this._imageryLayer.transparentBackColor=s.Util.getCesiumColor(this.options.transparentBackColor),this._imageryLayer.transparentBackColorTolerance=this.options.transparentBackColorTolerance)}}async function p(e){return(e=s.LayerUtil.converOptions(e)).url instanceof n.Resource&&(e.url=e.url.url),n.defined(e.transparentBackColor)&&(delete e.transparentBackColor,delete e.transparentBackColorTolerance),new n.SuperMapImageryProvider(e)}h.createImageryProvider=p,s.layer.SmImgLayer=h;const c="supermap_img";s.LayerUtil.register(c,h),s.LayerUtil.registerImageryProvider(c,p);const y=s.Cesium,m=s.layer.BaseLayer;class d extends m{get layer(){return this._mvtLayer}_mountedHook(){this._mvtLayer=this._map.scene.addVectorTilesMap({viewer:this._map.viewer,canvasWidth:512,...this.options});const e=this._map.scene,t=new y.ScreenSpaceEventHandler(e.canvas);t.setInputAction((t=>{if(!this.show)return;const r=s.PointUtil.getCurrentMousePosition(e,t.position);this._mvtLayer.queryRenderedFeatures([r],{}).reduce(((e,o)=>{const i=o.feature.properties;if(!i)return;const a=s.Util.getPopupForConfig(this.options,i),n={data:i,event:t};this._map.openPopup(r,a,n)}))}),y.ScreenSpaceEventType.LEFT_CLICK),this.handler=t}_addedHook(){this._mvtLayer.show=!0}_removedHook(){this._mvtLayer&&(this._mvtLayer.show=!1)}setOpacity(e){this._mvtLayer&&(this._mvtLayer.alpha=parseFloat(e))}flyTo(e={}){return this.options.center?this._map.setCameraView(this.options.center,e):this.options.extent?this._map.flyToExtent(this.options.extent,e):this._mvtLayer?this._map.camera.flyTo({...e,destination:this._mvtLayer.rectangle}):Promise.resolve(!1)}}s.layer.SmMvtLayer=d,s.LayerUtil.register("supermap_mvt",d),e.S3MLayer=a,e.SmImgLayer=h,e.SmMvtLayer=d,Object.defineProperty(e,"__esModule",{value:!0})}));
