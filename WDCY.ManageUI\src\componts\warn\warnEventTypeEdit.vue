<template>
	<el-dialog draggable width="50%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" :model="personModel" label-width="120px">

			<el-form-item label="类型名称" prop="name">
				<el-input v-model="personModel.name" placeholder="类型名称"></el-input>
			</el-form-item>

			<el-form-item label="类型描述" prop="note">
				<el-input type="textarea" v-model="personModel.note" placeholder="类型描述" maxlength="200" show-word-limit></el-input>
			</el-form-item>

			<el-form-item label="标签" prop="tags">
				<!-- collapse-tags -->
				<el-select clearable style="width: 100%" v-model="personModel.tags" multiple placeholder="选择标签">
					<el-option v-for="item in tagList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="是否规则识别" prop="isRuleRecognize">
				<el-radio v-model="personModel.isRuleRecognize" :label="true">是</el-radio>
				<el-radio v-model="personModel.isRuleRecognize" :label="false">否</el-radio>
			</el-form-item>

			<el-form-item label="是否人员" prop="isPerson">
				<el-radio v-model="personModel.isPerson" :label="true">是</el-radio>
				<el-radio v-model="personModel.isPerson" :label="false">否</el-radio>
			</el-form-item>

			<el-form-item label="排序" prop="sort">
				<!-- <el-input onkeyup = "value=value.replace(/[^\d]/g,'')" v-model="personModel.sort" placeholder="排序"></el-input> -->
				<el-input-number v-model="personModel.sort" :min="1" :max="1000" :tep="1" controls-position="right" />
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<!-- <el-button @click="dialogVisible = false">Cancel</el-button> -->
				<el-button type="primary" @click="onSubmit">
					提 交
				</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script>
import { warnEventTypeListEdit, warnEventTypeListAdd } from "@/api/warn/warn"
import mitt from "@/utils/mitt";
export default {
	props: ['statusList', 'tagList'],
	data() {
		return {
			loading: false,
			personModel: {},
			communityId: localStorage.getItem("communityId"),
			dialog: {},
			rules: {
				name: [{
					required: true,
					message: '请输入类型名称',
					trigger: 'blur',
				}],
				isRuleRecognize: [{
					required: true,
					message: '请选择是否规则识别',
					trigger: 'blur',
				}],
				isPerson: [{
					required: true,
					message: '请选择是否人员',
					trigger: 'blur',
				}],
			}
		}
	},
	methods: {
		onSubmit() {
			if (this.personModel.tags) {
				this.personModel.tags = JSON.stringify(this.personModel.tags)
			}
			this.personModel.communityId = this.communityId
			if (this.personModel.id == 0) {
				warnEventTypeListAdd(this.personModel)
					.then(res => {
						this.$message.success(res.data.msg)
						this.$emit("search")
						this.dialog.show = false
					})
			} else {
				warnEventTypeListEdit(this.personModel)
					.then(res => {
						this.$message.success(res.data.msg)
						this.$emit("search")
						this.dialog.show = false
					})
			}
		}


	},
	mounted() {
		this.$nextTick(function () {
			mitt.on('openWarnEventTypeEdit', (person) => {
				if (person.tags) {
					person.tags = JSON.parse(person.tags)
				}
				this.personModel = person
				this.dialog.show = true
				this.dialog.title = "修改"
			})
			mitt.on('openWarnEventTypeAdd', () => {
				this.personModel = {
					id: 0,
					isRuleRecognize: true,
					isPerson: true,
					sort: 1
				}
				this.dialog.show = true
				this.dialog.title = "添加"
			})
		})
	}
}
</script>
