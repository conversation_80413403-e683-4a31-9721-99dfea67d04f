<template>
	<el-dialog draggable width="25%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="userForm" :model="userModel" label-width="80px">
			<el-row>
				<el-col>
					<el-form-item label="昵称" prop="nickName">
						<el-input v-model="userModel.nickName" placeholder="昵称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="用户名" prop="userName">
						<el-input v-model="userModel.userName" placeholder="用户名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="密码" prop="password">
						<el-input type="password" v-model="userModel.password" placeholder="密码"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			
			<el-col>
				<el-form-item label="授权过期" prop="authEndTime">
					<el-date-picker
						style="width:100%"
						v-model="userModel.authEndTime"
						type="date"
						placeholder="选择授权过期时间"
						value-format="YYYY-MM-DD HH:mm:ss"
						:size="size"
					/>
				</el-form-item>
				</el-col>
			<el-row>
				<el-col>
					<el-form-item label="邮箱">
						<el-input v-model="userModel.email" placeholder="邮箱"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="手机号">
						<el-input v-model="userModel.mobile" placeholder="手机号"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="用户组" prop="groupId">
						<el-cascader :show-all-levels="false" style="width: 100%;" v-model="groupId" :props="{checkStrictly:true}" :options="userGroupList" @change="handleChange" clearable placeholder="选择组"/>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="角色" prop="roleIds">
						<el-select style="width: 100%;" v-model="userModel.roleIds" multiple placeholder="选择角色">
							<el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit('userForm')">提 交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { registerUser } from "@/api/admin/user";
import mitt from "@/utils/mitt";
export default {
	props:['roleList','userGroupList'],
	data() {
		var checkPassword = (rule, value, callback) => {
			if(value == undefined || value == null || value == ''){
				return callback(new Error('请输入密码'));
			}
			var reg = /^[\da-zA-Z~!@#$%^&*]{6,18}$/
			if(!reg.test(value)){
				return callback(new Error('密码长度为6-18位字符'));
			}
			callback()
		}
		return {
			loading: false,
			userModel: {},
			dialog:{},
			rules: {
				nickName: [{
					required: true,
					message: '请输入用户名称',
					trigger: 'blur',
				}],
				userName: [{
					required: true,
					message: '请输入用户账号',
					trigger: 'blur',
				}],
				roleIds: [{
					required: true,
					message: '请选择角色',
					trigger: 'change',
				}],
				groupId: [{
					required: true,
					message: '请选择用户组',
					trigger: 'change',
				}],
				password: [{
					required: true,
					validator: checkPassword,
					trigger: 'change',
				}]
			}
		}
	},
	methods: {
		handleChange(e){
			if(e == null){
				this.userModel.groupId = null
				return
			}
			this.userModel.groupId = e[e.length-1]
		},
		onSubmit(formName){
			this.$refs[formName].validate((valid) => {
			  if (valid) {
			    registerUser(this.userModel)
			    .then(res =>{
			    	this.$message.success(res.data.msg)
			    	this.$emit('search')
			    	this.dialog.show = false
			    })
			  } else {
			    return false
			  }
			})
			
		}
	},
	mounted(){
		mitt.on('openUserRegister', (searchModel) => {
			this.userModel = {
				id:0
			}
			this.dialog.show = true
			this.dialog.title = "用户注册"
		})
	}
}
</script>
