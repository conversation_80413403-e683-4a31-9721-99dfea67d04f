<template>
	<el-row :gutter="20">
		<el-col :span="8" style="display:flex">
			<el-input style="margin-right:10px" v-model="searchModel.name" @keydown.enter="search" placeholder="车牌号" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.deviceName" @keydown.enter="search" placeholder="车闸设施" clearable />
			<el-input v-model="searchModel.address" @keydown.enter="search" placeholder="进出地点" clearable />
		</el-col>
		<el-col :span="8" style="display:flex">
			<el-date-picker
				v-model="searchModel.startTime"
				type="datetime"
				placeholder="选择开始日期"
				value-format="YYYY-MM-DD HH:mm:ss"
				:size="size"
				style="margin-right:10px"
			/>
			<el-date-picker
				style="margin-right:10px"
				v-model="searchModel.endTime"
				type="datetime"
				placeholder="选择结束日期"
				value-format="YYYY-MM-DD HH:mm:ss"
				:size="size"
			/>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table :data="personList" border height="calc(100vh - 300px)" style="width: 100%">
				<el-table-column prop="plateNumber" width="100" align="center" label="车牌号" />
				<el-table-column prop="collectionTime" width="180" align="center" label="时间" />
				<el-table-column prop="flowType" width="100" :formatter="formatFlowType" align="center" label="进出类型" />
				<el-table-column prop="address" align="left" label="进出地点" />
				<el-table-column prop="deviceName" width="300" align="center" label="车闸设施">
				</el-table-column>
				<el-table-column prop="capturePhoto" width="100" align="center" label="照片">
					<template #default="scope">
						<el-image preview-teleported fit="contain" style="width: 50px; height: 50px; z-index: 9999;" :src="imgServer+scope.row.capturePhoto" :preview-src-list="[imgServer+scope.row.capturePhoto]"></el-image>
					</template>
				</el-table-column>
				<el-table-column prop="captureFeaturePhoto" width="100" align="center" label="特征图">
					<template #default="scope">
						<el-image preview-teleported fit="contain" style="width: 50px; height: 50px; z-index: 9999;" :src="imgServer+scope.row.captureFeaturePhoto" :preview-src-list="[imgServer+scope.row.captureFeaturePhoto]"></el-image>
					</template>
				</el-table-column>
				<el-table-column align="center" width="100" label="操作" v-if="hasPerm('situation:vehicle:delete')">
					<template #default="scope">
							<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('situation:vehicle:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>
<script>
import { vehicleList,vehicleListDelete} from "@/api/situation/situation"
import { listDictByNameEn } from "@/api/admin/dict"
import { getDictCss, formatDict } from "@/utils/dict"
export default {
	data() {
		return {
			searchModel: {},
			personList: [],
			imgServer: import.meta.env.VITE_BASE_API,
			communityId:localStorage.getItem("communityId"),
			flowTypeList:[],
			total:0,
			pageSize: 10
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatFlowType(row, column, cellValue, index){
			return formatDict(this.flowTypeList, cellValue)
		},
		search() {
			this.searchModel.communityId=this.communityId
			vehicleList(this.searchModel)
			.then(res => {
				this.personList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		deleted(id){
			this.$confirm('删除信息, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					vehicleListDelete(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		
		//初始化时间往前推1周
		initTime(){
			var currentDate = new Date();
			// this.searchModel.endTime = this.formatDate(currentDate)
			currentDate.setDate(currentDate.getDate() - 7);
			this.searchModel.startTime = this.formatDate(currentDate)
		},
		async init(){
			try{
				this.initTime()
				this.searchModel.communityId=this.communityId
				let res = await vehicleList(this.searchModel)
				this.personList = res.data.result.list
				this.total = res.data.result.total

				let flowTypes = await listDictByNameEn('flow_type')
				this.flowTypeList = flowTypes.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
