<template>
  <div>
    <div v-if="playMode == 0">
      <video
        id="videoPlayerHls"
        autoplay
        muted
        class="video-js vjs-default-skin vjs-big-play-centered"
        preload="auto"
        :width="width"
        :height="height"
      ></video>
    </div>

    <div v-if="playMode == 1"></div>
  </div>
</template>
  
  <script>

import videojs from "video.js";

import {

queryMonitorByDevNo,

} from "@/api/device/device";

export default {

  computed: {
    mode: function () {
      return 1;
    },
  },

  props: {
    communityId: {
      type: String,
      default: "",
    },

    width: {
      type: Number,
      default: 850,
    },
    height: {
      type: Number,
      default: 550,
    },

    playMode: {
      type: Number,
      default: 0,
    },

    videoMode: {
      type: String,
      default: "HLS",
    },

    devId: {
      type: String,
      default: 0,
    },

    devNo: {
      type: String,
      default: "",
    },

    backStartTime: {
      type: String,
      default: "",
    },
    backEndTime: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      videoPlayerHls: null, // hls播放器对象
    };
  },

  mounted() {},
  created() {},

  methods: {
    
    initMonitorHLS() {
      // console.log("initMonitorHLS");
      var that = this;
      var videoSrc = "";
      if (!this.devId) return;
      const param = {
        id: this.devId,
        urlType: "hls",
        communityId: this.communityId,
        beginTime: this.backStartTime,
        endTime: this.backEndTime,
        playType: this.playMode == 0 ? "previewURLs" : "playbackURLs",
      };

      queryMonitorByDevNo(param)
        .then((res) => {
          if (res !== null && res.data.code === 0) {
            if (res.data.result !== null && res.data.result.url != null) {
              var result = res.data.result;

              videoSrc = result.url; // 地址
              if (videoSrc) {
                try {
                  that.videoPlayerHls = videojs(
                    "videoPlayerHls",
                    {
                      bigPlayButton: true, // 显示播放按钮
                      textTrackDisplay: false,
                      posterImage: true,
                      errorDisplay: false,
                      controlBar: true, // 显示控件
                    },
                    function () {
                      this.src(videoSrc);
                      setTimeout(() => {
                        var playPromise = this.play();

                        if (playPromise !== undefined) {
                          playPromise
                            .then((_) => {
                              // 这个时候可以安全的暂停
                            })
                            .catch((error) => {
                              console.log("hls falid", error);
                            });
                        }
                      }, 100);
                    }
                  );
                } catch (error) {
                  console.log("hls falid", error);
                }
              } else {
                that.$message({
                  type: "error",
                  message: "无视频播放资源",
                });
              }
            } else {
              that.$message({
                type: "error",
                message: "无视频播放资源",
              });
            }
          } else {
            that.$message({
              type: "error",
              message: "获取数据失败",
            });
          }
        })
        .catch(function (e) {
          that.$message({
            type: "error",
            message: e.data && e.data.msg ? e.data.msg : "获取数据失败",
          });
        });
    },

    destroyMonitorHLS() {
      // console.log("destroyMonitorHLS");
      if (this.videoPlayerHls) {
        this.videoPlayerHls.dispose(); // 关闭控件
        this.videoPlayerHls = null;
      }
    },
  },

  unmounted() {
    this.destroyMonitorHLS();
  },
};
</script>
  
  <style scoped lang="scss">
.monitor-box {

}
</style>
  