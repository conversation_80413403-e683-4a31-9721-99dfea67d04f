<template>
    <el-dialog draggable width="50%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
        <el-form :rules="rules" ref="form" :model="noticeModel" label-width="60px">
            <el-row>
                <el-col :span="16">
                    <el-form-item label="标题" prop="noticeTitle">
                        <el-input v-model="noticeModel.noticeTitle" placeholder="标题"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="类型" prop="noticeType">
                        <el-select style="width: 100%;" v-model="noticeModel.noticeType" class="m-2" placeholder="类型">
                            <el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn"
                                :value="parseInt(item.nameEn)"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="noticeModel.status">
                            <el-radio v-for="item in statusList" :key="item.nameEn"
                                :label="parseInt(item.nameEn)">{{ item.nameCn }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <div id="editor">
                </div>
                <!-- <textarea name="" id="" cols="170" rows="20" style="width:100%" v-model="editorData"></textarea> -->
                <!-- <el-col :span="24">
                    <el-form-item label="内容">
                        <editor v-model="noticeModel.noticeContent" :min-height="192"/>
                    </el-form-item>
                </el-col> -->
            </el-row>
        </el-form>
        <el-row justify="center">
            <el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提
                交</el-button>
        </el-row>
    </el-dialog>
</template>

<script>
import Editor from "wangeditor"
// import Quill from 'quill';
// import "quill/dist/quill.core.css";
// import "quill/dist/quill.snow.css";
// import "quill/dist/quill.bubble.css";
import mitt from "@/utils/mitt"
import { addNotice, editNotice } from "@/api/admin/notice"
import { fileUpload } from "@/api/admin/file";
export default {
    props: ['typeList', 'statusList'],
    data() {
        return {
            loading: false,
            dialog: {},
            noticeModel: {},
            imgServer: import.meta.env.VITE_BASE_API,
            rules: {
                noticeTitle: [
                    { required: true, message: "公告标题不能为空", trigger: "blur" }
                ],
                noticeType: [
                    { required: true, message: "公告类型不能为空", trigger: "change" }
                ]
            }
        }
    },
    methods: {
        onSubmit() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    this.noticeModel.noticeContent = editor.txt.html()
                    if (this.noticeModel.id == 0) {
                        // if (this.noticeModel.noticeType!=undefined && this.noticeModel.status!= undefined) {
                        delete (this.noticeModel.id)
                        // this.noticeModel.noticeType = String(this.noticeModel.noticeType)
                        // this.noticeModel.status = String(this.noticeModel.status)
                        addNotice(this.noticeModel)
                            .then(res => {
                                this.$message.success(res.data.msg)
                                this.$emit("search")
                                this.dialog.show = false
                            })
                        // }
                    } else {
                        editNotice(this.noticeModel)
                            .then(res => {
                                console.log(res);
                                this.$message.success(res.data.msg)
                                this.$emit("search")
                                this.dialog.show = false
                            })
                    }
                }
            })
        },
        init() {
            const container = document.getElementById('editor');
            // const container = document.querySelector('#editor');
            // const container = $('#editor').get(0);
            // const quill = new Quill(container);
        },
    },
    mounted() {
        var that = this
        this.$nextTick(function () {
            mitt.on("openNoticeEdit", (notice) => {
                console.log(this.imgServer);
                this.noticeModel = notice
                this.noticeModel.noticeType = Number(this.noticeModel.noticeType)
                this.noticeModel.status = Number(this.noticeModel.status)
                setTimeout(function () {
                    this.editor = new Editor("#editor")
                    editor.config.uploadImgServer = "/api/upload-img";
                    editor.config.pasteFilterStyle = false
                    editor.create()
                    this.editor.txt.html(notice.noticeContent)
                    editor.config.customUploadImg = (resultFiles, insertImgFn) => {
                        //拿到上传的文件 
                        console.log(resultFiles);

                        let form = new FormData();
                        form.append("file", resultFiles[0]);
        		        form.append("modulesName", 'admin');
        		        form.append("functionName", 'notice');
        		        form.append("communityId", localStorage.getItem('communityId'));
                        //上传图片，获取到url
                        fileUpload(form).then((res) => {
                            //将上传的文件转成url传入insertImgFn方法
                            insertImgFn(that.imgServer + res.data.result.url);
                        });
                    }
                }, 50)
                this.dialog.show = true
                this.dialog.title = "修改公告"
            })
            mitt.on("openNoticeAdd", () => {
                this.noticeModel = {
                    id: 0,
                    status: 1
                }
                setTimeout(function () {
                    this.editor = new Editor("#editor")
                    editor.config.uploadImgServer = "/api/upload-img";
                    editor.config.pasteFilterStyle = false
                    editor.create()
                    editor.config.customUploadImg = (resultFiles, insertImgFn) => {
                        //拿到上传的文件 
                        let form = new FormData();
                        form.append("file", resultFiles[0]);
                        form.append("modulesName", 'admin');
        		        form.append("functionName", 'notice');
        		        form.append("communityId", localStorage.getItem('communityId'));
                        //上传图片，获取到url
                        fileUpload(form).then((res) => {
                            //将上传的文件转成url传入insertImgFn方法
                            insertImgFn(that.imgServer + res.data.result.url);
                        });
                    }
                }, 50)
                this.dialog.show = true
                this.dialog.title = "发布公告"
            })
        })
    },
    watch: {
        'noticeModel.title'() {
            if (this.noticeModel.title) {
                this.noticeModel.title = this.noticeModel.title.trim()
            }
            // this.noticeModel.title = this.noticeModel.title.replace(/(^\s*)|(\s*$)/g, "")
        }
    },
    beforeDestroy() {
        this.Quill = null;
    },
}
</script>
<style scoped>
#editor{
    width:100%
}
</style>
