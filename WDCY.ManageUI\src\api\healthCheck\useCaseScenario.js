import request from '@/utils/request'
// 场景检查 分页
export const listCheckScene = (data) =>
	request({
		url: '/funcCheckScene/page',
		method: 'get',
		params: data
	})
    
// 用例检查 分页
export const listAllScene = (data) =>
request({
    url: '/funcCheckScene/list',
    method: 'get',
    params: data
})
// 场景检查 详情
export const getCheckScene = (id) =>
	request({
		url: '/funcCheckScene/'+id,
		method: 'get'
	})
	//场景检查 添加
export const addCheckScene = (data) =>
	request({
		url: '/funcCheckScene',
		method: 'post',
		data: data
	})
	// 场景检查 编辑
export const editCheckScene = (data) =>
	request({
		url: '/funcCheckScene',
		method: 'put',
		data: data
	})
	// 场景检查 删除
export const deleteCheckScene = (id) =>
	request({
		url: '/funcCheckScene/'+id,
		method: 'delete'
	})

    // 场景检查 检查
export const checkSceneTest = (id) =>
	request({
		url: '/funcCheckScene/caseTest',
		method: 'get',
		params: {
			id: id || ""
		}
	})

    

// 用例检查 分页
export const listTestCase = (data) =>
request({
    url: '/funcCheckCase/page',
    method: 'get',
    params: data
})

// 用例检查 详情
export const getTestCase = (id) =>
	request({
		url: '/funcCheckCase/'+id,
		method: 'get'
	})
	//用例检查 添加
export const addTestCase = (data) =>
	request({
		url: '/funcCheckCase',
		method: 'post',
		data: data
	})
	// 用例检查 编辑
export const editTestCase = (data) =>
	request({
		url: '/funcCheckCase',
		method: 'put',
		data: data
	})
	// 用例检查 删除
export const deleteTestCase = (id) =>
	request({
		url: '/funcCheckCase/'+id,
		method: 'delete'
	})
//用例批量新增
    export const batchCheckCase = (data) =>
	request({
		url: '/funcCheckCase/batch',
		method: 'post',
		data: data
	})