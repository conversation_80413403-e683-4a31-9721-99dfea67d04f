/**
 * Mars3D平台插件,结合heatmap可视化功能插件  mars3d-heatmap
 *
 * 版本信息：v3.8.13
 * 编译日期：2025-01-09 16:08
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：火星科技免费公开版 ，2024-08-01
 */
(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
	typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-heatmap"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';(function(_0x32d104,_0x46d956){var _0x337f27={_0x11eb46:0xc7,_0x2334b9:0x4b6,_0x2f8c1e:0x4f3,_0x2b3b3c:0x119,_0xa0ddb2:0x49c,_0x291ff3:0x132},_0x5e6e76=_0x32d104();function _0x181fbd(_0x9824,_0x5ba411){return _0x1a38(_0x9824-0x2ce,_0x5ba411);}function _0x4c926e(_0x42092a,_0x2190c6){return _0x1a38(_0x42092a- -0x2bc,_0x2190c6);}while(!![]){try{var _0x388dec=-parseInt(_0x4c926e(-0x12d,-_0x337f27._0x11eb46))/0x1*(-parseInt(_0x181fbd(0x498,0x491))/0x2)+parseInt(_0x4c926e(-0xe2,-0x114))/0x3+parseInt(_0x181fbd(0x516,0x530))/0x4*(-parseInt(_0x181fbd(0x480,0x4aa))/0x5)+parseInt(_0x181fbd(0x514,_0x337f27._0x2334b9))/0x6+parseInt(_0x181fbd(0x50d,_0x337f27._0x2f8c1e))/0x7+-parseInt(_0x4c926e(-0xd4,-_0x337f27._0x2b3b3c))/0x8*(parseInt(_0x181fbd(0x473,0x4d5))/0x9)+-parseInt(_0x181fbd(_0x337f27._0xa0ddb2,0x469))/0xa*(parseInt(_0x4c926e(-_0x337f27._0x291ff3,-0xe9))/0xb);if(_0x388dec===_0x46d956)break;else _0x5e6e76['push'](_0x5e6e76['shift']());}catch(_0x317dc0){_0x5e6e76['push'](_0x5e6e76['shift']());}}}(_0x4f0a,0x944f9));function _interopNamespace(_0x31802a){var _0x2ad6fa={_0x7878e1:0x45},_0x5a8220={_0x2db938:0x222};if(_0x31802a&&_0x31802a['__esModule'])return _0x31802a;var _0x5c9e54=Object[_0x262730(-_0x2ad6fa._0x7878e1,0x1d)](null);_0x31802a&&Object['keys'](_0x31802a)['forEach'](function(_0x438cc9){if(_0x438cc9!=='default'){var _0x28addf=Object['getOwnPropertyDescriptor'](_0x31802a,_0x438cc9);Object['defineProperty'](_0x5c9e54,_0x438cc9,_0x28addf['get']?_0x28addf:{'enumerable':!![],'get':function(){return _0x31802a[_0x438cc9];}});}});function _0x38194c(_0x467abb,_0x2ff514){return _0x1a38(_0x2ff514- -_0x5a8220._0x2db938,_0x467abb);}function _0x262730(_0x34a70f,_0x31d00b){return _0x1a38(_0x34a70f- -0x274,_0x31d00b);}return _0x5c9e54[_0x262730(-0x84,-0xc1)]=_0x31802a,_0x5c9e54;}function _mergeNamespaces(_0x181b8f,_0x18bea2){var _0x363de2={_0x594cd3:0x458},_0x4449a2={_0x2cc780:0xda,_0x38d7e9:0x1ef},_0x3e53b7={_0x9deae4:0x3b9},_0x520f1d={_0x3169e1:0x62c};_0x18bea2[_0x2e50b2(_0x363de2._0x594cd3,0x43d)](function(_0x427334){var _0x54831d={_0x1274c4:0xf};function _0x4e9c28(_0x1bc363,_0x106885){return _0x2e50b2(_0x1bc363,_0x106885-_0x54831d._0x1274c4);}function _0x2dcf3a(_0x5efe09,_0x5354a1){return _0x2e50b2(_0x5efe09,_0x5354a1- -_0x520f1d._0x3169e1);}_0x427334&&typeof _0x427334!==_0x4e9c28(0x4bc,0x469)&&!Array[_0x2dcf3a(-_0x4449a2._0x2cc780,-0x139)](_0x427334)&&Object['keys'](_0x427334)[_0x2dcf3a(-0x200,-_0x4449a2._0x38d7e9)](function(_0x295bb5){function _0x171e21(_0x326386,_0x838271){return _0x2dcf3a(_0x326386,_0x838271-0x53a);}function _0x4d539d(_0x4641dc,_0xe3c759){return _0x2dcf3a(_0xe3c759,_0x4641dc-0x576);}if(_0x295bb5!==_0x4d539d(0x3eb,0x3a5)&&!(_0x295bb5 in _0x181b8f)){var _0x573dab=Object[_0x171e21(0x41d,_0x3e53b7._0x9deae4)](_0x427334,_0x295bb5);Object['defineProperty'](_0x181b8f,_0x295bb5,_0x573dab['get']?_0x573dab:{'enumerable':!![],'get':function(){return _0x427334[_0x295bb5];}});}});});function _0x2e50b2(_0x593bb6,_0x509231){return _0x1a38(_0x509231-0x2b1,_0x593bb6);}return _0x181b8f;}var mars3d__namespace=_interopNamespace(mars3d),commonjsGlobal=typeof globalThis!=='undefined'?globalThis:typeof window!==_0x2d6513(0x28f,0x274)?window:typeof global!=='undefined'?global:typeof self!==_0x297e6d(0x4c7,0x529)?self:{},_0x507541={};function _0x1a38(_0x31deb9,_0x52a8fa){var _0x4f0a87=_0x4f0a();return _0x1a38=function(_0x1a3846,_0x45ad32){_0x1a3846=_0x1a3846-0x185;var _0x55991b=_0x4f0a87[_0x1a3846];if(_0x1a38['aicYXY']===undefined){var _0x149e0b=function(_0x507541){var _0x4ccd1e='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';var _0x469d0f='',_0x2ddeae='';for(var _0x1fe380=0x0,_0x511abf,_0x35ad72,_0x588151=0x0;_0x35ad72=_0x507541['charAt'](_0x588151++);~_0x35ad72&&(_0x511abf=_0x1fe380%0x4?_0x511abf*0x40+_0x35ad72:_0x35ad72,_0x1fe380++%0x4)?_0x469d0f+=String['fromCharCode'](0xff&_0x511abf>>(-0x2*_0x1fe380&0x6)):0x0){_0x35ad72=_0x4ccd1e['indexOf'](_0x35ad72);}for(var _0x2ebfac=0x0,_0x217aa0=_0x469d0f['length'];_0x2ebfac<_0x217aa0;_0x2ebfac++){_0x2ddeae+='%'+('00'+_0x469d0f['charCodeAt'](_0x2ebfac)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x2ddeae);};_0x1a38['iaNqjq']=_0x149e0b,_0x31deb9=arguments,_0x1a38['aicYXY']=!![];}var _0x5c2a0c=_0x4f0a87[0x0],_0x207cdf=_0x1a3846+_0x5c2a0c,_0x34b23e=_0x31deb9[_0x207cdf];return!_0x34b23e?(_0x55991b=_0x1a38['iaNqjq'](_0x55991b),_0x31deb9[_0x207cdf]=_0x55991b):_0x55991b=_0x34b23e,_0x55991b;},_0x1a38(_0x31deb9,_0x52a8fa);}_0x507541['exports']={};var heatmap$1=_0x507541;(function(_0x4c15b8){var _0x3b454e={_0x4d6820:0x53d,_0x20f380:0x581,_0x2597c0:0x54e,_0x2709a7:0x597,_0x321ef3:0x637,_0x551e62:0x407},_0x3aece0={_0x1e4c27:0x170,_0x33eddd:0x187},_0x151927={_0x5302cf:0x3a2};(function(_0x1530e9,_0x1a0e3f,_0x4774c6){function _0x4c77c3(_0x1bb63b,_0x550b1e){return _0x1a38(_0x1bb63b- -0x1d0,_0x550b1e);}_0x4c15b8[_0x4c77c3(0x2f,0x3d)]?_0x4c15b8['exports']=_0x4774c6():_0x1a0e3f[_0x1530e9]=_0x4774c6();}('h337',commonjsGlobal,function(){var _0x551b97={_0x296fc3:0x9c,_0x52b461:0x4c0,_0x1c5a9f:0x4a3},_0x2fdb11={_0x5b59d9:0x18,_0x4bca11:0x7b},_0x37afe6={_0x5186b2:0x1e1,_0x382a1b:0x1a6},_0xcbcbf7={_0x195c4c:0x50e},_0x3716fd={_0x24c273:0x168,_0x71086f:0x140,_0x3440b5:0x19f,_0x4ab090:0x124,_0x34149a:0xbf,_0x4689cb:0x49e,_0x523063:0x486},_0xb8fd17={_0x54cea6:0x56c,_0x5ad8d5:0x51e,_0x1e174f:0xab,_0xb41fef:0x4ae,_0x3009f7:0x4d7,_0x883480:0x52e,_0x523401:0x56,_0x4ac573:0x94,_0x59ca00:0x560,_0x54363e:0x532,_0x2a65c6:0x95,_0x536043:0xc3,_0x530325:0x63},_0x53a2bc={_0x5bb220:0x36e,_0x1b50b8:0x310,_0x1db6b9:0xe7,_0x34eaeb:0x3a6},_0x545671={_0x4d3f03:0x334,_0x1c15c4:0x383,_0xb40895:0x105,_0x581e19:0x15a},_0x4c04e4={_0x8716b6:0x6c,_0x43e2ea:0x1d2,_0x53a4c3:0x1b8,_0x20fb7c:0x12f,_0x54463b:0x154,_0x4d232b:0x1ca,_0x56544f:0x172,_0xc1efc:0x7,_0x4aa82e:0x175,_0x4c84c0:0x1cc,_0x8ebc8e:0x1cd},_0x1aeacb={_0x312e28:0x131,_0x124257:0x371,_0x4ba526:0x382,_0x1d709b:0x34e},_0xc42f12={_0x3b7922:0x434,_0xa194e8:0x475,_0x5db10e:0x276,_0x5c43a0:0x29d},_0x31585a={_0x34b433:0x435,_0x5a070b:0x423,_0x633b20:0x3ca,_0x2f2916:0x3ce,_0x557242:0x4b4},_0x5dca41={_0x1f5f36:0x123},_0x825e9e={_0x52f621:0x1ca,_0x3af465:0xca},_0x43e382={_0x727e8b:0xf2,_0x35bf2c:0x131,_0x58681d:0x6f,_0x33c407:0x58},_0x93d726={_0x3933da:0x47a,_0x415cb1:0x4bd,_0x2151ab:0x4db},_0x37126d={_0x12afae:0x21d,_0x405d9b:0x211,_0x59b256:0x41b,_0x390477:0x188,_0x258c15:0x17d,_0x357601:0x1f1,_0xe99c04:0x20f},_0x23468a={};_0x23468a['0.25']='rgb(0,0,255)',_0x23468a[_0x150e61(0x53d,_0x3b454e._0x4d6820)]=_0x2f952c(0x49f,0x438),_0x23468a['0.85']=_0x150e61(0x5c7,_0x3b454e._0x20f380),_0x23468a['1']='rgb(255,0,0)';var _0x501f35={};_0x501f35[_0x150e61(0x553,0x595)]=0x28,_0x501f35['defaultRenderer']='canvas2d',_0x501f35[_0x150e61(0x5b0,_0x3b454e._0x2597c0)]=_0x23468a,_0x501f35[_0x150e61(_0x3b454e._0x2709a7,0x576)]=0x1,_0x501f35[_0x150e61(0x5d5,_0x3b454e._0x321ef3)]=0x0,_0x501f35['defaultBlur']=0.85;function _0x150e61(_0x37f0f5,_0x5d1baa){return _0x1a38(_0x37f0f5-_0x151927._0x5302cf,_0x5d1baa);}_0x501f35['defaultXField']='x',_0x501f35[_0x150e61(0x594,0x5b1)]='y',_0x501f35['defaultValueField']=_0x2f952c(0x402,_0x3b454e._0x551e62),_0x501f35[_0x2f952c(0x3ce,0x384)]={};var _0x56dda1=_0x501f35,_0x516ce6=function _0x374f38(){var _0x5a049d={_0x2366bc:0x12a,_0x1ad0b2:0x2d},_0x5b3553={_0x190fa7:0x15a},_0x3f7fa8={_0x2742a2:0x201},_0x39128a={_0x4997c5:0x329},_0x3999b8={_0x57d0b5:0x55d,_0x1313b1:0x8b,_0x201634:0x4c7,_0x4dc3b8:0x507},_0xad6fc9={_0xb4c88a:0x32b},_0x236a4f={_0x24bf3e:0x3c2},_0x2248ec={_0x2f0370:0x2eb},_0x887765={_0x18e9f8:0x19f,_0x43cb2e:0x1a4,_0x51d4ff:0x4ae,_0x17e3a9:0x523},_0x2d2106=function _0x59ce23(_0x11e1ff){var _0x4a79da={_0x212158:0x3d2},_0x4514ff={_0x11d417:0x27f};this[_0x330236(-0x236,-0x220)]={};function _0x49c9dc(_0x12a484,_0x22222c){return _0x1a38(_0x22222c-_0x4514ff._0x11d417,_0x12a484);}this[_0x49c9dc(0x46e,0x46b)]=[],this['_radi']=[],this['_min']=0xa,this[_0x330236(-_0x37126d._0x12afae,-_0x37126d._0x405d9b)]=0x1,this['_xField']=_0x11e1ff[_0x49c9dc(_0x37126d._0x59b256,0x44c)]||_0x11e1ff[_0x330236(-_0x37126d._0x390477,-_0x37126d._0x258c15)];function _0x330236(_0x27cddc,_0xa7bffa){return _0x1a38(_0x27cddc- -_0x4a79da._0x212158,_0xa7bffa);}this['_yField']=_0x11e1ff['yField']||_0x11e1ff[_0x330236(-0x1e0,-0x196)],this['_valueField']=_0x11e1ff[_0x49c9dc(0x4f7,0x4c8)]||_0x11e1ff['defaultValueField'],_0x11e1ff[_0x330236(-_0x37126d._0x357601,-_0x37126d._0xe99c04)]&&(this[_0x330236(-0x1d2,-0x1e7)]=_0x11e1ff['radius']);},_0x5f2e9a=_0x56dda1['defaultRadius'];return _0x2d2106['prototype']={'_organiseData':function(_0x2f432a,_0xe59cfc){var _0x18af7b={_0x58fd40:0x33e},_0x17f563=_0x2f432a[this['_xField']],_0x555f56=_0x2f432a[this[_0x33518a(-_0x887765._0x18e9f8,-_0x887765._0x43cb2e)]],_0x389a47=this['_radi'],_0x226652=this[_0x33518a(-0x116,-0x152)],_0x67fe82=this[_0x33518a(-0x124,-0x189)],_0x2eb1fb=this['_min'],_0x2b6ef1=_0x2f432a[this[_0x33518a(-0x1a0,-0x14a)]]||0x1,_0x240e52=_0x2f432a['radius']||this[_0xa93980(0x507,0x4f9)]||_0x5f2e9a;!_0x226652[_0x17f563]&&(_0x226652[_0x17f563]=[],_0x389a47[_0x17f563]=[]);function _0x33518a(_0xb2a4ce,_0x2841ad){return _0x1a38(_0x2841ad- -_0x18af7b._0x58fd40,_0xb2a4ce);}!_0x226652[_0x17f563][_0x555f56]?(_0x226652[_0x17f563][_0x555f56]=_0x2b6ef1,_0x389a47[_0x17f563][_0x555f56]=_0x240e52):_0x226652[_0x17f563][_0x555f56]+=_0x2b6ef1;var _0xf7b6b9=_0x226652[_0x17f563][_0x555f56];function _0xa93980(_0x2562fb,_0x2031a9){return _0x1a38(_0x2031a9-0x2f9,_0x2562fb);}if(_0xf7b6b9>_0x67fe82)return!_0xe59cfc?this[_0xa93980(0x466,_0x887765._0x51d4ff)]=_0xf7b6b9:this['setDataMax'](_0xf7b6b9),![];else{if(_0xf7b6b9<_0x2eb1fb)return!_0xe59cfc?this[_0xa93980(0x48f,0x4d2)]=_0xf7b6b9:this[_0xa93980(0x51c,_0x887765._0x17e3a9)](_0xf7b6b9),![];else{var _0xf7c014={};return _0xf7c014['x']=_0x17f563,_0xf7c014['y']=_0x555f56,_0xf7c014['value']=_0x2b6ef1,_0xf7c014[_0xa93980(0x482,0x4da)]=_0x240e52,_0xf7c014['min']=_0x2eb1fb,_0xf7c014['max']=_0x67fe82,_0xf7c014;}}},'_unOrganizeData':function(){var _0x4c590d={_0x2fbc96:0x2dc},_0x3f923c=[],_0x574c20=this[_0x14d5da(0x2e7,0x2ed)];function _0x3ae01b(_0x3c6329,_0x313e39){return _0x1a38(_0x313e39-_0x4c590d._0x2fbc96,_0x3c6329);}var _0x36ae5c=this[_0x3ae01b(0x444,_0x93d726._0x3933da)];for(var _0x2acfb2 in _0x574c20){for(var _0x187e08 in _0x574c20[_0x2acfb2]){var _0xeb0e45={};_0xeb0e45['x']=_0x2acfb2,_0xeb0e45['y']=_0x187e08,_0xeb0e45[_0x3ae01b(0x4e4,_0x93d726._0x415cb1)]=_0x36ae5c[_0x2acfb2][_0x187e08],_0xeb0e45[_0x3ae01b(_0x93d726._0x2151ab,0x4e7)]=_0x574c20[_0x2acfb2][_0x187e08],_0x3f923c['push'](_0xeb0e45);}}var _0x349bc8={};_0x349bc8['min']=this['_min'],_0x349bc8['max']=this[_0x14d5da(0x2b0,0x29b)],_0x349bc8['data']=_0x3f923c;function _0x14d5da(_0x723e3b,_0x29b9c8){return _0x1a38(_0x723e3b-0xfb,_0x29b9c8);}return _0x349bc8;},'_onExtremaChange':function(){function _0x2fb156(_0x1e9091,_0x2ecf83){return _0x1a38(_0x2ecf83-0x1b6,_0x1e9091);}function _0x3bdae0(_0x24f268,_0x5b610f){return _0x1a38(_0x24f268- -_0x2248ec._0x2f0370,_0x5b610f);}this['_coordinator']['emit'](_0x2fb156(0x35a,_0x236a4f._0x24bf3e),{'min':this[_0x3bdae0(-0x112,-0x16e)],'max':this[_0x2fb156(0x31c,0x36b)]});},'addData':function(){var _0x57cd15={_0x5e8ecc:0x2b3};function _0x3fba9d(_0x5d5ac8,_0x489658){return _0x1a38(_0x489658- -_0x57cd15._0x5e8ecc,_0x5d5ac8);}function _0x16df53(_0x111b3f,_0x8fd123){return _0x1a38(_0x111b3f-_0xad6fc9._0xb4c88a,_0x8fd123);}if(arguments[0x0]['length']>0x0){var _0x55ae20=arguments[0x0],_0x6aa0e2=_0x55ae20['length'];while(_0x6aa0e2--){this['addData']['call'](this,_0x55ae20[_0x6aa0e2]);}}else{var _0x2004d5=this[_0x16df53(0x55f,_0x3999b8._0x57d0b5)](arguments[0x0],!![]);_0x2004d5&&(this[_0x3fba9d(-_0x3999b8._0x1313b1,-0xc7)]['length']===0x0&&(this['_min']=this[_0x3fba9d(-0xb0,-0xfe)]=_0x2004d5['value']),this[_0x16df53(_0x3999b8._0x201634,0x4f8)]['emit'](_0x16df53(_0x3999b8._0x4dc3b8,0x564),{'min':this[_0x16df53(0x504,0x4cd)],'max':this['_max'],'data':[_0x2004d5]}));}return this;},'setData':function(_0x457851){var _0x481f81={_0x4ae00a:0x17d},_0x457e82=_0x457851[_0x41dce8(_0x43e382._0x727e8b,_0x43e382._0x35bf2c)],_0x3082b8=_0x457e82[_0x2d505e(0x24,-0x1)];function _0x2d505e(_0x661f35,_0x469936){return _0x1a38(_0x661f35- -_0x481f81._0x4ae00a,_0x469936);}this[_0x2d505e(_0x43e382._0x58681d,0x72)]=[],this['_radi']=[];for(var _0xa1b1dd=0x0;_0xa1b1dd<_0x3082b8;_0xa1b1dd++){this['_organiseData'](_0x457e82[_0xa1b1dd],![]);}this['_max']=_0x457851['max'],this['_min']=_0x457851[_0x2d505e(0x7e,0x43)]||0x0,this['_onExtremaChange']();function _0x41dce8(_0x7294c7,_0x399859){return _0x1a38(_0x7294c7- -0xf7,_0x399859);}return this['_coordinator']['emit']('renderall',this[_0x2d505e(0xa,-_0x43e382._0x33c407)]()),this;},'removeData':function(){},'setDataMax':function(_0x49a7fa){function _0x23005f(_0x41eb4d,_0x1239fd){return _0x1a38(_0x41eb4d- -_0x39128a._0x4997c5,_0x1239fd);}this['_max']=_0x49a7fa,this['_onExtremaChange']();function _0x5d9fd8(_0x1b9d73,_0x28c71b){return _0x1a38(_0x1b9d73-0x86,_0x28c71b);}return this[_0x23005f(-0x18d,-_0x825e9e._0x52f621)]['emit'](_0x23005f(-0xdd,-_0x825e9e._0x3af465),this[_0x5d9fd8(0x20d,0x225)]()),this;},'setDataMin':function(_0x4db93f){this['_min']=_0x4db93f;function _0x52646b(_0x3c4b04,_0x52c020){return _0x1a38(_0x52c020- -_0x3f7fa8._0x2742a2,_0x3c4b04);}this['_onExtremaChange'](),this['_coordinator'][_0x52646b(0x31,0x35)](_0x1aa99f(_0x5a049d._0x2366bc,0xf2),this[_0x1aa99f(0x9,_0x5a049d._0x1ad0b2)]());function _0x1aa99f(_0x1b36a8,_0x5f5de7){return _0x1a38(_0x5f5de7- -_0x5b3553._0x190fa7,_0x1b36a8);}return this;},'setCoordinator':function(_0x14d603){this['_coordinator']=_0x14d603;},'_getInternalData':function(){var _0xd7b89a={};_0xd7b89a['max']=this['_max'],_0xd7b89a[_0x329d00(0x76,0xc5)]=this['_min'],_0xd7b89a[_0x261a2b(-0xeb,-0x130)]=this[_0x261a2b(-_0x5dca41._0x1f5f36,-0x12d)];function _0x261a2b(_0x327a17,_0x1f59c8){return _0x1a38(_0x1f59c8- -0x319,_0x327a17);}function _0x329d00(_0x1870cc,_0x565fef){return _0x1a38(_0x565fef- -0x136,_0x1870cc);}return _0xd7b89a['radi']=this[_0x329d00(0x96,0x68)],_0xd7b89a;},'getData':function(){return this['_unOrganizeData']();}},_0x2d2106;}();function _0x2f952c(_0x5757d3,_0x12a1b4){return _0x1a38(_0x12a1b4-0x1fc,_0x5757d3);}var _0x476f64=function _0x1ec40b(){var _0x3b7a6e={_0x19f8be:0x14d,_0x5c924d:0xf0,_0x6804c2:0xa1,_0x3dde67:0xe2},_0x3a7298={_0x5e5019:0x54a,_0x5b7d67:0x113,_0x462f7c:0x506,_0x1bdb74:0x4cd,_0x559754:0x4ea,_0x40820c:0x488,_0x411d4d:0x47c,_0xa79c49:0x145},_0x1843ca={_0x4d210d:0x129},_0x420cce={_0xe37376:0xfd},_0x150806={_0x230885:0x94,_0x34c90b:0x132},_0x54c3aa={_0x1102d7:0x57},_0x2ea5d4={_0x267db:0x29b},_0x31db9d=function(_0x19e3dc){var _0x16fc23=_0x19e3dc['gradient']||_0x19e3dc['defaultGradient'],_0xad5fa3=document[_0x36de25(0x42e,_0x31585a._0x34b433)](_0x36de25(0x41f,_0x31585a._0x5a070b)),_0x6bac7={};_0x6bac7['willReadFrequently']=!![];var _0x20b1ba=_0xad5fa3[_0x36de25(0x419,0x407)]('2d',_0x6bac7);function _0x471e20(_0x4c5025,_0x27a54f){return _0x1a38(_0x4c5025-_0x2ea5d4._0x267db,_0x27a54f);}_0xad5fa3['width']=0x100,_0xad5fa3[_0x36de25(_0x31585a._0x633b20,_0x31585a._0x2f2916)]=0x1;var _0x164f3f=_0x20b1ba['createLinearGradient'](0x0,0x0,0x100,0x1);for(var _0x8f293c in _0x16fc23){_0x164f3f['addColorStop'](_0x8f293c,_0x16fc23[_0x8f293c]);}function _0x36de25(_0x273c62,_0x9a5669){return _0x1a38(_0x273c62-0x1f6,_0x9a5669);}return _0x20b1ba[_0x471e20(_0x31585a._0x557242,0x483)]=_0x164f3f,_0x20b1ba['fillRect'](0x0,0x0,0x100,0x1),_0x20b1ba['getImageData'](0x0,0x0,0x100,0x1)[_0x36de25(0x3df,0x3a1)];},_0x4dca94=function(_0x2a9f82,_0x117380){function _0x252a47(_0x1470b3,_0x389a3e){return _0x1a38(_0x1470b3-0x7e,_0x389a3e);}function _0xe77026(_0x457718,_0x4866f1){return _0x1a38(_0x457718-0x20b,_0x4866f1);}var _0x38db40=document['createElement'](_0xe77026(_0xc42f12._0x3b7922,0x432)),_0xae4de4={};_0xae4de4[_0x252a47(0x23c,0x252)]=!![];var _0x539e52=_0x38db40[_0xe77026(0x42e,_0xc42f12._0xa194e8)]('2d',_0xae4de4),_0x1756db=_0x2a9f82,_0x29e2a6=_0x2a9f82;_0x38db40['width']=_0x38db40['height']=_0x2a9f82*0x2;if(_0x117380==0x1)_0x539e52[_0x252a47(_0xc42f12._0x5db10e,0x2a6)](),_0x539e52['arc'](_0x1756db,_0x29e2a6,_0x2a9f82,0x0,0x2*Math['PI'],![]),_0x539e52['fillStyle']='rgba(0,0,0,1)',_0x539e52[_0xe77026(0x42b,0x491)]();else{var _0xb1caa0=_0x539e52[_0x252a47(0x286,0x24b)](_0x1756db,_0x29e2a6,_0x2a9f82*_0x117380,_0x1756db,_0x29e2a6,_0x2a9f82);_0xb1caa0['addColorStop'](0x0,'rgba(0,0,0,1)'),_0xb1caa0[_0x252a47(_0xc42f12._0x5c43a0,0x24b)](0x1,'rgba(0,0,0,0)'),_0x539e52['fillStyle']=_0xb1caa0,_0x539e52[_0x252a47(0x284,0x2bf)](0x0,0x0,0x2*_0x2a9f82,0x2*_0x2a9f82);}return _0x38db40;},_0x204835=function(_0x5f3162){var _0x16ebb4={_0x3c1077:0x29b},_0x100cc6=[],_0x37c590=_0x5f3162['min'],_0x176083=_0x5f3162[_0x2f164e(-0xb5,-0xee)];function _0x4bc4ee(_0x5f28d1,_0x16ca6b){return _0x1a38(_0x5f28d1-0x133,_0x16ca6b);}var _0x808b1=_0x5f3162['radi'],_0x5f3162=_0x5f3162[_0x4bc4ee(0x31c,0x36c)],_0x5b9d41=Object['keys'](_0x5f3162),_0x3501a2=_0x5b9d41[_0x2f164e(-0x122,-0xfa)];while(_0x3501a2--){var _0x1fdfcc=_0x5b9d41[_0x3501a2],_0x24a263=Object['keys'](_0x5f3162[_0x1fdfcc]),_0x2114a2=_0x24a263[_0x2f164e(-_0x1aeacb._0x312e28,-0xfa)];while(_0x2114a2--){var _0x178f24=_0x24a263[_0x2114a2],_0x30cd04=_0x5f3162[_0x1fdfcc][_0x178f24],_0x4c2140=_0x808b1[_0x1fdfcc][_0x178f24],_0x210e95={};_0x210e95['x']=_0x1fdfcc,_0x210e95['y']=_0x178f24,_0x210e95[_0x4bc4ee(0x33e,_0x1aeacb._0x124257)]=_0x30cd04,_0x210e95['radius']=_0x4c2140,_0x100cc6[_0x4bc4ee(_0x1aeacb._0x4ba526,0x3c6)](_0x210e95);}}function _0x2f164e(_0x3e8913,_0x519ee9){return _0x1a38(_0x519ee9- -_0x16ebb4._0x3c1077,_0x3e8913);}var _0x3f3aef={};return _0x3f3aef[_0x4bc4ee(0x32e,_0x1aeacb._0x1d709b)]=_0x37c590,_0x3f3aef['max']=_0x176083,_0x3f3aef['data']=_0x100cc6,_0x3f3aef;};function _0x54904d(_0x2101f3){function _0x3a777c(_0x122c84,_0x276e0d){return _0x1a38(_0x276e0d- -_0x54c3aa._0x1102d7,_0x122c84);}var _0x1c2fbf=_0x2101f3[_0x3a777c(0x1ab,0x17c)],_0x1d806f=this[_0x3a777c(0x18f,0x13e)]=document['createElement'](_0x1276c4(0x64,0x84));function _0x1276c4(_0x111e3c,_0x1e77c9){return _0x1a38(_0x111e3c- -0x1c5,_0x1e77c9);}var _0x2a748a=this['canvas']=_0x2101f3['canvas']||document[_0x1276c4(0x73,_0x4c04e4._0x8716b6)](_0x3a777c(0x175,_0x4c04e4._0x43e2ea));this[_0x3a777c(0x182,_0x4c04e4._0x53a4c3)]=[0x2710,0x2710,0x0,0x0];var _0x825634=getComputedStyle(_0x2101f3['container'])||{};_0x2a748a['className']=_0x1276c4(0x4c,0x2c),this['_width']=_0x2a748a[_0x3a777c(_0x4c04e4._0x20fb7c,_0x4c04e4._0x54463b)]=_0x1d806f['width']=_0x2101f3[_0x3a777c(0x15a,_0x4c04e4._0x54463b)]||+_0x825634['width'][_0x3a777c(_0x4c04e4._0x4d232b,_0x4c04e4._0x56544f)](/px/,''),this['_height']=_0x2a748a['height']=_0x1d806f['height']=_0x2101f3['height']||+_0x825634[_0x1276c4(0xf,-0x2)][_0x3a777c(0x112,_0x4c04e4._0x56544f)](/px/,'');var _0x49268e={};_0x49268e[_0x1276c4(-_0x4c04e4._0xc1efc,0x38)]=!![],this['shadowCtx']=_0x1d806f[_0x3a777c(_0x4c04e4._0x4aa82e,_0x4c04e4._0x4c84c0)]('2d',_0x49268e);var _0x202897={};_0x202897['willReadFrequently']=!![],this['ctx']=_0x2a748a[_0x3a777c(0x1d6,0x1cc)]('2d',_0x202897),_0x2a748a[_0x3a777c(0x20e,0x1b2)]['cssText']=_0x1d806f['style']['cssText']='position:absolute;left:0;top:0;',_0x1c2fbf['style'][_0x3a777c(0x124,0x159)]=_0x3a777c(0x1fd,_0x4c04e4._0x8ebc8e),_0x1c2fbf['appendChild'](_0x2a748a),this['_palette']=_0x31db9d(_0x2101f3),this[_0x1276c4(0x79,0x30)]={},this['_setStyles'](_0x2101f3);}return _0x54904d['prototype']={'renderPartial':function(_0x180e15){function _0x361ce5(_0x22cab4,_0x489815){return _0x1a38(_0x489815- -0x28f,_0x22cab4);}_0x180e15['data']['length']>0x0&&(this[_0x361ce5(-0x21,-0x7d)](_0x180e15),this['_colorize']());},'renderAll':function(_0x3521d1){var _0x4524b7={_0x584115:0x344},_0x2bfc8c={_0x2e4f2d:0x148};this[_0x490468(0x3e,_0x150806._0x230885)]();function _0x490468(_0x1e1d22,_0x50bdd9){return _0x1a38(_0x1e1d22- -_0x2bfc8c._0x2e4f2d,_0x50bdd9);}function _0x129349(_0x34b897,_0x6b8054){return _0x1a38(_0x6b8054- -_0x4524b7._0x584115,_0x34b897);}_0x3521d1['data']['length']>0x0&&(this[_0x129349(-0x150,-_0x150806._0x34c90b)](_0x204835(_0x3521d1)),this[_0x129349(-0x10e,-0x140)]());},'_updateGradient':function(_0x1f8949){function _0x5a37ff(_0x17ae28,_0x12a523){return _0x1a38(_0x17ae28- -0xd8,_0x12a523);}this[_0x5a37ff(0x138,_0x420cce._0xe37376)]=_0x31db9d(_0x1f8949);},'updateConfig':function(_0x375d01){var _0x50b6bc={_0x4768e6:0x391};function _0x10b08e(_0x1e028f,_0x25af00){return _0x1a38(_0x25af00-_0x50b6bc._0x4768e6,_0x1e028f);}_0x375d01[_0x10b08e(0x580,0x570)]&&this['_updateGradient'](_0x375d01),this['_setStyles'](_0x375d01);},'setDimensions':function(_0xbd4ead,_0x2539ac){var _0x4c2ac6={_0x151baf:0x32e};function _0x17c1d2(_0x480e32,_0x4e5839){return _0x1a38(_0x480e32- -_0x4c2ac6._0x151baf,_0x4e5839);}this[_0x454a00(0x386,0x3b7)]=_0xbd4ead,this[_0x454a00(0x33e,0x31e)]=_0x2539ac;function _0x454a00(_0x175450,_0x57fdf7){return _0x1a38(_0x175450-0x19f,_0x57fdf7);}this[_0x454a00(0x3c8,0x3b5)]['width']=this[_0x454a00(_0x545671._0x4d3f03,_0x545671._0x1c15c4)]['width']=_0xbd4ead,this[_0x17c1d2(-_0x545671._0xb40895,-0x119)][_0x17c1d2(-_0x545671._0x581e19,-0x103)]=this['shadowCanvas']['height']=_0x2539ac;},'_clear':function(){function _0x247a9f(_0x9606c2,_0x2f1889){return _0x1a38(_0x9606c2-_0x1843ca._0x4d210d,_0x2f1889);}this['shadowCtx'][_0x247a9f(_0x53a2bc._0x5bb220,0x326)](0x0,0x0,this[_0x247a9f(_0x53a2bc._0x1b50b8,0x36a)],this[_0x22ddbc(-_0x53a2bc._0x1db6b9,-0xf6)]);function _0x22ddbc(_0x3cdf0a,_0x3422c2){return _0x1a38(_0x3cdf0a- -0x286,_0x3422c2);}this['ctx'][_0x247a9f(0x36e,_0x53a2bc._0x34eaeb)](0x0,0x0,this['_width'],this['_height']);},'_setStyles':function(_0x1fb00e){this['_blur']=_0x1fb00e['blur']==0x0?0x0:_0x1fb00e[_0x6a37f8(0x517,_0x3a7298._0x5e5019)]||_0x1fb00e['defaultBlur'];_0x1fb00e['backgroundColor']&&(this['canvas']['style'][_0x418f19(-0x19a,-0x179)]=_0x1fb00e['backgroundColor']);function _0x418f19(_0x561946,_0x4309b9){return _0x1a38(_0x4309b9- -0x307,_0x561946);}this[_0x418f19(-_0x3a7298._0x5b7d67,-0x120)]=this[_0x6a37f8(_0x3a7298._0x462f7c,0x4f0)][_0x6a37f8(0x488,_0x3a7298._0x1bdb74)]=this[_0x418f19(-0x122,-0x172)][_0x6a37f8(0x488,_0x3a7298._0x559754)]=_0x1fb00e[_0x6a37f8(_0x3a7298._0x40820c,0x4de)]||this['_width'],this[_0x6a37f8(_0x3a7298._0x411d4d,0x43a)]=this['canvas']['height']=this['shadowCanvas']['height']=_0x1fb00e['height']||this['_height'],this[_0x6a37f8(0x494,0x44e)]=(_0x1fb00e[_0x418f19(-0x112,-0x116)]||0x0)*0xff,this['_maxOpacity']=(_0x1fb00e[_0x6a37f8(0x49e,0x497)]||_0x1fb00e['defaultMaxOpacity'])*0xff;function _0x6a37f8(_0x42a73f,_0x31052f){return _0x1a38(_0x42a73f-0x2dd,_0x31052f);}this['_minOpacity']=(_0x1fb00e[_0x418f19(-_0x3a7298._0xa79c49,-0xf4)]||_0x1fb00e['defaultMinOpacity'])*0xff,this['_useGradientOpacity']=!!_0x1fb00e['useGradientOpacity'];},'_drawAlpha':function(_0x178de6){var _0x206b3f={_0x19a799:0x323},_0x1c7bef=this[_0x440437(-0x104,-0xf9)]=_0x178de6[_0x335175(_0xb8fd17._0x54cea6,_0xb8fd17._0x5ad8d5)],_0x1071af=this['_max']=_0x178de6[_0x440437(-0x154,-0x125)];function _0x440437(_0x2c89a9,_0x3daa33){return _0x1a38(_0x3daa33- -0x2d2,_0x2c89a9);}function _0x335175(_0x21fe12,_0x315205){return _0x1a38(_0x315205-_0x206b3f._0x19a799,_0x21fe12);}var _0x178de6=_0x178de6[_0x440437(-_0xb8fd17._0x1e174f,-0xe9)]||[],_0x48bb10=_0x178de6['length'],_0x2bac43=0x1-this[_0x335175(0x44c,_0xb8fd17._0xb41fef)];while(_0x48bb10--){var _0x1c46bc=_0x178de6[_0x48bb10],_0x1b95e3=_0x1c46bc['x'],_0xf4f6c0=_0x1c46bc['y'],_0x1fe7c2=_0x1c46bc['radius'],_0x50cd38=Math[_0x440437(-0xfe,-0xd7)](_0x1c46bc[_0x335175(_0xb8fd17._0x3009f7,_0xb8fd17._0x883480)],_0x1071af),_0x345752=_0x1b95e3-_0x1fe7c2,_0x31e5af=_0xf4f6c0-_0x1fe7c2,_0x115ab2=this['shadowCtx'],_0x2af3ec;!this['_templates'][_0x1fe7c2]?this['_templates'][_0x1fe7c2]=_0x2af3ec=_0x4dca94(_0x1fe7c2,_0x2bac43):_0x2af3ec=this[_0x440437(-_0xb8fd17._0x523401,-_0xb8fd17._0x4ac573)][_0x1fe7c2];var _0x101e57=(_0x50cd38-_0x1c7bef)/(_0x1071af-_0x1c7bef);_0x115ab2[_0x335175(0x59e,_0xb8fd17._0x59ca00)]=_0x101e57<0.01?0.01:_0x101e57,_0x115ab2['drawImage'](_0x2af3ec,_0x345752,_0x31e5af),_0x345752<this[_0x335175(0x566,_0xb8fd17._0x54363e)][0x0]&&(this['_renderBoundaries'][0x0]=_0x345752),_0x31e5af<this[_0x440437(-_0xb8fd17._0x2a65c6,-_0xb8fd17._0x536043)][0x1]&&(this[_0x440437(-0xde,-0xc3)][0x1]=_0x31e5af),_0x345752+0x2*_0x1fe7c2>this[_0x440437(-0x83,-_0xb8fd17._0x536043)][0x2]&&(this[_0x440437(-_0xb8fd17._0x530325,-_0xb8fd17._0x536043)][0x2]=_0x345752+0x2*_0x1fe7c2),_0x31e5af+0x2*_0x1fe7c2>this[_0x440437(-0xb0,-0xc3)][0x3]&&(this['_renderBoundaries'][0x3]=_0x31e5af+0x2*_0x1fe7c2);}},'_colorize':function(){var _0x4fafc9=this[_0x1d591a(0x45d,0x4aa)][0x0],_0x55778b=this['_renderBoundaries'][0x1],_0x53ee5e=this['_renderBoundaries'][0x2]-_0x4fafc9,_0xf68260=this[_0x2991f7(_0x3716fd._0x24c273,0x153)][0x3]-_0x55778b,_0x4dfd59=this[_0x2991f7(_0x3716fd._0x71086f,_0x3716fd._0x3440b5)],_0x4beaf6=this['_height'],_0x32edda=this['_opacity'],_0x46a02a=this['_maxOpacity'],_0x1a44b9=this[_0x2991f7(_0x3716fd._0x4ab090,_0x3716fd._0x34149a)],_0x2d3f86=this[_0x2991f7(0x12f,0x112)];_0x4fafc9<0x0&&(_0x4fafc9=0x0);_0x55778b<0x0&&(_0x55778b=0x0);_0x4fafc9+_0x53ee5e>_0x4dfd59&&(_0x53ee5e=_0x4dfd59-_0x4fafc9);function _0x1d591a(_0x3d069f,_0x1a0975){return _0x1a38(_0x1a0975-0x29b,_0x3d069f);}_0x55778b+_0xf68260>_0x4beaf6&&(_0xf68260=_0x4beaf6-_0x55778b);var _0x18a2eb=this['shadowCtx']['getImageData'](_0x4fafc9,_0x55778b,_0x53ee5e,_0xf68260),_0x56e533=_0x18a2eb[_0x2991f7(0x142,0x11f)];function _0x2991f7(_0x790d28,_0xbe7633){return _0x1a38(_0x790d28- -0xa7,_0xbe7633);}var _0x943555=_0x56e533['length'],_0x1287a8=this['_palette'];for(var _0x59213b=0x3;_0x59213b<_0x943555;_0x59213b+=0x4){var _0x12cdc9=_0x56e533[_0x59213b],_0x3cdafa=_0x12cdc9*0x4;if(!_0x3cdafa)continue;var _0x8a6dfa;_0x32edda>0x0?_0x8a6dfa=_0x32edda:_0x12cdc9<_0x46a02a?_0x12cdc9<_0x1a44b9?_0x8a6dfa=_0x1a44b9:_0x8a6dfa=_0x12cdc9:_0x8a6dfa=_0x46a02a,_0x56e533[_0x59213b-0x3]=_0x1287a8[_0x3cdafa],_0x56e533[_0x59213b-0x2]=_0x1287a8[_0x3cdafa+0x1],_0x56e533[_0x59213b-0x1]=_0x1287a8[_0x3cdafa+0x2],_0x56e533[_0x59213b]=_0x2d3f86?_0x1287a8[_0x3cdafa+0x3]:_0x8a6dfa;}this['ctx'][_0x1d591a(_0x3716fd._0x4689cb,_0x3716fd._0x523063)](_0x18a2eb,_0x4fafc9,_0x55778b),this['_renderBoundaries']=[0x3e8,0x3e8,0x0,0x0];},'getValueAt':function(_0x4e98c4){var _0x3ec39b,_0x4f50dd=this['shadowCtx'],_0x88a3aa=_0x4f50dd['getImageData'](_0x4e98c4['x'],_0x4e98c4['y'],0x1,0x1);function _0x77d842(_0x2308a7,_0x3780fb){return _0x1a38(_0x3780fb-0x1fd,_0x2308a7);}function _0xcfa5f8(_0x205efe,_0x5ea2b9){return _0x1a38(_0x5ea2b9- -0xf9,_0x205efe);}var _0x4ad469=_0x88a3aa[_0xcfa5f8(_0x3b7a6e._0x19f8be,_0x3b7a6e._0x5c924d)][0x3],_0x11c296=this['_max'],_0x2991e3=this['_min'];return _0x3ec39b=Math[_0xcfa5f8(_0x3b7a6e._0x6804c2,_0x3b7a6e._0x3dde67)](_0x11c296-_0x2991e3)*(_0x4ad469/0xff)>>0x0,_0x3ec39b;},'getDataURL':function(){return this['canvas']['toDataURL']();}},_0x54904d;}(),_0x1c8857=function _0xca3444(){var _0x52f782=![];function _0x5c99f2(_0x3824d0,_0x3534b4){return _0x2f952c(_0x3824d0,_0x3534b4-0x16b);}return _0x56dda1['defaultRenderer']===_0x5c99f2(_0xcbcbf7._0x195c4c,0x574)&&(_0x52f782=_0x476f64),_0x52f782;}(),_0x341420={};_0x341420['merge']=function(){var _0x408dfb={},_0x4da78c=arguments['length'];for(var _0x45ec09=0x0;_0x45ec09<_0x4da78c;_0x45ec09++){var _0x4c8d5b=arguments[_0x45ec09];for(var _0x1d5043 in _0x4c8d5b){_0x408dfb[_0x1d5043]=_0x4c8d5b[_0x1d5043];}}return _0x408dfb;};var _0x54cd59=_0x341420,_0x53d642=function _0x4b9b17(){var _0x3dbda7={_0x291de6:0x536},_0x4b0906={_0x4855ab:0x179,_0x27bb0c:0x213,_0x2fb1fb:0x257,_0x593d5e:0x37c,_0x1642f3:0x34e},_0xdeb23d={_0x44d1ce:0x88,_0x47aec0:0xd7},_0x539f1f={_0x386aaa:0x79,_0x321ce8:0x5dd},_0x2ad62a={_0x597f0b:0x3a7},_0x21b043={_0x516a49:0x2ba},_0x350feb={_0x485da9:0x2a1},_0x3077d2={_0x553986:0x5c},_0x1bf977={_0x28db98:0x230},_0x2fb83b={_0x77a582:0x14a,_0x279d3d:0x5a,_0x4f488a:0x68},_0xb8f831={_0x160c46:0x8b,_0x5b5371:0x42},_0x426cc4={_0x4f1cd1:0x167},_0x26e11d={_0x52fe03:0x1b6},_0x1baabb=function _0x177d1b(){function _0x5236d0(_0x3e6b13,_0x20a090){return _0x1a38(_0x3e6b13- -0x37d,_0x20a090);}function _0x2783b1(){function _0x27ed76(_0x426710,_0x264e4c){return _0x1a38(_0x426710-0x1a,_0x264e4c);}this[_0x27ed76(0x216,_0x26e11d._0x52fe03)]={};}return _0x2783b1[_0x5236d0(-0x15c,-_0x426cc4._0x4f1cd1)]={'on':function(_0x5064c0,_0x10f252,_0x22d002){var _0x5b5410={_0x6780fe:0x56f},_0x8bdd86={_0x3576bf:0x3d9},_0x599169=this['cStore'];!_0x599169[_0x5064c0]&&(_0x599169[_0x5064c0]=[]),_0x599169[_0x5064c0]['push'](function(_0x19651b){function _0xc923d9(_0x5779b8,_0x589d6d){return _0x1a38(_0x589d6d-_0x8bdd86._0x3576bf,_0x5779b8);}return _0x10f252[_0xc923d9(0x572,_0x5b5410._0x6780fe)](_0x22d002,_0x19651b);});},'emit':function(_0x15fc06,_0x4624db){var _0x3bfcd8=this['cStore'];if(_0x3bfcd8[_0x15fc06]){var _0x48e226=_0x3bfcd8[_0x15fc06]['length'];for(var _0x29cc07=0x0;_0x29cc07<_0x48e226;_0x29cc07++){var _0x3b3328=_0x3bfcd8[_0x15fc06][_0x29cc07];_0x3b3328(_0x4624db);}}}},_0x2783b1;}(),_0x18537b=function(_0x502136){var _0x420c4c={_0x4aa2fa:0x60},_0x58e4fb=_0x502136[_0x4f0475(0x1f0,0x1e5)],_0xc0b30d=_0x502136['_coordinator'],_0x25d92b=_0x502136['_store'];function _0x4f0475(_0x5e64bc,_0x453733){return _0x1a38(_0x5e64bc- -_0x420c4c._0x4aa2fa,_0x453733);}_0xc0b30d['on']('renderpartial',_0x58e4fb[_0x4f0475(0x137,_0x2fb83b._0x77a582)],_0x58e4fb);function _0x1eae83(_0x4e85ff,_0x583950){return _0x1a38(_0x583950- -0x230,_0x4e85ff);}_0xc0b30d['on'](_0x1eae83(_0x2fb83b._0x279d3d,0x1c),_0x58e4fb[_0x1eae83(-_0x2fb83b._0x4f488a,-0x41)],_0x58e4fb),_0xc0b30d['on']('extremachange',function(_0x544f41){var _0x4b53bb={_0x3cfe9a:0xe5};function _0x2c0177(_0x336278,_0x43c463){return _0x4f0475(_0x336278- -0x1c4,_0x43c463);}function _0x4324b2(_0x325874,_0x49b9c0){return _0x1eae83(_0x325874,_0x49b9c0-_0x4b53bb._0x3cfe9a);}_0x502136[_0x2c0177(-0x2d,-0x11)]['onExtremaChange']&&_0x502136['_config'][_0x4324b2(_0xb8f831._0x160c46,_0xb8f831._0x5b5371)]({'min':_0x544f41['min'],'max':_0x544f41['max'],'gradient':_0x502136['_config']['gradient']||_0x502136['_config']['defaultGradient']});}),_0x25d92b[_0x1eae83(-0x9,-0x5f)](_0xc0b30d);};function _0x4967c5(){function _0x21dce3(_0x35b9d2,_0x150262){return _0x1a38(_0x150262-_0x1bf977._0x28db98,_0x35b9d2);}var _0x2e8241=this['_config']=_0x54cd59['merge'](_0x56dda1,arguments[0x0]||{});function _0x35a092(_0x367307,_0x5d66a3){return _0x1a38(_0x5d66a3- -_0x3077d2._0x553986,_0x367307);}this['_coordinator']=new _0x1baabb();if(_0x2e8241['plugin']){var _0x1f00ae=_0x2e8241['plugin'];if(!_0x56dda1['plugins'][_0x1f00ae])throw new Error('Plugin\x20\x27'+_0x1f00ae+'\x27\x20not\x20found.\x20Maybe\x20it\x20was\x20not\x20registered.');else{var _0x32fa72=_0x56dda1['plugins'][_0x1f00ae];this['_renderer']=new _0x32fa72[(_0x21dce3(0x442,0x3e9))](_0x2e8241),this[_0x35a092(_0x37afe6._0x5186b2,_0x37afe6._0x382a1b)]=new _0x32fa72['store'](_0x2e8241);}}else this['_renderer']=new _0x1c8857(_0x2e8241),this['_store']=new _0x516ce6(_0x2e8241);_0x18537b(this);}return _0x4967c5['prototype']={'addData':function(){return this['_store']['addData']['apply'](this['_store'],arguments),this;},'removeData':function(){var _0x441c3e={_0x39ec55:0x29a};function _0x27f0c0(_0x78d556,_0x408ecf){return _0x1a38(_0x78d556- -0x226,_0x408ecf);}this['_store']['removeData']&&this[_0x27f0c0(-0x24,_0x2fdb11._0x5b59d9)]['removeData'][_0x27f0c0(-0x49,-_0x2fdb11._0x4bca11)](this['_store'],arguments);function _0x332340(_0x544aa7,_0x169560){return _0x1a38(_0x169560- -_0x441c3e._0x39ec55,_0x544aa7);}return this;},'setData':function(){this[_0xa53bb2(-0xb8,-0xd3)][_0xa53bb2(-_0x551b97._0x296fc3,-0x90)][_0x522d88(0x47e,_0x551b97._0x52b461)](this[_0x522d88(_0x551b97._0x1c5a9f,0x490)],arguments);function _0x522d88(_0x3da9a2,_0x935108){return _0x1a38(_0x3da9a2-_0x350feb._0x485da9,_0x935108);}function _0xa53bb2(_0x3df644,_0x17db49){return _0x1a38(_0x3df644- -_0x21b043._0x516a49,_0x17db49);}return this;},'setDataMax':function(){function _0x5a09e9(_0x4cef07,_0xf81cc0){return _0x1a38(_0xf81cc0-_0x2ad62a._0x597f0b,_0x4cef07);}function _0x581855(_0x5cf38a,_0x5d43f6){return _0x1a38(_0x5d43f6- -0x256,_0x5cf38a);}return this['_store']['setDataMax'][_0x581855(-0x18,-_0x539f1f._0x386aaa)](this[_0x5a09e9(_0x539f1f._0x321ce8,0x5a9)],arguments),this;},'setDataMin':function(){return this['_store']['setDataMin']['apply'](this['_store'],arguments),this;},'configure':function(_0x50c942){this[_0x5571d5(0x26,_0xdeb23d._0x44d1ce)]=_0x54cd59['merge'](this[_0x5571d5(_0xdeb23d._0x47aec0,0x88)],_0x50c942);function _0xc9053e(_0x2fd783,_0x5994be){return _0x1a38(_0x2fd783- -0x11e,_0x5994be);}this[_0x5571d5(0x11e,0xe1)]['updateConfig'](this['_config']),this['_coordinator']['emit']('renderall',this['_store'][_0x5571d5(0x55,0x18)]());function _0x5571d5(_0x37608a,_0x57f4c0){return _0x1a38(_0x57f4c0- -0x16f,_0x37608a);}return this;},'repaint':function(){function _0x551685(_0x153ebb,_0x5e6d9e){return _0x1a38(_0x153ebb- -0x23,_0x5e6d9e);}function _0x30c35c(_0x145635,_0x180f27){return _0x1a38(_0x145635-0x130,_0x180f27);}return this[_0x551685(_0x4b0906._0x4855ab,0x14d)][_0x551685(_0x4b0906._0x27bb0c,_0x4b0906._0x2fb1fb)](_0x30c35c(_0x4b0906._0x593d5e,_0x4b0906._0x1642f3),this['_store'][_0x30c35c(0x2b7,0x29f)]()),this;},'getData':function(){return this['_store']['getData']();},'getDataURL':function(){return this['_renderer']['getDataURL']();},'getValueAt':function(_0x1514c6){var _0x57a04c={_0x4ce9b1:0x38a};function _0x237721(_0x34f6ce,_0x1b3ccf){return _0x1a38(_0x1b3ccf-_0x57a04c._0x4ce9b1,_0x34f6ce);}if(this['_store']['getValueAt'])return this['_store']['getValueAt'](_0x1514c6);else return this['_renderer']['getValueAt']?this['_renderer'][_0x237721(0x51d,_0x3dbda7._0x291de6)](_0x1514c6):null;}},_0x4967c5;}(),_0x18b1b3={'create':function(_0x318807){return new _0x53d642(_0x318807);},'register':function(_0x26e968,_0x3ff142){function _0x5c664a(_0x838483,_0x36cef0){return _0x2f952c(_0x36cef0,_0x838483- -0x214);}_0x56dda1[_0x5c664a(_0x3aece0._0x1e4c27,_0x3aece0._0x33eddd)][_0x26e968]=_0x3ff142;}};return _0x18b1b3;}));}(heatmap$1));var heatmap=heatmap$1['exports'],_0x4e093c={};_0x4e093c[_0x297e6d(0x4ea,0x4bc)]=null,_0x4e093c['default']=heatmap;var h337=_mergeNamespaces(_0x4e093c,[heatmap$1[_0x297e6d(0x4ae,0x456)]]),HeatMaterial='uniform\x20sampler2D\x20image;\x0a\x0aczm_material\x20czm_getMaterial(czm_materialInput\x20materialInput)\x20{\x0a\x20\x20czm_material\x20material\x20=\x20czm_getDefaultMaterial(materialInput);\x0a\x20\x20vec2\x20st\x20=\x20materialInput.st;\x0a\x20\x20vec4\x20colorImage\x20=\x20texture(image,\x20st);\x0a\x20\x20if(colorImage.rgb\x20==\x20vec3(1.0)\x20||\x20colorImage.rgb\x20==\x20vec3(0.0))\x20{\x0a\x20\x20\x20\x20discard;\x0a\x20\x20}\x0a\x20\x20material.diffuse\x20=\x20colorImage.rgb;\x0a\x20\x20material.alpha\x20=\x20colorImage.a;\x0a\x20\x20return\x20material;\x0a}\x0a';if(!heatmap$1['exports'][_0x297e6d(0x4de,0x4b4)])throw new Error('请引入\x20heatmap.js\x20库\x20');const Cesium=mars3d__namespace[_0x2d6513(0x24f,0x282)],BaseLayer=mars3d__namespace['layer'][_0x2d6513(0x1f5,0x222)];function _0x297e6d(_0x3f7c1d,_0x382d0b){var _0x25667c={_0x41705b:0x2af};return _0x1a38(_0x3f7c1d-_0x25667c._0x41705b,_0x382d0b);}var _0xaf5561={};_0xaf5561['0.4']='blue',_0xaf5561['0.6']=_0x297e6d(0x47f,0x46f),_0xaf5561['0.8']=_0x297e6d(0x4d4,0x48d),_0xaf5561['0.9']='red';var _0x5bbd67={};_0x5bbd67['maxOpacity']=0.8,_0x5bbd67[_0x297e6d(0x4c2,0x4fa)]=0.1,_0x5bbd67['blur']=0.85,_0x5bbd67[_0x2d6513(0x279,0x23d)]=0x19,_0x5bbd67[_0x2d6513(0x27c,0x23b)]=_0xaf5561;const DEF_HEATSTYLE=_0x5bbd67;var _0x393e7f={};_0x393e7f[_0x297e6d(0x46e,0x48b)]=1.5,_0x393e7f['arcBlurScale']=1.5,_0x393e7f[_0x297e6d(0x4e6,0x4be)]=Cesium['EllipsoidSurfaceAppearance']['VERTEX_FORMAT'];const DEF_STYLE=_0x393e7f;class HeatLayer extends BaseLayer{constructor(_0x4f7091={}){var _0x15370e={_0x1ac079:0xe5,_0x32f753:0xa4,_0x26ae30:0x8d,_0x1f8219:0x94,_0x10b7e4:0xe5,_0x5bc3dc:0x104};function _0x2a4829(_0x5a0a08,_0x46adbd){return _0x297e6d(_0x5a0a08- -0x534,_0x46adbd);}super(_0x4f7091),this[_0x2a4829(-_0x15370e._0x1ac079,-_0x15370e._0x32f753)][_0x56768f(-0x48,-_0x15370e._0x26ae30)]=this[_0x56768f(-0x8c,-0xf1)]['redrawRatio']||0x1,this['options']['heatStyle']={...DEF_HEATSTYLE,...this['options'][_0x2a4829(-0x3e,-_0x15370e._0x1f8219)]};function _0x56768f(_0x2f619a,_0x339604){return _0x297e6d(_0x2f619a- -0x4db,_0x339604);}this[_0x2a4829(-_0x15370e._0x10b7e4,-_0x15370e._0x5bc3dc)]['style']={...DEF_STYLE,...this['options']['style']};}get['layer'](){var _0x58b274={_0x1118bc:0x286},_0x2f04ab={_0x4be2fd:0x1cf};function _0x31f5c3(_0x569804,_0x5baf59){return _0x297e6d(_0x5baf59- -_0x2f04ab._0x4be2fd,_0x569804);}return this[_0x31f5c3(0x23d,_0x58b274._0x1118bc)];}get['heatStyle'](){return this['options']['heatStyle'];}set['heatStyle'](_0x565081){var _0x18ffd4={_0x2ac0b2:0x2a3,_0x17d3de:0x272,_0x294933:0x1b1,_0x12485b:0x1d3,_0x1969a0:0x172,_0x4364bd:0x2e9,_0x7c7b18:0x330,_0x62e6ea:0x2e3},_0x168cb2={_0x47bd7f:0x1c3};function _0x761519(_0x5ba1c4,_0xd84796){return _0x2d6513(_0x5ba1c4,_0xd84796- -0x31);}this['options'][_0x761519(_0x18ffd4._0x2ac0b2,_0x18ffd4._0x17d3de)]=mars3d__namespace[_0x761519(_0x18ffd4._0x294933,_0x18ffd4._0x12485b)][_0x761519(_0x18ffd4._0x1969a0,0x1cf)](this['options'][_0x761519(0x280,0x272)],_0x565081);function _0x189cc1(_0x3d2318,_0x24321c){return _0x297e6d(_0x24321c- -_0x168cb2._0x47bd7f,_0x3d2318);}if(this[_0x189cc1(0x27f,0x2cc)]){this[_0x189cc1(_0x18ffd4._0x4364bd,0x2cc)]['configure'](this[_0x761519(0x16c,0x1cb)]['heatStyle']);const _0x5a74f2=this['_heat']['_renderer']['canvas'][_0x189cc1(0x302,_0x18ffd4._0x7c7b18)]('image/png',0x1);this[_0x189cc1(_0x18ffd4._0x62e6ea,0x29a)](_0x5a74f2);}}get['style'](){var _0x1cd37f={_0x494ec3:0x129};function _0x50c96f(_0x15da2c,_0x1eb101){return _0x297e6d(_0x1eb101- -0x2ed,_0x15da2c);}function _0x5072b5(_0x2abfe6,_0x124265){return _0x2d6513(_0x2abfe6,_0x124265- -_0x1cd37f._0x494ec3);}return this[_0x50c96f(0x1b1,0x162)][_0x50c96f(0x18b,0x1cb)];}set[_0x2d6513(0x234,0x265)](_0x22aa24){var _0x2414d4={_0x36b44a:0x3e0,_0x426599:0x3db},_0x3549e4={_0x3a4b30:0x4},_0x1d1112={_0x589366:0xdd};function _0x55c6ed(_0x29a300,_0x3ffcda){return _0x297e6d(_0x3ffcda- -_0x1d1112._0x589366,_0x29a300);}function _0xfe3397(_0x55aecb,_0x58463a){return _0x2d6513(_0x55aecb,_0x58463a-_0x3549e4._0x3a4b30);}this[_0x55c6ed(0x375,0x372)][_0x55c6ed(_0x2414d4._0x36b44a,_0x2414d4._0x426599)]=mars3d__namespace['Util']['merge'](this[_0xfe3397(0x1c8,0x200)]['style'],_0x22aa24);}get['positions'](){return this['_positions'];}set['positions'](_0x43dfe9){var _0x50d75e={_0x37d419:0x541},_0x2f3da9={_0xff20f9:0x2c0};function _0x4777aa(_0x1db75d,_0x2edc54){return _0x2d6513(_0x2edc54,_0x1db75d-_0x2f3da9._0xff20f9);}this[_0x4777aa(0x54c,_0x50d75e._0x37d419)](_0x43dfe9);}get['coordinates'](){var _0x287ccb={_0x2fcdd3:0x44a,_0x1a35d6:0x17b},_0x16d166={_0xe01ec4:0x20e};const _0x204b06=[];return this['points']['forEach'](_0xcd4103=>{function _0x4fa50b(_0x5b2c59,_0x424ec1){return _0x1a38(_0x5b2c59-_0x16d166._0xe01ec4,_0x424ec1);}function _0x31359d(_0x19139d,_0x437659){return _0x1a38(_0x19139d- -0x325,_0x437659);}_0x204b06[_0x4fa50b(0x45d,_0x287ccb._0x2fcdd3)](_0xcd4103[_0x31359d(-_0x287ccb._0x1a35d6,-0x124)]());}),_0x204b06;}get[_0x297e6d(0x45e,0x4be)](){return this['_rectangle'];}['_setOptionsHook'](_0x3b6c14,_0x2852eb){var _0x5acd78={_0x267d0f:0x401,_0x415266:0x3f0};function _0x38d559(_0x39424f,_0x20c5c1){return _0x297e6d(_0x20c5c1- -0xf4,_0x39424f);}_0x3b6c14['positions']&&(this[_0x38d559(_0x5acd78._0x267d0f,_0x5acd78._0x415266)]=_0x3b6c14['positions']);}[_0x297e6d(0x469,0x4bf)](){var _0x2f541b={_0x1fa098:0x7f,_0x12004d:0x1b};function _0x56e4de(_0x386e02,_0x77aae6){return _0x297e6d(_0x77aae6- -0x339,_0x386e02);}function _0x3050f8(_0x408e18,_0x217aed){return _0x297e6d(_0x408e18- -0x44d,_0x217aed);}if(this['style'][_0x3050f8(0xb6,0xfa)]==='image'){var _0x33b27e={};_0x33b27e[_0x3050f8(_0x2f541b._0x1fa098,0x4c)]='EPSG:3857',_0x33b27e[_0x3050f8(0x22,_0x2f541b._0x12004d)]=!![],this['_layer']=new mars3d__namespace['layer']['ImageLayer'](_0x33b27e);}else{var _0x1f223b={};_0x1f223b['private']=!![],this[_0x56e4de(0x149,0x11c)]=new mars3d__namespace['layer'][(_0x56e4de(0xe5,0x108))](_0x1f223b);}}['_addedHook'](){var _0x3fbd68={_0x5d0bf2:0x13c,_0x25defd:0x3d5,_0x30755a:0x3a9,_0x2bfc9b:0x36a,_0x14baf4:0x379,_0x47034d:0x3c7,_0x1ff072:0x3db,_0x4dcf26:0xc7,_0x5bbb7b:0x132,_0x376090:0x149,_0x27bb6d:0xb9,_0x10fc7e:0x12f,_0x14bea6:0x36b},_0x4bde89={_0x197f1d:0x109},_0x5c9c08={_0x11eeeb:0x388};this['_map']['addLayer'](this['_layer']),this['_container']=mars3d__namespace[_0x5201cb(0x128,_0x3fbd68._0x5d0bf2)][_0x287dcc(0x3f3,_0x3fbd68._0x25defd)]('div',_0x287dcc(_0x3fbd68._0x30755a,_0x3fbd68._0x2bfc9b),this[_0x287dcc(0x398,0x339)][_0x287dcc(0x3a8,_0x3fbd68._0x14baf4)]);this[_0x287dcc(0x334,0x346)][_0x5201cb(0x15c,0x152)]&&(this[_0x5201cb(0x15c,0x158)]=this[_0x287dcc(0x303,0x346)][_0x287dcc(_0x3fbd68._0x47034d,_0x3fbd68._0x1ff072)]);function _0x5201cb(_0xfe621b,_0x577722){return _0x297e6d(_0xfe621b- -_0x5c9c08._0x11eeeb,_0x577722);}this[_0x5201cb(_0x3fbd68._0x4dcf26,0xed)]['redrawZoom']&&(this['_map']['on'](mars3d__namespace[_0x5201cb(0x141,_0x3fbd68._0x5bbb7b)]['cameraMoveEnd'],this[_0x5201cb(_0x3fbd68._0x376090,0x1ab)],this),this['_onCameraMoveEnd']());function _0x287dcc(_0x28509c,_0x1e0152){return _0x297e6d(_0x1e0152- -_0x4bde89._0x197f1d,_0x28509c);}this[_0x5201cb(0xc7,_0x3fbd68._0x27bb6d)][_0x5201cb(0xec,_0x3fbd68._0x10fc7e)]&&this[_0x287dcc(0x349,_0x3fbd68._0x14bea6)]();}['_removedHook'](){var _0x2528d4={_0x5bf666:0x343,_0x124203:0x330,_0x243995:0x2cb},_0x2c7fb9={_0x9996ea:0x24c};function _0x23343e(_0x31c3b1,_0x3939bf){return _0x2d6513(_0x3939bf,_0x31c3b1-_0x2c7fb9._0x9996ea);}function _0x49eb0c(_0x4bd317,_0x241142){return _0x2d6513(_0x4bd317,_0x241142-0xba);}this[_0x49eb0c(0x2aa,0x2b6)]['redrawZoom']&&this[_0x49eb0c(0x30d,0x2a9)][_0x49eb0c(0x2fb,_0x2528d4._0x5bf666)](mars3d__namespace[_0x49eb0c(0x387,_0x2528d4._0x124203)][_0x49eb0c(_0x2528d4._0x243995,0x2ed)],this[_0x23343e(0x4ca,0x4c1)],this),this['_container']&&(mars3d__namespace['DomUtil']['remove'](this['_container']),delete this['_container']),this['clear'](),this[_0x23343e(0x43b,0x427)]['removeLayer'](this['_layer']);}[_0x2d6513(0x29a,0x29d)](_0x25acfc){_0x25acfc&&this['_updatePositionsHook']();}['addPosition'](_0x12f634){this['_positions']=this['_positions']||[];function _0x3f64d5(_0x1debca,_0x5d40e6){return _0x297e6d(_0x1debca- -0x31e,_0x5d40e6);}this['_positions']['push'](_0x12f634),this[_0x3f64d5(0x1c2,0x1b2)]();}[_0x297e6d(0x4df,0x4d9)](_0xfeac50){this['_positions']=_0xfeac50,this['_updatePositionsHook']();}['clear'](){var _0x1beaca={_0x1bf4aa:0xca,_0x1eeb78:0x175,_0x2ac845:0xef};function _0x31decf(_0x3a244c,_0x235188){return _0x297e6d(_0x235188- -0x5ca,_0x3a244c);}function _0x50dc46(_0x3263f4,_0x484fe1){return _0x297e6d(_0x3263f4- -0xb4,_0x484fe1);}this[_0x31decf(-0xdb,-_0x1beaca._0x1bf4aa)]&&(this[_0x31decf(-0x1a2,-_0x1beaca._0x1eeb78)][_0x31decf(-0xaf,-_0x1beaca._0x2ac845)](this['_graphic'],!![]),delete this[_0x31decf(-0x76,-_0x1beaca._0x1bf4aa)]),this[_0x50dc46(0x402,0x428)]&&(this['_layer']['removeGraphic'](this[_0x50dc46(0x402,0x3d5)],!![]),delete this['_graphic2']);}['_updatePositionsHook'](){var _0x433e6b={_0x1afd15:0x1a0,_0x54f9dc:0x13d,_0x32dc02:0x365},_0x2fd5a7={_0x119436:0x38f};function _0x3df6bd(_0x3e3634,_0x2e2f41){return _0x2d6513(_0x2e2f41,_0x3e3634- -_0x2fd5a7._0x119436);}if(!this['show']||!this[_0x3df6bd(-_0x433e6b._0x1afd15,-0x166)]||!this['positions']||this['positions']['length']===0x0)return this;const _0x3b6699=this[_0x3df6bd(-_0x433e6b._0x54f9dc,-0x174)]();function _0xbbb6ad(_0x15d82f,_0x2ce0ca){return _0x297e6d(_0x15d82f- -0xef,_0x2ce0ca);}return this[_0xbbb6ad(0x36e,_0x433e6b._0x32dc02)](_0x3b6699),this;}[_0x2d6513(0x1c2,0x224)](_0x188166){var _0x3ff697={_0x3553d0:0x5a9},_0x1c036f={_0x5dd0fc:0xa8};function _0x12d0d2(_0x46e05b,_0x9af284){return _0x297e6d(_0x46e05b-_0x1c036f._0x5dd0fc,_0x9af284);}return _0x188166!==null&&_0x188166!==void 0x0&&_0x188166['isFormat']&&this['_rectangle']?mars3d__namespace['PolyUtil']['formatRectangle'](this[_0x12d0d2(_0x3ff697._0x3553d0,0x5c2)]):this['_rectangle'];}['_onCameraMoveEnd'](){var _0x4455d4={_0x50cdf4:0x8c,_0x45cf37:0x368,_0x418dcf:0x428,_0x432806:0x3d0},_0x2b5726={_0x392ad8:0x4ce};if(!this[_0x76fe38(0x371,0x3b5)]||!this[_0x3461aa(0x83,0x21)]||!this[_0x3461aa(-0x6a,-_0x4455d4._0x50cdf4)])return;let _0xd7fc8;const _0x39e7e3=getSurfaceDistance(this[_0x76fe38(0x361,_0x4455d4._0x45cf37)][_0x76fe38(0x471,_0x4455d4._0x418dcf)])/0x2;function _0x76fe38(_0x1abff7,_0x5db51f){return _0x297e6d(_0x5db51f- -0xda,_0x1abff7);}if(_0x39e7e3&&_0x39e7e3<this['_bounds'][_0x3461aa(-0x85,-0x3e)]){const _0x566668=this[_0x76fe38(_0x4455d4._0x432806,0x375)]['redrawRatio']*_0x39e7e3/this['_bounds'][_0x76fe38(0x3ed,0x3b6)];_0xd7fc8=this['heatStyle']['radius']*_0x566668,_0xd7fc8=Math['max'](_0xd7fc8,0x2);}else _0xd7fc8=this['heatStyle']['radius'];function _0x3461aa(_0x2d943a,_0x446e70){return _0x297e6d(_0x446e70- -_0x2b5726._0x392ad8,_0x2d943a);}_0xd7fc8&&this['updateRadius'](_0xd7fc8);}[_0x2d6513(0x230,0x261)](_0xfca3cb){var _0x31cfac={_0x237732:0x3c0,_0x439581:0x40,_0x55e4c2:0x3fb,_0xb097b0:0x464,_0x1636a9:0x46,_0x2ef17c:0x48d,_0x2564cd:0x6b,_0x32fff7:0x1b,_0x24999c:0xaf},_0x29a7da={_0x1e05d7:0x46},_0x206aee={_0x138642:0x600,_0x3abf89:0x558,_0x1f7433:0x5c2},_0xca88d8={_0x21feff:0x15e};let _0xafc0e1,_0x81c48a,_0x49dbcb,_0x2da0e1;this[_0x6d4bf(0x409,0x3d3)]['rectangle']?(_0xafc0e1=this['options']['rectangle']['xmin'],_0x81c48a=this['options']['rectangle']['xmax'],_0x49dbcb=this['options'][_0x6d4bf(0x418,_0x31cfac._0x237732)]['ymin'],_0x2da0e1=this[_0x5ccb67(-0x78,-0x8c)][_0x5ccb67(-0x69,-_0x31cfac._0x439581)][_0x6d4bf(0x45c,_0x31cfac._0x55e4c2)]):_0xfca3cb['forEach']((_0x316ea5,_0x32f5fa)=>{function _0x4a7485(_0x4aec98,_0x4e3aaa){return _0x6d4bf(_0x4aec98- -0x3ab,_0x4e3aaa);}function _0x5cbf1a(_0x17aae8,_0x43096e){return _0x6d4bf(_0x17aae8-_0xca88d8._0x21feff,_0x43096e);}_0x32f5fa===0x0?(_0xafc0e1=_0x316ea5['lng'],_0x81c48a=_0x316ea5[_0x5cbf1a(0x558,0x5b0)],_0x49dbcb=_0x316ea5['lat'],_0x2da0e1=_0x316ea5[_0x5cbf1a(_0x206aee._0x138642,0x661)]):(_0xafc0e1=Math['min'](_0xafc0e1,_0x316ea5['lng']),_0x81c48a=Math['max'](_0x81c48a,_0x316ea5[_0x5cbf1a(_0x206aee._0x3abf89,0x528)]),_0x49dbcb=Math[_0x5cbf1a(_0x206aee._0x1f7433,0x563)](_0x49dbcb,_0x316ea5['lat']),_0x2da0e1=Math['max'](_0x2da0e1,_0x316ea5['lat']));});let _0x43d974=_0x81c48a-_0xafc0e1;function _0x5ccb67(_0x5ddefd,_0x1c8b48){return _0x2d6513(_0x1c8b48,_0x5ddefd- -0x274);}let _0x375588=_0x2da0e1-_0x49dbcb;_0x43d974===0x0&&(_0x43d974=0x1);_0x375588===0x0&&(_0x375588=0x1);const _0x2d05e2=this['options']['rectanglePadding']??0.2;!this['options'][_0x5ccb67(-0x69,-0x76)]&&(_0xafc0e1-=_0x43d974*_0x2d05e2,_0x49dbcb-=_0x375588*_0x2d05e2,_0x81c48a+=_0x43d974*_0x2d05e2,_0x2da0e1+=_0x375588*_0x2d05e2);_0xafc0e1=Math['max'](_0xafc0e1,-0xb4),_0x81c48a=Math[_0x6d4bf(_0x31cfac._0xb097b0,0x49b)](_0x81c48a,0xb4),_0x49dbcb=Math['max'](_0x49dbcb,-0x5a),_0x2da0e1=Math['min'](_0x2da0e1,0x5a);var _0x2f8bde={};_0x2f8bde['xmin']=_0xafc0e1,_0x2f8bde['xmax']=_0x81c48a,_0x2f8bde['ymin']=_0x49dbcb,_0x2f8bde['ymax']=_0x2da0e1;const _0x4f8a56=_0x2f8bde;function _0x6d4bf(_0x5b4b7d,_0x5f5e0c){return _0x297e6d(_0x5b4b7d- -_0x29a7da._0x1e05d7,_0x5f5e0c);}_0x4f8a56[_0x5ccb67(-_0x31cfac._0x1636a9,-0x26)]=_0x81c48a-_0xafc0e1,_0x4f8a56['diffY']=_0x2da0e1-_0x49dbcb,_0x4f8a56[_0x5ccb67(-0x69,-0x36)]=Cesium[_0x6d4bf(0x44e,_0x31cfac._0x2ef17c)]['fromDegrees'](_0xafc0e1,_0x49dbcb,_0x81c48a,_0x2da0e1);const _0x16fe16=Math[_0x5ccb67(-_0x31cfac._0x2564cd,-0xd1)](_0x4f8a56['rectangle'][_0x5ccb67(-0x44,-0x8e)],_0x4f8a56['rectangle']['width']);return _0x4f8a56['granularity']=_0x16fe16,_0x4f8a56[_0x5ccb67(-0x37,_0x31cfac._0x32fff7)]=Cesium[_0x5ccb67(-0x4c,-_0x31cfac._0x24999c)]['chordLength'](_0x16fe16,this['_map']['scene']['globe']['ellipsoid']['maximumRadius'])/(0x1+0x2*_0x2d05e2),_0x4f8a56;}[_0x2d6513(0x20f,0x252)](){var _0x39a949={_0x220940:0x2f4,_0x81a769:0x5af,_0x25be4e:0x567,_0x45815d:0x56b,_0x490e52:0x591,_0x285a13:0x2a2,_0x564b49:0x2b7,_0x218168:0x58a,_0x5221a3:0x251,_0x4b5e5f:0x324,_0x168272:0x2c6,_0xec8ea0:0x2f0,_0x158aec:0x550,_0x2e4ed2:0x288,_0x2b2534:0x294,_0x4d8bec:0x574,_0x4b7dd5:0x559,_0xf34b1:0x2e4,_0x599210:0x27f,_0x1077dc:0x279,_0x24593e:0x241,_0x522961:0x286,_0x261fd1:0x21f,_0x3c58c8:0x57d,_0x460b51:0x55e,_0x4becdd:0x5b2,_0x2bfa6c:0x562},_0x5cefac={_0x2b0399:0xb,_0x196622:0x33a,_0x1acfa9:0x55},_0x520a49={_0x3660d9:0xe5},_0x147753={_0x3f48a3:0x1be,_0x3a650e:0x170,_0x19bfa6:0x393,_0x370c5d:0x1bc},_0x3a475d={_0x216f4d:0x3d9};function _0x4a6978(_0x5a917c,_0x1ab7fe){return _0x297e6d(_0x1ab7fe- -0x1d6,_0x5a917c);}const _0x42d88b=this[_0x485134(0x5c2,0x596)],_0xaf4bd3=[];_0x42d88b['forEach'](_0x43d58a=>{function _0xbccec6(_0x5b598e,_0x4997a8){return _0x485134(_0x5b598e- -_0x3a475d._0x216f4d,_0x4997a8);}const _0x2d4f62=mars3d__namespace[_0xbccec6(_0x147753._0x3f48a3,_0x147753._0x3a650e)][_0xe844bf(0x3ad,_0x147753._0x19bfa6)](_0x43d58a);function _0xe844bf(_0x2128a6,_0xc7d5a0){return _0x485134(_0x2128a6- -0x20e,_0xc7d5a0);}if(!_0x2d4f62)return;_0x2d4f62[_0xbccec6(0x1c6,_0x147753._0x370c5d)]=_0x43d58a['value']??0x1,_0xaf4bd3['push'](_0x2d4f62);});function _0x485134(_0x2a8973,_0x41e98d){return _0x297e6d(_0x2a8973-_0x520a49._0x3660d9,_0x41e98d);}this[_0x485134(0x5af,0x55c)]=this['_getBounds'](_0xaf4bd3),this['_rectangle']=this[_0x4a6978(0x352,_0x39a949._0x220940)]['rectangle'];let _0x68b7ab,_0x3f99dc;this[_0x485134(_0x39a949._0x81a769,_0x39a949._0x25be4e)][_0x4a6978(0x256,0x2ab)]>this[_0x4a6978(0x2c1,0x2f4)]['diffY']?(_0x68b7ab=this[_0x485134(0x534,_0x39a949._0x45815d)]['canvasSize']??document['body'][_0x485134(_0x39a949._0x490e52,0x588)],_0x3f99dc=mars3d__namespace['Util'][_0x4a6978(_0x39a949._0x285a13,_0x39a949._0x564b49)](_0x68b7ab/this['_bounds']['diffX']*this[_0x485134(0x5af,_0x39a949._0x218168)]['diffY'])):(_0x3f99dc=this['options']['canvasSize']??document['body']['clientHeight'],_0x68b7ab=mars3d__namespace['Util'][_0x4a6978(_0x39a949._0x5221a3,0x2b7)](_0x3f99dc/this['_bounds']['diffY']*this['_bounds']['diffX']));this['_canvasWidth']=_0x68b7ab,this['_canvasHeight']=_0x3f99dc,this[_0x4a6978(_0x39a949._0x4b5e5f,_0x39a949._0x168272)]['style'][_0x4a6978(0x2d0,_0x39a949._0xec8ea0)]=_0x485134(_0x39a949._0x158aec,0x583)+_0x68b7ab+_0x4a6978(_0x39a949._0x2e4ed2,_0x39a949._0x2b2534)+_0x3f99dc+'px;display:none;';var _0xa83123={...this['heatStyle']};_0xa83123['container']=this['_container'];const _0x1e50fe=_0xa83123;this[_0x485134(_0x39a949._0x4d8bec,_0x39a949._0x4b7dd5)]?this['_heat']['configure'](_0x1e50fe):this['_heat']=heatmap$1['exports']['create'](_0x1e50fe);let _0xd68330=_0xaf4bd3[0x0]['value']??0x1,_0x3e64cd=_0xaf4bd3[0x0][_0x4a6978(0x29f,_0x39a949._0xf34b1)]??0x0;const _0x413fe9=[];_0xaf4bd3['forEach'](_0x56d118=>{var _0x371c9c={_0x5b6b80:0x256},_0x5b6148={_0x107e0d:0x9a};const _0x420982=Math[_0x184d63(0x2ea,0x33a)]((_0x56d118[_0x3a32d2(0x14,_0x5cefac._0x2b0399)]-this[_0x3a32d2(0x9e,0xf6)]['xmin'])/this['_bounds'][_0x3a32d2(0x55,0x1d)]*_0x68b7ab),_0x13455b=Math[_0x184d63(0x336,_0x5cefac._0x196622)]((this['_bounds']['ymax']-_0x56d118['lat'])/this['_bounds']['diffY']*_0x3f99dc);function _0x184d63(_0x445f2e,_0x150ee6){return _0x4a6978(_0x445f2e,_0x150ee6-_0x5b6148._0x107e0d);}const _0x9d20f2=_0x56d118['value']||0x1;_0xd68330=Math['max'](_0xd68330,_0x9d20f2),_0x3e64cd=Math[_0x184d63(0x362,0x36e)](_0x3e64cd,_0x9d20f2);var _0x548b77={};_0x548b77['x']=_0x420982;function _0x3a32d2(_0x4207a7,_0x83cfcd){return _0x4a6978(_0x83cfcd,_0x4207a7- -_0x371c9c._0x5b6b80);}_0x548b77['y']=_0x13455b,_0x548b77[_0x3a32d2(0x8e,_0x5cefac._0x1acfa9)]=_0x9d20f2,_0x413fe9['push'](_0x548b77);});var _0x2d21f3={};_0x2d21f3['min']=this[_0x4a6978(_0x39a949._0x599210,_0x39a949._0x1077dc)]['min']??_0x3e64cd,_0x2d21f3[_0x4a6978(_0x39a949._0x24593e,_0x39a949._0x522961)]=this[_0x4a6978(_0x39a949._0x261fd1,_0x39a949._0x1077dc)]['max']??_0xd68330,_0x2d21f3[_0x485134(_0x39a949._0x3c58c8,_0x39a949._0x460b51)]=_0x413fe9;const _0x20a475=_0x2d21f3;this['_heat'][_0x485134(_0x39a949._0x4becdd,_0x39a949._0x2bfa6c)](_0x20a475);const _0x48f2bc=this['_heat'][_0x485134(0x5e4,0x633)]['canvas'][_0x485134(0x5d8,0x59d)]('image/png',0x1);return _0x48f2bc;}['_getArcHeatCanvas'](){var _0x401eed={_0x4b9f7a:0x1ea,_0x5c8c56:0x567,_0x43922c:0x5ba,_0x36cb8c:0x229,_0x14054d:0x230,_0xe4765b:0x5ed,_0x24c265:0x216,_0x4ee340:0x227,_0x2d0c28:0x61c,_0xba91b1:0x270,_0x3df222:0x2b4},_0x1c0481={_0x5c7ddb:0xf7},_0x232e2f={_0x1e9f17:0x268},_0xab8ff={};_0xab8ff[_0x37a68d(0x193,_0x401eed._0x4b9f7a)]=_0x5a53c0(_0x401eed._0x5c8c56,_0x401eed._0x43922c),_0xab8ff['0.55']='rgb(140,140,140)',_0xab8ff[_0x37a68d(0x259,_0x401eed._0x36cb8c)]=_0x37a68d(_0x401eed._0x14054d,0x272),_0xab8ff['1']='rgb(255,255,255)';function _0x37a68d(_0x19e1c3,_0x5021a0){return _0x297e6d(_0x5021a0- -_0x232e2f._0x1e9f17,_0x19e1c3);}function _0x5a53c0(_0x26ff48,_0x3823cb){return _0x297e6d(_0x3823cb-_0x1c0481._0x5c7ddb,_0x26ff48);}this[_0x37a68d(0x1c6,0x227)]['configure']({'radius':this['heatStyle']['radius']*this['style']['arcRadiusScale'],'blur':this[_0x5a53c0(0x633,_0x401eed._0xe4765b)]['blur']*this[_0x5a53c0(0x5ca,0x5af)]['arcBlurScale'],'gradient':this['heatStyle']['gradientArc']||_0xab8ff});const _0x2fea87=this[_0x37a68d(_0x401eed._0x24c265,_0x401eed._0x4ee340)][_0x5a53c0(_0x401eed._0x2d0c28,0x5f6)][_0x37a68d(0x24c,_0x401eed._0xba91b1)]['toDataURL']('image/png',0x1);return this[_0x37a68d(0x28a,_0x401eed._0x4ee340)]['configure'](this[_0x5a53c0(0x58a,0x546)][_0x37a68d(_0x401eed._0x3df222,0x28e)]),_0x2fea87;}['updateRadius'](_0x5f30f1){var _0x5f152c={_0x53ea50:0x2c5,_0x311f9c:0x297};const _0x54959b=this['_heat'][_0x33518b(0x2bb,_0x5f152c._0x53ea50)]();if(_0x54959b!==null&&_0x54959b!==void 0x0&&_0x54959b['data'])for(const _0x1b8c50 in _0x54959b['data']){const _0x2de906=_0x54959b['data'][_0x1b8c50];_0x2de906['radius']=_0x5f30f1;}function _0x30e1ad(_0x33ed79,_0x447921){return _0x297e6d(_0x33ed79- -0x151,_0x447921);}function _0x33518b(_0x3852bd,_0x57e243){return _0x297e6d(_0x3852bd- -0x241,_0x57e243);}this['_heat']['setData'](_0x54959b);const _0x29614b=this['_heat']['_renderer'][_0x33518b(_0x5f152c._0x311f9c,0x2a2)]['toDataURL']('image/png',0x1);this['_updateGraphic'](_0x29614b);}['getPointData'](_0x3bfab6){var _0xa4421b={_0xd18115:0x333,_0x282fbb:0x343,_0x1bb84c:0x20b,_0x4bd383:0x2fb,_0x43576d:0x167,_0x215aa4:0x105,_0x50ff29:0x1c2,_0x5cbbcf:0x16b,_0x137b2b:0x220},_0x379723={_0x4501f6:0x33b},_0x3cf95a={_0x19d23d:0x1e8};const _0x2f4b3e=mars3d__namespace['LngLatPoint'][_0xb55709(_0xa4421b._0xd18115,0x2ee)](_0x3bfab6);if(!_0x2f4b3e||!this[_0xb55709(_0xa4421b._0x282fbb,0x2e2)])return{};function _0xb55709(_0x4749a3,_0x4a0aa2){return _0x297e6d(_0x4a0aa2- -_0x3cf95a._0x19d23d,_0x4749a3);}if(_0x2f4b3e['lng']<this['_bounds']['xmin']||_0x2f4b3e['lng']>this['_bounds']['xmax']||_0x2f4b3e[_0x5b4442(_0xa4421b._0x1bb84c,0x1ad)]<this[_0x5b4442(0x1de,0x18f)]['ymin']||_0x2f4b3e[_0xb55709(_0xa4421b._0x4bd383,0x300)]>this[_0xb55709(0x2ac,0x2e2)][_0x5b4442(0x157,_0xa4421b._0x43576d)])return{};const _0x267fbd=(_0x2f4b3e[_0x5b4442(0xfc,_0xa4421b._0x215aa4)]-this['_bounds']['xmin'])/(this[_0xb55709(0x312,0x2e2)]['xmax']-this['_bounds']['xmin'])*this[_0x5b4442(0x177,_0xa4421b._0x50ff29)],_0x3a6401=(this['_bounds'][_0xb55709(0x292,0x2ba)]-_0x2f4b3e[_0x5b4442(0x1ef,0x1ad)])/(this[_0x5b4442(_0xa4421b._0x5cbbcf,0x18f)]['ymax']-this[_0xb55709(0x29c,0x2e2)][_0xb55709(_0xa4421b._0x137b2b,0x27a)])*this[_0xb55709(0x34c,0x312)];var _0x2fae75={};_0x2fae75['x']=_0x267fbd,_0x2fae75['y']=_0x3a6401;const _0x58976e=this[_0x5b4442(0x119,0x154)]['getValueAt'](_0x2fae75),_0x444dca=this['_heat']['_renderer']['ctx'][_0x5b4442(0xd4,0x136)](_0x267fbd-0x1,_0x3a6401-0x1,0x1,0x1)['data'];var _0x57da15={};_0x57da15['x']=_0x267fbd,_0x57da15['y']=_0x3a6401,_0x57da15[_0x5b4442(0x17b,0x17f)]=_0x58976e;function _0x5b4442(_0x26085d,_0x1c8679){return _0x297e6d(_0x1c8679- -_0x379723._0x4501f6,_0x26085d);}return _0x57da15['color']='rgba('+_0x444dca[0x0]+','+_0x444dca[0x1]+','+_0x444dca[0x2]+','+_0x444dca[0x3]+')',_0x57da15;}['_updateGraphic'](_0x2262ef){var _0x1f2e5={_0x1a09da:0x281,_0x1b5cf2:0x24a,_0x4b9be4:0x21d,_0x17398e:0x2e1,_0x1bb6c1:0x23d,_0x3eade1:0x24c,_0x51f9ac:0x290,_0x4b8f3c:0x24c,_0x5d8d02:0x3f4},_0x2b6857={_0x548480:0x21b};function _0x2c918a(_0x56e7bc,_0x1d00b6){return _0x2d6513(_0x1d00b6,_0x56e7bc-0xf9);}function _0x1c4ef0(_0x36bf35,_0x2ff379){return _0x297e6d(_0x2ff379- -_0x2b6857._0x548480,_0x36bf35);}if(this[_0x1c4ef0(_0x1f2e5._0x1a09da,0x29d)]['type']==='image')this['_layer'][_0x1c4ef0(_0x1f2e5._0x1b5cf2,_0x1f2e5._0x4b9be4)]({'url':_0x2262ef,'rectangle':this['_rectangle'],'opacity':this[_0x1c4ef0(0x27e,0x29d)]['opacity']});else this[_0x2c918a(0x35e,0x327)][_0x1c4ef0(_0x1f2e5._0x17398e,0x28d)]?this['_graphic']&&this[_0x1c4ef0(0x2bf,0x2e5)]['rectangle']['equals'](this['_rectangle'])?(this['_graphic'][_0x1c4ef0(_0x1f2e5._0x1bb6c1,_0x1f2e5._0x3eade1)][_0x1c4ef0(0x28b,0x277)]=_0x2262ef,this['_graphic']['uniforms']['bumpMap']=this[_0x1c4ef0(_0x1f2e5._0x51f9ac,0x2aa)](),this['_graphic2']&&(this['_graphic2'][_0x1c4ef0(0x1e9,_0x1f2e5._0x4b8f3c)]['image']=_0x2262ef,this[_0x2c918a(0x35c,0x348)]['uniforms']['bumpMap']=this['_graphic'][_0x2c918a(0x30d,0x2dd)]['bumpMap'])):this['_createArcGraphic'](_0x2262ef):this[_0x2c918a(0x3a6,_0x1f2e5._0x5d8d02)]&&this['_graphic']['rectangle'][_0x1c4ef0(0x282,0x292)](this[_0x2c918a(0x3a7,0x392)])?this[_0x1c4ef0(0x27e,0x2e5)]['uniforms']['image']=_0x2262ef:this['_createGraphic'](_0x2262ef);}[_0x2d6513(0x233,0x242)](_0xaf162f){var _0x439ce8={_0x589a25:0x54b,_0x5b4e18:0x7c,_0x1f8d0d:0x9a,_0x22de03:0x513,_0x5f4273:0x34,_0xf12741:0x57e,_0x16f0b0:0x59c},_0x1df462={_0x3d4a49:0x113};function _0x285d87(_0x44ecf5,_0x305569){return _0x2d6513(_0x305569,_0x44ecf5- -0x1ca);}this['clear']();var _0x4bc0ca={};_0x4bc0ca[_0x58e3cf(0x5a5,_0x439ce8._0x589a25)]=_0xaf162f;function _0x58e3cf(_0x4639fb,_0xca8bb7){return _0x297e6d(_0x4639fb-_0x1df462._0x3d4a49,_0xca8bb7);}this[_0x58e3cf(0x613,0x63f)]=new mars3d__namespace['graphic'][(_0x285d87(_0x439ce8._0x5b4e18,_0x439ce8._0x1f8d0d))]({...this[_0x58e3cf(0x562,_0x439ce8._0x22de03)],'private':!![],'flyTo':![],'rectangle':this['_rectangle'],'appearance':new Cesium[(_0x285d87(_0x439ce8._0x5f4273,-0x1a))]({'material':mars3d__namespace['MaterialUtil']['createMaterial'](mars3d__namespace[_0x58e3cf(0x5cc,_0x439ce8._0xf12741)][_0x58e3cf(0x55f,_0x439ce8._0x16f0b0)],_0x4bc0ca),'flat':!![]})}),this['_layer']['addGraphic'](this['_graphic']);}['_createArcGraphic'](_0x299841){var _0x250108={_0x1523a8:0x295,_0x177a43:0x11e,_0x275775:0x217,_0x4cccf0:0xe1,_0x4641b8:0x82,_0x12319d:0x278,_0x4ef76e:0x2a2,_0x1d64b6:0x268,_0x51f496:0x220,_0x445de4:0x1e0,_0x246e89:0xf5,_0x5db85f:0x2a2,_0x2ad586:0x23d,_0x482345:0x9c,_0x34a002:0x2ca,_0x310053:0x6,_0x5546ef:0x112,_0x4f5056:0x74,_0x1691ac:0x134,_0x1971d1:0x1d6},_0x357cb8={_0x420d6d:0x228};function _0x6848a5(_0x37643a,_0x2e88e9){return _0x297e6d(_0x37643a- -_0x357cb8._0x420d6d,_0x2e88e9);}this['clear']();var _0x3495dd={};_0x3495dd['enabled']=!![];var _0x5c5685={};_0x5c5685['enabled']=!![];const _0x94c163=Cesium['RenderState']['fromCache']({'cull':_0x3495dd,'depthTest':_0x5c5685,'stencilTest':{'enabled':!![],'frontFunction':Cesium[_0x6848a5(0x24a,0x238)][_0x6848a5(0x275,_0x250108._0x1523a8)],'frontOperation':{'fail':Cesium['StencilOperation']['KEEP'],'zFail':Cesium[_0x4ca040(-0xba,-_0x250108._0x177a43)][_0x6848a5(_0x250108._0x275775,0x257)],'zPass':Cesium[_0x4ca040(-_0x250108._0x4cccf0,-_0x250108._0x177a43)]['REPLACE']},'backFunction':Cesium['StencilFunction'][_0x4ca040(-_0x250108._0x4641b8,-0xc4)],'backOperation':{'fail':Cesium['StencilOperation']['KEEP'],'zFail':Cesium[_0x6848a5(0x21b,0x277)][_0x6848a5(0x217,0x1d6)],'zPass':Cesium[_0x4ca040(-0xb6,-0x11e)]['REPLACE']},'reference':0x2,'mask':0x2},'blending':Cesium['BlendingState']['ALPHA_BLEND']}),_0x466199=Math['floor'](this[_0x6848a5(0x290,_0x250108._0x12319d)][_0x4ca040(-0x171,-0x119)]??this[_0x6848a5(_0x250108._0x4ef76e,0x248)][_0x6848a5(_0x250108._0x1d64b6,0x2a6)]*0.05)+0.1;this['style'][_0x6848a5(_0x250108._0x51f496,_0x250108._0x445de4)]&&delete this['style']['diffHeight'];this['style'][_0x4ca040(-0x9a,-_0x250108._0x246e89)]=this[_0x6848a5(_0x250108._0x5db85f,_0x250108._0x2ad586)]['granularity']/(this['style']['splitNum'],0x64);const _0x42db62=new Cesium['Material']({'fabric':{'uniforms':{'image':_0x299841,'repeat':new Cesium[(_0x6848a5(0x256,0x221))](0x1,0x1),'color':new Cesium['Color'](0x1,0x1,0x1,0x0),'bumpMap':this[_0x4ca040(-0xfa,-_0x250108._0x482345)]()},'source':HeatMaterial},'translucent':!![]}),_0x2986a0=this['style']['arcDirection']||0x1;this['_graphic']=new mars3d__namespace['graphic']['RectanglePrimitive']({...this['options'],'private':!![],'flyTo':![],'rectangle':this['_rectangle'],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'flat':!![],'aboveGround':!![],'renderState':_0x94c163,'material':_0x42db62,'vertexShaderSource':getVertexShaderSource(_0x466199*_0x2986a0)})});function _0x4ca040(_0x309974,_0x3e74e5){return _0x2d6513(_0x309974,_0x3e74e5- -0x30e);}this[_0x6848a5(0x22d,0x240)][_0x6848a5(_0x250108._0x34a002,0x305)](this[_0x4ca040(-_0x250108._0x310053,-0x61)]),this['style']['arcDirection']===0x0&&(this['_graphic2']=new mars3d__namespace['graphic']['RectanglePrimitive']({...this[_0x4ca040(-0xca,-_0x250108._0x5546ef)],'private':!![],'flyTo':![],'rectangle':this[_0x4ca040(-_0x250108._0x4f5056,-0x60)],'appearance':new Cesium[(_0x4ca040(-_0x250108._0x1691ac,-0x110))]({'flat':!![],'aboveGround':!![],'renderState':_0x94c163,'material':_0x42db62,'vertexShaderSource':getVertexShaderSource(-_0x466199)})}),this[_0x6848a5(0x22d,_0x250108._0x1971d1)]['addGraphic'](this['_graphic2']));}}mars3d__namespace[_0x2d6513(0x295,0x234)][_0x297e6d(0x4cb,0x527)]('heat',HeatLayer),mars3d__namespace[_0x2d6513(0x199,0x1e1)]['HeatLayer']=HeatLayer,mars3d__namespace['h337']=h337;function getVertexShaderSource(_0x23eaa6){var _0x5103e8={_0x24176d:0x10b};function _0x177cf3(_0x4f88a6,_0x5cf803){return _0x297e6d(_0x4f88a6-_0x5103e8._0x24176d,_0x5cf803);}return'in\x20vec3\x20position3DHigh;\x0a\x20\x20in\x20vec3\x20position3DLow;\x0a\x20\x20in\x20vec2\x20st;\x0a\x20\x20in\x20float\x20batchId;\x0a\x20\x20uniform\x20sampler2D\x20bumpMap_3;\x0a\x20\x20out\x20vec3\x20v_positionMC;\x0a\x20\x20out\x20vec3\x20v_positionEC;\x0a\x20\x20out\x20vec2\x20v_st;\x0a\x0a\x20\x20void\x20main()\x0a\x20\x20{\x0a\x20\x20\x20\x20vec4\x20p\x20=\x20czm_computePosition();\x0a\x20\x20\x20\x20v_positionMC\x20=\x20position3DHigh\x20+\x20position3DLow;\x0a\x20\x20\x20\x20v_positionEC\x20=\x20(czm_modelViewRelativeToEye\x20*\x20p).xyz;\x0a\x20\x20\x20\x20v_st\x20=\x20st;\x0a\x20\x20\x20\x20vec4\x20color\x20=\x20texture(bumpMap_3,\x20v_st);\x0a\x20\x20\x20\x20float\x20centerBump\x20=\x20distance(vec3(0.0),color.rgb);\x0a\x20\x20\x20\x20vec3\x20upDir\x20=\x20normalize(v_positionMC.xyz);\x0a\x20\x20\x20\x20vec3\x20disPos\x20=\x20upDir\x20*\x20centerBump\x20*\x20'+_0x23eaa6+_0x177cf3(0x5ec,0x5a4);}function getSurfaceDistance(_0x301866){var _0x32018f={_0x38ce7a:0x92,_0x525ffd:0x17,_0x295980:0x9c};const _0x3219b4=_0x301866['globe'][_0x14e5af(-0x6d,-_0x32018f._0x38ce7a)],_0x1b2ee7=_0x301866[_0x52f6a3(0x208,0x1c9)],_0x301939=_0x1b2ee7[_0x14e5af(-_0x32018f._0x525ffd,-0x46)]/0x2,_0x2f7f94=_0x1b2ee7['clientHeight']/0x2,_0x171cd9=_0x1b2ee7['clientWidth']/0x64,_0x1abba0=new Cesium[(_0x14e5af(-0x45,0x12))](_0x301939,_0x2f7f94);let _0x9d8096,_0x68929;_0x1abba0['x']=_0x301939;function _0x52f6a3(_0x861766,_0x329d6e){return _0x2d6513(_0x861766,_0x329d6e- -0xbc);}for(let _0x465ea4=0x0;_0x465ea4<0x64;_0x465ea4++){_0x1abba0['y']=_0x171cd9*_0x465ea4;const _0x30957b=_0x301866['camera']['pickEllipsoid'](_0x1abba0,_0x3219b4);if(_0x30957b){_0x9d8096=_0x30957b;break;}}function _0x14e5af(_0x3b150e,_0x2dd1a4){return _0x297e6d(_0x3b150e- -0x4c3,_0x2dd1a4);}for(let _0x4c26b8=0x64;_0x4c26b8>0x0;_0x4c26b8--){_0x1abba0['y']=_0x171cd9*_0x4c26b8;const _0x415f98=_0x301866[_0x14e5af(-0x5e,-_0x32018f._0x295980)][_0x14e5af(0x14,0x46)](_0x1abba0,_0x3219b4);if(_0x415f98){_0x68929=_0x415f98;break;}}return _0x9d8096&&_0x68929?mars3d__namespace['MeasureUtil'][_0x52f6a3(0x152,0x138)]([_0x9d8096,_0x68929]):0x0;}exports[_0x2d6513(0x1df,0x231)]=HeatLayer;function _0x2d6513(_0x4344d2,_0x5f5dea){return _0x1a38(_0x5f5dea-0x5c,_0x4344d2);}var _0x466027={};function _0x4f0a(){var _0x474abf=['zMLSBfjLy3q','x2DYyxbOAwmY','y3jLyxrLuMfKAwfSr3jHzgLLBNq','C3r5Bgu','twf0zxjPywXuExbL','DMfSDwu','zxH0CMvTywnOyw5Nzq','y2fUDMfZmMq','zgvMyxvSDeDYywrPzw50','x3jLBMrLCKjVDw5KyxjPzxm','x3bHBgv0Dgu','AgvHDg1HCc1Jyw52yxm','x2rYyxDbBhbOyq','BwLUt3bHy2L0Eq','CMDIkdaSmcWWkq','x19LC01VzhvSzq','x2DLDefYy0HLyxrdyw52yxm','y3nZvgv4Da','Dw5KzwzPBMvK','zMLSBfn0EwXL','rxzLBNruExbL','x2jVDw5KCW','CMvNAxn0zxi','y3jZ','C2v0rgf0yq','ywrKq29SB3jtDg9W','zMLSBa','ChjVDg90ExbL','x29Uq2fTzxjHtw92zuvUza','z2v0q29UDgv4Da','CMvSyxrPDMu','EwvSBg93','q2vZAxvT','CgfYC2u','CgLJA0vSBgLWC29Pza','y2fUDMfZ','C2v0rgf0yu1PBG','CMDIkdiXnIWYmtySmJe2kq','CMvTB3zLr3jHCgHPyW','B2zM','x3bVC2L0Aw9UCW','y3jLyxrL','C2v0ug9ZAxrPB25Z','x3vWzgf0zvbVC2L0Aw9UC0HVB2S','oWOGicaGCcaRpxzLyZqOzgLZug9ZldaUmcK7cIaGicbNBf9qB3nPDgLVBIa9ign6Bv9TB2rLBfzPzxDqCM9Qzwn0Aw9UuMvSyxrPDMvuB0v5zsaQiha7cIaGFqO','zgvMyxvSDe1PBK9WywnPDhK','x29Yz2fUAxnLrgf0yq','Cg9ZAxrPB25Z','zw1PDa','DMvYDgv4rM9YBwf0','y3jLyxrLrwXLBwvUDa','Bgf0','yMX1CG','x19WCM90B19F','CMDIkdaSmJu1ldaP','z2XVyMfSqwXWAge','x3rLBxbSyxrLCW','ntG3mJq2mwrQwvfTza','C2HVDW','x3nOB3DiB29R','AxnbCNjHEq','ywrKr3jHCgHPyW','Dg9eyxrHvvjm','y2XLyxjszwn0','mZKXotuZnLLQsKjywG','AgvHDfn0EwXL','mtzque1mqLm','DMfSDwvgAwvSza','zgvMyxvSDfHgAwvSza','x2nHBNzHC0HLAwDODa','CMvUzgvYywXS','z2v0rgf0yq','x2nHBNzHC1DPzhrO','ChvZAa','x3jLBMrLCMvY','x2DYyxbOAwm','x3jLy3rHBMDSzq','C2nLBMu','DhLWzq','Bgf5zxi','x2nSzwfY','x2DLDeLUDgvYBMfSrgf0yq','CgX1z2LUCW','C2v0t3b0Aw9UCW','mtiXEKzftvb1','x2jSDxi','zM9YrwfJAa','B25fEhrYzw1Hq2HHBMDL','yMfJA2DYB3vUzenVBg9Y','mxLyvfPRtG','s0vfua','Bg5N','r3jHCgHPy0XHEwvY','x21HCa','u3rLBMnPBe9WzxjHDgLVBG','C2HHzg93q2fUDMfZ','y2fSBa','CMvUzgvYugfYDgLHBa','z2v0u3vYzMfJzurPC3rHBMnL','zgLMzKHLAwDODa','x3LgAwvSza','mc41nq','x2nVB3jKAw5HDg9Y','sw1Hz2uY','x3jHzgK','x2HLAwDODa','B3b0Aw9UCW','BgvUz3rO','rwXSAxbZB2LKu3vYzMfJzufWCgvHCMfUy2u','mc4Ynq','BwvYz2u','ndm1mdy5B29Lr2Ho','x2XHEwvY','zwXSAxbZB2LK','vxrPBa','C3rYAw5N','Dg9bCNjHEq','D2LKDgG','z2v0vMfSDwvbDa','Bwf4','x3vWzgf0zuDYyxbOAwm','CMvJDgfUz2XL','Cg9ZAxrPB24','zgvMyxvSDfjHzgL1CW','oduYmdy1ALLAA2rK','Ew1PBG','zgvMAw5LuhjVCgvYDhK','x21HEa','y2fTzxjH','x29WywnPDhK','Dw5PzM9YBxm','CMvUzgvYzxi','x21VDw50zwriB29R','ChG7AgvPz2H0oG','D2LKDgG6','z3jHBNvSyxjPDhK','D2LSBfjLywrgCMvXDwvUDgX5','yxjJuMfKAxvZu2nHBgu','ChjPDMf0zq','Bwf4t3bHy2L0Eq','z2v0sw1Hz2veyxrH','u3rLBMnPBez1BMn0Aw9U','BwfYCZnKlwHLyxrTyxaGBwfYCZnKlwHPzgveAxy','zMX5vg8','qMfZzuXHEwvY','CM91BMq','z2v0uMvJDgfUz2XL','CMvWBgfJzq','mJa5mZe3mKjOEhrlvG','x21PBK9WywnPDhK','twf0Aa','EezPzwXK','mty3otCXmgTjvwLYrW','q2fYDgvZAwfUmG','z3jLzw4','C2v0q29VCMrPBMf0B3i','zgLMzLG','y29UDgfPBMvY','AgvPz2H0','sgvHDeXHEwvY','x3vZzuDYywrPzw50t3bHy2L0Eq','y2fTzxjHtw92zuvUza','tgf5zxjvDgLS','x21PBG','mtKZote3meT4tevyCa','ywjZ','CMvUzgvYCgfYDgLHBa','yxbWBhK','zM9YBwf0tNvT','z3jHzgLLBNq','x2HLyxq','CMfKAxvZ','mc44nq','Aw1Hz2u','CMvKCMf3uMf0Aw8','uMvJDgfUz2XL','x2nYzwf0zuDYyxbOAwm','x3DPzhrO','ogLnCxfnEa','zgf0yq','uMvJDgfUz2XLuhjPBwL0AxzL','Chv0sw1Hz2veyxrH','x2rHDge','x2nVBNrHAw5LCG','quXxqvLt','CMvUzgvYqwXS','zgvMyxvSDa','B3bHy2L0Eq','zgvMyxvSDfLgAwvSza','Ew1HEa','x3zHBhvLrMLLBgq','zgvMyxvSDe1HEe9WywnPDhK','x2DLDeHLyxrdyw52yxm','x2nVBMzPzW','yMvNAw5qyxrO','yxjJ','z2v0t3DUuhjVCgvYDhLezxnJCMLWDg9Y','BwLU','y1n0B3jL','y2XPzw50v2LKDgG','zxf1ywXZ','zxHWB3j0CW','x2nMz1jHzgL1CW','rg9TvxrPBa','x3n0B3jL','tg5Ntgf0ug9PBNq','x2nVBg9YAxPL','x2DLDejVDw5KCW'];_0x4f0a=function(){return _0x474abf;};return _0x4f0a();}_0x466027['value']=!![],Object[_0x2d6513(0x22c,0x210)](exports,_0x2d6513(0x25d,0x271),_0x466027);
}));
