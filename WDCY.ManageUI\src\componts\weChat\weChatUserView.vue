<template>
  <div>
    <el-dialog :fullscreen="true" top="0vh" v-loading="loading" v-model="dialog.show" :title="dialog.title"
      custom-class="dialogcss" align-center="true">
      <div style="display:flex; justify-content: space-between;background-color: rgb(241, 245, 255);">

        <!-- 左侧区域，人员、住房、车辆信息 -->
        <div class="detailBox">

          <div style="display:flex;flex-direction:column;height: 40%;padding: 10px;" class="bgcfff">
            <div style="margin-bottom:10px">
              <span class="blue_icon"></span>
              <span style="margin-left:8px; display:inline-block">人员信息</span>
            </div>
            <div style="display:flex">
              <el-image style="height:231px; width:170px;margin-right: 27px;" fit="contain"
                :src="imgServer + wxUserView.photo">
                <template #error>
                  <!-- <el-image preview-teleported fit="contain" style="height:231px; width:170px;margin-right: 27px;"
                    :src="errorHeadImg" :preview-src-list="[errorHeadImg]"></el-image> -->
                </template>
              </el-image>
              <el-descriptions column="1" size="default">
                <el-descriptions-item label="姓名">{{ wxUserView.name }}</el-descriptions-item>
                <el-descriptions-item label="性别">{{ formatDict(sexList, wxUserView.gender) }}</el-descriptions-item>
                <!-- <el-descriptions-item label="年龄">{{ wxUserView.age }}</el-descriptions-item> -->
                <el-descriptions-item label="证件类型">{{ formatDict(certificateTypeTagList, wxUserView.certificateType) }}</el-descriptions-item>
                <el-descriptions-item label="联系电话">{{ wxUserView.phone }}</el-descriptions-item>
                <el-descriptions-item label="证件编号">{{ wxUserView.idCard }}</el-descriptions-item>
                <el-descriptions-item label="登记时间">{{ wxUserView.createTime }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>

          <div style="display:flex;flex-direction:column;height: 30%;padding: 10px" class="bgcfff">
            <div style="margin-bottom:10px">
              <span class="blue_icon"></span>
              <span style="margin-left:8px; display:inline-block;margin-right: 20px;">住房信息</span>
              <el-select v-model="searchModel.communityId" placeholder="选择小区" clearable>
                <el-option v-for="item in personList" :key="item.communityId" :label="item.communityName"
                  :value="item.communityId"></el-option>
              </el-select>
            </div>
            <el-scrollbar>
              
              
              <div style="height: 100%;">
                <div v-for="item in personBelongList" @click="someOnePerson(item.idCard)" :key="item" class="houseItem"
                  style="display:flex; flex-direction:column">
                  <div><span style="margin-right:17px">居住地址</span> {{ item.address }}</div>
                  <div><span style="margin-right:17px">人员类型</span>
                    {{ item.personType == 0 ? "业主" : (item.personType == 1 ? "租客" : (item.personType == 2 ? "家属" : "暂住")) }}</div>
                  <div><span style="margin-right:17px">房屋状态</span>
                    {{ item.status == 0 ? "待审核" : (item.status == 1 ? "正常" : (item.status == 2 ? "审核退回" : "搬出")) }}</div>
                </div>
              </div>
            </el-scrollbar>
          </div>

          <div style="display:flex;flex-direction:column;height: 20%;padding: 10px" class="bgcfff">
            <div style="margin-bottom:10px">
              <span class="blue_icon"></span>
              <span style="margin-left:8px; display:inline-block">车辆信息</span>
            </div>
            <el-scrollbar>
              <div style="height: 100%">
                <div v-for="item in vehicleList" @click="getSomeOneVehicleCapture(item.vehicleNumber)" :key="item" class="carItme"
                  style="display:flex;justify-content: space-between;margin-bottom: 5px;">
                  <div style="width:50%;"><span
                      style="margin-right:17px">登记车辆</span> {{ item.vehicleNumber }}</div>
                  <div style="width:50%;"><span style="margin-right:17px">登记时间</span>
                    {{ !item.validBeginTime ? "":item.validBeginTime.substr(0, 10) }}</div>
                </div>
                <!-- <el-descriptions v-for="item in vehicleList" column="2" :key="item" class="houseItem" >
              <el-descriptions-item label="登记车辆">{{item.vehicleNumber}}</el-descriptions-item>
              <el-descriptions-item label="登记时间">{{item.validBeginTime.substr(0,10)}}</el-descriptions-item>
            </el-descriptions> -->
              </div>
            </el-scrollbar>
          </div>
        </div>

        <!-- 中间区域，人行预警抓拍 -->
        <div class="detailBox">
          <div class="bgcfff" style="height:100%;padding: 10px;display: flex;flex-direction: column;">
            <div>
              <div style="margin-bottom:10px">
                <span class="blue_icon"></span>
                <span style="margin-left:8px; display:inline-block">人脸预警抓拍</span>
              </div>
              <div style="display:flex; justify-content: space-around;">
                <el-date-picker style="width:240px" value-format="YYYY-MM-DD" v-model="peopleStartToEndTime"
                type="daterange" unlink-panels start-placeholder="开始时间" end-placeholder="到期时间" @change="onChange" />
                <div style="flex:1"></div>
                <el-button type="primary" @click="searchPerson">查 询</el-button>
              </div>
            </div>
            <!-- <div style="height:94%; overflow-x:scroll; overflow-y:scroll"> -->
            <el-scrollbar height="94%" style="flex:1">
              <div v-for="(item, index) in tableDataTrack" :key="index">
                <div class="track-vertical-item">
                  <div style="padding: 15px; padding-top: 110px; border-right: 2px #0084a9 solid;">
                    <div style="width:95px">
                      {{ item.time }}
                    </div>

                  </div>

                  <!-- <div style="height: 100%; width: 2px; background: #0084a9"></div> -->

                  <div class="track-horizontal" v-for="(item2, index2) in item.list" :key="index2">
                    <div class="track-horizontal-item">
                      <!-- <img :src="baseUrl + item2.image" @error="imgError"
                        class="track-item-photo" alt="" /> -->


                      <el-image preview-teleported fit="contain" class="track-item-photo" :src="imgServer+item2.image" :preview-src-list="[imgServer+item2.image]">
                        <template #error>
                          <!-- <el-image preview-teleported fit="contain" class="track-item-photo" :src="errorHeadImg" :preview-src-list="[errorHeadImg]"></el-image> -->
                        </template>
                      </el-image>



                      <div class="track-item-time">{{ item2.time }}</div>
                      {{ item2.communityName }}
                      <div class="track-horizontal-inout">
                        <div class="track-horizontal-inout-line"></div>
                        <div class="card_door" :style="{ backgroundImage: 'url(' + (item2.access == 0 ? inbg : outbg) + ')' }"></div>
                      </div>
                    </div>
                  </div>

                </div>
              </div>

            </el-scrollbar>
            <!-- </div> -->
          </div>
        </div>

        <!-- 右侧区域，车行预警抓拍 -->
        <div class="detailBox">
          <div class="bgcfff" style="height:100%;padding: 10px;display: flex;flex-direction: column;">
            <div>
              <div style="margin-bottom:10px">
                <span class="blue_icon"></span>
                <span style="margin-left:8px; display:inline-block">车辆预警抓拍</span>
              </div>
              <!-- <el-date-picker style="width:240px" value-format="YYYY-MM-DD" v-model="carStartToEndTime" type="daterange"
                unlink-panels start-placeholder="开始时间" end-placeholder="到期时间" @change="onChange('asfaasfsf')" />
              <el-button @click="searchVehicle">查询</el-button> -->


              <div style="display:flex; justify-content: space-around;">
                <el-date-picker style="width:240px" value-format="YYYY-MM-DD" v-model="carStartToEndTime" type="daterange"
                unlink-panels start-placeholder="开始时间" end-placeholder="到期时间" @change="onChange('asfaasfsf')" />
                <el-input v-model="selectCarNum" style="flex:1;margin: 0 10px;" placeholder="输入车牌号"></el-input>
                <el-button type="primary" @click="searchVehicle">查 询</el-button>
              </div>
            </div>
            <!-- <div style="height:94%; overflow-x:scroll; overflow-y:scroll"> -->
            <el-scrollbar height="94%" style="flex:1">

              <div v-for="(item, index) in tableDataTrackVehicle" :key="index">
                <div class="track-vertical-item">
                  <div style="padding: 15px; padding-top: 110px; border-right: 2px #0084a9 solid;">
                    <div style="width:95px">
                      {{ item.time }}
                    </div>

                  </div>

                  <!-- <div style="height: 100%; width: 2px; background: #0084a9"></div> -->

                  <div class="track-horizontal" v-for="(item2, index2) in item.list" :key="index2">
                    <div class="track-horizontal-item">
                      <!-- <img :src="imgServer+item2.image" @error="imgError" class="track-item-photo" alt="" /> -->

                      <el-image preview-teleported fit="contain" class="track-item-photo" :src="imgServer+item2.image" :preview-src-list="[imgServer+item2.image]">
                        <template #error>
                          <!-- 暂无图片 -->
                          <!-- <el-image preview-teleported fit="contain" class="track-item-photo" :src="errorHeadImg" :preview-src-list="[errorHeadImg]"></el-image> -->
                        </template>
                      </el-image>
                      <div class="track-item-time">{{ item2.time }}</div>
                      {{ item2.communityName }}
                      <div class="track-horizontal-inout">
                        <div class="track-horizontal-inout-line"></div>
                        <div class="card_door" :style="{ backgroundImage: 'url(' + (item2.access == 0 ? inbg : outbg) + ')' }"></div>
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </el-scrollbar>

            <!-- </div> -->
          </div>
        </div>
      </div>

    </el-dialog>
  </div>
</template>
<script>
import mitt from "@/utils/mitt"
// import vuescroll from "vuescroll"
import { listDictByNameEn } from "@/api/admin/dict"
import { getDictCss, formatDict } from "@/utils/dict"
import errImg from "../../assets/img/defaultHead.png"
import { weChatPersonList, weChatVehicleList } from "@/api/weChat/weChatUser"
export default {
  props:["certificateTypeTagList", 'sexList'],
  data() {
    return {
      dialog: {},
      searchModel: {},
      loading: false,
      imgServer: import.meta.env.VITE_BASE_API,
      personBelongList: [],
      wxUserIdCard: '',
      wxUserView: [],
      personList: [],
      vehicleList: [],
      errorHeadImg: errImg,
      personTypeList: [],
      peopleStartToEndTime: [],
      carStartToEndTime: [],
      tableDataTrack: [],
      tableDataTrackVehicle: [],
      selectCarNum: ''
    };
  },
  methods: {
    formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
    // 点击左侧车辆信息，显示该车辆的车行记录
    getSomeOneVehicleCapture(carNum) {
      if (carNum) {
        this.selectCarNum = carNum
        this.getVehicleCapture({ vehicleNumber: carNum })
      }
    },
    someOnePerson(idCard) {

    },
    // 选择日期查询人行预警抓拍
    searchPerson() {
          // let searchModel = {}
          console.log(this.peopleStartToEndTime);
          if (this.peopleStartToEndTime && (this.peopleStartToEndTime[0] || this.peopleStartToEndTime[1])) {
            this.searchModel.startTime = this.peopleStartToEndTime[0].substr(0, 10)
            this.searchModel.endTime = this.peopleStartToEndTime[1].substr(0, 10)
          }
          this.searchModel.idCard = String(this.wxUserIdCard)
          console.log(this.searchModel);
          this.getPersonCapture(this.searchModel)
          // this.getPersonCapture({ idCard: String(this.wxUserIdCard) })
        // this.searchModel = {}
        
    },
    // 选择日期查询车型预警抓拍
    searchVehicle() {
      
      console.log(this.carStartToEndTime, this.selectCarNum);
      if (this.carStartToEndTime && this.selectCarNum) {
        
        if (this.carStartToEndTime[0]) {
          this.searchModel.startTime = this.carStartToEndTime[0].substr(0, 10)
          this.searchModel.endTime = this.carStartToEndTime[1].substr(0, 10)
        }
        this.searchModel.vehicleNumber = this.selectCarNum
        this.getVehicleCapture(this.searchModel)
      } else {
        console.log(this.selectCarNum);
        if (this.selectCarNum) {
          this.getVehicleCapture({ vehicleNumber: this.vehicleList[0].vehicleNumber })
        } else {
          this.$message.error("暂无车辆信息")
          return
        }
      }
      this.searchModel = {}
    },
    onChange(val) {
      // console.log(val, this.carStartToEndTime);
    },
    personType(row, column, cellValue, index) {
      let result = ''
      for (let item of this.personTypeList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn
        }
      }
      return result
    },
    //获取人脸预警抓拍信息并处理
    getPersonCapture(data) {
      weChatPersonList(data).then(res => {
        this.tableDataTrack = []
        let personRun = res.data.result
        var time = []
        for (const item in personRun) {
          time.push(item)
        }
        time.sort()
        time.reverse()

        time.map((item) => {
          var tempTrack = {};
          tempTrack.time = item
          tempTrack.list = personRun[item]
          this.tableDataTrack.push(tempTrack);
        })
        console.log(this.tableDataTrack);
      })
    },
    //获取车辆预警抓拍信息并处理
    getVehicleCapture(data) {
      weChatVehicleList(data).then(res => {
        this.tableDataTrackVehicle = []
        let vehicleRun = res.data.result
        let time = []
        for (const item in vehicleRun) {
          time.push(item)
        }
        time.sort()
        time.reverse()

        time.map((item) => {
          let tempTrack = {};
          tempTrack.time = item
          tempTrack.list = vehicleRun[item]
          this.tableDataTrackVehicle.push(tempTrack);
        })
        console.log(this.tableDataTrack);
      })
    },
    async init() {
      let person_type = await listDictByNameEn('person_type')
      this.personTypeList = person_type.data.result
    },
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("weChatUserView", (data) => {
        let {userInfo,weChatView} = data
        console.log(userInfo,weChatView);
        this.selectCarNum = ''
        this.tableDataTrack = []
        this.tableDataTrackVehicle = []
        this.tableDataTrack = []
console.log(userInfo);
        this.wxUserIdCard = userInfo.idCard
        this.wxUserView = weChatView
        this.personBelongList = userInfo.personBelongList
        this.vehicleList = userInfo.vehicleList
        this.personList = userInfo.personList
        if (this.personList) {
          this.searchModel.communityId = this.personList[0].communityId
        } else {
          this.searchModel.communityId = ''
        }
        console.log(this.personList);
        this.init();
        this.dialog.show = true;
        this.dialog.title = "详细信息";
        // this.someOneCar(this.vehicleList[0].vehicleNumber)

        this.getPersonCapture({ idCard: String(this.wxUserIdCard) })
        if (this.vehicleList[0]) {
          this.selectCarNum = this.vehicleList[0].vehicleNumber
          this.getVehicleCapture({ vehicleNumber: this.vehicleList[0].vehicleNumber })
        }

      });
    });
  },
};
</script>
<style lang="less" scoped>
* {
  box-sizing: border-box;
}

.dialogcss {
  background-color: #f1f5ff;
  position: relative;
  top: 20px;
}

.detailBox {
  width: 32%;
  height: 80vh;
  margin: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  /* background-color: #fff; */
  >div{
  border-radius: 12px;

  }
}

.bgcfff {
  background-color: #fff;
}

.blue_icon {
  height: 16px;
  width: 4px;
  background-color: #4B96FF;
  display: inline-block;
  border-radius: 1px;
}

.houseItem {
  width: 100%;
  /* height: 100px; */
  padding: 10px;
  background: rgba(241, 245, 255, 0.39);
  opacity: 1;
  border-radius: 4px;
  box-sizing: border-box;
  cursor: default;
}

.carItme {
  width: 100%;
  /* height: 100px; */
  padding: 10px;
  background: rgba(241, 245, 255, 0.39);
  opacity: 1;
  border-radius: 4px;
  box-sizing: border-box;
  cursor: pointer;
}

.track-vertical-item {
  // width: auto;
  height: 220px;
  display: flex;

  // width:800px

}

.track-horizontal {
  text-align: center;
}



.track-horizontal-item {

  width: 130px;

  .track-item-photo {
    width: 92px;
    height: 104px;
    object-fit: cover;
  }


  .track-item-time {
    width: 92px;
    height: 26px;
    margin-top: -15px;
    background: #0084A9;
    color:#fff;
    line-height: 26px;
    align-items: center;
    margin-top: -3px !important;
    text-align: center;
    justify-content: center;
    margin: auto;
    left: 0;
    right: 0;


  }

  .track-horizontal-inout {
    width: 130px;
    height: 34px;
    margin-top: 5px;
    align-items: center;
    text-align: center;
    justify-content: center;
    position: relative;

    .track-horizontal-inout-line {
      width: 100%;
      height: 2px;
      background: #0084A9;
      margin-top: 17px;
      position: absolute;

    }

    .card_door {
      width: 34px;
      height: 34px;
      -webkit-display: flex;
      display: flex;
      -webkit-align-items: center;
      align-items: center;
      position: absolute;
      left: 35%;
      -webkit-justify-content: center;
      justify-content: center;
      // background: url("~@/assets/images/card_door_bg.png") no-repeat;
      background-size: 100% 100%;

      span {
        color: black;
        font-size: 14px;
      }
    }
  }
}

/deep/.el-dialog__body {
  background-color: #f1f5ff;
}
</style>