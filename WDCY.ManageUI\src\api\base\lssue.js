import request from '@/utils/request'

export const issueSta = (data) =>
	request({
		url: '/issue/sta',
		method: 'get',
		params: data
	})
export const issuePage = (data) =>
	request({
		url: '/issue/page',
		method: 'get',
		params: data,
		timeout: 1000 * 60 * 10
	})

export const issueBatchIssue = (data) =>
	request({
		url: '/issue/batch-issue',
		method: 'post',
		data: data,
		timeout: 1000 * 60 * 30
	})
export const issueBatchDelte = (data) =>
	request({
		url: '/issue/batch-delete',
		method: 'post',
		data: data
	})
export const issueAddIssue = (data) =>
	request({
		url: '/issue/add-issue',
		method: 'post',
		data: data
	})
export const listSuggestion = (data) =>
	request({
		url: '/suggestion/page',
		method: 'get',
		params: data
	})
