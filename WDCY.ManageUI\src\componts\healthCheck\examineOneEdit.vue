<template>
	<el-dialog draggable width="40%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="healthExaminaModel" label-width="100px" disabled>
			<el-row>
				<el-col :span="24">
					<el-form-item label="功能名称" prop="funcName">
						<el-input maxlength="50" show-word-limit v-model="healthExaminaModel.funcName" placeholder="功能名称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="接口路径" prop="apiPath">
						<el-input maxlength="200" show-word-limit v-model="healthExaminaModel.apiPath" placeholder="接口路径"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="功能分类" prop="funcCategory">
						<!-- <el-input v-model="healthExaminaModel.funcCategory" placeholder="功能分类"></el-input> -->
                        <el-select style="width: 100%" v-model="healthExaminaModel.funcCategory" clearable placeholder="功能分类">
							<el-option v-for="item in categoryList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="功能版本" prop="funcVer">
						<el-input v-model="healthExaminaModel.funcVer" placeholder="功能版本"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="权限控制" prop="hasRight">
                        <el-select style="width: 100%" v-model="healthExaminaModel.hasRight" clearable placeholder="权限控制">
							<el-option v-for="item in hasRightList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="healthExaminaModel.status">
                            <el-radio-button  v-for="item in statusList" :key="item.nameEn"
                                :label="parseInt(item.nameEn)">{{ item.nameCn }}</el-radio-button >
                        </el-radio-group>
                    </el-form-item>
            	</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
				<el-form-item label="备注" prop="note">
					<el-input
					v-model="healthExaminaModel.note"
					maxlength="200"
					placeholder="请简单说明区域系统情况"
					show-word-limit
					type="textarea"
					/>
				</el-form-item>
				</el-col>
			</el-row>
            <el-row>
                <el-col :span="24">
                <el-form-item label="扩展参数" prop="expandParams">
                    <JsonEditorVue
                    language="cn"
                    class="editor"
                    :modelValue="jsonVal"
                    @update:modelValue="changeJson"
                    />
                </el-form-item>
                </el-col>
            </el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="close">完 成
			</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import mitt from "@/utils/mitt";
import JsonEditorVue from "json-editor-vue3";
export default {
	props: ['statusList', 'categoryList', 'hasRightList', 'methodList'],
    components: { JsonEditorVue },
	data() {
		return {
			loading: false,
			healthExaminaModel: {},
			dialog: {},
            jsonVal: {},
			personId: "",
			startToEndTime: [],
			imgServer: import.meta.env.VITE_BASE_API,
		}
	},
	methods: {
        changeJson(json) {
        	this.jsonVal = json;
        },
        close(){
            this.dialog.show = false
        },
        init(){
        }
	},
	mounted() {
        this.jsonVal = {};
		this.$nextTick(function () {
			mitt.on('openExamineOneEdit', (data) => {
                console.log(data);
				this.healthExaminaModel = data
				this.dialog.show = true
				this.dialog.title = "检查结果"
                this.jsonVal = JSON.parse(this.healthExaminaModel.expandParams);
			})
		})
	},
    created(){
        this.init()
    }
}
</script>
<style scoped>

.editor {
  width: 805px;
}
.avatar-uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}
</style>
