<template>
  <extra-data-detail></extra-data-detail>
  <el-row :gutter="20">
    <el-col :span="6" style="display:flex">
      <!-- <el-input style="margin-right:10px" v-model="searchModel.name" placeholder="事件类型" clearable /> -->
      <el-select clearable style="margin-right:10px; width:380px" v-model="searchModel.name" class="m-2" placeholder="事件类型" >
        <el-option
        v-for="(item, index) in eventSourceType"
        :key="index"
        :label="item.name"
        :value="item.name"
        />
      </el-select>


      <el-input v-model="searchModel.content" placeholder="预警内容" clearable />
    </el-col>
    <el-col :span="5" style="display:flex">
			<el-select clearable style="margin-right:10px" v-model="searchModel.eventLevel" class="m-2" placeholder="选择等级" >
        <el-option
        v-for="(item, index) in eventLevelList"
        :key="index"
        :label="item.nameCn"
        :value="item.nameEn"
        />
      </el-select>
      <el-select clearable style="" v-model="searchModel.status" class="m-2" placeholder="选择状态" >
        <el-option
        v-for="(item, index) in statusList"
        :key="index"
        :label="item.nameCn"
        :value="item.nameEn"
        />
      </el-select>
		</el-col>
    <el-col :span="5" style="display:flex">
			<el-date-picker
				v-model="searchModel.beginTime"
				type="date"
				placeholder="选择开始日期"
				value-format="YYYY-MM-DD"
				:size="size"
				style="margin-right:10px"
			/>
			<el-date-picker
				style="margin-right:10px"
				v-model="searchModel.endTime"
				type="date"
				placeholder="选择结束日期"
				value-format="YYYY-MM-DD"
				:size="size"
			/>
		</el-col>
    <el-col :span="4">
      <el-button type="primary" @click="search">搜 索</el-button>
    </el-col>
    <el-col :span="4" :push="0">
      <el-button style="float:right" :disabled="ids.length == 0" type="primary" @click="batchDelete">批量删除</el-button>
      <el-button style="float:right;margin-right: 10px;" :disabled="ids.length == 0" type="primary" @click="batch">批量确认</el-button>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col>
      <el-table row-key="id" :data="dataList" border ref="multipleTable" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
        <el-table-column prop="name" align="center" label="事件类型" />
        <el-table-column prop="title" align="center" show-overflow-tooltip label="预警标题">
          <template #default="scope">
            <el-icon style="font-size: 18px;">
              <ChatLineSquare v-if="formatExtraData(scope.row.extraData)" @click="viewExtraData(scope.row.extraData)" class="extra-data"/>
            </el-icon>
            {{scope.row.title}}
          </template>
        </el-table-column>
        <el-table-column prop="eventLevel" width="90" style="background-color:red" align="center" label="事件等级" >
					<template #default="scope">
					  	<div :style="'background-color:' + getDictCss(eventLevelList, scope.row.eventLevel)">{{ formatDict(eventLevelList, scope.row.eventLevel) }}</div>
					</template>
				</el-table-column>
        <el-table-column prop="content" align="center" show-overflow-tooltip label="预警内容" />
        <el-table-column prop="address" align="center" show-overflow-tooltip label="发生地点" />
        <el-table-column prop="warnTime" width="168" align="center" label="预警时间" sortable/>

        <el-table-column prop="picUrls" align="center" width="75" label="照片">
					<template #default="scope">
						<div style="background-color:#eee;width:50px;height:50px" v-if="scope.row.picUrls == null || scope.row.picUrls == '' || scope.row.picUrls == undefined">暂无数据</div>
						<el-image preview-teleported 
							v-else fit="contain" style="width: 50px; height: 50px" 
						 	:src="imgServer+scope.row.picUrls[0]" :preview-src-list="swiper(scope.row.picUrls)">
						</el-image>
					</template>
				</el-table-column>

        <el-table-column prop="status" width="100" align="center" label="处理状态">
          <template #default="scope">
					  <el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sureTime" width="168" align="center" label="确认时间" sortable/>
        <!-- <el-table-column prop="createTime" width="160" align="center" label="预警来源设施" />
			<el-table-column prop="updateTime" width="160" align="center" label="预警策略" /> -->
        <el-table-column align="center" width="150" label="操作" v-if="hasPerm('warn:warnRecord:delete') || hasPerm('warn:warnRecord:notarize')">
          <template #default="scope">
            <el-button v-if="hasPerm('warn:warnRecord:notarize') && scope.row.status === 0" type="text" size="default"
              @click="confirm(scope.row)">确认</el-button>
            <el-button v-if="scope.row.status === 1" type="text" size="default"
              @click="view(scope.row)">查看</el-button>
            <!-- <el-button type="text" size="default" @click="viewExtraData(scope.row.extraData)">附加数据</el-button> -->
            <el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('warn:warnRecord:delete')">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange"
        @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
    </el-col>
  </el-row>
  <el-dialog draggable width="40%" v-model="dialog.show" destroy-on-close title="确认事件">
    <el-form :rules="rules" ref="form" :model="confirmOneModel" >
      <el-row style="padding:0;margin-bottom:15px">
        <el-col>
          <el-button v-if="confirmOneModel.status == 0" class="button" type="text">
            <el-upload :show-file-list="false" multiple :http-request="loadingImg">上传图片</el-upload>
          </el-button>
          <el-scrollbar v-if="confirmOneModel.surePicUrls" style="height:220px">
            <div v-if="confirmOneModel.surePicUrls.length" style="height: 220px;display: flex;flex-wrap: nowrap;">
              <div style="margin-right:20px;position: relative;" v-for="(item,index) in confirmOneModel.surePicUrls" :key="item">
                <div @click="deletePic(index)" style="position:absolute;right:-2px;cursor: pointer;z-index: 99;">
                  <el-icon size="20px" v-if="confirmOneModel.status == 0"><DeleteFilled /></el-icon>
                </div>
                <!-- :preview-src-list="[imgServer + item]" -->
                <el-image fit="contain" style="height: 200px; width: 200px" :preview-src-list="swiper(confirmOneModel.surePicUrls)"
                  :src="imgServer + item">
                </el-image>
              </div>
            </div>
          </el-scrollbar>
        </el-col>
      </el-row>
			<el-row style="padding:0;margin:0">
				<el-col :span="24">
				<el-form-item label="确认意见" prop="note">
					<el-input
					v-model="confirmOneModel.sureSuggestion"
					maxlength="200"
					placeholder="请简单说明场景情况"
					show-word-limit
					type="textarea"
					/>
				</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center" style="padding:0;margin:0">
			<el-button type="primary" v-if="confirmOneModel.status == 0" style="width: 100px;height: 30px;margin-top: 20px;" @click="confirmSubmit">保 存
			</el-button>
			<el-button type="primary" v-if="confirmOneModel.status == 1" style="width: 100px;height: 30px;margin-top: 20px;" @click="dialog.show = false">关 闭
			</el-button>
		</el-row>
  </el-dialog>
</template>

<script>
import {
  warnRecordList,
  warnRecordDelete,
  warnInfoConfirm,
  warnInfoDelete,
  warnInfo,
  getEventSourceName
} from "@/api/warn/warn";
import { listDictByNameEn } from "@/api/admin/dict";
import { getDictCss, formatDict } from "@/utils/dict"
import mitt from "@/utils/mitt"
import extraDataDetail from "@/componts/warn/extraDataDetail.vue"
import { fileUpload } from "@/api/admin/file";
import { registerRuntimeCompiler } from 'vue';

export default {
  components: { extraDataDetail },
  data() {
    return {
      searchModel: {},
      confirmOneModel: {
        surePicUrls:[]
      },
      dialog: {
        show:false
      },
      dataList: [],
      statusList: [],
      eventSourceType: [],
      ids: [],
			imgServer: import.meta.env.VITE_BASE_API,
      communityId: localStorage.getItem("communityId"),
			eventLevelList: [],
      tagList: [],
      total: 0,
      pageSize: 10
    };
  },
  methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
    handleSelectionChange(val) {
      let list = []
      for (let item of val) {
        list.push(item.id)
      }
      this.ids = list
    },
    fomatImg(row){
      let extraData = JSON.parse(row)
      if (extraData) {
        // console.log(this.imgServer + extraData.capturePhoto);
        return this.imgServer + extraData.capturePhoto
      } else {
        return 
      }
    },
    // 图片列表
    swiper(pic) {
			let list = []
			for (let i = 0; i < pic.length; i++) {
				list[i] = this.imgServer + pic[i]
			}
			return list
		},
    search() {
      this.searchModel.communityId = this.communityId;
			this.dataList.picUrls = JSON.stringify(this.dataList.picUrls)
      // debugger;
      warnRecordList(this.searchModel)
        .then((res) => {
          this.dataList = res.data.result.list;
          for (let i = 0; i < this.dataList.length; i++) {
						if (this.dataList[i].picUrls && this.dataList[i].picUrls.includes('[')) {
              this.dataList[i].picUrls = JSON.parse(this.dataList[i].picUrls)
            }else {
              let pic = this.dataList[i].picUrls
              this.dataList[i].picUrls = []
              this.dataList[i].picUrls.push(pic)
              console.log(this.dataList[i].picUrls);
            }
					}
          
          this.total = res.data.result.total;
        })
    },
    batch() {
      console.log('批量确认记录已处理',this.ids);
      this.$confirm(`批量确认以下记录已处理, 是否继续? \r\n【${this.ids}】`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          warnInfo(this.ids)
            .then((res) => {
              this.search();
              this.$message.success(res.data.msg);
              this.$refs.multipleTable.clearSelection()
            })
        })
        .catch(() => { });
    },
    batchDelete() {
      console.log('批量删除记录已处理',this.ids);
      this.$confirm(`批量删除以下记录已处理, 是否继续? \r\n【${this.ids}】`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          warnInfoDelete(this.ids)
            .then((res) => {
              this.search();
              this.$message.success(res.data.msg);
              this.$refs.multipleTable.clearSelection()
            })
        })
        .catch(() => { });
    },
    deleted(id) {
      this.$confirm("删除记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          warnRecordDelete(id)
            .then((res) => {
              this.search();
              this.$message.success(res.data.msg);
            })
        })
        .catch(() => { });
    },
    deletePic(item){
      this.confirmOneModel.surePicUrls.splice(item,1)
    },
    formatExtraData(data){
      if (!data) {
        return
      }
      let extraData = JSON.parse(data)
      if (extraData.desc || extraData.photo) {
        return true
      }else {
        return false
      }

    },
    viewExtraData(data){
      if (data) {
        let extraData = JSON.parse(data)
        console.log(extraData);
        mitt.emit('openExtraDataView',extraData)

      }
    },
    confirm(row) {
      this.dialog.show = true
      this.confirmOneModel= {
            surePicUrls:[]
          }
      this.confirmOneModel.id = row.id
      this.confirmOneModel.status = row.status
      // this.ids = []
      // const sendData = {
      //   id:id
      // }
      //     warnInfoConfirm(this.ids)
      //       .then((res) => {
      //         this.search();
      //         this.$message.success(res.data.msg);
      //       })
    },
    view(row){
      console.log(row);
      this.dialog.show = true
      this.confirmOneModel = JSON.parse(JSON.stringify(row))
      if (!this.confirmOneModel.surePicUrls) {
        this.confirmOneModel.surePicUrls = []
      } else {
        this.confirmOneModel.surePicUrls = JSON.parse(this.confirmOneModel.surePicUrls)
      }
    },
    confirmSubmit(){
      // this.confirmOneModel.surePicUrls = JSON.stringify(this.confirmOneModel.surePicUrls)
      console.log(this.confirmOneModel);
      warnInfoConfirm(this.confirmOneModel)
        .then((res) => {
          this.search();
          this.$message.success(res.data.msg);
          this.confirmOneModel= {
            surePicUrls:[]
          },
          this.dialog.show = false
        })
    },
    // 上传图片
    loadingImg(files) {
      let form = new FormData();
      form.append("file", files.file);
      form.append("modulesName", 'warn');
      form.append("functionName", 'warnRecord');
      form.append("communityId", localStorage.getItem('communityId'));
      fileUpload(form).then((res) => {
          this.confirmOneModel.surePicUrls.push(res.data.result.url);
          console.log(this.confirmOneModel);
        if (res.data.code == 0) {
          // this.$message.success("上传成功");
        }
      });
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    handleSizeChange(num) {
      this.searchModel.pageSize = num
      this.search()
    },
    async init() {
      try {
        this.searchModel.communityId = this.communityId;
        let res = await warnRecordList(this.searchModel);
        this.dataList = res.data.result.list;
        this.total = res.data.result.total;

        let eventLevel_res = await listDictByNameEn('event_level')
				this.eventLevelList = eventLevel_res.data.result
        getEventSourceName({communityId:this.communityId}).then(res => {
          this.eventSourceType = res.data.result
        })
        let deviceStatus = await listDictByNameEn("warn_handle_status");
        this.statusList = deviceStatus.data.result;

        // 处理图片数据
        for (let i = 0; i < this.dataList.length; i++) {
          if (this.dataList[i].picUrls && this.dataList[i].picUrls.includes('[')) {
            this.dataList[i].picUrls = JSON.parse(this.dataList[i].picUrls)
          } else {
            let pic = this.dataList[i].picUrls
            this.dataList[i].picUrls = []
            this.dataList[i].picUrls.push(pic)
            console.log(this.dataList[i].picUrls);
          }
        }
      } catch (err) {
        console.log(err);
      }
    },
  },
  created() {
    this.init();
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
.extra-data{
  color:#6094ff;
  position: absolute;
  top: 4px;
}
.extra-data:hover{
  color:#88baff;
  cursor: pointer;
}
</style>
