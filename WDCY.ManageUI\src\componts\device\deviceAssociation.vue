<template>
    <el-dialog draggable
    top="3vh"
    width="1000px"
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title">
    <el-form :rules="rules" :model="warnEventSourceModel" label-width="120px">
      <el-row>
        <el-col style="display: flex; justify-content: flex-end;">
          <el-select v-model="deviceModel.linkType" placeholder="设施类型" clearable >
            <el-option v-for="item in deviceType" :key="item.nameEn" :label="item.nameCn"
              :value="item.nameCn"></el-option>
          </el-select>
			    <el-button style="margin-left:10px" type="primary" @click="deviceSearch">搜 索</el-button>
          
        </el-col>
        <el-col style="margin-top:10px">
          <!-- <el-form-item label="关联设施" prop="note" style=" height: 380px;width:100%"> -->
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="warnEventSourceModel.warnEventDetectRules"
                  style="width: 100%;height: 335px; overflow:auto"
                  border
                >
                  <el-table-column align="center" label="序号" width="115">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.linkPriority" :min="1" size="small" style="width: 80px" @change="editSort(scope.row)"></el-input-number>
                      <!-- <el-tag>#{{scope.row.sort}}#</el-tag> -->
                    </template>
                  </el-table-column>
                  <el-table-column prop="id" align="center" label="ID" width="200"></el-table-column>
                  <el-table-column prop="linkDevId" align="center" label="设施ID" width="200" ></el-table-column>
                  <el-table-column prop="linkDevName" show-overflow-tooltip align="center" label="设施名称" ></el-table-column>
                  <el-table-column prop="linkDevIp" align="center" label="设施IP" width="130"></el-table-column>
                  <el-table-column prop="linkType" align="center" width="81" label="设施类型" />

                  <!-- <el-table-column align="center" label="状态" width="80">
                    <template #default="scope">
                      <el-switch
                        :active-value="1"
                        :inactive-value="0"
                        v-model="scope.row.status"
                      >
                      </el-switch>
                    </template>
                  </el-table-column> -->
                  <el-table-column align="center" label="操作" width="80">
                    <template #default="scope">
                      <el-button
                        type="text"
                        size="default"
                        @click="remove(scope.row.id)"
                        >移除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col
                  :span="24"
                  style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  "
                >
                <el-button
                  style="font-size: 25px; width: 100%; border-radius: 0; margin-top: 10px;"
                  size="default"
                  @click="add"
                >
                  <el-icon :size="20">
                    <plus></plus>
                  </el-icon>
                </el-button>
              </el-col>
            </el-row>
          <!-- </el-form-item> -->
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center">
      <el-button style="width: 100px;margin-top: 10px;" type="primary" @click="close"
        >关 闭</el-button
      >
    </el-row>
    </el-dialog>
    <!-- 选择设施弹窗 -->
    <el-dialog top="3vh"
    width="1000px"
    v-loading="loading"
    v-model="secondDialog.show"
    :title="secondDialog.title">
    <el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.name" @keydown.enter="search" placeholder="名称" clearable />
		</el-col>
		<el-col :span="4">
			<!-- <el-cascader style="width: 100%;" :props="{ checkStrictly: true }" :options="typeNodeList"
				@change="handleChange" clearable placeholder="选择类型" /> -->
        <el-select v-model="searchModel.deviceTypeName" placeholder="选择设施类型" clearable>
            <el-option v-for="item in deviceType" :key="item.nameEn" :label="item.nameCn"
              :value="item.nameCn"></el-option>
          </el-select>
		</el-col>
		<el-col :span="4">
			<el-select style="width: 100%;" v-model="searchModel.status" placeholder="状态" clearable>
				<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
					:value="parseInt(item.nameEn)"></el-option>
			</el-select>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
	</el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-table row-key="id" :data="tableList" ref="multipleTable" border style="width: 100%;height: 610px;margin: 10px 0;" @selection-change="handleSelectionChange">
          <el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
          <el-table-column prop="devNo" show-overflow-tooltip align="center" label="设施编号" />
          <el-table-column prop="name" show-overflow-tooltip align="center" label="名称" />
          <el-table-column prop="devIp" width="120" align="center" label="设施IP" />
          <el-table-column prop="typeName" width="120" align="center" label="设施类型" />

          <el-table-column prop="status" width="120" align="center" label="状态">
            <template #default="scope">
              <el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
     
    </el-row>
    <el-row justify="center">
      <el-button style="width: 100px" :disabled="isDisabled=='0'" type="primary" @click="addSubmit"
        >提 交</el-button
      >
    </el-row>
    </el-dialog>
</template>

<script>
import { deviceInfoAll2List, deviceLink, deviceLinkAdd, deviceLinkEdit, deviceLinkDelete, getDeviceTypeList } from "@/api/device/device"
import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict"

export default {
    props: ["statusList", "dataList", "typeNodeList", 'total', 'deviceType'],
  data() {
    return {
      loading: false,
      warnEventSourceModel: {warnEventDetectRules:[]},
      deviceList: [],
      communityId: localStorage.getItem("communityId"),
      dialog: {},
      secondDialog: {},
      deviceLinkDtoList: [],
      searchModel: {},
      deviceModel: {},
      tableList:[],
      devId:""
    };
  },
  methods: {
    getDictCss(dicList, cellValue) {
        return getDictCss(dicList, cellValue)
    }, 
    formatDict(dicList, cellValue) {
        return formatDict(dicList, cellValue)
    },
    close(){
      this.dialog.show = false
    },
    search() {
        // this.searchModel.type = 5
        // this.searchModel.pageSize = this.total
        this.searchModel.communityId = this.communityId
        getDeviceTypeList(this.searchModel)
            .then(res => {
                this.tableList = res.data.result
                // this.total = res.data.result.total
            })
    },
    deviceSearch(){
      this.deviceModel.devId = this.devId
      deviceLink(this.deviceModel).then(res => {
        // console.log(res);
        this.warnEventSourceModel.warnEventDetectRules = res.data.result
			})
    },
    editSort(row){
      console.log(row);
      deviceLinkEdit(row)
    },
    remove(id) {
      this.$confirm('删除设施, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deviceLinkDelete(id)
					.then(res => {
            this.deviceSearch()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })

      // deviceLinkDelete(id)
      
    },
    add() {
        this.secondDialog.show = true
        this.secondDialog.title='选择设施'
        this.searchModel = {}
        this.search()
    },
    addSubmit(){
      console.log(this.deviceLinkDtoList);
      const data = {deviceLinkDtoList:this.deviceLinkDtoList,devId:this.devId}
      deviceLinkAdd(data).then(res => {
        this.deviceSearch()
        this.$refs.multipleTable.clearSelection()
        this.$message.success(res.data.msg);
        this.secondDialog.show = false;
      })
    },
    // 多选操作
    handleSelectionChange(val) {
      console.log(val);
      let list = []
      const index = 0
      for (let item of val) {
        list.push({linkDevId:item.id,linkType:item.typeName,linkPriority:this.warnEventSourceModel.warnEventDetectRules.length + 1})
      }
      console.log(list);
      this.deviceLinkDtoList = list
    },
    handleChange(e) {
			if (e == null) {
				this.searchModel.devTypeId = null
				return
			} else {
				this.searchModel.devTypeId = e[e.length - 1]
			}
		},
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openDeviceAssociation", (data) => {
        this.devId = data.id
        this.warnEventSourceModel.warnEventDetectRules = []
			deviceLink({devId:data.id,pageSize:9999}).then(res => {
        // console.log(res);
        this.warnEventSourceModel.warnEventDetectRules = res.data.result
			})
        
        console.log(data);
        this.dialog.show = true;
        this.dialog.title = "设施关联";
        this.search()
      });
    });
  },
}
</script>