/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.119
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as fn,c as Tn}from"./chunk-IWAQ2DE4.js";var Dn=Tn((nt,bt)=>{var _=_||{};_.scope={};_.arrayIteratorImpl=function(p){var c=0;return function(){return c<p.length?{done:!1,value:p[c++]}:{done:!0}}};_.arrayIterator=function(p){return{next:_.arrayIteratorImpl(p)}};_.makeIterator=function(p){var c=typeof Symbol<"u"&&Symbol.iterator&&p[Symbol.iterator];return c?c.call(p):_.arrayIterator(p)};_.ASSUME_ES5=!1;_.ASSUME_NO_NATIVE_MAP=!1;_.ASSUME_NO_NATIVE_SET=!1;_.SIMPLE_FROUND_POLYFILL=!1;_.ISOLATE_POLYFILLS=!1;_.FORCE_POLYFILL_PROMISE=!1;_.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;_.getGlobal=function(p){p=[typeof globalThis=="object"&&globalThis,p,typeof window=="object"&&window,typeof self=="object"&&self,typeof global=="object"&&global];for(var c=0;c<p.length;++c){var s=p[c];if(s&&s.Math==Math)return s}throw Error("Cannot find global object")};_.global=_.getGlobal(nt);_.defineProperty=_.ASSUME_ES5||typeof Object.defineProperties=="function"?Object.defineProperty:function(p,c,s){return p==Array.prototype||p==Object.prototype||(p[c]=s.value),p};_.IS_SYMBOL_NATIVE=typeof Symbol=="function"&&typeof Symbol("x")=="symbol";_.TRUST_ES6_POLYFILLS=!_.ISOLATE_POLYFILLS||_.IS_SYMBOL_NATIVE;_.polyfills={};_.propertyToPolyfillSymbol={};_.POLYFILL_PREFIX="$jscp$";_.polyfill=function(p,c,s,y){c&&(_.ISOLATE_POLYFILLS?_.polyfillIsolated(p,c,s,y):_.polyfillUnisolated(p,c,s,y))};_.polyfillUnisolated=function(p,c,s,y){for(s=_.global,p=p.split("."),y=0;y<p.length-1;y++){var a=p[y];if(!(a in s))return;s=s[a]}p=p[p.length-1],y=s[p],c=c(y),c!=y&&c!=null&&_.defineProperty(s,p,{configurable:!0,writable:!0,value:c})};_.polyfillIsolated=function(p,c,s,y){var a=p.split(".");p=a.length===1,y=a[0],y=!p&&y in _.polyfills?_.polyfills:_.global;for(var g=0;g<a.length-1;g++){var i=a[g];if(!(i in y))return;y=y[i]}a=a[a.length-1],s=_.IS_SYMBOL_NATIVE&&s==="es6"?y[a]:null,c=c(s),c!=null&&(p?_.defineProperty(_.polyfills,a,{configurable:!0,writable:!0,value:c}):c!==s&&(_.propertyToPolyfillSymbol[a]===void 0&&(s=1e9*Math.random()>>>0,_.propertyToPolyfillSymbol[a]=_.IS_SYMBOL_NATIVE?_.global.Symbol(a):_.POLYFILL_PREFIX+s+"$"+a),_.defineProperty(y,_.propertyToPolyfillSymbol[a],{configurable:!0,writable:!0,value:c})))};_.polyfill("Promise",function(p){function c(){this.batch_=null}function s(i){return i instanceof a?i:new a(function(l,h){l(i)})}if(p&&(!(_.FORCE_POLYFILL_PROMISE||_.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&typeof _.global.PromiseRejectionEvent>"u")||!_.global.Promise||_.global.Promise.toString().indexOf("[native code]")===-1))return p;c.prototype.asyncExecute=function(i){if(this.batch_==null){this.batch_=[];var l=this;this.asyncExecuteFunction(function(){l.executeBatch_()})}this.batch_.push(i)};var y=_.global.setTimeout;c.prototype.asyncExecuteFunction=function(i){y(i,0)},c.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var i=this.batch_;this.batch_=[];for(var l=0;l<i.length;++l){var h=i[l];i[l]=null;try{h()}catch(I){this.asyncThrow_(I)}}}this.batch_=null},c.prototype.asyncThrow_=function(i){this.asyncExecuteFunction(function(){throw i})};var a=function(i){this.state_=0,this.result_=void 0,this.onSettledCallbacks_=[],this.isRejectionHandled_=!1;var l=this.createResolveAndReject_();try{i(l.resolve,l.reject)}catch(h){l.reject(h)}};a.prototype.createResolveAndReject_=function(){function i(I){return function(C){h||(h=!0,I.call(l,C))}}var l=this,h=!1;return{resolve:i(this.resolveTo_),reject:i(this.reject_)}},a.prototype.resolveTo_=function(i){if(i===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(i instanceof a)this.settleSameAsPromise_(i);else{t:switch(typeof i){case"object":var l=i!=null;break t;case"function":l=!0;break t;default:l=!1}l?this.resolveToNonPromiseObj_(i):this.fulfill_(i)}},a.prototype.resolveToNonPromiseObj_=function(i){var l=void 0;try{l=i.then}catch(h){this.reject_(h);return}typeof l=="function"?this.settleSameAsThenable_(l,i):this.fulfill_(i)},a.prototype.reject_=function(i){this.settle_(2,i)},a.prototype.fulfill_=function(i){this.settle_(1,i)},a.prototype.settle_=function(i,l){if(this.state_!=0)throw Error("Cannot settle("+i+", "+l+"): Promise already settled in state"+this.state_);this.state_=i,this.result_=l,this.state_===2&&this.scheduleUnhandledRejectionCheck_(),this.executeOnSettledCallbacks_()},a.prototype.scheduleUnhandledRejectionCheck_=function(){var i=this;y(function(){if(i.notifyUnhandledRejection_()){var l=_.global.console;typeof l<"u"&&l.error(i.result_)}},1)},a.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var i=_.global.CustomEvent,l=_.global.Event,h=_.global.dispatchEvent;return typeof h>"u"?!0:(typeof i=="function"?i=new i("unhandledrejection",{cancelable:!0}):typeof l=="function"?i=new l("unhandledrejection",{cancelable:!0}):(i=_.global.document.createEvent("CustomEvent"),i.initCustomEvent("unhandledrejection",!1,!0,i)),i.promise=this,i.reason=this.result_,h(i))},a.prototype.executeOnSettledCallbacks_=function(){if(this.onSettledCallbacks_!=null){for(var i=0;i<this.onSettledCallbacks_.length;++i)g.asyncExecute(this.onSettledCallbacks_[i]);this.onSettledCallbacks_=null}};var g=new c;return a.prototype.settleSameAsPromise_=function(i){var l=this.createResolveAndReject_();i.callWhenSettled_(l.resolve,l.reject)},a.prototype.settleSameAsThenable_=function(i,l){var h=this.createResolveAndReject_();try{i.call(l,h.resolve,h.reject)}catch(I){h.reject(I)}},a.prototype.then=function(i,l){function h(f,T){return typeof f=="function"?function(G){try{I(f(G))}catch(V){C(V)}}:T}var I,C,K=new a(function(f,T){I=f,C=T});return this.callWhenSettled_(h(i,I),h(l,C)),K},a.prototype.catch=function(i){return this.then(void 0,i)},a.prototype.callWhenSettled_=function(i,l){function h(){switch(I.state_){case 1:i(I.result_);break;case 2:l(I.result_);break;default:throw Error("Unexpected state: "+I.state_)}}var I=this;this.onSettledCallbacks_==null?g.asyncExecute(h):this.onSettledCallbacks_.push(h),this.isRejectionHandled_=!0},a.resolve=s,a.reject=function(i){return new a(function(l,h){h(i)})},a.race=function(i){return new a(function(l,h){for(var I=_.makeIterator(i),C=I.next();!C.done;C=I.next())s(C.value).callWhenSettled_(l,h)})},a.all=function(i){var l=_.makeIterator(i),h=l.next();return h.done?s([]):new a(function(I,C){function K(G){return function(V){f[G]=V,T--,T==0&&I(f)}}var f=[],T=0;do f.push(void 0),T++,s(h.value).callWhenSettled_(K(f.length-1),C),h=l.next();while(!h.done)})},a},"es6","es3");_.owns=function(p,c){return Object.prototype.hasOwnProperty.call(p,c)};_.assign=_.TRUST_ES6_POLYFILLS&&typeof Object.assign=="function"?Object.assign:function(p,c){for(var s=1;s<arguments.length;s++){var y=arguments[s];if(y)for(var a in y)_.owns(y,a)&&(p[a]=y[a])}return p};_.polyfill("Object.assign",function(p){return p||_.assign},"es6","es3");_.checkStringArgs=function(p,c,s){if(p==null)throw new TypeError("The 'this' value for String.prototype."+s+" must not be null or undefined");if(c instanceof RegExp)throw new TypeError("First argument to String.prototype."+s+" must not be a regular expression");return p+""};_.polyfill("String.prototype.startsWith",function(p){return p||function(c,s){var y=_.checkStringArgs(this,c,"startsWith");c+="";var a=y.length,g=c.length;s=Math.max(0,Math.min(s|0,y.length));for(var i=0;i<g&&s<a;)if(y[s++]!=c[i++])return!1;return i>=g}},"es6","es3");_.polyfill("Array.prototype.copyWithin",function(p){function c(s){return s=Number(s),s===1/0||s===-1/0?s:s|0}return p||function(s,y,a){var g=this.length;if(s=c(s),y=c(y),a=a===void 0?g:c(a),s=0>s?Math.max(g+s,0):Math.min(s,g),y=0>y?Math.max(g+y,0):Math.min(y,g),a=0>a?Math.max(g+a,0):Math.min(a,g),s<y)for(;y<a;)y in this?this[s++]=this[y++]:(delete this[s++],y++);else for(a=Math.min(a,g+y-s),s+=a-y;a>y;)--a in this?this[--s]=this[a]:delete this[--s];return this}},"es6","es3");_.typedArrayCopyWithin=function(p){return p||Array.prototype.copyWithin};_.polyfill("Int8Array.prototype.copyWithin",_.typedArrayCopyWithin,"es6","es5");_.polyfill("Uint8Array.prototype.copyWithin",_.typedArrayCopyWithin,"es6","es5");_.polyfill("Uint8ClampedArray.prototype.copyWithin",_.typedArrayCopyWithin,"es6","es5");_.polyfill("Int16Array.prototype.copyWithin",_.typedArrayCopyWithin,"es6","es5");_.polyfill("Uint16Array.prototype.copyWithin",_.typedArrayCopyWithin,"es6","es5");_.polyfill("Int32Array.prototype.copyWithin",_.typedArrayCopyWithin,"es6","es5");_.polyfill("Uint32Array.prototype.copyWithin",_.typedArrayCopyWithin,"es6","es5");_.polyfill("Float32Array.prototype.copyWithin",_.typedArrayCopyWithin,"es6","es5");_.polyfill("Float64Array.prototype.copyWithin",_.typedArrayCopyWithin,"es6","es5");var dt=function(){var p=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<"u"&&(p=p||__filename),function(c){function s(e){return t.locateFile?t.locateFile(e,Q):Q+e}function y(e,r,n){var o=r+n;for(n=r;e[n]&&!(n>=o);)++n;if(16<n-r&&e.buffer&&Ot)return Ot.decode(e.subarray(r,n));for(o="";r<n;){var m=e[r++];if(m&128){var b=e[r++]&63;if((m&224)==192)o+=String.fromCharCode((m&31)<<6|b);else{var X=e[r++]&63;m=(m&240)==224?(m&15)<<12|b<<6|X:(m&7)<<18|b<<12|X<<6|e[r++]&63,65536>m?o+=String.fromCharCode(m):(m-=65536,o+=String.fromCharCode(55296|m>>10,56320|m&1023))}}else o+=String.fromCharCode(m)}return o}function a(e,r){return e?y(tt,e,r):""}function g(){var e=_t.buffer;t.HEAP8=q=new Int8Array(e),t.HEAP16=new Int16Array(e),t.HEAP32=et=new Int32Array(e),t.HEAPU8=tt=new Uint8Array(e),t.HEAPU16=new Uint16Array(e),t.HEAPU32=x=new Uint32Array(e),t.HEAPF32=new Float32Array(e),t.HEAPF64=new Float64Array(e)}function i(e){throw t.onAbort&&t.onAbort(e),e="Aborted("+e+")",J(e),Gt=!0,e=new WebAssembly.RuntimeError(e+". Build with -sASSERTIONS for more info."),ot(e),e}function l(e){try{if(e==z&&Z)return new Uint8Array(Z);if(ut)return ut(e);throw"both async and sync fetching of the wasm failed"}catch(r){i(r)}}function h(){if(!Z&&(It||$)){if(typeof fetch=="function"&&!z.startsWith("file://"))return fetch(z,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+z+"'";return e.arrayBuffer()}).catch(function(){return l(z)});if(st)return new Promise(function(e,r){st(z,function(n){e(new Uint8Array(n))},r)})}return Promise.resolve().then(function(){return l(z)})}function I(e){for(;0<e.length;)e.shift()(t)}function C(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(r){x[this.ptr+4>>2]=r},this.get_type=function(){return x[this.ptr+4>>2]},this.set_destructor=function(r){x[this.ptr+8>>2]=r},this.get_destructor=function(){return x[this.ptr+8>>2]},this.set_refcount=function(r){et[this.ptr>>2]=r},this.set_caught=function(r){q[this.ptr+12>>0]=r?1:0},this.get_caught=function(){return q[this.ptr+12>>0]!=0},this.set_rethrown=function(r){q[this.ptr+13>>0]=r?1:0},this.get_rethrown=function(){return q[this.ptr+13>>0]!=0},this.init=function(r,n){this.set_adjusted_ptr(0),this.set_type(r),this.set_destructor(n),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){et[this.ptr>>2]+=1},this.release_ref=function(){var r=et[this.ptr>>2];return et[this.ptr>>2]=r-1,r===1},this.set_adjusted_ptr=function(r){x[this.ptr+16>>2]=r},this.get_adjusted_ptr=function(){return x[this.ptr+16>>2]},this.get_exception_ptr=function(){if(ln(this.get_type()))return x[this.excPtr>>2];var r=this.get_adjusted_ptr();return r!==0?r:this.excPtr}}function K(){function e(){if(!it&&(it=!0,t.calledRun=!0,!Gt)){if(Rt=!0,I(ct),ht(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),t.postRun)for(typeof t.postRun=="function"&&(t.postRun=[t.postRun]);t.postRun.length;)Pt.unshift(t.postRun.shift());I(Pt)}}if(!(0<k)){if(t.preRun)for(typeof t.preRun=="function"&&(t.preRun=[t.preRun]);t.preRun.length;)jt.unshift(t.preRun.shift());I(jt),0<k||(t.setStatus?(t.setStatus("Running..."),setTimeout(function(){setTimeout(function(){t.setStatus("")},1),e()},1)):e())}}function f(){}function T(e){return(e||f).__cache__}function G(e,r){var n=T(r),o=n[e];return o||(o=Object.create((r||f).prototype),o.ptr=e,n[e]=o)}function V(e){if(typeof e=="string"){for(var r=0,n=0;n<e.length;++n){var o=e.charCodeAt(n);127>=o?r++:2047>=o?r+=2:55296<=o&&57343>=o?(r+=4,++n):r+=3}if(r=Array(r+1),n=0,o=r.length,0<o){o=n+o-1;for(var m=0;m<e.length;++m){var b=e.charCodeAt(m);if(55296<=b&&57343>=b){var X=e.charCodeAt(++m);b=65536+((b&1023)<<10)|X&1023}if(127>=b){if(n>=o)break;r[n++]=b}else{if(2047>=b){if(n+1>=o)break;r[n++]=192|b>>6}else{if(65535>=b){if(n+2>=o)break;r[n++]=224|b>>12}else{if(n+3>=o)break;r[n++]=240|b>>18,r[n++]=128|b>>12&63}r[n++]=128|b>>6&63}r[n++]=128|b&63}}r[n]=0}return e=d.alloc(r,q),d.copy(r,q,e),e}return e}function at(e){if(typeof e=="object"){var r=d.alloc(e,q);return d.copy(e,q,r),r}return e}function H(){throw"cannot construct a VoidPtr, no constructor in IDL"}function B(){this.ptr=Mt(),T(B)[this.ptr]=this}function w(){this.ptr=Ft(),T(w)[this.ptr]=this}function Y(){this.ptr=wt(),T(Y)[this.ptr]=this}function A(){this.ptr=Vt(),T(A)[this.ptr]=this}function E(){this.ptr=Jt(),T(E)[this.ptr]=this}function j(){this.ptr=ne(),T(j)[this.ptr]=this}function P(){this.ptr=ae(),T(P)[this.ptr]=this}function O(){this.ptr=ce(),T(O)[this.ptr]=this}function W(){this.ptr=de(),T(W)[this.ptr]=this}function v(){throw"cannot construct a Status, no constructor in IDL"}function R(){this.ptr=Ie(),T(R)[this.ptr]=this}function S(){this.ptr=Ge(),T(S)[this.ptr]=this}function M(){this.ptr=Re(),T(M)[this.ptr]=this}function N(){this.ptr=Ue(),T(N)[this.ptr]=this}function U(){this.ptr=we(),T(U)[this.ptr]=this}function F(){this.ptr=We(),T(F)[this.ptr]=this}function L(){this.ptr=He(),T(L)[this.ptr]=this}function D(){this.ptr=Ke(),T(D)[this.ptr]=this}function u(){this.ptr=_r(),T(u)[this.ptr]=this}c=c===void 0?{}:c;var t=typeof c<"u"?c:{},ht,ot;t.ready=new Promise(function(e,r){ht=e,ot=r});var At=!1,Tt=!1;t.onRuntimeInitialized=function(){At=!0,Tt&&typeof t.onModuleLoaded=="function"&&t.onModuleLoaded(t)},t.onModuleParsed=function(){Tt=!0,At&&typeof t.onModuleLoaded=="function"&&t.onModuleLoaded(t)},t.isVersionSupported=function(e){return typeof e!="string"?!1:(e=e.split("."),2>e.length||3<e.length?!1:e[0]==1&&0<=e[1]&&5>=e[1]?!0:!(e[0]!=0||10<e[1]))};var Dt=Object.assign({},t),It=typeof window=="object",$=typeof importScripts=="function",gt=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",Q="";if(gt){var vt=fn("fs"),pt=fn("path");Q=$?pt.dirname(Q)+"/":__dirname+"/";var Et=function(e,r){return e=e.startsWith("file://")?new URL(e):pt.normalize(e),vt.readFileSync(e,r?void 0:"utf8")},ut=function(e){return e=Et(e,!0),e.buffer||(e=new Uint8Array(e)),e},st=function(e,r,n){e=e.startsWith("file://")?new URL(e):pt.normalize(e),vt.readFile(e,function(o,m){o?n(o):r(m.buffer)})};1<process.argv.length&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),t.inspect=function(){return"[Emscripten Module object]"}}else(It||$)&&($?Q=self.location.href:typeof document<"u"&&document.currentScript&&(Q=document.currentScript.src),p&&(Q=p),Q=Q.indexOf("blob:")!==0?Q.substr(0,Q.replace(/[?#].*/,"").lastIndexOf("/")+1):"",Et=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},$&&(ut=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),st=function(e,r,n){var o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="arraybuffer",o.onload=function(){o.status==200||o.status==0&&o.response?r(o.response):n()},o.onerror=n,o.send(null)});var dn=t.print||console.log.bind(console),J=t.printErr||console.warn.bind(console);Object.assign(t,Dt),Dt=null;var Z;t.wasmBinary&&(Z=t.wasmBinary),typeof WebAssembly!="object"&&i("no native wasm support detected");var _t,Gt=!1,Ot=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0,q,tt,et,x,jt=[],ct=[],Pt=[],Rt=!1,k=0,yt=null,rt=null,z="draco_decoder.wasm";z.startsWith("data:application/octet-stream;base64,")||(z=s(z));var bn=0,hn=[null,[],[]],An={b:function(e,r,n){throw new C(e).init(r,n),bn++,e},a:function(){i("")},g:function(e,r,n){tt.copyWithin(e,r,r+n)},e:function(e){var r=tt.length;if(e>>>=0,2147483648<e)return!1;for(var n=1;4>=n;n*=2){var o=r*(1+.2/n);o=Math.min(o,e+100663296);var m=Math;o=Math.max(e,o),m=m.min.call(m,2147483648,o+(65536-o%65536)%65536);t:{o=_t.buffer;try{_t.grow(m-o.byteLength+65535>>>16),g();var b=1;break t}catch{}b=void 0}if(b)return!0}return!1},f:function(e){return 52},d:function(e,r,n,o,m){return 70},c:function(e,r,n,o){for(var m=0,b=0;b<n;b++){var X=x[r>>2],mn=x[r+4>>2];r+=8;for(var lt=0;lt<mn;lt++){var mt=tt[X+lt],ft=hn[e];mt===0||mt===10?((e===1?dn:J)(y(ft,0)),ft.length=0):ft.push(mt)}m+=mn}return x[o>>2]=m,0}};(function(){function e(m,b){t.asm=m.exports,_t=t.asm.h,g(),ct.unshift(t.asm.i),k--,t.monitorRunDependencies&&t.monitorRunDependencies(k),k==0&&(yt!==null&&(clearInterval(yt),yt=null),rt&&(m=rt,rt=null,m()))}function r(m){e(m.instance)}function n(m){return h().then(function(b){return WebAssembly.instantiate(b,o)}).then(function(b){return b}).then(m,function(b){J("failed to asynchronously prepare wasm: "+b),i(b)})}var o={a:An};if(k++,t.monitorRunDependencies&&t.monitorRunDependencies(k),t.instantiateWasm)try{return t.instantiateWasm(o,e)}catch(m){J("Module.instantiateWasm callback failed with error: "+m),ot(m)}return function(){return Z||typeof WebAssembly.instantiateStreaming!="function"||z.startsWith("data:application/octet-stream;base64,")||z.startsWith("file://")||gt||typeof fetch!="function"?n(r):fetch(z,{credentials:"same-origin"}).then(function(m){return WebAssembly.instantiateStreaming(m,o).then(r,function(b){return J("wasm streaming compile failed: "+b),J("falling back to ArrayBuffer instantiation"),n(r)})})}().catch(ot),{}})();var St=t._emscripten_bind_VoidPtr___destroy___0=function(){return(St=t._emscripten_bind_VoidPtr___destroy___0=t.asm.k).apply(null,arguments)},Mt=t._emscripten_bind_DecoderBuffer_DecoderBuffer_0=function(){return(Mt=t._emscripten_bind_DecoderBuffer_DecoderBuffer_0=t.asm.l).apply(null,arguments)},Nt=t._emscripten_bind_DecoderBuffer_Init_2=function(){return(Nt=t._emscripten_bind_DecoderBuffer_Init_2=t.asm.m).apply(null,arguments)},Ut=t._emscripten_bind_DecoderBuffer___destroy___0=function(){return(Ut=t._emscripten_bind_DecoderBuffer___destroy___0=t.asm.n).apply(null,arguments)},Ft=t._emscripten_bind_AttributeTransformData_AttributeTransformData_0=function(){return(Ft=t._emscripten_bind_AttributeTransformData_AttributeTransformData_0=t.asm.o).apply(null,arguments)},Lt=t._emscripten_bind_AttributeTransformData_transform_type_0=function(){return(Lt=t._emscripten_bind_AttributeTransformData_transform_type_0=t.asm.p).apply(null,arguments)},Ct=t._emscripten_bind_AttributeTransformData___destroy___0=function(){return(Ct=t._emscripten_bind_AttributeTransformData___destroy___0=t.asm.q).apply(null,arguments)},wt=t._emscripten_bind_GeometryAttribute_GeometryAttribute_0=function(){return(wt=t._emscripten_bind_GeometryAttribute_GeometryAttribute_0=t.asm.r).apply(null,arguments)},zt=t._emscripten_bind_GeometryAttribute___destroy___0=function(){return(zt=t._emscripten_bind_GeometryAttribute___destroy___0=t.asm.s).apply(null,arguments)},Vt=t._emscripten_bind_PointAttribute_PointAttribute_0=function(){return(Vt=t._emscripten_bind_PointAttribute_PointAttribute_0=t.asm.t).apply(null,arguments)},Bt=t._emscripten_bind_PointAttribute_size_0=function(){return(Bt=t._emscripten_bind_PointAttribute_size_0=t.asm.u).apply(null,arguments)},Wt=t._emscripten_bind_PointAttribute_GetAttributeTransformData_0=function(){return(Wt=t._emscripten_bind_PointAttribute_GetAttributeTransformData_0=t.asm.v).apply(null,arguments)},Qt=t._emscripten_bind_PointAttribute_attribute_type_0=function(){return(Qt=t._emscripten_bind_PointAttribute_attribute_type_0=t.asm.w).apply(null,arguments)},xt=t._emscripten_bind_PointAttribute_data_type_0=function(){return(xt=t._emscripten_bind_PointAttribute_data_type_0=t.asm.x).apply(null,arguments)},Yt=t._emscripten_bind_PointAttribute_num_components_0=function(){return(Yt=t._emscripten_bind_PointAttribute_num_components_0=t.asm.y).apply(null,arguments)},Ht=t._emscripten_bind_PointAttribute_normalized_0=function(){return(Ht=t._emscripten_bind_PointAttribute_normalized_0=t.asm.z).apply(null,arguments)},qt=t._emscripten_bind_PointAttribute_byte_stride_0=function(){return(qt=t._emscripten_bind_PointAttribute_byte_stride_0=t.asm.A).apply(null,arguments)},kt=t._emscripten_bind_PointAttribute_byte_offset_0=function(){return(kt=t._emscripten_bind_PointAttribute_byte_offset_0=t.asm.B).apply(null,arguments)},Xt=t._emscripten_bind_PointAttribute_unique_id_0=function(){return(Xt=t._emscripten_bind_PointAttribute_unique_id_0=t.asm.C).apply(null,arguments)},Kt=t._emscripten_bind_PointAttribute___destroy___0=function(){return(Kt=t._emscripten_bind_PointAttribute___destroy___0=t.asm.D).apply(null,arguments)},Jt=t._emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=function(){return(Jt=t._emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=t.asm.E).apply(null,arguments)},$t=t._emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=function(){return($t=t._emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=t.asm.F).apply(null,arguments)},Zt=t._emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=function(){return(Zt=t._emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=t.asm.G).apply(null,arguments)},te=t._emscripten_bind_AttributeQuantizationTransform_min_value_1=function(){return(te=t._emscripten_bind_AttributeQuantizationTransform_min_value_1=t.asm.H).apply(null,arguments)},ee=t._emscripten_bind_AttributeQuantizationTransform_range_0=function(){return(ee=t._emscripten_bind_AttributeQuantizationTransform_range_0=t.asm.I).apply(null,arguments)},re=t._emscripten_bind_AttributeQuantizationTransform___destroy___0=function(){return(re=t._emscripten_bind_AttributeQuantizationTransform___destroy___0=t.asm.J).apply(null,arguments)},ne=t._emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=function(){return(ne=t._emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=t.asm.K).apply(null,arguments)},oe=t._emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=function(){return(oe=t._emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=t.asm.L).apply(null,arguments)},_e=t._emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=function(){return(_e=t._emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=t.asm.M).apply(null,arguments)},ie=t._emscripten_bind_AttributeOctahedronTransform___destroy___0=function(){return(ie=t._emscripten_bind_AttributeOctahedronTransform___destroy___0=t.asm.N).apply(null,arguments)},ae=t._emscripten_bind_PointCloud_PointCloud_0=function(){return(ae=t._emscripten_bind_PointCloud_PointCloud_0=t.asm.O).apply(null,arguments)},pe=t._emscripten_bind_PointCloud_num_attributes_0=function(){return(pe=t._emscripten_bind_PointCloud_num_attributes_0=t.asm.P).apply(null,arguments)},ue=t._emscripten_bind_PointCloud_num_points_0=function(){return(ue=t._emscripten_bind_PointCloud_num_points_0=t.asm.Q).apply(null,arguments)},se=t._emscripten_bind_PointCloud___destroy___0=function(){return(se=t._emscripten_bind_PointCloud___destroy___0=t.asm.R).apply(null,arguments)},ce=t._emscripten_bind_Mesh_Mesh_0=function(){return(ce=t._emscripten_bind_Mesh_Mesh_0=t.asm.S).apply(null,arguments)},ye=t._emscripten_bind_Mesh_num_faces_0=function(){return(ye=t._emscripten_bind_Mesh_num_faces_0=t.asm.T).apply(null,arguments)},le=t._emscripten_bind_Mesh_num_attributes_0=function(){return(le=t._emscripten_bind_Mesh_num_attributes_0=t.asm.U).apply(null,arguments)},me=t._emscripten_bind_Mesh_num_points_0=function(){return(me=t._emscripten_bind_Mesh_num_points_0=t.asm.V).apply(null,arguments)},fe=t._emscripten_bind_Mesh___destroy___0=function(){return(fe=t._emscripten_bind_Mesh___destroy___0=t.asm.W).apply(null,arguments)},de=t._emscripten_bind_Metadata_Metadata_0=function(){return(de=t._emscripten_bind_Metadata_Metadata_0=t.asm.X).apply(null,arguments)},be=t._emscripten_bind_Metadata___destroy___0=function(){return(be=t._emscripten_bind_Metadata___destroy___0=t.asm.Y).apply(null,arguments)},he=t._emscripten_bind_Status_code_0=function(){return(he=t._emscripten_bind_Status_code_0=t.asm.Z).apply(null,arguments)},Ae=t._emscripten_bind_Status_ok_0=function(){return(Ae=t._emscripten_bind_Status_ok_0=t.asm._).apply(null,arguments)},Te=t._emscripten_bind_Status_error_msg_0=function(){return(Te=t._emscripten_bind_Status_error_msg_0=t.asm.$).apply(null,arguments)},De=t._emscripten_bind_Status___destroy___0=function(){return(De=t._emscripten_bind_Status___destroy___0=t.asm.aa).apply(null,arguments)},Ie=t._emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=function(){return(Ie=t._emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=t.asm.ba).apply(null,arguments)},ge=t._emscripten_bind_DracoFloat32Array_GetValue_1=function(){return(ge=t._emscripten_bind_DracoFloat32Array_GetValue_1=t.asm.ca).apply(null,arguments)},ve=t._emscripten_bind_DracoFloat32Array_size_0=function(){return(ve=t._emscripten_bind_DracoFloat32Array_size_0=t.asm.da).apply(null,arguments)},Ee=t._emscripten_bind_DracoFloat32Array___destroy___0=function(){return(Ee=t._emscripten_bind_DracoFloat32Array___destroy___0=t.asm.ea).apply(null,arguments)},Ge=t._emscripten_bind_DracoInt8Array_DracoInt8Array_0=function(){return(Ge=t._emscripten_bind_DracoInt8Array_DracoInt8Array_0=t.asm.fa).apply(null,arguments)},Oe=t._emscripten_bind_DracoInt8Array_GetValue_1=function(){return(Oe=t._emscripten_bind_DracoInt8Array_GetValue_1=t.asm.ga).apply(null,arguments)},je=t._emscripten_bind_DracoInt8Array_size_0=function(){return(je=t._emscripten_bind_DracoInt8Array_size_0=t.asm.ha).apply(null,arguments)},Pe=t._emscripten_bind_DracoInt8Array___destroy___0=function(){return(Pe=t._emscripten_bind_DracoInt8Array___destroy___0=t.asm.ia).apply(null,arguments)},Re=t._emscripten_bind_DracoUInt8Array_DracoUInt8Array_0=function(){return(Re=t._emscripten_bind_DracoUInt8Array_DracoUInt8Array_0=t.asm.ja).apply(null,arguments)},Se=t._emscripten_bind_DracoUInt8Array_GetValue_1=function(){return(Se=t._emscripten_bind_DracoUInt8Array_GetValue_1=t.asm.ka).apply(null,arguments)},Me=t._emscripten_bind_DracoUInt8Array_size_0=function(){return(Me=t._emscripten_bind_DracoUInt8Array_size_0=t.asm.la).apply(null,arguments)},Ne=t._emscripten_bind_DracoUInt8Array___destroy___0=function(){return(Ne=t._emscripten_bind_DracoUInt8Array___destroy___0=t.asm.ma).apply(null,arguments)},Ue=t._emscripten_bind_DracoInt16Array_DracoInt16Array_0=function(){return(Ue=t._emscripten_bind_DracoInt16Array_DracoInt16Array_0=t.asm.na).apply(null,arguments)},Fe=t._emscripten_bind_DracoInt16Array_GetValue_1=function(){return(Fe=t._emscripten_bind_DracoInt16Array_GetValue_1=t.asm.oa).apply(null,arguments)},Le=t._emscripten_bind_DracoInt16Array_size_0=function(){return(Le=t._emscripten_bind_DracoInt16Array_size_0=t.asm.pa).apply(null,arguments)},Ce=t._emscripten_bind_DracoInt16Array___destroy___0=function(){return(Ce=t._emscripten_bind_DracoInt16Array___destroy___0=t.asm.qa).apply(null,arguments)},we=t._emscripten_bind_DracoUInt16Array_DracoUInt16Array_0=function(){return(we=t._emscripten_bind_DracoUInt16Array_DracoUInt16Array_0=t.asm.ra).apply(null,arguments)},ze=t._emscripten_bind_DracoUInt16Array_GetValue_1=function(){return(ze=t._emscripten_bind_DracoUInt16Array_GetValue_1=t.asm.sa).apply(null,arguments)},Ve=t._emscripten_bind_DracoUInt16Array_size_0=function(){return(Ve=t._emscripten_bind_DracoUInt16Array_size_0=t.asm.ta).apply(null,arguments)},Be=t._emscripten_bind_DracoUInt16Array___destroy___0=function(){return(Be=t._emscripten_bind_DracoUInt16Array___destroy___0=t.asm.ua).apply(null,arguments)},We=t._emscripten_bind_DracoInt32Array_DracoInt32Array_0=function(){return(We=t._emscripten_bind_DracoInt32Array_DracoInt32Array_0=t.asm.va).apply(null,arguments)},Qe=t._emscripten_bind_DracoInt32Array_GetValue_1=function(){return(Qe=t._emscripten_bind_DracoInt32Array_GetValue_1=t.asm.wa).apply(null,arguments)},xe=t._emscripten_bind_DracoInt32Array_size_0=function(){return(xe=t._emscripten_bind_DracoInt32Array_size_0=t.asm.xa).apply(null,arguments)},Ye=t._emscripten_bind_DracoInt32Array___destroy___0=function(){return(Ye=t._emscripten_bind_DracoInt32Array___destroy___0=t.asm.ya).apply(null,arguments)},He=t._emscripten_bind_DracoUInt32Array_DracoUInt32Array_0=function(){return(He=t._emscripten_bind_DracoUInt32Array_DracoUInt32Array_0=t.asm.za).apply(null,arguments)},qe=t._emscripten_bind_DracoUInt32Array_GetValue_1=function(){return(qe=t._emscripten_bind_DracoUInt32Array_GetValue_1=t.asm.Aa).apply(null,arguments)},ke=t._emscripten_bind_DracoUInt32Array_size_0=function(){return(ke=t._emscripten_bind_DracoUInt32Array_size_0=t.asm.Ba).apply(null,arguments)},Xe=t._emscripten_bind_DracoUInt32Array___destroy___0=function(){return(Xe=t._emscripten_bind_DracoUInt32Array___destroy___0=t.asm.Ca).apply(null,arguments)},Ke=t._emscripten_bind_MetadataQuerier_MetadataQuerier_0=function(){return(Ke=t._emscripten_bind_MetadataQuerier_MetadataQuerier_0=t.asm.Da).apply(null,arguments)},Je=t._emscripten_bind_MetadataQuerier_HasEntry_2=function(){return(Je=t._emscripten_bind_MetadataQuerier_HasEntry_2=t.asm.Ea).apply(null,arguments)},$e=t._emscripten_bind_MetadataQuerier_GetIntEntry_2=function(){return($e=t._emscripten_bind_MetadataQuerier_GetIntEntry_2=t.asm.Fa).apply(null,arguments)},Ze=t._emscripten_bind_MetadataQuerier_GetIntEntryArray_3=function(){return(Ze=t._emscripten_bind_MetadataQuerier_GetIntEntryArray_3=t.asm.Ga).apply(null,arguments)},tr=t._emscripten_bind_MetadataQuerier_GetDoubleEntry_2=function(){return(tr=t._emscripten_bind_MetadataQuerier_GetDoubleEntry_2=t.asm.Ha).apply(null,arguments)},er=t._emscripten_bind_MetadataQuerier_GetStringEntry_2=function(){return(er=t._emscripten_bind_MetadataQuerier_GetStringEntry_2=t.asm.Ia).apply(null,arguments)},rr=t._emscripten_bind_MetadataQuerier_NumEntries_1=function(){return(rr=t._emscripten_bind_MetadataQuerier_NumEntries_1=t.asm.Ja).apply(null,arguments)},nr=t._emscripten_bind_MetadataQuerier_GetEntryName_2=function(){return(nr=t._emscripten_bind_MetadataQuerier_GetEntryName_2=t.asm.Ka).apply(null,arguments)},or=t._emscripten_bind_MetadataQuerier___destroy___0=function(){return(or=t._emscripten_bind_MetadataQuerier___destroy___0=t.asm.La).apply(null,arguments)},_r=t._emscripten_bind_Decoder_Decoder_0=function(){return(_r=t._emscripten_bind_Decoder_Decoder_0=t.asm.Ma).apply(null,arguments)},ir=t._emscripten_bind_Decoder_DecodeArrayToPointCloud_3=function(){return(ir=t._emscripten_bind_Decoder_DecodeArrayToPointCloud_3=t.asm.Na).apply(null,arguments)},ar=t._emscripten_bind_Decoder_DecodeArrayToMesh_3=function(){return(ar=t._emscripten_bind_Decoder_DecodeArrayToMesh_3=t.asm.Oa).apply(null,arguments)},pr=t._emscripten_bind_Decoder_GetAttributeId_2=function(){return(pr=t._emscripten_bind_Decoder_GetAttributeId_2=t.asm.Pa).apply(null,arguments)},ur=t._emscripten_bind_Decoder_GetAttributeIdByName_2=function(){return(ur=t._emscripten_bind_Decoder_GetAttributeIdByName_2=t.asm.Qa).apply(null,arguments)},sr=t._emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=function(){return(sr=t._emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=t.asm.Ra).apply(null,arguments)},cr=t._emscripten_bind_Decoder_GetAttribute_2=function(){return(cr=t._emscripten_bind_Decoder_GetAttribute_2=t.asm.Sa).apply(null,arguments)},yr=t._emscripten_bind_Decoder_GetAttributeByUniqueId_2=function(){return(yr=t._emscripten_bind_Decoder_GetAttributeByUniqueId_2=t.asm.Ta).apply(null,arguments)},lr=t._emscripten_bind_Decoder_GetMetadata_1=function(){return(lr=t._emscripten_bind_Decoder_GetMetadata_1=t.asm.Ua).apply(null,arguments)},mr=t._emscripten_bind_Decoder_GetAttributeMetadata_2=function(){return(mr=t._emscripten_bind_Decoder_GetAttributeMetadata_2=t.asm.Va).apply(null,arguments)},fr=t._emscripten_bind_Decoder_GetFaceFromMesh_3=function(){return(fr=t._emscripten_bind_Decoder_GetFaceFromMesh_3=t.asm.Wa).apply(null,arguments)},dr=t._emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=function(){return(dr=t._emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=t.asm.Xa).apply(null,arguments)},br=t._emscripten_bind_Decoder_GetTrianglesUInt16Array_3=function(){return(br=t._emscripten_bind_Decoder_GetTrianglesUInt16Array_3=t.asm.Ya).apply(null,arguments)},hr=t._emscripten_bind_Decoder_GetTrianglesUInt32Array_3=function(){return(hr=t._emscripten_bind_Decoder_GetTrianglesUInt32Array_3=t.asm.Za).apply(null,arguments)},Ar=t._emscripten_bind_Decoder_GetAttributeFloat_3=function(){return(Ar=t._emscripten_bind_Decoder_GetAttributeFloat_3=t.asm._a).apply(null,arguments)},Tr=t._emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=function(){return(Tr=t._emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=t.asm.$a).apply(null,arguments)},Dr=t._emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=function(){return(Dr=t._emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=t.asm.ab).apply(null,arguments)},Ir=t._emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3=function(){return(Ir=t._emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3=t.asm.bb).apply(null,arguments)},gr=t._emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3=function(){return(gr=t._emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3=t.asm.cb).apply(null,arguments)},vr=t._emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3=function(){return(vr=t._emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3=t.asm.db).apply(null,arguments)},Er=t._emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3=function(){return(Er=t._emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3=t.asm.eb).apply(null,arguments)},Gr=t._emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3=function(){return(Gr=t._emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3=t.asm.fb).apply(null,arguments)},Or=t._emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3=function(){return(Or=t._emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3=t.asm.gb).apply(null,arguments)},jr=t._emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5=function(){return(jr=t._emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5=t.asm.hb).apply(null,arguments)},Pr=t._emscripten_bind_Decoder_SkipAttributeTransform_1=function(){return(Pr=t._emscripten_bind_Decoder_SkipAttributeTransform_1=t.asm.ib).apply(null,arguments)},Rr=t._emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1=function(){return(Rr=t._emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1=t.asm.jb).apply(null,arguments)},Sr=t._emscripten_bind_Decoder_DecodeBufferToPointCloud_2=function(){return(Sr=t._emscripten_bind_Decoder_DecodeBufferToPointCloud_2=t.asm.kb).apply(null,arguments)},Mr=t._emscripten_bind_Decoder_DecodeBufferToMesh_2=function(){return(Mr=t._emscripten_bind_Decoder_DecodeBufferToMesh_2=t.asm.lb).apply(null,arguments)},Nr=t._emscripten_bind_Decoder___destroy___0=function(){return(Nr=t._emscripten_bind_Decoder___destroy___0=t.asm.mb).apply(null,arguments)},Ur=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=function(){return(Ur=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=t.asm.nb).apply(null,arguments)},Fr=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=function(){return(Fr=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=t.asm.ob).apply(null,arguments)},Lr=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=function(){return(Lr=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=t.asm.pb).apply(null,arguments)},Cr=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=function(){return(Cr=t._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=t.asm.qb).apply(null,arguments)},wr=t._emscripten_enum_draco_GeometryAttribute_Type_INVALID=function(){return(wr=t._emscripten_enum_draco_GeometryAttribute_Type_INVALID=t.asm.rb).apply(null,arguments)},zr=t._emscripten_enum_draco_GeometryAttribute_Type_POSITION=function(){return(zr=t._emscripten_enum_draco_GeometryAttribute_Type_POSITION=t.asm.sb).apply(null,arguments)},Vr=t._emscripten_enum_draco_GeometryAttribute_Type_NORMAL=function(){return(Vr=t._emscripten_enum_draco_GeometryAttribute_Type_NORMAL=t.asm.tb).apply(null,arguments)},Br=t._emscripten_enum_draco_GeometryAttribute_Type_COLOR=function(){return(Br=t._emscripten_enum_draco_GeometryAttribute_Type_COLOR=t.asm.ub).apply(null,arguments)},Wr=t._emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=function(){return(Wr=t._emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=t.asm.vb).apply(null,arguments)},Qr=t._emscripten_enum_draco_GeometryAttribute_Type_GENERIC=function(){return(Qr=t._emscripten_enum_draco_GeometryAttribute_Type_GENERIC=t.asm.wb).apply(null,arguments)},xr=t._emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=function(){return(xr=t._emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=t.asm.xb).apply(null,arguments)},Yr=t._emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=function(){return(Yr=t._emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=t.asm.yb).apply(null,arguments)},Hr=t._emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=function(){return(Hr=t._emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=t.asm.zb).apply(null,arguments)},qr=t._emscripten_enum_draco_DataType_DT_INVALID=function(){return(qr=t._emscripten_enum_draco_DataType_DT_INVALID=t.asm.Ab).apply(null,arguments)},kr=t._emscripten_enum_draco_DataType_DT_INT8=function(){return(kr=t._emscripten_enum_draco_DataType_DT_INT8=t.asm.Bb).apply(null,arguments)},Xr=t._emscripten_enum_draco_DataType_DT_UINT8=function(){return(Xr=t._emscripten_enum_draco_DataType_DT_UINT8=t.asm.Cb).apply(null,arguments)},Kr=t._emscripten_enum_draco_DataType_DT_INT16=function(){return(Kr=t._emscripten_enum_draco_DataType_DT_INT16=t.asm.Db).apply(null,arguments)},Jr=t._emscripten_enum_draco_DataType_DT_UINT16=function(){return(Jr=t._emscripten_enum_draco_DataType_DT_UINT16=t.asm.Eb).apply(null,arguments)},$r=t._emscripten_enum_draco_DataType_DT_INT32=function(){return($r=t._emscripten_enum_draco_DataType_DT_INT32=t.asm.Fb).apply(null,arguments)},Zr=t._emscripten_enum_draco_DataType_DT_UINT32=function(){return(Zr=t._emscripten_enum_draco_DataType_DT_UINT32=t.asm.Gb).apply(null,arguments)},tn=t._emscripten_enum_draco_DataType_DT_INT64=function(){return(tn=t._emscripten_enum_draco_DataType_DT_INT64=t.asm.Hb).apply(null,arguments)},en=t._emscripten_enum_draco_DataType_DT_UINT64=function(){return(en=t._emscripten_enum_draco_DataType_DT_UINT64=t.asm.Ib).apply(null,arguments)},rn=t._emscripten_enum_draco_DataType_DT_FLOAT32=function(){return(rn=t._emscripten_enum_draco_DataType_DT_FLOAT32=t.asm.Jb).apply(null,arguments)},nn=t._emscripten_enum_draco_DataType_DT_FLOAT64=function(){return(nn=t._emscripten_enum_draco_DataType_DT_FLOAT64=t.asm.Kb).apply(null,arguments)},on=t._emscripten_enum_draco_DataType_DT_BOOL=function(){return(on=t._emscripten_enum_draco_DataType_DT_BOOL=t.asm.Lb).apply(null,arguments)},_n=t._emscripten_enum_draco_DataType_DT_TYPES_COUNT=function(){return(_n=t._emscripten_enum_draco_DataType_DT_TYPES_COUNT=t.asm.Mb).apply(null,arguments)},an=t._emscripten_enum_draco_StatusCode_OK=function(){return(an=t._emscripten_enum_draco_StatusCode_OK=t.asm.Nb).apply(null,arguments)},pn=t._emscripten_enum_draco_StatusCode_DRACO_ERROR=function(){return(pn=t._emscripten_enum_draco_StatusCode_DRACO_ERROR=t.asm.Ob).apply(null,arguments)},un=t._emscripten_enum_draco_StatusCode_IO_ERROR=function(){return(un=t._emscripten_enum_draco_StatusCode_IO_ERROR=t.asm.Pb).apply(null,arguments)},sn=t._emscripten_enum_draco_StatusCode_INVALID_PARAMETER=function(){return(sn=t._emscripten_enum_draco_StatusCode_INVALID_PARAMETER=t.asm.Qb).apply(null,arguments)},cn=t._emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=function(){return(cn=t._emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=t.asm.Rb).apply(null,arguments)},yn=t._emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=function(){return(yn=t._emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=t.asm.Sb).apply(null,arguments)};t._malloc=function(){return(t._malloc=t.asm.Tb).apply(null,arguments)},t._free=function(){return(t._free=t.asm.Ub).apply(null,arguments)};var ln=function(){return(ln=t.asm.Vb).apply(null,arguments)};t.___start_em_js=15856,t.___stop_em_js=15954;var it;if(rt=function e(){it||K(),it||(rt=e)},t.preInit)for(typeof t.preInit=="function"&&(t.preInit=[t.preInit]);0<t.preInit.length;)t.preInit.pop()();K(),f.prototype=Object.create(f.prototype),f.prototype.constructor=f,f.prototype.__class__=f,f.__cache__={},t.WrapperObject=f,t.getCache=T,t.wrapPointer=G,t.castObject=function(e,r){return G(e.ptr,r)},t.NULL=G(0),t.destroy=function(e){if(!e.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";e.__destroy__(),delete T(e.__class__)[e.ptr]},t.compare=function(e,r){return e.ptr===r.ptr},t.getPointer=function(e){return e.ptr},t.getClass=function(e){return e.__class__};var d={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(d.needed){for(var e=0;e<d.temps.length;e++)t._free(d.temps[e]);d.temps.length=0,t._free(d.buffer),d.buffer=0,d.size+=d.needed,d.needed=0}d.buffer||(d.size+=128,d.buffer=t._malloc(d.size),d.buffer||i(void 0)),d.pos=0},alloc:function(e,r){return d.buffer||i(void 0),e=e.length*r.BYTES_PER_ELEMENT,e=e+7&-8,d.pos+e>=d.size?(0<e||i(void 0),d.needed+=e,r=t._malloc(e),d.temps.push(r)):(r=d.buffer+d.pos,d.pos+=e),r},copy:function(e,r,n){switch(n>>>=0,r.BYTES_PER_ELEMENT){case 2:n>>>=1;break;case 4:n>>>=2;break;case 8:n>>>=3}for(var o=0;o<e.length;o++)r[n+o]=e[o]}};return H.prototype=Object.create(f.prototype),H.prototype.constructor=H,H.prototype.__class__=H,H.__cache__={},t.VoidPtr=H,H.prototype.__destroy__=H.prototype.__destroy__=function(){St(this.ptr)},B.prototype=Object.create(f.prototype),B.prototype.constructor=B,B.prototype.__class__=B,B.__cache__={},t.DecoderBuffer=B,B.prototype.Init=B.prototype.Init=function(e,r){var n=this.ptr;d.prepare(),typeof e=="object"&&(e=at(e)),r&&typeof r=="object"&&(r=r.ptr),Nt(n,e,r)},B.prototype.__destroy__=B.prototype.__destroy__=function(){Ut(this.ptr)},w.prototype=Object.create(f.prototype),w.prototype.constructor=w,w.prototype.__class__=w,w.__cache__={},t.AttributeTransformData=w,w.prototype.transform_type=w.prototype.transform_type=function(){return Lt(this.ptr)},w.prototype.__destroy__=w.prototype.__destroy__=function(){Ct(this.ptr)},Y.prototype=Object.create(f.prototype),Y.prototype.constructor=Y,Y.prototype.__class__=Y,Y.__cache__={},t.GeometryAttribute=Y,Y.prototype.__destroy__=Y.prototype.__destroy__=function(){zt(this.ptr)},A.prototype=Object.create(f.prototype),A.prototype.constructor=A,A.prototype.__class__=A,A.__cache__={},t.PointAttribute=A,A.prototype.size=A.prototype.size=function(){return Bt(this.ptr)},A.prototype.GetAttributeTransformData=A.prototype.GetAttributeTransformData=function(){return G(Wt(this.ptr),w)},A.prototype.attribute_type=A.prototype.attribute_type=function(){return Qt(this.ptr)},A.prototype.data_type=A.prototype.data_type=function(){return xt(this.ptr)},A.prototype.num_components=A.prototype.num_components=function(){return Yt(this.ptr)},A.prototype.normalized=A.prototype.normalized=function(){return!!Ht(this.ptr)},A.prototype.byte_stride=A.prototype.byte_stride=function(){return qt(this.ptr)},A.prototype.byte_offset=A.prototype.byte_offset=function(){return kt(this.ptr)},A.prototype.unique_id=A.prototype.unique_id=function(){return Xt(this.ptr)},A.prototype.__destroy__=A.prototype.__destroy__=function(){Kt(this.ptr)},E.prototype=Object.create(f.prototype),E.prototype.constructor=E,E.prototype.__class__=E,E.__cache__={},t.AttributeQuantizationTransform=E,E.prototype.InitFromAttribute=E.prototype.InitFromAttribute=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),!!$t(r,e)},E.prototype.quantization_bits=E.prototype.quantization_bits=function(){return Zt(this.ptr)},E.prototype.min_value=E.prototype.min_value=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),te(r,e)},E.prototype.range=E.prototype.range=function(){return ee(this.ptr)},E.prototype.__destroy__=E.prototype.__destroy__=function(){re(this.ptr)},j.prototype=Object.create(f.prototype),j.prototype.constructor=j,j.prototype.__class__=j,j.__cache__={},t.AttributeOctahedronTransform=j,j.prototype.InitFromAttribute=j.prototype.InitFromAttribute=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),!!oe(r,e)},j.prototype.quantization_bits=j.prototype.quantization_bits=function(){return _e(this.ptr)},j.prototype.__destroy__=j.prototype.__destroy__=function(){ie(this.ptr)},P.prototype=Object.create(f.prototype),P.prototype.constructor=P,P.prototype.__class__=P,P.__cache__={},t.PointCloud=P,P.prototype.num_attributes=P.prototype.num_attributes=function(){return pe(this.ptr)},P.prototype.num_points=P.prototype.num_points=function(){return ue(this.ptr)},P.prototype.__destroy__=P.prototype.__destroy__=function(){se(this.ptr)},O.prototype=Object.create(f.prototype),O.prototype.constructor=O,O.prototype.__class__=O,O.__cache__={},t.Mesh=O,O.prototype.num_faces=O.prototype.num_faces=function(){return ye(this.ptr)},O.prototype.num_attributes=O.prototype.num_attributes=function(){return le(this.ptr)},O.prototype.num_points=O.prototype.num_points=function(){return me(this.ptr)},O.prototype.__destroy__=O.prototype.__destroy__=function(){fe(this.ptr)},W.prototype=Object.create(f.prototype),W.prototype.constructor=W,W.prototype.__class__=W,W.__cache__={},t.Metadata=W,W.prototype.__destroy__=W.prototype.__destroy__=function(){be(this.ptr)},v.prototype=Object.create(f.prototype),v.prototype.constructor=v,v.prototype.__class__=v,v.__cache__={},t.Status=v,v.prototype.code=v.prototype.code=function(){return he(this.ptr)},v.prototype.ok=v.prototype.ok=function(){return!!Ae(this.ptr)},v.prototype.error_msg=v.prototype.error_msg=function(){return a(Te(this.ptr))},v.prototype.__destroy__=v.prototype.__destroy__=function(){De(this.ptr)},R.prototype=Object.create(f.prototype),R.prototype.constructor=R,R.prototype.__class__=R,R.__cache__={},t.DracoFloat32Array=R,R.prototype.GetValue=R.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),ge(r,e)},R.prototype.size=R.prototype.size=function(){return ve(this.ptr)},R.prototype.__destroy__=R.prototype.__destroy__=function(){Ee(this.ptr)},S.prototype=Object.create(f.prototype),S.prototype.constructor=S,S.prototype.__class__=S,S.__cache__={},t.DracoInt8Array=S,S.prototype.GetValue=S.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),Oe(r,e)},S.prototype.size=S.prototype.size=function(){return je(this.ptr)},S.prototype.__destroy__=S.prototype.__destroy__=function(){Pe(this.ptr)},M.prototype=Object.create(f.prototype),M.prototype.constructor=M,M.prototype.__class__=M,M.__cache__={},t.DracoUInt8Array=M,M.prototype.GetValue=M.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),Se(r,e)},M.prototype.size=M.prototype.size=function(){return Me(this.ptr)},M.prototype.__destroy__=M.prototype.__destroy__=function(){Ne(this.ptr)},N.prototype=Object.create(f.prototype),N.prototype.constructor=N,N.prototype.__class__=N,N.__cache__={},t.DracoInt16Array=N,N.prototype.GetValue=N.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),Fe(r,e)},N.prototype.size=N.prototype.size=function(){return Le(this.ptr)},N.prototype.__destroy__=N.prototype.__destroy__=function(){Ce(this.ptr)},U.prototype=Object.create(f.prototype),U.prototype.constructor=U,U.prototype.__class__=U,U.__cache__={},t.DracoUInt16Array=U,U.prototype.GetValue=U.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),ze(r,e)},U.prototype.size=U.prototype.size=function(){return Ve(this.ptr)},U.prototype.__destroy__=U.prototype.__destroy__=function(){Be(this.ptr)},F.prototype=Object.create(f.prototype),F.prototype.constructor=F,F.prototype.__class__=F,F.__cache__={},t.DracoInt32Array=F,F.prototype.GetValue=F.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),Qe(r,e)},F.prototype.size=F.prototype.size=function(){return xe(this.ptr)},F.prototype.__destroy__=F.prototype.__destroy__=function(){Ye(this.ptr)},L.prototype=Object.create(f.prototype),L.prototype.constructor=L,L.prototype.__class__=L,L.__cache__={},t.DracoUInt32Array=L,L.prototype.GetValue=L.prototype.GetValue=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),qe(r,e)},L.prototype.size=L.prototype.size=function(){return ke(this.ptr)},L.prototype.__destroy__=L.prototype.__destroy__=function(){Xe(this.ptr)},D.prototype=Object.create(f.prototype),D.prototype.constructor=D,D.prototype.__class__=D,D.__cache__={},t.MetadataQuerier=D,D.prototype.HasEntry=D.prototype.HasEntry=function(e,r){var n=this.ptr;return d.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:V(r),!!Je(n,e,r)},D.prototype.GetIntEntry=D.prototype.GetIntEntry=function(e,r){var n=this.ptr;return d.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:V(r),$e(n,e,r)},D.prototype.GetIntEntryArray=D.prototype.GetIntEntryArray=function(e,r,n){var o=this.ptr;d.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:V(r),n&&typeof n=="object"&&(n=n.ptr),Ze(o,e,r,n)},D.prototype.GetDoubleEntry=D.prototype.GetDoubleEntry=function(e,r){var n=this.ptr;return d.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:V(r),tr(n,e,r)},D.prototype.GetStringEntry=D.prototype.GetStringEntry=function(e,r){var n=this.ptr;return d.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:V(r),a(er(n,e,r))},D.prototype.NumEntries=D.prototype.NumEntries=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),rr(r,e)},D.prototype.GetEntryName=D.prototype.GetEntryName=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),a(nr(n,e,r))},D.prototype.__destroy__=D.prototype.__destroy__=function(){or(this.ptr)},u.prototype=Object.create(f.prototype),u.prototype.constructor=u,u.prototype.__class__=u,u.__cache__={},t.Decoder=u,u.prototype.DecodeArrayToPointCloud=u.prototype.DecodeArrayToPointCloud=function(e,r,n){var o=this.ptr;return d.prepare(),typeof e=="object"&&(e=at(e)),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),G(ir(o,e,r,n),v)},u.prototype.DecodeArrayToMesh=u.prototype.DecodeArrayToMesh=function(e,r,n){var o=this.ptr;return d.prepare(),typeof e=="object"&&(e=at(e)),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),G(ar(o,e,r,n),v)},u.prototype.GetAttributeId=u.prototype.GetAttributeId=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),pr(n,e,r)},u.prototype.GetAttributeIdByName=u.prototype.GetAttributeIdByName=function(e,r){var n=this.ptr;return d.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:V(r),ur(n,e,r)},u.prototype.GetAttributeIdByMetadataEntry=u.prototype.GetAttributeIdByMetadataEntry=function(e,r,n){var o=this.ptr;return d.prepare(),e&&typeof e=="object"&&(e=e.ptr),r=r&&typeof r=="object"?r.ptr:V(r),n=n&&typeof n=="object"?n.ptr:V(n),sr(o,e,r,n)},u.prototype.GetAttribute=u.prototype.GetAttribute=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),G(cr(n,e,r),A)},u.prototype.GetAttributeByUniqueId=u.prototype.GetAttributeByUniqueId=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),G(yr(n,e,r),A)},u.prototype.GetMetadata=u.prototype.GetMetadata=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),G(lr(r,e),W)},u.prototype.GetAttributeMetadata=u.prototype.GetAttributeMetadata=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),G(mr(n,e,r),W)},u.prototype.GetFaceFromMesh=u.prototype.GetFaceFromMesh=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!fr(o,e,r,n)},u.prototype.GetTriangleStripsFromMesh=u.prototype.GetTriangleStripsFromMesh=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),dr(n,e,r)},u.prototype.GetTrianglesUInt16Array=u.prototype.GetTrianglesUInt16Array=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!br(o,e,r,n)},u.prototype.GetTrianglesUInt32Array=u.prototype.GetTrianglesUInt32Array=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!hr(o,e,r,n)},u.prototype.GetAttributeFloat=u.prototype.GetAttributeFloat=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Ar(o,e,r,n)},u.prototype.GetAttributeFloatForAllPoints=u.prototype.GetAttributeFloatForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Tr(o,e,r,n)},u.prototype.GetAttributeIntForAllPoints=u.prototype.GetAttributeIntForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Dr(o,e,r,n)},u.prototype.GetAttributeInt8ForAllPoints=u.prototype.GetAttributeInt8ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Ir(o,e,r,n)},u.prototype.GetAttributeUInt8ForAllPoints=u.prototype.GetAttributeUInt8ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!gr(o,e,r,n)},u.prototype.GetAttributeInt16ForAllPoints=u.prototype.GetAttributeInt16ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!vr(o,e,r,n)},u.prototype.GetAttributeUInt16ForAllPoints=u.prototype.GetAttributeUInt16ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Er(o,e,r,n)},u.prototype.GetAttributeInt32ForAllPoints=u.prototype.GetAttributeInt32ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Gr(o,e,r,n)},u.prototype.GetAttributeUInt32ForAllPoints=u.prototype.GetAttributeUInt32ForAllPoints=function(e,r,n){var o=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),!!Or(o,e,r,n)},u.prototype.GetAttributeDataArrayForAllPoints=u.prototype.GetAttributeDataArrayForAllPoints=function(e,r,n,o,m){var b=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),n&&typeof n=="object"&&(n=n.ptr),o&&typeof o=="object"&&(o=o.ptr),m&&typeof m=="object"&&(m=m.ptr),!!jr(b,e,r,n,o,m)},u.prototype.SkipAttributeTransform=u.prototype.SkipAttributeTransform=function(e){var r=this.ptr;e&&typeof e=="object"&&(e=e.ptr),Pr(r,e)},u.prototype.GetEncodedGeometryType_Deprecated=u.prototype.GetEncodedGeometryType_Deprecated=function(e){var r=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),Rr(r,e)},u.prototype.DecodeBufferToPointCloud=u.prototype.DecodeBufferToPointCloud=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),G(Sr(n,e,r),v)},u.prototype.DecodeBufferToMesh=u.prototype.DecodeBufferToMesh=function(e,r){var n=this.ptr;return e&&typeof e=="object"&&(e=e.ptr),r&&typeof r=="object"&&(r=r.ptr),G(Mr(n,e,r),v)},u.prototype.__destroy__=u.prototype.__destroy__=function(){Nr(this.ptr)},function(){function e(){t.ATTRIBUTE_INVALID_TRANSFORM=Ur(),t.ATTRIBUTE_NO_TRANSFORM=Fr(),t.ATTRIBUTE_QUANTIZATION_TRANSFORM=Lr(),t.ATTRIBUTE_OCTAHEDRON_TRANSFORM=Cr(),t.INVALID=wr(),t.POSITION=zr(),t.NORMAL=Vr(),t.COLOR=Br(),t.TEX_COORD=Wr(),t.GENERIC=Qr(),t.INVALID_GEOMETRY_TYPE=xr(),t.POINT_CLOUD=Yr(),t.TRIANGULAR_MESH=Hr(),t.DT_INVALID=qr(),t.DT_INT8=kr(),t.DT_UINT8=Xr(),t.DT_INT16=Kr(),t.DT_UINT16=Jr(),t.DT_INT32=$r(),t.DT_UINT32=Zr(),t.DT_INT64=tn(),t.DT_UINT64=en(),t.DT_FLOAT32=rn(),t.DT_FLOAT64=nn(),t.DT_BOOL=on(),t.DT_TYPES_COUNT=_n(),t.OK=an(),t.DRACO_ERROR=pn(),t.IO_ERROR=un(),t.INVALID_PARAMETER=sn(),t.UNSUPPORTED_VERSION=cn(),t.UNKNOWN_VERSION=yn()}Rt?e():ct.unshift(e)}(),typeof t.onModuleParsed=="function"&&t.onModuleParsed(),t.Decoder.prototype.GetEncodedGeometryType=function(e){if(e.__class__&&e.__class__===t.DecoderBuffer)return t.Decoder.prototype.GetEncodedGeometryType_Deprecated(e);if(8>e.byteLength)return t.INVALID_GEOMETRY_TYPE;switch(e[7]){case 0:return t.POINT_CLOUD;case 1:return t.TRIANGULAR_MESH;default:return t.INVALID_GEOMETRY_TYPE}},c.ready}}();typeof nt=="object"&&typeof bt=="object"?bt.exports=dt:typeof define=="function"&&define.amd?define([],function(){return dt}):typeof nt=="object"&&(nt.DracoDecoderModule=dt)});export{Dn as a};
