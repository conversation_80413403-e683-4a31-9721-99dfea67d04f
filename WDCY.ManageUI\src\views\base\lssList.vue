<template>
	<el-row class="select_box">
		<el-col :span="8">
			<div class="card house" @click.stop="addPush(0,'房屋')">
				<div class="card_content_box">
					<div class="card_content_img house_img"></div>
					<div class="card_content">
						<div class="card_content_tit text_blue" @click.stop="toHouseList">未下发{{houseNum}}条</div>
						<div class="card_content_des">
							<el-button type="text" style="color: #000000;" @click.stop="addPush(0,'房屋')" v-if="hasPerm('base:issue:add')">房屋新增下发</el-button>
						</div>
					</div>
				</div>
			</div>
		</el-col>
	
		<el-col :span="8">
			<div class="card person" @click.stop="addPush(4,'人员')">
				<div class="card_content_box">
					<div class="card_content_img person_img"></div>
					<div class="card_content">
						<div class="card_content_tit text_red" @click.stop="toHousePersonList">未下发{{personNum}}条</div>
						<div class="card_content_des">
							<el-button type="text" style="color: #000000;" @click.stop="addPush(4,'人员')" v-if="hasPerm('base:issue:add')">人员新增下发</el-button>
						</div>
					</div>
				</div>
			</div>
		</el-col>
		
		<el-col :span="8">
			<div class="card car" @click.stop="addPush(5,'车辆')">
				<div class="card_content_box">
					<div class="card_content_img car_img"></div>
					<div class="card_content">
						<div class="card_content_tit text_green" @click.stop="toCarList">未下发{{carNum}}条</div>
						<div class="card_content_des">
							<el-button type="text" style="color: #000000;"  @click.stop="addPush(5,'车辆')" v-if="hasPerm('base:issue:add')">车辆新增下发</el-button>
						</div>
					</div>
				</div>
			</div>
		</el-col>
		
	</el-row>
	<el-row style="margin-top: 20px; display: block;" class="select_box gundong">
		<div class="title">
			<div class="title_img"></div>
			<span style="padding-left: 10px;font-size: 15px;font-weight: 600;">操作步骤说明</span>
		</div>
		<div class="describe">
			<span style="font-size: 14px;">
				<h3>一、新增资料</h3>
				<ol>
					<li>当新增<b>楼宇、单元、楼层、房间</b>信息时，需要点击<b class="bgc_blue" style="text-shadow:1px 1px rgba(0,0,0,.2);">“房屋新增下发”</b>按钮，等待运行结果。</li>
					<li>当从管理后台<b>新增人员</b>或<b>审核通过小程序录入的人员</b>时，需要点击<b class="bgc_red" style="text-shadow:1px 1px rgba(0,0,0,.2)">“人员新增下发”</b>按钮，等待运行结果。</li>
					<li>当<b>新增车辆</b>时，需要点击<b class="bgc_green" style="text-shadow:1px 1px rgba(0,0,0,.2)">“车辆新增下发”</b>按钮，等待运行结果。</li>
				</ol>
				<h3>二、更新或删除资料</h3>
				<ol>
					<li>当更新或删除<b>房屋信息</b>时，需要点击<b>房屋</b>的<b class="text_blue">“未下发XX条”</b>整个按钮，进入待下发列表界面，执行<b>“下发”</b>动作，等待运行结果。</li>
					<li>当更新或删除<b>人员信息</b>时，需要点击<b>人员</b>的<b class="text_red">“未下发XX条”</b>整个按钮，进入待下发列表界面，执行<b>“下发”</b>动作，等待运行结果。</li>
					<li>当更新或删除<b>车辆信息</b>时，需要点击<b>车辆</b>的<b class="text_green">“未下发XX条”</b>整个按钮，进入待下发列表界面，执行<b>“下发”</b>动作，等待运行结果。</li>
				</ol>
				<h3>三、自动同步资料</h3>
				<ol>
					<li>系统会根据系统配置，自动于指定时间按楼栋<b>房屋、人员、车辆</b>的顺序以新增状态同步全部资料，用于查缺补漏所有设施</li>
					<li>系统会针对<b>房屋、人员、车辆</b>下发过程中<b>失败的结果</b>或<b>更新与删除</b>结果执行同步操作。</li>
				</ol>
			</span>
		</div>
	</el-row>
</template>

<script setup>
import { issueSta,issuePage,issueAddIssue } from "@/api/base/lssue.js"
import {useRouter} from 'vue-router'
	const router = useRouter()
	function toHouseList(){
		router.push({
			path:"/lssData",
			query:{
				index:1
			}
		})
	}
	function toHousePersonList(){
		router.push({
			path:"/lssData",
			query:{
				index:2
			}
		})
	}
	function toCarList(){
		router.push({
			path:"/lssData",
			query:{
				index:3
			}
		})
	}
</script>

<script>
import { ElLoading } from 'element-plus'
import { useRouter } from 'vue-router'
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
export default {
	data() {
		return {
			searchModel: {
				communityId: localStorage.getItem("communityId")
			},
			houseNum: 0,
			personNum: 0,
			carNum: 0,
			vehicleList: [],
			total: 0,
			fullLoading: null
		}
	},
	methods: {
		search() {
			listVehicle(this.searchModel)
				.then(res => {
					this.vehicleList = res.data.result.list
					this.total = res.data.result.total
				})
		},
		addPush(dataType,text) {
			this.$confirm(`开始${text}下发,是否继续？`,'提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				tupe: 'warning'
			}).then(() => {
				this.fullLoading = ElLoading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.7)' , text: `正在执行${text}下发，请不要刷新！！！`});
				issueAddIssue({ dataType: dataType, communityId: localStorage.getItem("communityId") })
				.then(res => {
					this.fullLoading.close()
					this.$message.success(res.data.msg)
				})
			}).catch(()=> { })
			
		},
		deleted(id) {
			this.$confirm('删除车辆, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteVehicle(id)
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		async init() {
			mitt.off('openVehicleEdit')

			try {
				issueSta(this.searchModel)
					.then(res => {
						if (res.data.result) {
							for (let item of res.data.result) {
								if (item.dataType == 0) {
									this.houseNum = item.value
								} else if (item.dataType == 4) {
									this.personNum = item.value
								} else if (item.dataType == 5) {
									this.carNum = item.value
								}
							}
						}
					})
					.catch(err => {})
			} catch (err) {
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	 .select_box{
		height: 300px;
		background-color: #ffffff;
		padding: 0 30px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.card{
		height: 260px;
		width: 500px;
		overflow: hidden;
		box-sizing: border-box;
		background-repeat: no-repeat;
		background-size: 100%;
		border-radius: 5px;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.card:hover{
		cursor: pointer;
	}
	.house{
		background-image: url(../../assets/img/b_house.png);
	}
	.person{
		background-image: url(../../assets/img/b_person.png);
	}
	.car{
		background-image: url(../../assets/img/b_car.png);
	}
	.title{
		display: flex;
		justify-content: flex-start;
		align-items: flex-end;
		height: 30px;
		width: 200px;
		/* padding: 20px; */
		margin-top: 20px;
	}
	.card_content_box{
		height: 30%;
		width: 260px;
		display: flex;
		justify-content: center;
	}
	.card_content_img{
		width: 35%;
		height: 100%;
		box-sizing: border-box;
		background-repeat: no-repeat;
		background-size: 100%;
	}
	.card_content{
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100%;
		width: 65%;
	}
	.card_content_tit{
		text-align: center;
		width: 100%;
		height: 50%;
		line-height: 40px;
	}
	.card_content_tit:hover{
		text-decoration: underline;
	}
	.gundong{
		overflow: auto;
		height: auto;
	}
	.card_content_des{
		font-size: 15px;
		text-align: center;
		height: 50%;
		width: 100%;
		line-height: 40px;
	}
	.text_blue{
		color: #78A3FF;
	}
	.text_red{
		color: #FF7272;
	}
	.text_green{
		color: #17D0B1;
	}
	.house_img{
		background-image: url(../../assets/img/home.png);
	}
	.person_img{
		background-image: url(../../assets/img/person.png);
	}
	.car_img{
		background-image: url(../../assets/img/car.png);
	}
	.title_img{
		height: 25px;
		width: 20px;
		overflow: hidden;
		box-sizing: border-box;
		background-repeat: no-repeat;
		background-size: 100%;
		background-image: url(../../assets/img/essay.png);
	}
	.describe{
		padding: 20px;
		padding-top: 10px;
		padding-left: 50px;
	}
	.bgc_blue{
		background-color: rgb(241, 246, 255);
		color: #000;
	}
	.bgc_red{
		background-color: rgb(255, 241, 241);
		color: #000;
	}
	.bgc_green{
		background-color: rgb(231, 250, 247);
		color: #000;
	}
	/* 隐藏滚动条 */
	::-webkit-scrollbar{
		display: none;
	}
	b{
		color: rgba(8, 82, 143, 0.849);
	}
</style>

