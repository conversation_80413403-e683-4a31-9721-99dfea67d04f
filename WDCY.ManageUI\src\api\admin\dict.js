import request from '@/utils/request'

export const listDict = (data) =>
	request({
		url: '/dict',
		method: 'get',
		params: data
	})
export const listDictByNameEn = (nameEn) =>
	request({
		url: '/dict/listDictByNameEn/'+nameEn,
		method: 'get'
	})
export const listTagList = (nameEn) =>
	request({
		url: '/dict/tagList/'+nameEn,
		method: 'get'
	})
export const queryListDictBySuffix = (nameEn) =>
	request({
		url: '/dict/queryListDictBySuffix/'+nameEn,
		method: 'get'
	})
export const addDict = (data) =>
	request({
		url: '/dict',
		method: 'post',
		data: data
	})
export const editDict = (data) =>
	request({
		url: '/dict',
		method: 'put',
		data: data
	})
export const deleteDict = (id) =>
	request({
		url: '/dict',
		method: 'delete',
		params: {
			id: id
		}
	})
export const addTagDict = (data) =>
	request({
		url: '/dict/tag',
		method: 'post',
		data: data
	})
export const editTagDict = (data) =>
	request({
		url: '/dict/tag',
		method: 'put',
		data: data
	})
export const deleteTagDict = (id) =>
	request({
		url: '/dict/tag',
		method: 'delete',
		params: {
			id: id
		}
	})
