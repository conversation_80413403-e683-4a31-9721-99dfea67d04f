import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
// import { mars3dPlugin } from 'vite-plugin-mars3d';


const packageJson = require("./package.json");

// 获取当前时间并格式化
function getCurrentTime() {
  var date = new Date();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  if (month <= 9) {
    month = "0" + month;
  }
  if (day <= 9) {
    day = "0" + day;
  }
  var currentTime = date.getFullYear() + "-" + month + "-" + day;

  return currentTime;
}

// vite.config.js
// import { viteCommonjs } from '@originjs/vite-plugin-commonjs'

function pathResolve(dir) {
  return resolve(__dirname, ".", dir);
}

export default ({ mode }) => {

  // 加载所有环境变量
  const env = loadEnv(mode, process.cwd());

  // 现在可以通过 env.KEY 访问定义在 .env 文件中的变量了
  const myEnvVariable = env.VITE_BASE_AREA;

  const outputDir =
    "../../wdcyOutput/" + getCurrentTime() + "/wdcy" + "后台" +
    packageJson.version +
    myEnvVariable +
    getCurrentTime();

  return defineConfig({
    plugins: [vue()],
    // plugins: [vue(),viteCommonjs()],
    // plugins: [vue(), mars3dPlugin({ cesiumPackageName: "cesium" })],
    base: "./",
    envDir: "./",
    publicDir: "./public",
    server: {
      host: "0.0.0.0",
      open: true,
    },
    build: {
      outDir: outputDir,
      publicPath: "/",
      assetsDir: "./static",
      cssCodeSplit: false,
    },
    resolve: {
      alias: {
        "@": pathResolve("src"),
      },
    },
    css: {
      // css预处理器
      preprocessorOptions: {
        scss: {
          // 定义全局的scss变量
          // 给导入的路径最后加上 ;
          additionalData: `@import '@/assets/css/utils.scss';`,
        },
      },
    },
  });
};

// export default defineConfig({
//   // plugins: [vue(),viteCommonjs()],
//   plugins: [
//     vue(),

//   ],

//   base: "./",
//   envDir: "./config",
//   publicDir: "./public",
//   server: {
//     host: "0.0.0.0",
//     open: true,
//   },
//   build: {
//     outDir: outputDir,
//     publicPath: "/",
//     assetsDir: "./static",
//     cssCodeSplit: false,
//   },
//   resolve: {
//     alias: {
//       "@": pathResolve("src"),
//     },
//   },
//   css: {
//     // css预处理器
//     preprocessorOptions: {
//       scss: {
//         // 定义全局的scss变量
//         // 给导入的路径最后加上 ;
//         additionalData: `@import '@/assets/css/utils.scss';`,
//       },
//     },
//   },
// });
