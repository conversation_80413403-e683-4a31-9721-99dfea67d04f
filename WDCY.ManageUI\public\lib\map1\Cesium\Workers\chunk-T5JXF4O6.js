/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.124
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as _,b as Me,c as Oe,d as Ie,e as Q,f as un}from"./chunk-HWH6BBH6.js";import{a as E}from"./chunk-DW4EQ6YI.js";import{a as Se}from"./chunk-GVFM5H7M.js";import{a as O}from"./chunk-CJFJKB5V.js";import{a as j,b as s}from"./chunk-4BGNE6LH.js";import{a as Sn,c as Rn,d as Ze,e as p}from"./chunk-OX5CTLXY.js";var Pt=Rn((en,nn)=>{/*! https://mths.be/punycode v1.4.0 by @mathias */(function(e){var n=typeof en=="object"&&en&&!en.nodeType&&en,t=typeof nn=="object"&&nn&&!nn.nodeType&&nn,o=typeof global=="object"&&global;(o.global===o||o.window===o||o.self===o)&&(e=o);var i,r=**********,a=36,u=1,d=26,m=38,l=700,w=72,T=128,C="-",P=/^xn--/,A=/[^\x20-\x7E]/,q=/[\x2E\u3002\uFF0E\uFF61]/g,k={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},L=a-u,F=Math.floor,B=String.fromCharCode,W;function H(b){throw new RangeError(k[b])}function ee(b,I){for(var N=b.length,Y=[];N--;)Y[N]=I(b[N]);return Y}function re(b,I){var N=b.split("@"),Y="";N.length>1&&(Y=N[0]+"@",b=N[1]),b=b.replace(q,".");var ie=b.split("."),ae=ee(ie,I).join(".");return Y+ae}function Z(b){for(var I=[],N=0,Y=b.length,ie,ae;N<Y;)ie=b.charCodeAt(N++),ie>=55296&&ie<=56319&&N<Y?(ae=b.charCodeAt(N++),(ae&64512)==56320?I.push(((ie&1023)<<10)+(ae&1023)+65536):(I.push(ie),N--)):I.push(ie);return I}function oe(b){return ee(b,function(I){var N="";return I>65535&&(I-=65536,N+=B(I>>>10&1023|55296),I=56320|I&1023),N+=B(I),N}).join("")}function J(b){return b-48<10?b-22:b-65<26?b-65:b-97<26?b-97:a}function c(b,I){return b+22+75*(b<26)-((I!=0)<<5)}function f(b,I,N){var Y=0;for(b=N?F(b/l):b>>1,b+=F(b/I);b>L*d>>1;Y+=a)b=F(b/L);return F(Y+(L+1)*b/(b+m))}function h(b){var I=[],N=b.length,Y,ie=0,ae=T,ne=w,ue,de,ye,me,he,X,_e,Te,je;for(ue=b.lastIndexOf(C),ue<0&&(ue=0),de=0;de<ue;++de)b.charCodeAt(de)>=128&&H("not-basic"),I.push(b.charCodeAt(de));for(ye=ue>0?ue+1:0;ye<N;){for(me=ie,he=1,X=a;ye>=N&&H("invalid-input"),_e=J(b.charCodeAt(ye++)),(_e>=a||_e>F((r-ie)/he))&&H("overflow"),ie+=_e*he,Te=X<=ne?u:X>=ne+d?d:X-ne,!(_e<Te);X+=a)je=a-Te,he>F(r/je)&&H("overflow"),he*=je;Y=I.length+1,ne=f(ie-me,Y,me==0),F(ie/Y)>r-ae&&H("overflow"),ae+=F(ie/Y),ie%=Y,I.splice(ie++,0,ae)}return oe(I)}function y(b){var I,N,Y,ie,ae,ne,ue,de,ye,me,he,X=[],_e,Te,je,fn;for(b=Z(b),_e=b.length,I=T,N=0,ae=w,ne=0;ne<_e;++ne)he=b[ne],he<128&&X.push(B(he));for(Y=ie=X.length,ie&&X.push(C);Y<_e;){for(ue=r,ne=0;ne<_e;++ne)he=b[ne],he>=I&&he<ue&&(ue=he);for(Te=Y+1,ue-I>F((r-N)/Te)&&H("overflow"),N+=(ue-I)*Te,I=ue,ne=0;ne<_e;++ne)if(he=b[ne],he<I&&++N>r&&H("overflow"),he==I){for(de=N,ye=a;me=ye<=ae?u:ye>=ae+d?d:ye-ae,!(de<me);ye+=a)fn=de-me,je=a-me,X.push(B(c(me+fn%je,0))),de=F(fn/je);X.push(B(c(de,0))),ae=f(N,Te,Y==ie),N=0,++Y}++N,++I}return X.join("")}function g(b){return re(b,function(I){return P.test(I)?h(I.slice(4).toLowerCase()):I})}function v(b){return re(b,function(I){return A.test(I)?"xn--"+y(I):I})}if(i={version:"1.3.2",ucs2:{decode:Z,encode:oe},decode:h,encode:y,toASCII:v,toUnicode:g},typeof define=="function"&&typeof define.amd=="object"&&define.amd)define("punycode",function(){return i});else if(n&&t)if(nn.exports==n)t.exports=i;else for(W in i)i.hasOwnProperty(W)&&(n[W]=i[W]);else e.punycode=i})(en)});var zt=Rn((Ut,Mn)=>{/*!
 * URI.js - Mutating URLs
 * IPv6 Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,n){"use strict";typeof Mn=="object"&&Mn.exports?Mn.exports=n():typeof define=="function"&&define.amd?define(n):e.IPv6=n(e)})(Ut,function(e){"use strict";var n=e&&e.IPv6;function t(i){var r=i.toLowerCase(),a=r.split(":"),u=a.length,d=8;a[0]===""&&a[1]===""&&a[2]===""?(a.shift(),a.shift()):a[0]===""&&a[1]===""?a.shift():a[u-1]===""&&a[u-2]===""&&a.pop(),u=a.length,a[u-1].indexOf(".")!==-1&&(d=7);var m;for(m=0;m<u&&a[m]!=="";m++);if(m<d)for(a.splice(m,1,"0000");a.length<d;)a.splice(m,0,"0000");for(var l,w=0;w<d;w++){l=a[w].split("");for(var T=0;T<3&&(l[0]==="0"&&l.length>1);T++)l.splice(0,1);a[w]=l.join("")}var C=-1,P=0,A=0,q=-1,k=!1;for(w=0;w<d;w++)k?a[w]==="0"?A+=1:(k=!1,A>P&&(C=q,P=A)):a[w]==="0"&&(k=!0,q=w,A=1);A>P&&(C=q,P=A),P>1&&a.splice(C,P,""),u=a.length;var L="";for(a[0]===""&&(L=":"),w=0;w<u&&(L+=a[w],w!==u-1);w++)L+=":";return a[u-1]===""&&(L+=":"),L}function o(){return e.IPv6===this&&(e.IPv6=n),this}return{best:t,noConflict:o}})});var qt=Rn((It,Pn)=>{/*!
 * URI.js - Mutating URLs
 * Second Level Domain (SLD) Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,n){"use strict";typeof Pn=="object"&&Pn.exports?Pn.exports=n():typeof define=="function"&&define.amd?define(n):e.SecondLevelDomains=n(e)})(It,function(e){"use strict";var n=e&&e.SecondLevelDomains,t={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return!1;var r=o.lastIndexOf(".",i-1);if(r<=0||r>=i-1)return!1;var a=t.list[o.slice(i+1)];return a?a.indexOf(" "+o.slice(r+1,i)+" ")>=0:!1},is:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return!1;var r=o.lastIndexOf(".",i-1);if(r>=0)return!1;var a=t.list[o.slice(i+1)];return a?a.indexOf(" "+o.slice(0,i)+" ")>=0:!1},get:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return null;var r=o.lastIndexOf(".",i-1);if(r<=0||r>=i-1)return null;var a=t.list[o.slice(i+1)];return!a||a.indexOf(" "+o.slice(r+1,i)+" ")<0?null:o.slice(r+1)},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=n),this}};return t})});var Qe=Rn((Dt,Un)=>{/*!
 * URI.js - Mutating URLs
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,n){"use strict";typeof Un=="object"&&Un.exports?Un.exports=n(Pt(),zt(),qt()):typeof define=="function"&&define.amd?define(["./punycode","./IPv6","./SecondLevelDomains"],n):e.URI=n(e.punycode,e.IPv6,e.SecondLevelDomains,e)})(Dt,function(e,n,t,o){"use strict";var i=o&&o.URI;function r(c,f){var h=arguments.length>=1,y=arguments.length>=2;if(!(this instanceof r))return h?y?new r(c,f):new r(c):new r;if(c===void 0){if(h)throw new TypeError("undefined is not a valid argument for URI");typeof location<"u"?c=location.href+"":c=""}if(c===null&&h)throw new TypeError("null is not a valid argument for URI");return this.href(c),f!==void 0?this.absoluteTo(f):this}function a(c){return/^[0-9]+$/.test(c)}r.version="1.19.11";var u=r.prototype,d=Object.prototype.hasOwnProperty;function m(c){return c.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function l(c){return c===void 0?"Undefined":String(Object.prototype.toString.call(c)).slice(8,-1)}function w(c){return l(c)==="Array"}function T(c,f){var h={},y,g;if(l(f)==="RegExp")h=null;else if(w(f))for(y=0,g=f.length;y<g;y++)h[f[y]]=!0;else h[f]=!0;for(y=0,g=c.length;y<g;y++){var v=h&&h[c[y]]!==void 0||!h&&f.test(c[y]);v&&(c.splice(y,1),g--,y--)}return c}function C(c,f){var h,y;if(w(f)){for(h=0,y=f.length;h<y;h++)if(!C(c,f[h]))return!1;return!0}var g=l(f);for(h=0,y=c.length;h<y;h++)if(g==="RegExp"){if(typeof c[h]=="string"&&c[h].match(f))return!0}else if(c[h]===f)return!0;return!1}function P(c,f){if(!w(c)||!w(f)||c.length!==f.length)return!1;c.sort(),f.sort();for(var h=0,y=c.length;h<y;h++)if(c[h]!==f[h])return!1;return!0}function A(c){var f=/^\/+|\/+$/g;return c.replace(f,"")}r._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:r.preventInvalidHostname,duplicateQueryParameters:r.duplicateQueryParameters,escapeQuerySpace:r.escapeQuerySpace}},r.preventInvalidHostname=!1,r.duplicateQueryParameters=!1,r.escapeQuerySpace=!0,r.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,r.idn_expression=/[^a-z0-9\._-]/i,r.punycode_expression=/(xn--)/i,r.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,r.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,r.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/ig,r.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},r.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,r.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,r.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},r.hostProtocols=["http","https"],r.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,r.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},r.getDomAttribute=function(c){if(!(!c||!c.nodeName)){var f=c.nodeName.toLowerCase();if(!(f==="input"&&c.type!=="image"))return r.domAttributes[f]}};function q(c){return escape(c)}function k(c){return encodeURIComponent(c).replace(/[!'()*]/g,q).replace(/\*/g,"%2A")}r.encode=k,r.decode=decodeURIComponent,r.iso8859=function(){r.encode=escape,r.decode=unescape},r.unicode=function(){r.encode=k,r.decode=decodeURIComponent},r.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/ig,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/ig,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/ig,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},r.encodeQuery=function(c,f){var h=r.encode(c+"");return f===void 0&&(f=r.escapeQuerySpace),f?h.replace(/%20/g,"+"):h},r.decodeQuery=function(c,f){c+="",f===void 0&&(f=r.escapeQuerySpace);try{return r.decode(f?c.replace(/\+/g,"%20"):c)}catch{return c}};var L={encode:"encode",decode:"decode"},F,B=function(c,f){return function(h){try{return r[f](h+"").replace(r.characters[c][f].expression,function(y){return r.characters[c][f].map[y]})}catch{return h}}};for(F in L)r[F+"PathSegment"]=B("pathname",L[F]),r[F+"UrnPathSegment"]=B("urnpath",L[F]);var W=function(c,f,h){return function(y){var g;h?g=function(N){return r[f](r[h](N))}:g=r[f];for(var v=(y+"").split(c),b=0,I=v.length;b<I;b++)v[b]=g(v[b]);return v.join(c)}};r.decodePath=W("/","decodePathSegment"),r.decodeUrnPath=W(":","decodeUrnPathSegment"),r.recodePath=W("/","encodePathSegment","decode"),r.recodeUrnPath=W(":","encodeUrnPathSegment","decode"),r.encodeReserved=B("reserved","encode"),r.parse=function(c,f){var h;return f||(f={preventInvalidHostname:r.preventInvalidHostname}),c=c.replace(r.leading_whitespace_expression,""),c=c.replace(r.ascii_tab_whitespace,""),h=c.indexOf("#"),h>-1&&(f.fragment=c.substring(h+1)||null,c=c.substring(0,h)),h=c.indexOf("?"),h>-1&&(f.query=c.substring(h+1)||null,c=c.substring(0,h)),c=c.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://"),c=c.replace(/^[/\\]{2,}/i,"//"),c.substring(0,2)==="//"?(f.protocol=null,c=c.substring(2),c=r.parseAuthority(c,f)):(h=c.indexOf(":"),h>-1&&(f.protocol=c.substring(0,h)||null,f.protocol&&!f.protocol.match(r.protocol_expression)?f.protocol=void 0:c.substring(h+1,h+3).replace(/\\/g,"/")==="//"?(c=c.substring(h+3),c=r.parseAuthority(c,f)):(c=c.substring(h+1),f.urn=!0))),f.path=c,f},r.parseHost=function(c,f){c||(c=""),c=c.replace(/\\/g,"/");var h=c.indexOf("/"),y,g;if(h===-1&&(h=c.length),c.charAt(0)==="[")y=c.indexOf("]"),f.hostname=c.substring(1,y)||null,f.port=c.substring(y+2,h)||null,f.port==="/"&&(f.port=null);else{var v=c.indexOf(":"),b=c.indexOf("/"),I=c.indexOf(":",v+1);I!==-1&&(b===-1||I<b)?(f.hostname=c.substring(0,h)||null,f.port=null):(g=c.substring(0,h).split(":"),f.hostname=g[0]||null,f.port=g[1]||null)}return f.hostname&&c.substring(h).charAt(0)!=="/"&&(h++,c="/"+c),f.preventInvalidHostname&&r.ensureValidHostname(f.hostname,f.protocol),f.port&&r.ensureValidPort(f.port),c.substring(h)||"/"},r.parseAuthority=function(c,f){return c=r.parseUserinfo(c,f),r.parseHost(c,f)},r.parseUserinfo=function(c,f){var h=c,y=c.indexOf("\\");y!==-1&&(c=c.replace(/\\/g,"/"));var g=c.indexOf("/"),v=c.lastIndexOf("@",g>-1?g:c.length-1),b;return v>-1&&(g===-1||v<g)?(b=c.substring(0,v).split(":"),f.username=b[0]?r.decode(b[0]):null,b.shift(),f.password=b[0]?r.decode(b.join(":")):null,c=h.substring(v+1)):(f.username=null,f.password=null),c},r.parseQuery=function(c,f){if(!c)return{};if(c=c.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,""),!c)return{};for(var h={},y=c.split("&"),g=y.length,v,b,I,N=0;N<g;N++)v=y[N].split("="),b=r.decodeQuery(v.shift(),f),I=v.length?r.decodeQuery(v.join("="),f):null,b!=="__proto__"&&(d.call(h,b)?((typeof h[b]=="string"||h[b]===null)&&(h[b]=[h[b]]),h[b].push(I)):h[b]=I);return h},r.build=function(c){var f="",h=!1;return c.protocol&&(f+=c.protocol+":"),!c.urn&&(f||c.hostname)&&(f+="//",h=!0),f+=r.buildAuthority(c)||"",typeof c.path=="string"&&(c.path.charAt(0)!=="/"&&h&&(f+="/"),f+=c.path),typeof c.query=="string"&&c.query&&(f+="?"+c.query),typeof c.fragment=="string"&&c.fragment&&(f+="#"+c.fragment),f},r.buildHost=function(c){var f="";if(c.hostname)r.ip6_expression.test(c.hostname)?f+="["+c.hostname+"]":f+=c.hostname;else return"";return c.port&&(f+=":"+c.port),f},r.buildAuthority=function(c){return r.buildUserinfo(c)+r.buildHost(c)},r.buildUserinfo=function(c){var f="";return c.username&&(f+=r.encode(c.username)),c.password&&(f+=":"+r.encode(c.password)),f&&(f+="@"),f},r.buildQuery=function(c,f,h){var y="",g,v,b,I;for(v in c)if(v!=="__proto__"&&d.call(c,v))if(w(c[v]))for(g={},b=0,I=c[v].length;b<I;b++)c[v][b]!==void 0&&g[c[v][b]+""]===void 0&&(y+="&"+r.buildQueryParameter(v,c[v][b],h),f!==!0&&(g[c[v][b]+""]=!0));else c[v]!==void 0&&(y+="&"+r.buildQueryParameter(v,c[v],h));return y.substring(1)},r.buildQueryParameter=function(c,f,h){return r.encodeQuery(c,h)+(f!==null?"="+r.encodeQuery(f,h):"")},r.addQuery=function(c,f,h){if(typeof f=="object")for(var y in f)d.call(f,y)&&r.addQuery(c,y,f[y]);else if(typeof f=="string"){if(c[f]===void 0){c[f]=h;return}else typeof c[f]=="string"&&(c[f]=[c[f]]);w(h)||(h=[h]),c[f]=(c[f]||[]).concat(h)}else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter")},r.setQuery=function(c,f,h){if(typeof f=="object")for(var y in f)d.call(f,y)&&r.setQuery(c,y,f[y]);else if(typeof f=="string")c[f]=h===void 0?null:h;else throw new TypeError("URI.setQuery() accepts an object, string as the name parameter")},r.removeQuery=function(c,f,h){var y,g,v;if(w(f))for(y=0,g=f.length;y<g;y++)c[f[y]]=void 0;else if(l(f)==="RegExp")for(v in c)f.test(v)&&(c[v]=void 0);else if(typeof f=="object")for(v in f)d.call(f,v)&&r.removeQuery(c,v,f[v]);else if(typeof f=="string")h!==void 0?l(h)==="RegExp"?!w(c[f])&&h.test(c[f])?c[f]=void 0:c[f]=T(c[f],h):c[f]===String(h)&&(!w(h)||h.length===1)?c[f]=void 0:w(c[f])&&(c[f]=T(c[f],h)):c[f]=void 0;else throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter")},r.hasQuery=function(c,f,h,y){switch(l(f)){case"String":break;case"RegExp":for(var g in c)if(d.call(c,g)&&f.test(g)&&(h===void 0||r.hasQuery(c,g,h)))return!0;return!1;case"Object":for(var v in f)if(d.call(f,v)&&!r.hasQuery(c,v,f[v]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(l(h)){case"Undefined":return f in c;case"Boolean":var b=!!(w(c[f])?c[f].length:c[f]);return h===b;case"Function":return!!h(c[f],f,c);case"Array":if(!w(c[f]))return!1;var I=y?C:P;return I(c[f],h);case"RegExp":return w(c[f])?y?C(c[f],h):!1:!!(c[f]&&c[f].match(h));case"Number":h=String(h);case"String":return w(c[f])?y?C(c[f],h):!1:c[f]===h;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},r.joinPaths=function(){for(var c=[],f=[],h=0,y=0;y<arguments.length;y++){var g=new r(arguments[y]);c.push(g);for(var v=g.segment(),b=0;b<v.length;b++)typeof v[b]=="string"&&f.push(v[b]),v[b]&&h++}if(!f.length||!h)return new r("");var I=new r("").segment(f);return(c[0].path()===""||c[0].path().slice(0,1)==="/")&&I.path("/"+I.path()),I.normalize()},r.commonPath=function(c,f){var h=Math.min(c.length,f.length),y;for(y=0;y<h;y++)if(c.charAt(y)!==f.charAt(y)){y--;break}return y<1?c.charAt(0)===f.charAt(0)&&c.charAt(0)==="/"?"/":"":((c.charAt(y)!=="/"||f.charAt(y)!=="/")&&(y=c.substring(0,y).lastIndexOf("/")),c.substring(0,y+1))},r.withinString=function(c,f,h){h||(h={});var y=h.start||r.findUri.start,g=h.end||r.findUri.end,v=h.trim||r.findUri.trim,b=h.parens||r.findUri.parens,I=/[a-z0-9-]=["']?$/i;for(y.lastIndex=0;;){var N=y.exec(c);if(!N)break;var Y=N.index;if(h.ignoreHtml){var ie=c.slice(Math.max(Y-3,0),Y);if(ie&&I.test(ie))continue}for(var ae=Y+c.slice(Y).search(g),ne=c.slice(Y,ae),ue=-1;;){var de=b.exec(ne);if(!de)break;var ye=de.index+de[0].length;ue=Math.max(ue,ye)}if(ue>-1?ne=ne.slice(0,ue)+ne.slice(ue).replace(v,""):ne=ne.replace(v,""),!(ne.length<=N[0].length)&&!(h.ignore&&h.ignore.test(ne))){ae=Y+ne.length;var me=f(ne,Y,ae,c);if(me===void 0){y.lastIndex=ae;continue}me=String(me),c=c.slice(0,Y)+me+c.slice(ae),y.lastIndex=Y+me.length}}return y.lastIndex=0,c},r.ensureValidHostname=function(c,f){var h=!!c,y=!!f,g=!1;if(y&&(g=C(r.hostProtocols,f)),g&&!h)throw new TypeError("Hostname cannot be empty, if protocol is "+f);if(c&&c.match(r.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(c).match(r.invalid_hostname_characters))throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-:_]')}},r.ensureValidPort=function(c){if(c){var f=Number(c);if(!(a(f)&&f>0&&f<65536))throw new TypeError('Port "'+c+'" is not a valid port')}},r.noConflict=function(c){if(c){var f={URI:this.noConflict()};return o.URITemplate&&typeof o.URITemplate.noConflict=="function"&&(f.URITemplate=o.URITemplate.noConflict()),o.IPv6&&typeof o.IPv6.noConflict=="function"&&(f.IPv6=o.IPv6.noConflict()),o.SecondLevelDomains&&typeof o.SecondLevelDomains.noConflict=="function"&&(f.SecondLevelDomains=o.SecondLevelDomains.noConflict()),f}else o.URI===this&&(o.URI=i);return this},u.build=function(c){return c===!0?this._deferred_build=!0:(c===void 0||this._deferred_build)&&(this._string=r.build(this._parts),this._deferred_build=!1),this},u.clone=function(){return new r(this)},u.valueOf=u.toString=function(){return this.build(!1)._string};function H(c){return function(f,h){return f===void 0?this._parts[c]||"":(this._parts[c]=f||null,this.build(!h),this)}}function ee(c,f){return function(h,y){return h===void 0?this._parts[c]||"":(h!==null&&(h=h+"",h.charAt(0)===f&&(h=h.substring(1))),this._parts[c]=h,this.build(!y),this)}}u.protocol=H("protocol"),u.username=H("username"),u.password=H("password"),u.hostname=H("hostname"),u.port=H("port"),u.query=ee("query","?"),u.fragment=ee("fragment","#"),u.search=function(c,f){var h=this.query(c,f);return typeof h=="string"&&h.length?"?"+h:h},u.hash=function(c,f){var h=this.fragment(c,f);return typeof h=="string"&&h.length?"#"+h:h},u.pathname=function(c,f){if(c===void 0||c===!0){var h=this._parts.path||(this._parts.hostname?"/":"");return c?(this._parts.urn?r.decodeUrnPath:r.decodePath)(h):h}else return this._parts.urn?this._parts.path=c?r.recodeUrnPath(c):"":this._parts.path=c?r.recodePath(c):"/",this.build(!f),this},u.path=u.pathname,u.href=function(c,f){var h;if(c===void 0)return this.toString();this._string="",this._parts=r._parts();var y=c instanceof r,g=typeof c=="object"&&(c.hostname||c.path||c.pathname);if(c.nodeName){var v=r.getDomAttribute(c);c=c[v]||"",g=!1}if(!y&&g&&c.pathname!==void 0&&(c=c.toString()),typeof c=="string"||c instanceof String)this._parts=r.parse(String(c),this._parts);else if(y||g){var b=y?c._parts:c;for(h in b)h!=="query"&&d.call(this._parts,h)&&(this._parts[h]=b[h]);b.query&&this.query(b.query,!1)}else throw new TypeError("invalid input");return this.build(!f),this},u.is=function(c){var f=!1,h=!1,y=!1,g=!1,v=!1,b=!1,I=!1,N=!this._parts.urn;switch(this._parts.hostname&&(N=!1,h=r.ip4_expression.test(this._parts.hostname),y=r.ip6_expression.test(this._parts.hostname),f=h||y,g=!f,v=g&&t&&t.has(this._parts.hostname),b=g&&r.idn_expression.test(this._parts.hostname),I=g&&r.punycode_expression.test(this._parts.hostname)),c.toLowerCase()){case"relative":return N;case"absolute":return!N;case"domain":case"name":return g;case"sld":return v;case"ip":return f;case"ip4":case"ipv4":case"inet4":return h;case"ip6":case"ipv6":case"inet6":return y;case"idn":return b;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return I}return null};var re=u.protocol,Z=u.port,oe=u.hostname;u.protocol=function(c,f){if(c&&(c=c.replace(/:(\/\/)?$/,""),!c.match(r.protocol_expression)))throw new TypeError('Protocol "'+c+`" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]`);return re.call(this,c,f)},u.scheme=u.protocol,u.port=function(c,f){return this._parts.urn?c===void 0?"":this:(c!==void 0&&(c===0&&(c=null),c&&(c+="",c.charAt(0)===":"&&(c=c.substring(1)),r.ensureValidPort(c))),Z.call(this,c,f))},u.hostname=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c!==void 0){var h={preventInvalidHostname:this._parts.preventInvalidHostname},y=r.parseHost(c,h);if(y!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');c=h.hostname,this._parts.preventInvalidHostname&&r.ensureValidHostname(c,this._parts.protocol)}return oe.call(this,c,f)},u.origin=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){var h=this.protocol(),y=this.authority();return y?(h?h+"://":"")+this.authority():""}else{var g=r(c);return this.protocol(g.protocol()).authority(g.authority()).build(!f),this}},u.host=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0)return this._parts.hostname?r.buildHost(this._parts):"";var h=r.parseHost(c,this._parts);if(h!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');return this.build(!f),this},u.authority=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0)return this._parts.hostname?r.buildAuthority(this._parts):"";var h=r.parseAuthority(c,this._parts);if(h!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');return this.build(!f),this},u.userinfo=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){var h=r.buildUserinfo(this._parts);return h&&h.substring(0,h.length-1)}else return c[c.length-1]!=="@"&&(c+="@"),r.parseUserinfo(c,this._parts),this.build(!f),this},u.resource=function(c,f){var h;return c===void 0?this.path()+this.search()+this.hash():(h=r.parse(c),this._parts.path=h.path,this._parts.query=h.query,this._parts.fragment=h.fragment,this.build(!f),this)},u.subdomain=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,h)||""}else{var y=this._parts.hostname.length-this.domain().length,g=this._parts.hostname.substring(0,y),v=new RegExp("^"+m(g));if(c&&c.charAt(c.length-1)!=="."&&(c+="."),c.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");return c&&r.ensureValidHostname(c,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(v,c),this.build(!f),this}},u.domain=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c=="boolean"&&(f=c,c=void 0),c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.match(/\./g);if(h&&h.length<2)return this._parts.hostname;var y=this._parts.hostname.length-this.tld(f).length-1;return y=this._parts.hostname.lastIndexOf(".",y-1)+1,this._parts.hostname.substring(y)||""}else{if(!c)throw new TypeError("cannot set domain empty");if(c.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");if(r.ensureValidHostname(c,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=c;else{var g=new RegExp(m(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(g,c)}return this.build(!f),this}},u.tld=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c=="boolean"&&(f=c,c=void 0),c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.lastIndexOf("."),y=this._parts.hostname.substring(h+1);return f!==!0&&t&&t.list[y.toLowerCase()]&&t.get(this._parts.hostname)||y}else{var g;if(c)if(c.match(/[^a-zA-Z0-9-]/))if(t&&t.is(c))g=new RegExp(m(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(g,c);else throw new TypeError('TLD "'+c+'" contains characters other than [A-Z0-9]');else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");g=new RegExp(m(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(g,c)}else throw new TypeError("cannot set TLD empty");return this.build(!f),this}},u.directory=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0||c===!0){if(!this._parts.path&&!this._parts.hostname)return"";if(this._parts.path==="/")return"/";var h=this._parts.path.length-this.filename().length-1,y=this._parts.path.substring(0,h)||(this._parts.hostname?"/":"");return c?r.decodePath(y):y}else{var g=this._parts.path.length-this.filename().length,v=this._parts.path.substring(0,g),b=new RegExp("^"+m(v));return this.is("relative")||(c||(c="/"),c.charAt(0)!=="/"&&(c="/"+c)),c&&c.charAt(c.length-1)!=="/"&&(c+="/"),c=r.recodePath(c),this._parts.path=this._parts.path.replace(b,c),this.build(!f),this}},u.filename=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c!="string"){if(!this._parts.path||this._parts.path==="/")return"";var h=this._parts.path.lastIndexOf("/"),y=this._parts.path.substring(h+1);return c?r.decodePathSegment(y):y}else{var g=!1;c.charAt(0)==="/"&&(c=c.substring(1)),c.match(/\.?\//)&&(g=!0);var v=new RegExp(m(this.filename())+"$");return c=r.recodePath(c),this._parts.path=this._parts.path.replace(v,c),g?this.normalizePath(f):this.build(!f),this}},u.suffix=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0||c===!0){if(!this._parts.path||this._parts.path==="/")return"";var h=this.filename(),y=h.lastIndexOf("."),g,v;return y===-1?"":(g=h.substring(y+1),v=/^[a-z0-9%]+$/i.test(g)?g:"",c?r.decodePathSegment(v):v)}else{c.charAt(0)==="."&&(c=c.substring(1));var b=this.suffix(),I;if(b)c?I=new RegExp(m(b)+"$"):I=new RegExp(m("."+b)+"$");else{if(!c)return this;this._parts.path+="."+r.recodePath(c)}return I&&(c=r.recodePath(c),this._parts.path=this._parts.path.replace(I,c)),this.build(!f),this}},u.segment=function(c,f,h){var y=this._parts.urn?":":"/",g=this.path(),v=g.substring(0,1)==="/",b=g.split(y);if(c!==void 0&&typeof c!="number"&&(h=f,f=c,c=void 0),c!==void 0&&typeof c!="number")throw new Error('Bad segment "'+c+'", must be 0-based integer');if(v&&b.shift(),c<0&&(c=Math.max(b.length+c,0)),f===void 0)return c===void 0?b:b[c];if(c===null||b[c]===void 0)if(w(f)){b=[];for(var I=0,N=f.length;I<N;I++)!f[I].length&&(!b.length||!b[b.length-1].length)||(b.length&&!b[b.length-1].length&&b.pop(),b.push(A(f[I])))}else(f||typeof f=="string")&&(f=A(f),b[b.length-1]===""?b[b.length-1]=f:b.push(f));else f?b[c]=A(f):b.splice(c,1);return v&&b.unshift(""),this.path(b.join(y),h)},u.segmentCoded=function(c,f,h){var y,g,v;if(typeof c!="number"&&(h=f,f=c,c=void 0),f===void 0){if(y=this.segment(c,f,h),!w(y))y=y!==void 0?r.decode(y):void 0;else for(g=0,v=y.length;g<v;g++)y[g]=r.decode(y[g]);return y}if(!w(f))f=typeof f=="string"||f instanceof String?r.encode(f):f;else for(g=0,v=f.length;g<v;g++)f[g]=r.encode(f[g]);return this.segment(c,f,h)};var J=u.query;return u.query=function(c,f){if(c===!0)return r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof c=="function"){var h=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace),y=c.call(this,h);return this._parts.query=r.buildQuery(y||h,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!f),this}else return c!==void 0&&typeof c!="string"?(this._parts.query=r.buildQuery(c,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!f),this):J.call(this,c,f)},u.setQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof c=="string"||c instanceof String)y[c]=f!==void 0?f:null;else if(typeof c=="object")for(var g in c)d.call(c,g)&&(y[g]=c[g]);else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");return this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.addQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.addQuery(y,c,f===void 0?null:f),this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.removeQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.removeQuery(y,c,f),this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.hasQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.hasQuery(y,c,f,h)},u.setSearch=u.setQuery,u.addSearch=u.addQuery,u.removeSearch=u.removeQuery,u.hasSearch=u.hasQuery,u.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},u.normalizeProtocol=function(c){return typeof this._parts.protocol=="string"&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!c)),this},u.normalizeHostname=function(c){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&n&&(this._parts.hostname=n.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!c)),this},u.normalizePort=function(c){return typeof this._parts.protocol=="string"&&this._parts.port===r.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!c)),this},u.normalizePath=function(c){var f=this._parts.path;if(!f)return this;if(this._parts.urn)return this._parts.path=r.recodeUrnPath(this._parts.path),this.build(!c),this;if(this._parts.path==="/")return this;f=r.recodePath(f);var h,y="",g,v;for(f.charAt(0)!=="/"&&(h=!0,f="/"+f),(f.slice(-3)==="/.."||f.slice(-2)==="/.")&&(f+="/"),f=f.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),h&&(y=f.substring(1).match(/^(\.\.\/)+/)||"",y&&(y=y[0]));g=f.search(/\/\.\.(\/|$)/),g!==-1;){if(g===0){f=f.substring(3);continue}v=f.substring(0,g).lastIndexOf("/"),v===-1&&(v=g),f=f.substring(0,v)+f.substring(g+3)}return h&&this.is("relative")&&(f=y+f.substring(1)),this._parts.path=f,this.build(!c),this},u.normalizePathname=u.normalizePath,u.normalizeQuery=function(c){return typeof this._parts.query=="string"&&(this._parts.query.length?this.query(r.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!c)),this},u.normalizeFragment=function(c){return this._parts.fragment||(this._parts.fragment=null,this.build(!c)),this},u.normalizeSearch=u.normalizeQuery,u.normalizeHash=u.normalizeFragment,u.iso8859=function(){var c=r.encode,f=r.decode;r.encode=escape,r.decode=decodeURIComponent;try{this.normalize()}finally{r.encode=c,r.decode=f}return this},u.unicode=function(){var c=r.encode,f=r.decode;r.encode=k,r.decode=unescape;try{this.normalize()}finally{r.encode=c,r.decode=f}return this},u.readable=function(){var c=this.clone();c.username("").password("").normalize();var f="";if(c._parts.protocol&&(f+=c._parts.protocol+"://"),c._parts.hostname&&(c.is("punycode")&&e?(f+=e.toUnicode(c._parts.hostname),c._parts.port&&(f+=":"+c._parts.port)):f+=c.host()),c._parts.hostname&&c._parts.path&&c._parts.path.charAt(0)!=="/"&&(f+="/"),f+=c.path(!0),c._parts.query){for(var h="",y=0,g=c._parts.query.split("&"),v=g.length;y<v;y++){var b=(g[y]||"").split("=");h+="&"+r.decodeQuery(b[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),b[1]!==void 0&&(h+="="+r.decodeQuery(b[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}f+="?"+h.substring(1)}return f+=r.decodeQuery(c.hash(),!0),f},u.absoluteTo=function(c){var f=this.clone(),h=["protocol","username","password","hostname","port"],y,g,v;if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(c instanceof r||(c=new r(c)),f._parts.protocol||(f._parts.protocol=c._parts.protocol,this._parts.hostname))return f;for(g=0;v=h[g];g++)f._parts[v]=c._parts[v];return f._parts.path?(f._parts.path.substring(-2)===".."&&(f._parts.path+="/"),f.path().charAt(0)!=="/"&&(y=c.directory(),y=y||(c.path().indexOf("/")===0?"/":""),f._parts.path=(y?y+"/":"")+f._parts.path,f.normalizePath())):(f._parts.path=c._parts.path,f._parts.query||(f._parts.query=c._parts.query)),f.build(),f},u.relativeTo=function(c){var f=this.clone().normalize(),h,y,g,v,b;if(f._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(c=new r(c).normalize(),h=f._parts,y=c._parts,v=f.path(),b=c.path(),v.charAt(0)!=="/")throw new Error("URI is already relative");if(b.charAt(0)!=="/")throw new Error("Cannot calculate a URI relative to another relative URI");if(h.protocol===y.protocol&&(h.protocol=null),h.username!==y.username||h.password!==y.password||h.protocol!==null||h.username!==null||h.password!==null)return f.build();if(h.hostname===y.hostname&&h.port===y.port)h.hostname=null,h.port=null;else return f.build();if(v===b)return h.path="",f.build();if(g=r.commonPath(v,b),!g)return f.build();var I=y.path.substring(g.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return h.path=I+h.path.substring(g.length)||"./",f.build()},u.equals=function(c){var f=this.clone(),h=new r(c),y={},g={},v={},b,I,N;if(f.normalize(),h.normalize(),f.toString()===h.toString())return!0;if(b=f.query(),I=h.query(),f.query(""),h.query(""),f.toString()!==h.toString()||b.length!==I.length)return!1;y=r.parseQuery(b,this._parts.escapeQuerySpace),g=r.parseQuery(I,this._parts.escapeQuerySpace);for(N in y)if(d.call(y,N)){if(w(y[N])){if(!P(y[N],g[N]))return!1}else if(y[N]!==g[N])return!1;v[N]=!0}for(N in g)if(d.call(g,N)&&!v[N])return!1;return!0},u.preventInvalidHostname=function(c){return this._parts.preventInvalidHostname=!!c,this},u.duplicateQueryParameters=function(c){return this._parts.duplicateQueryParameters=!!c,this},u.escapeQuerySpace=function(c){return this._parts.escapeQuerySpace=!!c,this},r})});function M(e,n,t,o){this.x=O(e,0),this.y=O(n,0),this.z=O(t,0),this.w=O(o,0)}M.fromElements=function(e,n,t,o,i){return p(i)?(i.x=e,i.y=n,i.z=t,i.w=o,i):new M(e,n,t,o)};M.fromColor=function(e,n){return s.typeOf.object("color",e),p(n)?(n.x=e.red,n.y=e.green,n.z=e.blue,n.w=e.alpha,n):new M(e.red,e.green,e.blue,e.alpha)};M.clone=function(e,n){if(p(e))return p(n)?(n.x=e.x,n.y=e.y,n.z=e.z,n.w=e.w,n):new M(e.x,e.y,e.z,e.w)};M.packedLength=4;M.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e.x,n[t++]=e.y,n[t++]=e.z,n[t]=e.w,n};M.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new M),t.x=e[n++],t.y=e[n++],t.z=e[n++],t.w=e[n],t};M.packArray=function(e,n){s.defined("array",e);let t=e.length,o=t*4;if(!p(n))n=new Array(o);else{if(!Array.isArray(n)&&n.length!==o)throw new j("If result is a typed array, it must have exactly array.length * 4 elements");n.length!==o&&(n.length=o)}for(let i=0;i<t;++i)M.pack(e[i],n,i*4);return n};M.unpackArray=function(e,n){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!==0)throw new j("array length must be a multiple of 4.");let t=e.length;p(n)?n.length=t/4:n=new Array(t/4);for(let o=0;o<t;o+=4){let i=o/4;n[i]=M.unpack(e,o,n[i])}return n};M.fromArray=M.unpack;M.maximumComponent=function(e){return s.typeOf.object("cartesian",e),Math.max(e.x,e.y,e.z,e.w)};M.minimumComponent=function(e){return s.typeOf.object("cartesian",e),Math.min(e.x,e.y,e.z,e.w)};M.minimumByComponent=function(e,n,t){return s.typeOf.object("first",e),s.typeOf.object("second",n),s.typeOf.object("result",t),t.x=Math.min(e.x,n.x),t.y=Math.min(e.y,n.y),t.z=Math.min(e.z,n.z),t.w=Math.min(e.w,n.w),t};M.maximumByComponent=function(e,n,t){return s.typeOf.object("first",e),s.typeOf.object("second",n),s.typeOf.object("result",t),t.x=Math.max(e.x,n.x),t.y=Math.max(e.y,n.y),t.z=Math.max(e.z,n.z),t.w=Math.max(e.w,n.w),t};M.clamp=function(e,n,t,o){s.typeOf.object("value",e),s.typeOf.object("min",n),s.typeOf.object("max",t),s.typeOf.object("result",o);let i=E.clamp(e.x,n.x,t.x),r=E.clamp(e.y,n.y,t.y),a=E.clamp(e.z,n.z,t.z),u=E.clamp(e.w,n.w,t.w);return o.x=i,o.y=r,o.z=a,o.w=u,o};M.magnitudeSquared=function(e){return s.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};M.magnitude=function(e){return Math.sqrt(M.magnitudeSquared(e))};var En=new M;M.distance=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),M.subtract(e,n,En),M.magnitude(En)};M.distanceSquared=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),M.subtract(e,n,En),M.magnitudeSquared(En)};M.normalize=function(e,n){s.typeOf.object("cartesian",e),s.typeOf.object("result",n);let t=M.magnitude(e);if(n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,isNaN(n.x)||isNaN(n.y)||isNaN(n.z)||isNaN(n.w))throw new j("normalized result is not a number");return n};M.dot=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),e.x*n.x+e.y*n.y+e.z*n.z+e.w*n.w};M.multiplyComponents=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x*n.x,t.y=e.y*n.y,t.z=e.z*n.z,t.w=e.w*n.w,t};M.divideComponents=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x/n.x,t.y=e.y/n.y,t.z=e.z/n.z,t.w=e.w/n.w,t};M.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x+n.x,t.y=e.y+n.y,t.z=e.z+n.z,t.w=e.w+n.w,t};M.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x-n.x,t.y=e.y-n.y,t.z=e.z-n.z,t.w=e.w-n.w,t};M.multiplyByScalar=function(e,n,t){return s.typeOf.object("cartesian",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x*n,t.y=e.y*n,t.z=e.z*n,t.w=e.w*n,t};M.divideByScalar=function(e,n,t){return s.typeOf.object("cartesian",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,t.w=e.w/n,t};M.negate=function(e,n){return s.typeOf.object("cartesian",e),s.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=-e.w,n};M.abs=function(e,n){return s.typeOf.object("cartesian",e),s.typeOf.object("result",n),n.x=Math.abs(e.x),n.y=Math.abs(e.y),n.z=Math.abs(e.z),n.w=Math.abs(e.w),n};var Et=new M;M.lerp=function(e,n,t,o){return s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o),M.multiplyByScalar(n,t,Et),o=M.multiplyByScalar(e,1-t,o),M.add(Et,o,o)};var zo=new M;M.mostOrthogonalAxis=function(e,n){s.typeOf.object("cartesian",e),s.typeOf.object("result",n);let t=M.normalize(e,zo);return M.abs(t,t),t.x<=t.y?t.x<=t.z?t.x<=t.w?n=M.clone(M.UNIT_X,n):n=M.clone(M.UNIT_W,n):t.z<=t.w?n=M.clone(M.UNIT_Z,n):n=M.clone(M.UNIT_W,n):t.y<=t.z?t.y<=t.w?n=M.clone(M.UNIT_Y,n):n=M.clone(M.UNIT_W,n):t.z<=t.w?n=M.clone(M.UNIT_Z,n):n=M.clone(M.UNIT_W,n),n};M.equals=function(e,n){return e===n||p(e)&&p(n)&&e.x===n.x&&e.y===n.y&&e.z===n.z&&e.w===n.w};M.equalsArray=function(e,n,t){return e.x===n[t]&&e.y===n[t+1]&&e.z===n[t+2]&&e.w===n[t+3]};M.equalsEpsilon=function(e,n,t,o){return e===n||p(e)&&p(n)&&E.equalsEpsilon(e.x,n.x,t,o)&&E.equalsEpsilon(e.y,n.y,t,o)&&E.equalsEpsilon(e.z,n.z,t,o)&&E.equalsEpsilon(e.w,n.w,t,o)};M.ZERO=Object.freeze(new M(0,0,0,0));M.ONE=Object.freeze(new M(1,1,1,1));M.UNIT_X=Object.freeze(new M(1,0,0,0));M.UNIT_Y=Object.freeze(new M(0,1,0,0));M.UNIT_Z=Object.freeze(new M(0,0,1,0));M.UNIT_W=Object.freeze(new M(0,0,0,1));M.prototype.clone=function(e){return M.clone(this,e)};M.prototype.equals=function(e){return M.equals(this,e)};M.prototype.equalsEpsilon=function(e,n,t){return M.equalsEpsilon(this,e,n,t)};M.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Jn=new Float32Array(1),le=new Uint8Array(Jn.buffer),Io=new Uint32Array([287454020]),qo=new Uint8Array(Io.buffer),Tt=qo[0]===68;M.packFloat=function(e,n){return s.typeOf.number("value",e),p(n)||(n=new M),Jn[0]=e,Tt?(n.x=le[0],n.y=le[1],n.z=le[2],n.w=le[3]):(n.x=le[3],n.y=le[2],n.z=le[1],n.w=le[0]),n};M.unpackFloat=function(e){return s.typeOf.object("packedFloat",e),Tt?(le[0]=e.x,le[1]=e.y,le[2]=e.z,le[3]=e.w):(le[0]=e.w,le[1]=e.z,le[2]=e.y,le[3]=e.x),Jn[0]};var qe=M;function S(e,n,t,o,i,r,a,u,d,m,l,w,T,C,P,A){this[0]=O(e,0),this[1]=O(i,0),this[2]=O(d,0),this[3]=O(T,0),this[4]=O(n,0),this[5]=O(r,0),this[6]=O(m,0),this[7]=O(C,0),this[8]=O(t,0),this[9]=O(a,0),this[10]=O(l,0),this[11]=O(P,0),this[12]=O(o,0),this[13]=O(u,0),this[14]=O(w,0),this[15]=O(A,0)}S.packedLength=16;S.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e[0],n[t++]=e[1],n[t++]=e[2],n[t++]=e[3],n[t++]=e[4],n[t++]=e[5],n[t++]=e[6],n[t++]=e[7],n[t++]=e[8],n[t++]=e[9],n[t++]=e[10],n[t++]=e[11],n[t++]=e[12],n[t++]=e[13],n[t++]=e[14],n[t]=e[15],n};S.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new S),t[0]=e[n++],t[1]=e[n++],t[2]=e[n++],t[3]=e[n++],t[4]=e[n++],t[5]=e[n++],t[6]=e[n++],t[7]=e[n++],t[8]=e[n++],t[9]=e[n++],t[10]=e[n++],t[11]=e[n++],t[12]=e[n++],t[13]=e[n++],t[14]=e[n++],t[15]=e[n],t};S.packArray=function(e,n){s.defined("array",e);let t=e.length,o=t*16;if(!p(n))n=new Array(o);else{if(!Array.isArray(n)&&n.length!==o)throw new j("If result is a typed array, it must have exactly array.length * 16 elements");n.length!==o&&(n.length=o)}for(let i=0;i<t;++i)S.pack(e[i],n,i*16);return n};S.unpackArray=function(e,n){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,16),e.length%16!==0)throw new j("array length must be a multiple of 16.");let t=e.length;p(n)?n.length=t/16:n=new Array(t/16);for(let o=0;o<t;o+=16){let i=o/16;n[i]=S.unpack(e,o,n[i])}return n};S.clone=function(e,n){if(p(e))return p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):new S(e[0],e[4],e[8],e[12],e[1],e[5],e[9],e[13],e[2],e[6],e[10],e[14],e[3],e[7],e[11],e[15])};S.fromArray=S.unpack;S.fromColumnMajorArray=function(e,n){return s.defined("values",e),S.clone(e,n)};S.fromRowMajorArray=function(e,n){return s.defined("values",e),p(n)?(n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=e[1],n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=e[2],n[9]=e[6],n[10]=e[10],n[11]=e[14],n[12]=e[3],n[13]=e[7],n[14]=e[11],n[15]=e[15],n):new S(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])};S.fromRotationTranslation=function(e,n,t){return s.typeOf.object("rotation",e),n=O(n,_.ZERO),p(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=0,t[4]=e[3],t[5]=e[4],t[6]=e[5],t[7]=0,t[8]=e[6],t[9]=e[7],t[10]=e[8],t[11]=0,t[12]=n.x,t[13]=n.y,t[14]=n.z,t[15]=1,t):new S(e[0],e[3],e[6],n.x,e[1],e[4],e[7],n.y,e[2],e[5],e[8],n.z,0,0,0,1)};S.fromTranslationQuaternionRotationScale=function(e,n,t,o){s.typeOf.object("translation",e),s.typeOf.object("rotation",n),s.typeOf.object("scale",t),p(o)||(o=new S);let i=t.x,r=t.y,a=t.z,u=n.x*n.x,d=n.x*n.y,m=n.x*n.z,l=n.x*n.w,w=n.y*n.y,T=n.y*n.z,C=n.y*n.w,P=n.z*n.z,A=n.z*n.w,q=n.w*n.w,k=u-w-P+q,L=2*(d-A),F=2*(m+C),B=2*(d+A),W=-u+w-P+q,H=2*(T-l),ee=2*(m-C),re=2*(T+l),Z=-u-w+P+q;return o[0]=k*i,o[1]=B*i,o[2]=ee*i,o[3]=0,o[4]=L*r,o[5]=W*r,o[6]=re*r,o[7]=0,o[8]=F*a,o[9]=H*a,o[10]=Z*a,o[11]=0,o[12]=e.x,o[13]=e.y,o[14]=e.z,o[15]=1,o};S.fromTranslationRotationScale=function(e,n){return s.typeOf.object("translationRotationScale",e),S.fromTranslationQuaternionRotationScale(e.translation,e.rotation,e.scale,n)};S.fromTranslation=function(e,n){return s.typeOf.object("translation",e),S.fromRotationTranslation(Q.IDENTITY,e,n)};S.fromScale=function(e,n){return s.typeOf.object("scale",e),p(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e.y,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e.z,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new S(e.x,0,0,0,0,e.y,0,0,0,0,e.z,0,0,0,0,1)};S.fromUniformScale=function(e,n){return s.typeOf.number("scale",e),p(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new S(e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1)};S.fromRotation=function(e,n){return s.typeOf.object("rotation",e),p(n)||(n=new S),n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=0,n[4]=e[3],n[5]=e[4],n[6]=e[5],n[7]=0,n[8]=e[6],n[9]=e[7],n[10]=e[8],n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n};var Je=new _,Ge=new _,pn=new _;S.fromCamera=function(e,n){s.typeOf.object("camera",e);let t=e.position,o=e.direction,i=e.up;s.typeOf.object("camera.position",t),s.typeOf.object("camera.direction",o),s.typeOf.object("camera.up",i),_.normalize(o,Je),_.normalize(_.cross(Je,i,Ge),Ge),_.normalize(_.cross(Ge,Je,pn),pn);let r=Ge.x,a=Ge.y,u=Ge.z,d=Je.x,m=Je.y,l=Je.z,w=pn.x,T=pn.y,C=pn.z,P=t.x,A=t.y,q=t.z,k=r*-P+a*-A+u*-q,L=w*-P+T*-A+C*-q,F=d*P+m*A+l*q;return p(n)?(n[0]=r,n[1]=w,n[2]=-d,n[3]=0,n[4]=a,n[5]=T,n[6]=-m,n[7]=0,n[8]=u,n[9]=C,n[10]=-l,n[11]=0,n[12]=k,n[13]=L,n[14]=F,n[15]=1,n):new S(r,a,u,k,w,T,C,L,-d,-m,-l,F,0,0,0,1)};S.computePerspectiveFieldOfView=function(e,n,t,o,i){s.typeOf.number.greaterThan("fovY",e,0),s.typeOf.number.lessThan("fovY",e,Math.PI),s.typeOf.number.greaterThan("near",t,0),s.typeOf.number.greaterThan("far",o,0),s.typeOf.object("result",i);let a=1/Math.tan(e*.5),u=a/n,d=(o+t)/(t-o),m=2*o*t/(t-o);return i[0]=u,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=a,i[6]=0,i[7]=0,i[8]=0,i[9]=0,i[10]=d,i[11]=-1,i[12]=0,i[13]=0,i[14]=m,i[15]=0,i};S.computeOrthographicOffCenter=function(e,n,t,o,i,r,a){s.typeOf.number("left",e),s.typeOf.number("right",n),s.typeOf.number("bottom",t),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.number("far",r),s.typeOf.object("result",a);let u=1/(n-e),d=1/(o-t),m=1/(r-i),l=-(n+e)*u,w=-(o+t)*d,T=-(r+i)*m;return u*=2,d*=2,m*=-2,a[0]=u,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=d,a[6]=0,a[7]=0,a[8]=0,a[9]=0,a[10]=m,a[11]=0,a[12]=l,a[13]=w,a[14]=T,a[15]=1,a};S.computePerspectiveOffCenter=function(e,n,t,o,i,r,a){s.typeOf.number("left",e),s.typeOf.number("right",n),s.typeOf.number("bottom",t),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.number("far",r),s.typeOf.object("result",a);let u=2*i/(n-e),d=2*i/(o-t),m=(n+e)/(n-e),l=(o+t)/(o-t),w=-(r+i)/(r-i),T=-1,C=-2*r*i/(r-i);return a[0]=u,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=d,a[6]=0,a[7]=0,a[8]=m,a[9]=l,a[10]=w,a[11]=T,a[12]=0,a[13]=0,a[14]=C,a[15]=0,a};S.computeInfinitePerspectiveOffCenter=function(e,n,t,o,i,r){s.typeOf.number("left",e),s.typeOf.number("right",n),s.typeOf.number("bottom",t),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.object("result",r);let a=2*i/(n-e),u=2*i/(o-t),d=(n+e)/(n-e),m=(o+t)/(o-t),l=-1,w=-1,T=-2*i;return r[0]=a,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=u,r[6]=0,r[7]=0,r[8]=d,r[9]=m,r[10]=l,r[11]=w,r[12]=0,r[13]=0,r[14]=T,r[15]=0,r};S.computeViewportTransformation=function(e,n,t,o){p(o)||(o=new S),e=O(e,O.EMPTY_OBJECT);let i=O(e.x,0),r=O(e.y,0),a=O(e.width,0),u=O(e.height,0);n=O(n,0),t=O(t,1);let d=a*.5,m=u*.5,l=(t-n)*.5,w=d,T=m,C=l,P=i+d,A=r+m,q=n+l,k=1;return o[0]=w,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=T,o[6]=0,o[7]=0,o[8]=0,o[9]=0,o[10]=C,o[11]=0,o[12]=P,o[13]=A,o[14]=q,o[15]=k,o};S.computeView=function(e,n,t,o,i){return s.typeOf.object("position",e),s.typeOf.object("direction",n),s.typeOf.object("up",t),s.typeOf.object("right",o),s.typeOf.object("result",i),i[0]=o.x,i[1]=t.x,i[2]=-n.x,i[3]=0,i[4]=o.y,i[5]=t.y,i[6]=-n.y,i[7]=0,i[8]=o.z,i[9]=t.z,i[10]=-n.z,i[11]=0,i[12]=-_.dot(o,e),i[13]=-_.dot(t,e),i[14]=_.dot(n,e),i[15]=1,i};S.toArray=function(e,n){return s.typeOf.object("matrix",e),p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]]};S.getElementIndex=function(e,n){return s.typeOf.number.greaterThanOrEquals("row",n,0),s.typeOf.number.lessThanOrEquals("row",n,3),s.typeOf.number.greaterThanOrEquals("column",e,0),s.typeOf.number.lessThanOrEquals("column",e,3),e*4+n};S.getColumn=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("result",t);let o=n*4,i=e[o],r=e[o+1],a=e[o+2],u=e[o+3];return t.x=i,t.y=r,t.z=a,t.w=u,t};S.setColumn=function(e,n,t,o){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=S.clone(e,o);let i=n*4;return o[i]=t.x,o[i+1]=t.y,o[i+2]=t.z,o[i+3]=t.w,o};S.getRow=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("result",t);let o=e[n],i=e[n+4],r=e[n+8],a=e[n+12];return t.x=o,t.y=i,t.z=r,t.w=a,t};S.setRow=function(e,n,t,o){return s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=S.clone(e,o),o[n]=t.x,o[n+4]=t.y,o[n+8]=t.z,o[n+12]=t.w,o};S.setTranslation=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.object("translation",n),s.typeOf.object("result",t),t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=n.x,t[13]=n.y,t[14]=n.z,t[15]=e[15],t};var Do=new _;S.setScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t);let o=S.getScale(e,Do),i=n.x/o.x,r=n.y/o.y,a=n.z/o.z;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*i,t[3]=e[3],t[4]=e[4]*r,t[5]=e[5]*r,t[6]=e[6]*r,t[7]=e[7],t[8]=e[8]*a,t[9]=e[9]*a,t[10]=e[10]*a,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};var No=new _;S.setUniformScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t);let o=S.getScale(e,No),i=n/o.x,r=n/o.y,a=n/o.z;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*i,t[3]=e[3],t[4]=e[4]*r,t[5]=e[5]*r,t[6]=e[6]*r,t[7]=e[7],t[8]=e[8]*a,t[9]=e[9]*a,t[10]=e[10]*a,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};var Gn=new _;S.getScale=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n.x=_.magnitude(_.fromElements(e[0],e[1],e[2],Gn)),n.y=_.magnitude(_.fromElements(e[4],e[5],e[6],Gn)),n.z=_.magnitude(_.fromElements(e[8],e[9],e[10],Gn)),n};var Ct=new _;S.getMaximumScale=function(e){return S.getScale(e,Ct),_.maximumComponent(Ct)};var ko=new _;S.setRotation=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let o=S.getScale(e,ko);return t[0]=n[0]*o.x,t[1]=n[1]*o.x,t[2]=n[2]*o.x,t[3]=e[3],t[4]=n[3]*o.y,t[5]=n[4]*o.y,t[6]=n[5]*o.y,t[7]=e[7],t[8]=n[6]*o.z,t[9]=n[7]*o.z,t[10]=n[8]*o.z,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};var Fo=new _;S.getRotation=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=S.getScale(e,Fo);return n[0]=e[0]/t.x,n[1]=e[1]/t.x,n[2]=e[2]/t.x,n[3]=e[4]/t.y,n[4]=e[5]/t.y,n[5]=e[6]/t.y,n[6]=e[8]/t.z,n[7]=e[9]/t.z,n[8]=e[10]/t.z,n};S.multiply=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e[0],i=e[1],r=e[2],a=e[3],u=e[4],d=e[5],m=e[6],l=e[7],w=e[8],T=e[9],C=e[10],P=e[11],A=e[12],q=e[13],k=e[14],L=e[15],F=n[0],B=n[1],W=n[2],H=n[3],ee=n[4],re=n[5],Z=n[6],oe=n[7],J=n[8],c=n[9],f=n[10],h=n[11],y=n[12],g=n[13],v=n[14],b=n[15],I=o*F+u*B+w*W+A*H,N=i*F+d*B+T*W+q*H,Y=r*F+m*B+C*W+k*H,ie=a*F+l*B+P*W+L*H,ae=o*ee+u*re+w*Z+A*oe,ne=i*ee+d*re+T*Z+q*oe,ue=r*ee+m*re+C*Z+k*oe,de=a*ee+l*re+P*Z+L*oe,ye=o*J+u*c+w*f+A*h,me=i*J+d*c+T*f+q*h,he=r*J+m*c+C*f+k*h,X=a*J+l*c+P*f+L*h,_e=o*y+u*g+w*v+A*b,Te=i*y+d*g+T*v+q*b,je=r*y+m*g+C*v+k*b,fn=a*y+l*g+P*v+L*b;return t[0]=I,t[1]=N,t[2]=Y,t[3]=ie,t[4]=ae,t[5]=ne,t[6]=ue,t[7]=de,t[8]=ye,t[9]=me,t[10]=he,t[11]=X,t[12]=_e,t[13]=Te,t[14]=je,t[15]=fn,t};S.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t[4]=e[4]+n[4],t[5]=e[5]+n[5],t[6]=e[6]+n[6],t[7]=e[7]+n[7],t[8]=e[8]+n[8],t[9]=e[9]+n[9],t[10]=e[10]+n[10],t[11]=e[11]+n[11],t[12]=e[12]+n[12],t[13]=e[13]+n[13],t[14]=e[14]+n[14],t[15]=e[15]+n[15],t};S.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t[4]=e[4]-n[4],t[5]=e[5]-n[5],t[6]=e[6]-n[6],t[7]=e[7]-n[7],t[8]=e[8]-n[8],t[9]=e[9]-n[9],t[10]=e[10]-n[10],t[11]=e[11]-n[11],t[12]=e[12]-n[12],t[13]=e[13]-n[13],t[14]=e[14]-n[14],t[15]=e[15]-n[15],t};S.multiplyTransformation=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e[0],i=e[1],r=e[2],a=e[4],u=e[5],d=e[6],m=e[8],l=e[9],w=e[10],T=e[12],C=e[13],P=e[14],A=n[0],q=n[1],k=n[2],L=n[4],F=n[5],B=n[6],W=n[8],H=n[9],ee=n[10],re=n[12],Z=n[13],oe=n[14],J=o*A+a*q+m*k,c=i*A+u*q+l*k,f=r*A+d*q+w*k,h=o*L+a*F+m*B,y=i*L+u*F+l*B,g=r*L+d*F+w*B,v=o*W+a*H+m*ee,b=i*W+u*H+l*ee,I=r*W+d*H+w*ee,N=o*re+a*Z+m*oe+T,Y=i*re+u*Z+l*oe+C,ie=r*re+d*Z+w*oe+P;return t[0]=J,t[1]=c,t[2]=f,t[3]=0,t[4]=h,t[5]=y,t[6]=g,t[7]=0,t[8]=v,t[9]=b,t[10]=I,t[11]=0,t[12]=N,t[13]=Y,t[14]=ie,t[15]=1,t};S.multiplyByMatrix3=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("rotation",n),s.typeOf.object("result",t);let o=e[0],i=e[1],r=e[2],a=e[4],u=e[5],d=e[6],m=e[8],l=e[9],w=e[10],T=n[0],C=n[1],P=n[2],A=n[3],q=n[4],k=n[5],L=n[6],F=n[7],B=n[8],W=o*T+a*C+m*P,H=i*T+u*C+l*P,ee=r*T+d*C+w*P,re=o*A+a*q+m*k,Z=i*A+u*q+l*k,oe=r*A+d*q+w*k,J=o*L+a*F+m*B,c=i*L+u*F+l*B,f=r*L+d*F+w*B;return t[0]=W,t[1]=H,t[2]=ee,t[3]=0,t[4]=re,t[5]=Z,t[6]=oe,t[7]=0,t[8]=J,t[9]=c,t[10]=f,t[11]=0,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};S.multiplyByTranslation=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("translation",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=o*e[0]+i*e[4]+r*e[8]+e[12],u=o*e[1]+i*e[5]+r*e[9]+e[13],d=o*e[2]+i*e[6]+r*e[10]+e[14];return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=a,t[13]=u,t[14]=d,t[15]=e[15],t};S.multiplyByScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z;return o===1&&i===1&&r===1?S.clone(e,t):(t[0]=o*e[0],t[1]=o*e[1],t[2]=o*e[2],t[3]=e[3],t[4]=i*e[4],t[5]=i*e[5],t[6]=i*e[6],t[7]=e[7],t[8]=r*e[8],t[9]=r*e[9],t[10]=r*e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t)};S.multiplyByUniformScale=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3],t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7],t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};S.multiplyByVector=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=n.w,u=e[0]*o+e[4]*i+e[8]*r+e[12]*a,d=e[1]*o+e[5]*i+e[9]*r+e[13]*a,m=e[2]*o+e[6]*i+e[10]*r+e[14]*a,l=e[3]*o+e[7]*i+e[11]*r+e[15]*a;return t.x=u,t.y=d,t.z=m,t.w=l,t};S.multiplyByPointAsVector=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=e[0]*o+e[4]*i+e[8]*r,u=e[1]*o+e[5]*i+e[9]*r,d=e[2]*o+e[6]*i+e[10]*r;return t.x=a,t.y=u,t.z=d,t};S.multiplyByPoint=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=e[0]*o+e[4]*i+e[8]*r+e[12],u=e[1]*o+e[5]*i+e[9]*r+e[13],d=e[2]*o+e[6]*i+e[10]*r+e[14];return t.x=a,t.y=u,t.z=d,t};S.multiplyByScalar=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7]*n,t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11]*n,t[12]=e[12]*n,t[13]=e[13]*n,t[14]=e[14]*n,t[15]=e[15]*n,t};S.negate=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n[4]=-e[4],n[5]=-e[5],n[6]=-e[6],n[7]=-e[7],n[8]=-e[8],n[9]=-e[9],n[10]=-e[10],n[11]=-e[11],n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=-e[15],n};S.transpose=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[1],o=e[2],i=e[3],r=e[6],a=e[7],u=e[11];return n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=t,n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=o,n[9]=r,n[10]=e[10],n[11]=e[14],n[12]=i,n[13]=a,n[14]=u,n[15]=e[15],n};S.abs=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n[4]=Math.abs(e[4]),n[5]=Math.abs(e[5]),n[6]=Math.abs(e[6]),n[7]=Math.abs(e[7]),n[8]=Math.abs(e[8]),n[9]=Math.abs(e[9]),n[10]=Math.abs(e[10]),n[11]=Math.abs(e[11]),n[12]=Math.abs(e[12]),n[13]=Math.abs(e[13]),n[14]=Math.abs(e[14]),n[15]=Math.abs(e[15]),n};S.equals=function(e,n){return e===n||p(e)&&p(n)&&e[12]===n[12]&&e[13]===n[13]&&e[14]===n[14]&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[4]===n[4]&&e[5]===n[5]&&e[6]===n[6]&&e[8]===n[8]&&e[9]===n[9]&&e[10]===n[10]&&e[3]===n[3]&&e[7]===n[7]&&e[11]===n[11]&&e[15]===n[15]};S.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(e[0]-n[0])<=t&&Math.abs(e[1]-n[1])<=t&&Math.abs(e[2]-n[2])<=t&&Math.abs(e[3]-n[3])<=t&&Math.abs(e[4]-n[4])<=t&&Math.abs(e[5]-n[5])<=t&&Math.abs(e[6]-n[6])<=t&&Math.abs(e[7]-n[7])<=t&&Math.abs(e[8]-n[8])<=t&&Math.abs(e[9]-n[9])<=t&&Math.abs(e[10]-n[10])<=t&&Math.abs(e[11]-n[11])<=t&&Math.abs(e[12]-n[12])<=t&&Math.abs(e[13]-n[13])<=t&&Math.abs(e[14]-n[14])<=t&&Math.abs(e[15]-n[15])<=t};S.getTranslation=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n.x=e[12],n.y=e[13],n.z=e[14],n};S.getMatrix3=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[4],n[4]=e[5],n[5]=e[6],n[6]=e[8],n[7]=e[9],n[8]=e[10],n};var Lo=new Q,xo=new Q,Bo=new qe,Qo=new qe(0,0,0,1);S.inverse=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[0],o=e[4],i=e[8],r=e[12],a=e[1],u=e[5],d=e[9],m=e[13],l=e[2],w=e[6],T=e[10],C=e[14],P=e[3],A=e[7],q=e[11],k=e[15],L=T*k,F=C*q,B=w*k,W=C*A,H=w*q,ee=T*A,re=l*k,Z=C*P,oe=l*q,J=T*P,c=l*A,f=w*P,h=L*u+W*d+H*m-(F*u+B*d+ee*m),y=F*a+re*d+J*m-(L*a+Z*d+oe*m),g=B*a+Z*u+c*m-(W*a+re*u+f*m),v=ee*a+oe*u+f*d-(H*a+J*u+c*d),b=F*o+B*i+ee*r-(L*o+W*i+H*r),I=L*t+Z*i+oe*r-(F*t+re*i+J*r),N=W*t+re*o+f*r-(B*t+Z*o+c*r),Y=H*t+J*o+c*i-(ee*t+oe*o+f*i);L=i*m,F=r*d,B=o*m,W=r*u,H=o*d,ee=i*u,re=t*m,Z=r*a,oe=t*d,J=i*a,c=t*u,f=o*a;let ie=L*A+W*q+H*k-(F*A+B*q+ee*k),ae=F*P+re*q+J*k-(L*P+Z*q+oe*k),ne=B*P+Z*A+c*k-(W*P+re*A+f*k),ue=ee*P+oe*A+f*q-(H*P+J*A+c*q),de=B*T+ee*C+F*w-(H*C+L*w+W*T),ye=oe*C+L*l+Z*T-(re*T+J*C+F*l),me=re*w+f*C+W*l-(c*C+B*l+Z*w),he=c*T+H*l+J*w-(oe*w+f*T+ee*l),X=t*h+o*y+i*g+r*v;if(Math.abs(X)<E.EPSILON21){if(Q.equalsEpsilon(S.getMatrix3(e,Lo),xo,E.EPSILON7)&&qe.equals(S.getRow(e,3,Bo),Qo))return n[0]=0,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=0,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=0,n[11]=0,n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=1,n;throw new Se("matrix is not invertible because its determinate is zero.")}return X=1/X,n[0]=h*X,n[1]=y*X,n[2]=g*X,n[3]=v*X,n[4]=b*X,n[5]=I*X,n[6]=N*X,n[7]=Y*X,n[8]=ie*X,n[9]=ae*X,n[10]=ne*X,n[11]=ue*X,n[12]=de*X,n[13]=ye*X,n[14]=me*X,n[15]=he*X,n};S.inverseTransformation=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[0],o=e[1],i=e[2],r=e[4],a=e[5],u=e[6],d=e[8],m=e[9],l=e[10],w=e[12],T=e[13],C=e[14],P=-t*w-o*T-i*C,A=-r*w-a*T-u*C,q=-d*w-m*T-l*C;return n[0]=t,n[1]=r,n[2]=d,n[3]=0,n[4]=o,n[5]=a,n[6]=m,n[7]=0,n[8]=i,n[9]=u,n[10]=l,n[11]=0,n[12]=P,n[13]=A,n[14]=q,n[15]=1,n};var Wo=new S;S.inverseTranspose=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),S.inverse(S.transpose(e,Wo),n)};S.IDENTITY=Object.freeze(new S(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1));S.ZERO=Object.freeze(new S(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0));S.COLUMN0ROW0=0;S.COLUMN0ROW1=1;S.COLUMN0ROW2=2;S.COLUMN0ROW3=3;S.COLUMN1ROW0=4;S.COLUMN1ROW1=5;S.COLUMN1ROW2=6;S.COLUMN1ROW3=7;S.COLUMN2ROW0=8;S.COLUMN2ROW1=9;S.COLUMN2ROW2=10;S.COLUMN2ROW3=11;S.COLUMN3ROW0=12;S.COLUMN3ROW1=13;S.COLUMN3ROW2=14;S.COLUMN3ROW3=15;Object.defineProperties(S.prototype,{length:{get:function(){return S.packedLength}}});S.prototype.clone=function(e){return S.clone(this,e)};S.prototype.equals=function(e){return S.equals(this,e)};S.equalsArray=function(e,n,t){return e[0]===n[t]&&e[1]===n[t+1]&&e[2]===n[t+2]&&e[3]===n[t+3]&&e[4]===n[t+4]&&e[5]===n[t+5]&&e[6]===n[t+6]&&e[7]===n[t+7]&&e[8]===n[t+8]&&e[9]===n[t+9]&&e[10]===n[t+10]&&e[11]===n[t+11]&&e[12]===n[t+12]&&e[13]===n[t+13]&&e[14]===n[t+14]&&e[15]===n[t+15]};S.prototype.equalsEpsilon=function(e,n){return S.equalsEpsilon(this,e,n)};S.prototype.toString=function(){return`(${this[0]}, ${this[4]}, ${this[8]}, ${this[12]})
(${this[1]}, ${this[5]}, ${this[9]}, ${this[13]})
(${this[2]}, ${this[6]}, ${this[10]}, ${this[14]})
(${this[3]}, ${this[7]}, ${this[11]}, ${this[15]})`};var G=S;function vt(e,n,t){t=O(t,!1);let o={},i=p(e),r=p(n),a,u,d;if(i)for(a in e)e.hasOwnProperty(a)&&(u=e[a],r&&t&&typeof u=="object"&&n.hasOwnProperty(a)?(d=n[a],typeof d=="object"?o[a]=vt(u,d,t):o[a]=u):o[a]=u);if(r)for(a in n)n.hasOwnProperty(a)&&!o.hasOwnProperty(a)&&(d=n[a],o[a]=d);return o}var De=vt;function Ho(e,n,t){s.defined("array",e),s.defined("itemToFind",n),s.defined("comparator",t);let o=0,i=e.length-1,r,a;for(;o<=i;){if(r=~~((o+i)/2),a=t(e[r],n),a<0){o=r+1;continue}if(a>0){i=r-1;continue}return r}return~(i+1)}var Be=Ho;function $o(e,n,t,o,i){this.xPoleWander=e,this.yPoleWander=n,this.xPoleOffset=t,this.yPoleOffset=o,this.ut1MinusUtc=i}var hn=$o;function Vo(e){if(e===null||isNaN(e))throw new j("year is required and must be a number.");return e%4===0&&e%100!==0||e%400===0}var dn=Vo;var At=[31,28,31,30,31,30,31,31,30,31,30,31];function Yo(e,n,t,o,i,r,a,u){e=O(e,1),n=O(n,1),t=O(t,1),o=O(o,0),i=O(i,0),r=O(r,0),a=O(a,0),u=O(u,!1),A(),q(),this.year=e,this.month=n,this.day=t,this.hour=o,this.minute=i,this.second=r,this.millisecond=a,this.isLeapSecond=u;function A(){s.typeOf.number.greaterThanOrEquals("Year",e,1),s.typeOf.number.lessThanOrEquals("Year",e,9999),s.typeOf.number.greaterThanOrEquals("Month",n,1),s.typeOf.number.lessThanOrEquals("Month",n,12),s.typeOf.number.greaterThanOrEquals("Day",t,1),s.typeOf.number.lessThanOrEquals("Day",t,31),s.typeOf.number.greaterThanOrEquals("Hour",o,0),s.typeOf.number.lessThanOrEquals("Hour",o,23),s.typeOf.number.greaterThanOrEquals("Minute",i,0),s.typeOf.number.lessThanOrEquals("Minute",i,59),s.typeOf.bool("IsLeapSecond",u),s.typeOf.number.greaterThanOrEquals("Second",r,0),s.typeOf.number.lessThanOrEquals("Second",r,u?60:59),s.typeOf.number.greaterThanOrEquals("Millisecond",a,0),s.typeOf.number.lessThan("Millisecond",a,1e3)}function q(){let k=n===2&&dn(e)?At[n-1]+1:At[n-1];if(t>k)throw new j("Month and Day represents invalid date")}}var Tn=Yo;function Xo(e,n){this.julianDate=e,this.offset=n}var te=Xo;var Zo={SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:24000005e-1},ce=Object.freeze(Zo);var Jo={UTC:0,TAI:1},$=Object.freeze(Jo);var Mt=new Tn,Cn=[31,28,31,30,31,30,31,31,30,31,30,31],vn=29;function Kn(e,n){return z.compare(e.julianDate,n.julianDate)}var Ke=new te;function jn(e){Ke.julianDate=e;let n=z.leapSeconds,t=Be(n,Ke,Kn);t<0&&(t=~t),t>=n.length&&(t=n.length-1);let o=n[t].offset;t>0&&z.secondsDifference(n[t].julianDate,e)>o&&(t--,o=n[t].offset),z.addSeconds(e,o,e)}function jt(e,n){Ke.julianDate=e;let t=z.leapSeconds,o=Be(t,Ke,Kn);if(o<0&&(o=~o),o===0)return z.addSeconds(e,-t[0].offset,n);if(o>=t.length)return z.addSeconds(e,-t[o-1].offset,n);let i=z.secondsDifference(t[o].julianDate,e);if(i===0)return z.addSeconds(e,-t[o].offset,n);if(!(i<=1))return z.addSeconds(e,-t[--o].offset,n)}function Ne(e,n,t){let o=n/ce.SECONDS_PER_DAY|0;return e+=o,n-=ce.SECONDS_PER_DAY*o,n<0&&(e--,n+=ce.SECONDS_PER_DAY),t.dayNumber=e,t.secondsOfDay=n,t}function et(e,n,t,o,i,r,a){let u=(n-14)/12|0,d=e+4800+u,m=(1461*d/4|0)+(367*(n-2-12*u)/12|0)-(3*((d+100)/100|0)/4|0)+t-32075;o=o-12,o<0&&(o+=24);let l=r+(o*ce.SECONDS_PER_HOUR+i*ce.SECONDS_PER_MINUTE+a*ce.SECONDS_PER_MILLISECOND);return l>=43200&&(m-=1),[m,l]}var Go=/^(\d{4})$/,Ko=/^(\d{4})-(\d{2})$/,er=/^(\d{4})-?(\d{3})$/,nr=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,tr=/^(\d{4})-?(\d{2})-?(\d{2})$/,nt=/([Z+\-])?(\d{2})?:?(\d{2})?$/,or=/^(\d{2})(\.\d+)?/.source+nt.source,rr=/^(\d{2}):?(\d{2})(\.\d+)?/.source+nt.source,ir=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+nt.source,Re="Invalid ISO 8601 date.";function z(e,n,t){this.dayNumber=void 0,this.secondsOfDay=void 0,e=O(e,0),n=O(n,0),t=O(t,$.UTC);let o=e|0;n=n+(e-o)*ce.SECONDS_PER_DAY,Ne(o,n,this),t===$.UTC&&jn(this)}z.fromGregorianDate=function(e,n){if(!(e instanceof Tn))throw new j("date must be a valid GregorianDate.");let t=et(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return p(n)?(Ne(t[0],t[1],n),jn(n),n):new z(t[0],t[1],$.UTC)};z.fromDate=function(e,n){if(!(e instanceof Date)||isNaN(e.getTime()))throw new j("date must be a valid JavaScript Date.");let t=et(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return p(n)?(Ne(t[0],t[1],n),jn(n),n):new z(t[0],t[1],$.UTC)};z.fromIso8601=function(e,n){if(typeof e!="string")throw new j(Re);e=e.replace(",",".");let t=e.split("T"),o,i=1,r=1,a=0,u=0,d=0,m=0,l=t[0],w=t[1],T,C;if(!p(l))throw new j(Re);let P;if(t=l.match(tr),t!==null){if(P=l.split("-").length-1,P>0&&P!==2)throw new j(Re);o=+t[1],i=+t[2],r=+t[3]}else if(t=l.match(Ko),t!==null)o=+t[1],i=+t[2];else if(t=l.match(Go),t!==null)o=+t[1];else{let L;if(t=l.match(er),t!==null){if(o=+t[1],L=+t[2],C=dn(o),L<1||C&&L>366||!C&&L>365)throw new j(Re)}else if(t=l.match(nr),t!==null){o=+t[1];let F=+t[2],B=+t[3]||0;if(P=l.split("-").length-1,P>0&&(!p(t[3])&&P!==1||p(t[3])&&P!==2))throw new j(Re);let W=new Date(Date.UTC(o,0,4));L=F*7+B-W.getUTCDay()-3}else throw new j(Re);T=new Date(Date.UTC(o,0,1)),T.setUTCDate(L),i=T.getUTCMonth()+1,r=T.getUTCDate()}if(C=dn(o),i<1||i>12||r<1||(i!==2||!C)&&r>Cn[i-1]||C&&i===2&&r>vn)throw new j(Re);let A;if(p(w)){if(t=w.match(ir),t!==null){if(P=w.split(":").length-1,P>0&&P!==2&&P!==3)throw new j(Re);a=+t[1],u=+t[2],d=+t[3],m=+(t[4]||0)*1e3,A=5}else if(t=w.match(rr),t!==null){if(P=w.split(":").length-1,P>2)throw new j(Re);a=+t[1],u=+t[2],d=+(t[3]||0)*60,A=4}else if(t=w.match(or),t!==null)a=+t[1],u=+(t[2]||0)*60,A=3;else throw new j(Re);if(u>=60||d>=61||a>24||a===24&&(u>0||d>0||m>0))throw new j(Re);let L=t[A],F=+t[A+1],B=+(t[A+2]||0);switch(L){case"+":a=a-F,u=u-B;break;case"-":a=a+F,u=u+B;break;case"Z":break;default:u=u+new Date(Date.UTC(o,i-1,r,a,u)).getTimezoneOffset();break}}let q=d===60;for(q&&d--;u>=60;)u-=60,a++;for(;a>=24;)a-=24,r++;for(T=C&&i===2?vn:Cn[i-1];r>T;)r-=T,i++,i>12&&(i-=12,o++),T=C&&i===2?vn:Cn[i-1];for(;u<0;)u+=60,a--;for(;a<0;)a+=24,r--;for(;r<1;)i--,i<1&&(i+=12,o--),T=C&&i===2?vn:Cn[i-1],r+=T;let k=et(o,i,r,a,u,d,m);return p(n)?(Ne(k[0],k[1],n),jn(n)):n=new z(k[0],k[1],$.UTC),q&&z.addSeconds(n,1,n),n};z.now=function(e){return z.fromDate(new Date,e)};var An=new z(0,0,$.TAI);z.toGregorianDate=function(e,n){if(!p(e))throw new j("julianDate is required.");let t=!1,o=jt(e,An);p(o)||(z.addSeconds(e,-1,An),o=jt(An,An),t=!0);let i=o.dayNumber,r=o.secondsOfDay;r>=43200&&(i+=1);let a=i+68569|0,u=4*a/146097|0;a=a-((146097*u+3)/4|0)|0;let d=4e3*(a+1)/1461001|0;a=a-(1461*d/4|0)+31|0;let m=80*a/2447|0,l=a-(2447*m/80|0)|0;a=m/11|0;let w=m+2-12*a|0,T=100*(u-49)+d+a|0,C=r/ce.SECONDS_PER_HOUR|0,P=r-C*ce.SECONDS_PER_HOUR,A=P/ce.SECONDS_PER_MINUTE|0;P=P-A*ce.SECONDS_PER_MINUTE;let q=P|0,k=(P-q)/ce.SECONDS_PER_MILLISECOND;return C+=12,C>23&&(C-=24),t&&(q+=1),p(n)?(n.year=T,n.month=w,n.day=l,n.hour=C,n.minute=A,n.second=q,n.millisecond=k,n.isLeapSecond=t,n):new Tn(T,w,l,C,A,q,k,t)};z.toDate=function(e){if(!p(e))throw new j("julianDate is required.");let n=z.toGregorianDate(e,Mt),t=n.second;return n.isLeapSecond&&(t-=1),new Date(Date.UTC(n.year,n.month-1,n.day,n.hour,n.minute,t,n.millisecond))};z.toIso8601=function(e,n){if(!p(e))throw new j("julianDate is required.");let t=z.toGregorianDate(e,Mt),o=t.year,i=t.month,r=t.day,a=t.hour,u=t.minute,d=t.second,m=t.millisecond;o===1e4&&i===1&&r===1&&a===0&&u===0&&d===0&&m===0&&(o=9999,i=12,r=31,a=24);let l;if(!p(n)&&m!==0){let w=m*.01;return l=w<1e-6?w.toFixed(20).replace(".","").replace(/0+$/,""):w.toString().replace(".",""),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}.${l}Z`}return!p(n)||n===0?`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}Z`:(l=(m*.01).toFixed(n).replace(".","").slice(0,n),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}.${l}Z`)};z.clone=function(e,n){if(p(e))return p(n)?(n.dayNumber=e.dayNumber,n.secondsOfDay=e.secondsOfDay,n):new z(e.dayNumber,e.secondsOfDay,$.TAI)};z.compare=function(e,n){if(!p(e))throw new j("left is required.");if(!p(n))throw new j("right is required.");let t=e.dayNumber-n.dayNumber;return t!==0?t:e.secondsOfDay-n.secondsOfDay};z.equals=function(e,n){return e===n||p(e)&&p(n)&&e.dayNumber===n.dayNumber&&e.secondsOfDay===n.secondsOfDay};z.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(z.secondsDifference(e,n))<=t};z.totalDays=function(e){if(!p(e))throw new j("julianDate is required.");return e.dayNumber+e.secondsOfDay/ce.SECONDS_PER_DAY};z.secondsDifference=function(e,n){if(!p(e))throw new j("left is required.");if(!p(n))throw new j("right is required.");return(e.dayNumber-n.dayNumber)*ce.SECONDS_PER_DAY+(e.secondsOfDay-n.secondsOfDay)};z.daysDifference=function(e,n){if(!p(e))throw new j("left is required.");if(!p(n))throw new j("right is required.");let t=e.dayNumber-n.dayNumber,o=(e.secondsOfDay-n.secondsOfDay)/ce.SECONDS_PER_DAY;return t+o};z.computeTaiMinusUtc=function(e){Ke.julianDate=e;let n=z.leapSeconds,t=Be(n,Ke,Kn);return t<0&&(t=~t,--t,t<0&&(t=0)),n[t].offset};z.addSeconds=function(e,n,t){if(!p(e))throw new j("julianDate is required.");if(!p(n))throw new j("seconds is required.");if(!p(t))throw new j("result is required.");return Ne(e.dayNumber,e.secondsOfDay+n,t)};z.addMinutes=function(e,n,t){if(!p(e))throw new j("julianDate is required.");if(!p(n))throw new j("minutes is required.");if(!p(t))throw new j("result is required.");let o=e.secondsOfDay+n*ce.SECONDS_PER_MINUTE;return Ne(e.dayNumber,o,t)};z.addHours=function(e,n,t){if(!p(e))throw new j("julianDate is required.");if(!p(n))throw new j("hours is required.");if(!p(t))throw new j("result is required.");let o=e.secondsOfDay+n*ce.SECONDS_PER_HOUR;return Ne(e.dayNumber,o,t)};z.addDays=function(e,n,t){if(!p(e))throw new j("julianDate is required.");if(!p(n))throw new j("days is required.");if(!p(t))throw new j("result is required.");let o=e.dayNumber+n;return Ne(o,e.secondsOfDay,t)};z.lessThan=function(e,n){return z.compare(e,n)<0};z.lessThanOrEquals=function(e,n){return z.compare(e,n)<=0};z.greaterThan=function(e,n){return z.compare(e,n)>0};z.greaterThanOrEquals=function(e,n){return z.compare(e,n)>=0};z.prototype.clone=function(e){return z.clone(this,e)};z.prototype.equals=function(e){return z.equals(this,e)};z.prototype.equalsEpsilon=function(e,n){return z.equalsEpsilon(this,e,n)};z.prototype.toString=function(){return z.toIso8601(this)};z.leapSeconds=[new te(new z(2441317,43210,$.TAI),10),new te(new z(2441499,43211,$.TAI),11),new te(new z(2441683,43212,$.TAI),12),new te(new z(2442048,43213,$.TAI),13),new te(new z(2442413,43214,$.TAI),14),new te(new z(2442778,43215,$.TAI),15),new te(new z(2443144,43216,$.TAI),16),new te(new z(2443509,43217,$.TAI),17),new te(new z(2443874,43218,$.TAI),18),new te(new z(2444239,43219,$.TAI),19),new te(new z(2444786,43220,$.TAI),20),new te(new z(2445151,43221,$.TAI),21),new te(new z(2445516,43222,$.TAI),22),new te(new z(2446247,43223,$.TAI),23),new te(new z(2447161,43224,$.TAI),24),new te(new z(2447892,43225,$.TAI),25),new te(new z(2448257,43226,$.TAI),26),new te(new z(2448804,43227,$.TAI),27),new te(new z(2449169,43228,$.TAI),28),new te(new z(2449534,43229,$.TAI),29),new te(new z(2450083,43230,$.TAI),30),new te(new z(2450630,43231,$.TAI),31),new te(new z(2451179,43232,$.TAI),32),new te(new z(2453736,43233,$.TAI),33),new te(new z(2454832,43234,$.TAI),34),new te(new z(2456109,43235,$.TAI),35),new te(new z(2457204,43236,$.TAI),36),new te(new z(2457754,43237,$.TAI),37)];var pe=z;var co=Ze(Qe(),1);function cr(e){return(e.length===0||e[e.length-1]!=="/")&&(e=`${e}/`),e}var Nt=cr;function kt(e,n){if(e===null||typeof e!="object")return e;n=O(n,!1);let t=new e.constructor;for(let o in e)if(e.hasOwnProperty(o)){let i=e[o];n&&(i=kt(i,n)),t[o]=i}return t}var tn=kt;function sr(){let e,n,t=new Promise(function(o,i){e=o,n=i});return{resolve:e,reject:n,promise:t}}var We=sr;var Ft=Ze(Qe(),1);function tt(e,n){let t;return typeof document<"u"&&(t=document),tt._implementation(e,n,t)}tt._implementation=function(e,n,t){if(!p(e))throw new j("relative uri is required.");if(!p(n)){if(typeof t>"u")return e;n=O(t.baseURI,t.location.href)}let o=new Ft.default(e);return o.scheme()!==""?o.toString():o.absoluteTo(n).toString()};var mn=tt;var Lt=Ze(Qe(),1);function ar(e,n){if(!p(e))throw new j("uri is required.");let t="",o=e.lastIndexOf("/");return o!==-1&&(t=e.substring(0,o+1)),n&&(e=new Lt.default(e),e.query().length!==0&&(t+=`?${e.query()}`),e.fragment().length!==0&&(t+=`#${e.fragment()}`)),t}var xt=ar;var Bt=Ze(Qe(),1);function fr(e){if(!p(e))throw new j("uri is required.");let n=new Bt.default(e);n.normalize();let t=n.path(),o=t.lastIndexOf("/");return o!==-1&&(t=t.substr(o+1)),o=t.lastIndexOf("."),o===-1?t="":t=t.substr(o+1),t}var Qt=fr;var Wt={};function ur(e,n,t){p(n)||(n=e.width),p(t)||(t=e.height);let o=Wt[n];p(o)||(o={},Wt[n]=o);let i=o[t];if(!p(i)){let r=document.createElement("canvas");r.width=n,r.height=t,i=r.getContext("2d",{willReadFrequently:!0}),i.globalCompositeOperation="copy",o[t]=i}return i.drawImage(e,0,0,n,t),i.getImageData(0,0,n,t).data}var ot=ur;var pr=/^blob:/i;function hr(e){return s.typeOf.string("uri",e),pr.test(e)}var zn=hr;var Ce;function dr(e){p(Ce)||(Ce=document.createElement("a")),Ce.href=window.location.href;let n=Ce.host,t=Ce.protocol;return Ce.href=e,Ce.href=Ce.href,t!==Ce.protocol||n!==Ce.host}var Ht=dr;var mr=/^data:/i;function yr(e){return s.typeOf.string("uri",e),mr.test(e)}var In=yr;function lr(e){let n=document.createElement("script");return n.async=!0,n.src=e,new Promise((t,o)=>{window.crossOriginIsolated&&n.setAttribute("crossorigin","anonymous");let i=document.getElementsByTagName("head")[0];n.onload=function(){n.onload=void 0,i.removeChild(n),t()},n.onerror=function(r){o(r)},i.appendChild(n)})}var $t=lr;function wr(e){if(!p(e))throw new j("obj is required.");let n="";for(let t in e)if(e.hasOwnProperty(t)){let o=e[t],i=`${encodeURIComponent(t)}=`;if(Array.isArray(o))for(let r=0,a=o.length;r<a;++r)n+=`${i+encodeURIComponent(o[r])}&`;else n+=`${i+encodeURIComponent(o)}&`}return n=n.slice(0,-1),n}var Vt=wr;function br(e){if(!p(e))throw new j("queryString is required.");let n={};if(e==="")return n;let t=e.replace(/\+/g,"%20").split(/[&;]/);for(let o=0,i=t.length;o<i;++o){let r=t[o].split("="),a=decodeURIComponent(r[0]),u=r[1];p(u)?u=decodeURIComponent(u):u="";let d=n[a];typeof d=="string"?n[a]=[d,u]:Array.isArray(d)?d.push(u):n[a]=u}return n}var Yt=br;var Or={UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5},fe=Object.freeze(Or);var gr={TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3},Xt=Object.freeze(gr);function qn(e){e=O(e,O.EMPTY_OBJECT);let n=O(e.throttleByServer,!1),t=O(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=O(e.priority,0),this.throttle=t,this.throttleByServer=n,this.type=O(e.type,Xt.OTHER),this.serverKey=e.serverKey,this.state=fe.UNISSUED,this.deferred=void 0,this.cancelled=!1}qn.prototype.cancel=function(){this.cancelled=!0};qn.prototype.clone=function(e){return p(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=fe.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new qn(this)};var Zt=qn;function _r(e){let n={};if(!e)return n;let t=e.split(`\r
`);for(let o=0;o<t.length;++o){let i=t[o],r=i.indexOf(": ");if(r>0){let a=i.substring(0,r),u=i.substring(r+2);n[a]=u}}return n}var Jt=_r;function Gt(e,n,t){this.statusCode=e,this.response=n,this.responseHeaders=t,typeof this.responseHeaders=="string"&&(this.responseHeaders=Jt(this.responseHeaders))}Gt.prototype.toString=function(){let e="Request has failed.";return p(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e};var yn=Gt;var Dn=Ze(Qe(),1);function ln(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}Object.defineProperties(ln.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}});ln.prototype.addEventListener=function(e,n){s.typeOf.func("listener",e),this._listeners.push(e),this._scopes.push(n);let t=this;return function(){t.removeEventListener(e,n)}};ln.prototype.removeEventListener=function(e,n){s.typeOf.func("listener",e);let t=this._listeners,o=this._scopes,i=-1;for(let r=0;r<t.length;r++)if(t[r]===e&&o[r]===n){i=r;break}return i!==-1?(this._insideRaiseEvent?(this._toRemove.push(i),t[i]=void 0,o[i]=void 0):(t.splice(i,1),o.splice(i,1)),!0):!1};function Sr(e,n){return n-e}ln.prototype.raiseEvent=function(){this._insideRaiseEvent=!0;let e,n=this._listeners,t=this._scopes,o=n.length;for(e=0;e<o;e++){let r=n[e];p(r)&&n[e].apply(t[e],arguments)}let i=this._toRemove;if(o=i.length,o>0){for(i.sort(Sr),e=0;e<o;e++){let r=i[e];n.splice(r,1),t.splice(r,1)}i.length=0}this._insideRaiseEvent=!1};var Kt=ln;function He(e){s.typeOf.object("options",e),s.defined("options.comparator",e.comparator),this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}Object.defineProperties(He.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){s.typeOf.number.greaterThanOrEquals("maximumLength",e,0);let n=this._length;if(e<n){let t=this._array;for(let o=e;o<n;++o)t[o]=void 0;this._length=e,t.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}});function rt(e,n,t){let o=e[n];e[n]=e[t],e[t]=o}He.prototype.reserve=function(e){e=O(e,this._length),this._array.length=e};He.prototype.heapify=function(e){e=O(e,0);let n=this._length,t=this._comparator,o=this._array,i=-1,r=!0;for(;r;){let a=2*(e+1),u=a-1;u<n&&t(o[u],o[e])<0?i=u:i=e,a<n&&t(o[a],o[i])<0&&(i=a),i!==e?(rt(o,i,e),e=i):r=!1}};He.prototype.resort=function(){let e=this._length;for(let n=Math.ceil(e/2);n>=0;--n)this.heapify(n)};He.prototype.insert=function(e){s.defined("element",e);let n=this._array,t=this._comparator,o=this._maximumLength,i=this._length++;for(i<n.length?n[i]=e:n.push(e);i!==0;){let a=Math.floor((i-1)/2);if(t(n[i],n[a])<0)rt(n,i,a),i=a;else break}let r;return p(o)&&this._length>o&&(r=n[o],this._length=o),r};He.prototype.pop=function(e){if(e=O(e,0),this._length===0)return;s.typeOf.number.lessThan("index",e,this._length);let n=this._array,t=n[e];return rt(n,e,--this._length),this.heapify(e),n[this._length]=void 0,t};var eo=He;function Rr(e,n){return e.priority-n.priority}var K={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0},on=20,we=new eo({comparator:Rr});we.maximumLength=on;we.reserve(on);var ve=[],Pe={},Er=typeof document<"u"?new Dn.default(document.location.href):new Dn.default,Nn=new Kt;function se(){}se.maximumRequests=50;se.maximumRequestsPerServer=18;se.requestsByServer={};se.throttleRequests=!0;se.debugShowStatistics=!1;se.requestCompletedEvent=Nn;Object.defineProperties(se,{statistics:{get:function(){return K}},priorityHeapLength:{get:function(){return on},set:function(e){if(e<on)for(;we.length>e;){let n=we.pop();$e(n)}on=e,we.maximumLength=e,we.reserve(e)}}});function no(e){p(e.priorityFunction)&&(e.priority=e.priorityFunction())}se.serverHasOpenSlots=function(e,n){n=O(n,1);let t=O(se.requestsByServer[e],se.maximumRequestsPerServer);return Pe[e]+n<=t};se.heapHasOpenSlots=function(e){return we.length+e<=on};function to(e){return e.state===fe.UNISSUED&&(e.state=fe.ISSUED,e.deferred=We()),e.deferred.promise}function Tr(e){return function(n){if(e.state===fe.CANCELLED)return;let t=e.deferred;--K.numberOfActiveRequests,--Pe[e.serverKey],Nn.raiseEvent(),e.state=fe.RECEIVED,e.deferred=void 0,t.resolve(n)}}function Cr(e){return function(n){e.state!==fe.CANCELLED&&(++K.numberOfFailedRequests,--K.numberOfActiveRequests,--Pe[e.serverKey],Nn.raiseEvent(n),e.state=fe.FAILED,e.deferred.reject(n))}}function oo(e){let n=to(e);return e.state=fe.ACTIVE,ve.push(e),++K.numberOfActiveRequests,++K.numberOfActiveRequestsEver,++Pe[e.serverKey],e.requestFunction().then(Tr(e)).catch(Cr(e)),n}function $e(e){let n=e.state===fe.ACTIVE;if(e.state=fe.CANCELLED,++K.numberOfCancelledRequests,p(e.deferred)){let t=e.deferred;e.deferred=void 0,t.reject()}n&&(--K.numberOfActiveRequests,--Pe[e.serverKey],++K.numberOfCancelledActiveRequests),p(e.cancelFunction)&&e.cancelFunction()}se.update=function(){let e,n,t=0,o=ve.length;for(e=0;e<o;++e){if(n=ve[e],n.cancelled&&$e(n),n.state!==fe.ACTIVE){++t;continue}t>0&&(ve[e-t]=n)}ve.length-=t;let i=we.internalArray,r=we.length;for(e=0;e<r;++e)no(i[e]);we.resort();let a=Math.max(se.maximumRequests-ve.length,0),u=0;for(;u<a&&we.length>0;){if(n=we.pop(),n.cancelled){$e(n);continue}if(n.throttleByServer&&!se.serverHasOpenSlots(n.serverKey)){$e(n);continue}oo(n),++u}vr()};se.getServerKey=function(e){s.typeOf.string("url",e);let n=new Dn.default(e);n.scheme()===""&&(n=n.absoluteTo(Er),n.normalize());let t=n.authority();/:/.test(t)||(t=`${t}:${n.scheme()==="https"?"443":"80"}`);let o=Pe[t];return p(o)||(Pe[t]=0),t};se.request=function(e){if(s.typeOf.object("request",e),s.typeOf.string("request.url",e.url),s.typeOf.func("request.requestFunction",e.requestFunction),In(e.url)||zn(e.url))return Nn.raiseEvent(),e.state=fe.RECEIVED,e.requestFunction();if(++K.numberOfAttemptedRequests,p(e.serverKey)||(e.serverKey=se.getServerKey(e.url)),se.throttleRequests&&e.throttleByServer&&!se.serverHasOpenSlots(e.serverKey))return;if(!se.throttleRequests||!e.throttle)return oo(e);if(ve.length>=se.maximumRequests)return;no(e);let n=we.insert(e);if(p(n)){if(n===e)return;$e(n)}return to(e)};function vr(){se.debugShowStatistics&&(K.numberOfActiveRequests===0&&K.lastNumberOfActiveRequests>0&&(K.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${K.numberOfAttemptedRequests}`),K.numberOfAttemptedRequests=0),K.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${K.numberOfCancelledRequests}`),K.numberOfCancelledRequests=0),K.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${K.numberOfCancelledActiveRequests}`),K.numberOfCancelledActiveRequests=0),K.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${K.numberOfFailedRequests}`),K.numberOfFailedRequests=0)),K.lastNumberOfActiveRequests=K.numberOfActiveRequests)}se.clearForSpecs=function(){for(;we.length>0;){let n=we.pop();$e(n)}let e=ve.length;for(let n=0;n<e;++n)$e(ve[n]);ve.length=0,Pe={},K.numberOfAttemptedRequests=0,K.numberOfActiveRequests=0,K.numberOfCancelledRequests=0,K.numberOfCancelledActiveRequests=0,K.numberOfFailedRequests=0,K.numberOfActiveRequestsEver=0,K.lastNumberOfActiveRequests=0};se.numberOfActiveRequestsByServer=function(e){return Pe[e]};se.requestHeap=we;var kn=se;var ro=Ze(Qe(),1);var wn={},rn={};wn.add=function(e,n){if(!p(e))throw new j("host is required.");if(!p(n)||n<=0)throw new j("port is required to be greater than 0.");let t=`${e.toLowerCase()}:${n}`;p(rn[t])||(rn[t]=!0)};wn.remove=function(e,n){if(!p(e))throw new j("host is required.");if(!p(n)||n<=0)throw new j("port is required to be greater than 0.");let t=`${e.toLowerCase()}:${n}`;p(rn[t])&&delete rn[t]};function Ar(e){let n=new ro.default(e);n.normalize();let t=n.authority();if(t.length!==0){if(n.authority(t),t.indexOf("@")!==-1&&(t=t.split("@")[1]),t.indexOf(":")===-1){let o=n.scheme();if(o.length===0&&(o=window.location.protocol,o=o.substring(0,o.length-1)),o==="http")t+=":80";else if(o==="https")t+=":443";else return}return t}}wn.contains=function(e){if(!p(e))throw new j("url is required.");let n=Ar(e);return!!(p(n)&&p(rn[n]))};wn.clear=function(){rn={}};var it=wn;var so=function(){try{let e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob",e.responseType==="blob"}catch{return!1}}();function U(e){e=O(e,O.EMPTY_OBJECT),typeof e=="string"&&(e={url:e}),s.typeOf.string("options.url",e.url),this._url=void 0,this._templateValues=Ae(e.templateValues,{}),this._queryParameters=Ae(e.queryParameters,{}),this.headers=Ae(e.headers,{}),this.request=O(e.request,new Zt),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=O(e.retryAttempts,0),this._retryCount=0,O(e.parseUrl,!0)?this.parseUrl(e.url,!0,!0):this._url=e.url,this._credits=e.credits}function Ae(e,n){return p(e)?tn(e):n}U.createIfNeeded=function(e){return e instanceof U?e.getDerivedResource({request:e.request}):typeof e!="string"?e:new U({url:e})};var cn;U.supportsImageBitmapOptions=function(){return p(cn)?cn:typeof createImageBitmap!="function"?(cn=Promise.resolve(!1),cn):(cn=U.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC"}).then(function(n){let t={imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"};return Promise.all([createImageBitmap(n,t),createImageBitmap(n)])}).then(function(n){let t=ot(n[0]),o=ot(n[1]);return t[1]!==o[1]}).catch(function(){return!1}),cn)};Object.defineProperties(U,{isBlobSupported:{get:function(){return so}}});Object.defineProperties(U.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){this.parseUrl(e,!1,!1)}},extension:{get:function(){return Qt(this._url)}},isDataUri:{get:function(){return In(this._url)}},isBlobUri:{get:function(){return zn(this._url)}},isCrossOriginUrl:{get:function(){return Ht(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}},credits:{get:function(){return this._credits}}});U.prototype.toString=function(){return this.getUrlComponent(!0,!0)};U.prototype.parseUrl=function(e,n,t,o){let i=new co.default(e),r=jr(i.query());this._queryParameters=n?Ln(r,this.queryParameters,t):r,i.search(""),i.fragment(""),p(o)&&i.scheme()===""&&(i=i.absoluteTo(mn(o))),this._url=i.toString()};function jr(e){return e.length===0?{}:e.indexOf("=")===-1?{[e]:void 0}:Yt(e)}function Ln(e,n,t){if(!t)return De(e,n);let o=tn(e,!0);for(let i in n)if(n.hasOwnProperty(i)){let r=o[i],a=n[i];p(r)?(Array.isArray(r)||(r=o[i]=[r]),o[i]=r.concat(a)):o[i]=Array.isArray(a)?a.slice():a}return o}U.prototype.getUrlComponent=function(e,n){if(this.isDataUri)return this._url;let t=this._url;e&&(t=`${t}${Mr(this.queryParameters)}`),t=t.replace(/%7B/g,"{").replace(/%7D/g,"}");let o=this._templateValues;return Object.keys(o).length>0&&(t=t.replace(/{(.*?)}/g,function(i,r){let a=o[r];return p(a)?encodeURIComponent(a):i})),n&&p(this.proxy)&&(t=this.proxy.getURL(t)),t};function Mr(e){let n=Object.keys(e);return n.length===0?"":n.length===1&&!p(e[n[0]])?`?${n[0]}`:`?${Vt(e)}`}U.prototype.setQueryParameters=function(e,n){n?this._queryParameters=Ln(this._queryParameters,e,!1):this._queryParameters=Ln(e,this._queryParameters,!1)};U.prototype.appendQueryParameters=function(e){this._queryParameters=Ln(e,this._queryParameters,!0)};U.prototype.setTemplateValues=function(e,n){n?this._templateValues=De(this._templateValues,e):this._templateValues=De(e,this._templateValues)};U.prototype.getDerivedResource=function(e){let n=this.clone();if(n._retryCount=0,p(e.url)){let t=O(e.preserveQueryParameters,!1);n.parseUrl(e.url,!0,t,this._url)}return p(e.queryParameters)&&(n._queryParameters=De(e.queryParameters,n.queryParameters)),p(e.templateValues)&&(n._templateValues=De(e.templateValues,n.templateValues)),p(e.headers)&&(n.headers=De(e.headers,n.headers)),p(e.proxy)&&(n.proxy=e.proxy),p(e.request)&&(n.request=e.request),p(e.retryCallback)&&(n.retryCallback=e.retryCallback),p(e.retryAttempts)&&(n.retryAttempts=e.retryAttempts),n};U.prototype.retryOnError=function(e){let n=this.retryCallback;if(typeof n!="function"||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);let t=this;return Promise.resolve(n(this,e)).then(function(o){return++t._retryCount,o})};U.prototype.clone=function(e){return p(e)?(e._url=this._url,e._queryParameters=tn(this._queryParameters),e._templateValues=tn(this._templateValues),e.headers=tn(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e):new U({url:this._url,queryParameters:this.queryParameters,templateValues:this.templateValues,headers:this.headers,proxy:this.proxy,retryCallback:this.retryCallback,retryAttempts:this.retryAttempts,request:this.request.clone(),parseUrl:!1,credits:p(this.credits)?this.credits.slice():void 0})};U.prototype.getBaseUri=function(e){return xt(this.getUrlComponent(e),e)};U.prototype.appendForwardSlash=function(){this._url=Nt(this._url)};U.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})};U.fetchArrayBuffer=function(e){return new U(e).fetchArrayBuffer()};U.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})};U.fetchBlob=function(e){return new U(e).fetchBlob()};U.prototype.fetchImage=function(e){e=O(e,O.EMPTY_OBJECT);let n=O(e.preferImageBitmap,!1),t=O(e.preferBlob,!1),o=O(e.flipY,!1),i=O(e.skipColorSpaceConversion,!1);if(st(this.request),!so||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!t)return ct({resource:this,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:n});let r=this.fetchBlob();if(!p(r))return;let a,u,d,m;return U.supportsImageBitmapOptions().then(function(l){return a=l,u=a&&n,r}).then(function(l){if(!p(l))return;if(m=l,u)return U.createImageBitmapFromBlob(l,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i});let w=window.URL.createObjectURL(l);return d=new U({url:w}),ct({resource:d,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:!1})}).then(function(l){if(p(l))return l.blob=m,u||window.URL.revokeObjectURL(d.url),l}).catch(function(l){return p(d)&&window.URL.revokeObjectURL(d.url),l.blob=m,Promise.reject(l)})};function ct(e){let n=e.resource,t=e.flipY,o=e.skipColorSpaceConversion,i=e.preferImageBitmap,r=n.request;r.url=n.url,r.requestFunction=function(){let u=!1;!n.isDataUri&&!n.isBlobUri&&(u=n.isCrossOriginUrl);let d=We();return U._Implementations.createImage(r,u,d,t,o,i),d.promise};let a=kn.request(r);if(p(a))return a.catch(function(u){return r.state!==fe.FAILED?Promise.reject(u):n.retryOnError(u).then(function(d){return d?(r.state=fe.UNISSUED,r.deferred=void 0,ct({resource:n,flipY:t,skipColorSpaceConversion:o,preferImageBitmap:i})):Promise.reject(u)})})}U.fetchImage=function(e){return new U(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})};U.prototype.fetchText=function(){return this.fetch({responseType:"text"})};U.fetchText=function(e){return new U(e).fetchText()};U.prototype.fetchJson=function(){let e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(p(e))return e.then(function(n){if(p(n))return JSON.parse(n)})};U.fetchJson=function(e){return new U(e).fetchJson()};U.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})};U.fetchXML=function(e){return new U(e).fetchXML()};U.prototype.fetchJsonp=function(e){e=O(e,"callback"),st(this.request);let n;do n=`loadJsonp${E.nextRandomNumber().toString().substring(2,8)}`;while(p(window[n]));return ao(this,e,n)};function ao(e,n,t){let o={};o[n]=t,e.setQueryParameters(o);let i=e.request,r=e.url;i.url=r,i.requestFunction=function(){let u=We();return window[t]=function(d){u.resolve(d);try{delete window[t]}catch{window[t]=void 0}},U._Implementations.loadAndExecuteScript(r,t,u),u.promise};let a=kn.request(i);if(p(a))return a.catch(function(u){return i.state!==fe.FAILED?Promise.reject(u):e.retryOnError(u).then(function(d){return d?(i.state=fe.UNISSUED,i.deferred=void 0,ao(e,n,t)):Promise.reject(u)})})}U.fetchJsonp=function(e){return new U(e).fetchJsonp(e.callbackParameterName)};U.prototype._makeRequest=function(e){let n=this;st(n.request);let t=n.request,o=n.url;t.url=o,t.requestFunction=function(){let r=e.responseType,a=De(e.headers,n.headers),u=e.overrideMimeType,d=e.method,m=e.data,l=We(),w=U._Implementations.loadWithXhr(o,r,d,m,a,l,u);return p(w)&&p(w.abort)&&(t.cancelFunction=function(){w.abort()}),l.promise};let i=kn.request(t);if(p(i))return i.then(function(r){return t.cancelFunction=void 0,r}).catch(function(r){return t.cancelFunction=void 0,t.state!==fe.FAILED?Promise.reject(r):n.retryOnError(r).then(function(a){return a?(t.state=fe.UNISSUED,t.deferred=void 0,n.fetch(e)):Promise.reject(r)})})};function st(e){if(e.state===fe.ISSUED||e.state===fe.ACTIVE)throw new Se("The Resource is already being fetched.");e.state=fe.UNISSUED,e.deferred=void 0}var Pr=/^data:(.*?)(;base64)?,(.*)$/;function Fn(e,n){let t=decodeURIComponent(n);return e?atob(t):t}function io(e,n){let t=Fn(e,n),o=new ArrayBuffer(t.length),i=new Uint8Array(o);for(let r=0;r<t.length;r++)i[r]=t.charCodeAt(r);return o}function Ur(e,n){n=O(n,"");let t=e[1],o=!!e[2],i=e[3],r,a;switch(n){case"":case"text":return Fn(o,i);case"arraybuffer":return io(o,i);case"blob":return r=io(o,i),new Blob([r],{type:t});case"document":return a=new DOMParser,a.parseFromString(Fn(o,i),t);case"json":return JSON.parse(Fn(o,i));default:throw new j(`Unhandled responseType: ${n}`)}}U.prototype.fetch=function(e){return e=Ae(e,{}),e.method="GET",this._makeRequest(e)};U.fetch=function(e){return new U(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.delete=function(e){return e=Ae(e,{}),e.method="DELETE",this._makeRequest(e)};U.delete=function(e){return new U(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})};U.prototype.head=function(e){return e=Ae(e,{}),e.method="HEAD",this._makeRequest(e)};U.head=function(e){return new U(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.options=function(e){return e=Ae(e,{}),e.method="OPTIONS",this._makeRequest(e)};U.options=function(e){return new U(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.post=function(e,n){return s.defined("data",e),n=Ae(n,{}),n.method="POST",n.data=e,this._makeRequest(n)};U.post=function(e){return new U(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.put=function(e,n){return s.defined("data",e),n=Ae(n,{}),n.method="PUT",n.data=e,this._makeRequest(n)};U.put=function(e){return new U(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U.prototype.patch=function(e,n){return s.defined("data",e),n=Ae(n,{}),n.method="PATCH",n.data=e,this._makeRequest(n)};U.patch=function(e){return new U(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};U._Implementations={};U._Implementations.loadImageElement=function(e,n,t){let o=new Image;o.onload=function(){o.naturalWidth===0&&o.naturalHeight===0&&o.width===0&&o.height===0&&(o.width=300,o.height=150),t.resolve(o)},o.onerror=function(i){t.reject(i)},n&&(it.contains(e)?o.crossOrigin="use-credentials":o.crossOrigin=""),o.src=e};U._Implementations.createImage=function(e,n,t,o,i,r){let a=e.url;U.supportsImageBitmapOptions().then(function(u){if(!(u&&r)){U._Implementations.loadImageElement(a,n,t);return}let d="blob",m="GET",l=We(),w=U._Implementations.loadWithXhr(a,d,m,void 0,void 0,l,void 0,void 0,void 0);return p(w)&&p(w.abort)&&(e.cancelFunction=function(){w.abort()}),l.promise.then(function(T){if(!p(T)){t.reject(new Se(`Successfully retrieved ${a} but it contained no content.`));return}return U.createImageBitmapFromBlob(T,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i})}).then(function(T){t.resolve(T)})}).catch(function(u){t.reject(u)})};U.createImageBitmapFromBlob=function(e,n){return s.defined("options",n),s.typeOf.bool("options.flipY",n.flipY),s.typeOf.bool("options.premultiplyAlpha",n.premultiplyAlpha),s.typeOf.bool("options.skipColorSpaceConversion",n.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:n.flipY?"flipY":"none",premultiplyAlpha:n.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:n.skipColorSpaceConversion?"none":"default"})};function zr(e,n,t,o,i,r,a){fetch(e,{method:t,headers:i}).then(async u=>{if(!u.ok){let d={};u.headers.forEach((m,l)=>{d[l]=m}),r.reject(new yn(u.status,u,d));return}switch(n){case"text":r.resolve(u.text());break;case"json":r.resolve(u.json());break;default:r.resolve(new Uint8Array(await u.arrayBuffer()).buffer);break}}).catch(()=>{r.reject(new yn)})}var Ir=typeof XMLHttpRequest>"u";U._Implementations.loadWithXhr=function(e,n,t,o,i,r,a){let u=Pr.exec(e);if(u!==null){r.resolve(Ur(u,n));return}if(Ir){zr(e,n,t,o,i,r,a);return}let d=new XMLHttpRequest;if(it.contains(e)&&(d.withCredentials=!0),d.open(t,e,!0),p(a)&&p(d.overrideMimeType)&&d.overrideMimeType(a),p(i))for(let l in i)i.hasOwnProperty(l)&&d.setRequestHeader(l,i[l]);p(n)&&(d.responseType=n);let m=!1;return typeof e=="string"&&(m=e.indexOf("file://")===0||typeof window<"u"&&window.location.origin==="file://"),d.onload=function(){if((d.status<200||d.status>=300)&&!(m&&d.status===0)){r.reject(new yn(d.status,d.response,d.getAllResponseHeaders()));return}let l=d.response,w=d.responseType;if(t==="HEAD"||t==="OPTIONS"){let C=d.getAllResponseHeaders().trim().split(/[\r\n]+/),P={};C.forEach(function(A){let q=A.split(": "),k=q.shift();P[k]=q.join(": ")}),r.resolve(P);return}if(d.status===204)r.resolve(void 0);else if(p(l)&&(!p(n)||w===n))r.resolve(l);else if(n==="json"&&typeof l=="string")try{r.resolve(JSON.parse(l))}catch(T){r.reject(T)}else(w===""||w==="document")&&p(d.responseXML)&&d.responseXML.hasChildNodes()?r.resolve(d.responseXML):(w===""||w==="text")&&p(d.responseText)?r.resolve(d.responseText):r.reject(new Se("Invalid XMLHttpRequest response type."))},d.onerror=function(l){r.reject(new yn)},d.send(o),d};U._Implementations.loadAndExecuteScript=function(e,n,t){return $t(e,n).catch(function(o){t.reject(o)})};U._DefaultImplementations={};U._DefaultImplementations.createImage=U._Implementations.createImage;U._DefaultImplementations.loadWithXhr=U._Implementations.loadWithXhr;U._DefaultImplementations.loadAndExecuteScript=U._Implementations.loadAndExecuteScript;U.DEFAULT=Object.freeze(new U({url:typeof document>"u"?"":document.location.href.split("?")[0]}));var ke=U;function On(e){e=O(e,O.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._addNewLeapSeconds=O(e.addNewLeapSeconds,!0),p(e.data)?fo(this,e.data):fo(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}On.fromUrl=async function(e,n){s.defined("url",e),n=O(n,O.EMPTY_OBJECT);let t=ke.createIfNeeded(e),o;try{o=await t.fetchJson()}catch{throw new Se(`An error occurred while retrieving the EOP data from the URL ${t.url}.`)}return new On({addNewLeapSeconds:n.addNewLeapSeconds,data:o})};On.NONE=Object.freeze({compute:function(e,n){return p(n)?(n.xPoleWander=0,n.yPoleWander=0,n.xPoleOffset=0,n.yPoleOffset=0,n.ut1MinusUtc=0):n=new hn(0,0,0,0,0),n}});On.prototype.compute=function(e,n){if(!p(this._samples))return;if(p(n)||(n=new hn(0,0,0,0,0)),this._samples.length===0)return n.xPoleWander=0,n.yPoleWander=0,n.xPoleOffset=0,n.yPoleOffset=0,n.ut1MinusUtc=0,n;let t=this._dates,o=this._lastIndex,i=0,r=0;if(p(o)){let u=t[o],d=t[o+1],m=pe.lessThanOrEquals(u,e),l=!p(d),w=l||pe.greaterThanOrEquals(d,e);if(m&&w)return i=o,!l&&d.equals(e)&&++i,r=i+1,po(this,t,this._samples,e,i,r,n),n}let a=Be(t,e,pe.compare,this._dateColumn);return a>=0?(a<t.length-1&&t[a+1].equals(e)&&++a,i=a,r=a):(r=~a,i=r-1,i<0&&(i=0)),this._lastIndex=i,po(this,t,this._samples,e,i,r,n),n};function qr(e,n){return pe.compare(e.julianDate,n)}function fo(e,n){if(!p(n.columnNames))throw new Se("Error in loaded EOP data: The columnNames property is required.");if(!p(n.samples))throw new Se("Error in loaded EOP data: The samples property is required.");let t=n.columnNames.indexOf("modifiedJulianDateUtc"),o=n.columnNames.indexOf("xPoleWanderRadians"),i=n.columnNames.indexOf("yPoleWanderRadians"),r=n.columnNames.indexOf("ut1MinusUtcSeconds"),a=n.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=n.columnNames.indexOf("yCelestialPoleOffsetRadians"),d=n.columnNames.indexOf("taiMinusUtcSeconds");if(t<0||o<0||i<0||r<0||a<0||u<0||d<0)throw new Se("Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");let m=e._samples=n.samples,l=e._dates=[];e._dateColumn=t,e._xPoleWanderRadiansColumn=o,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=r,e._xCelestialPoleOffsetRadiansColumn=a,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=d,e._columnCount=n.columnNames.length,e._lastIndex=void 0;let w,T=e._addNewLeapSeconds;for(let C=0,P=m.length;C<P;C+=e._columnCount){let A=m[C+t],q=m[C+d],k=A+ce.MODIFIED_JULIAN_DATE_DIFFERENCE,L=new pe(k,q,$.TAI);if(l.push(L),T){if(q!==w&&p(w)){let F=pe.leapSeconds,B=Be(F,L,qr);if(B<0){let W=new te(L,q);F.splice(~B,0,W)}}w=q}}}function uo(e,n,t,o,i){let r=t*o;i.xPoleWander=n[r+e._xPoleWanderRadiansColumn],i.yPoleWander=n[r+e._yPoleWanderRadiansColumn],i.xPoleOffset=n[r+e._xCelestialPoleOffsetRadiansColumn],i.yPoleOffset=n[r+e._yCelestialPoleOffsetRadiansColumn],i.ut1MinusUtc=n[r+e._ut1MinusUtcSecondsColumn]}function bn(e,n,t){return n+e*(t-n)}function po(e,n,t,o,i,r,a){let u=e._columnCount;if(r>n.length-1)return a.xPoleWander=0,a.yPoleWander=0,a.xPoleOffset=0,a.yPoleOffset=0,a.ut1MinusUtc=0,a;let d=n[i],m=n[r];if(d.equals(m)||o.equals(d))return uo(e,t,i,u,a),a;if(o.equals(m))return uo(e,t,r,u,a),a;let l=pe.secondsDifference(o,d)/pe.secondsDifference(m,d),w=i*u,T=r*u,C=t[w+e._ut1MinusUtcSecondsColumn],P=t[T+e._ut1MinusUtcSecondsColumn],A=P-C;if(A>.5||A<-.5){let q=t[w+e._taiMinusUtcSecondsColumn],k=t[T+e._taiMinusUtcSecondsColumn];q!==k&&(m.equals(o)?C=P:P-=k-q)}return a.xPoleWander=bn(l,t[w+e._xPoleWanderRadiansColumn],t[T+e._xPoleWanderRadiansColumn]),a.yPoleWander=bn(l,t[w+e._yPoleWanderRadiansColumn],t[T+e._yPoleWanderRadiansColumn]),a.xPoleOffset=bn(l,t[w+e._xCelestialPoleOffsetRadiansColumn],t[T+e._xCelestialPoleOffsetRadiansColumn]),a.yPoleOffset=bn(l,t[w+e._yCelestialPoleOffsetRadiansColumn],t[T+e._yCelestialPoleOffsetRadiansColumn]),a.ut1MinusUtc=bn(l,C,P),a}var ho=On;function be(e,n,t){this.heading=O(e,0),this.pitch=O(n,0),this.roll=O(t,0)}be.fromQuaternion=function(e,n){if(!p(e))throw new j("quaternion is required");p(n)||(n=new be);let t=2*(e.w*e.y-e.z*e.x),o=1-2*(e.x*e.x+e.y*e.y),i=2*(e.w*e.x+e.y*e.z),r=1-2*(e.y*e.y+e.z*e.z),a=2*(e.w*e.z+e.x*e.y);return n.heading=-Math.atan2(a,r),n.roll=Math.atan2(i,o),n.pitch=-E.asinClamped(t),n};be.fromDegrees=function(e,n,t,o){if(!p(e))throw new j("heading is required");if(!p(n))throw new j("pitch is required");if(!p(t))throw new j("roll is required");return p(o)||(o=new be),o.heading=e*E.RADIANS_PER_DEGREE,o.pitch=n*E.RADIANS_PER_DEGREE,o.roll=t*E.RADIANS_PER_DEGREE,o};be.clone=function(e,n){if(p(e))return p(n)?(n.heading=e.heading,n.pitch=e.pitch,n.roll=e.roll,n):new be(e.heading,e.pitch,e.roll)};be.equals=function(e,n){return e===n||p(e)&&p(n)&&e.heading===n.heading&&e.pitch===n.pitch&&e.roll===n.roll};be.equalsEpsilon=function(e,n,t,o){return e===n||p(e)&&p(n)&&E.equalsEpsilon(e.heading,n.heading,t,o)&&E.equalsEpsilon(e.pitch,n.pitch,t,o)&&E.equalsEpsilon(e.roll,n.roll,t,o)};be.prototype.clone=function(e){return be.clone(this,e)};be.prototype.equals=function(e){return be.equals(this,e)};be.prototype.equalsEpsilon=function(e,n,t){return be.equalsEpsilon(this,e,n,t)};be.prototype.toString=function(){return`(${this.heading}, ${this.pitch}, ${this.roll})`};var xn=be;var mo=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;function Dr(){let e=document.getElementsByTagName("script");for(let n=0,t=e.length;n<t;++n){let o=e[n].getAttribute("src"),i=mo.exec(o);if(i!==null)return i[1]}}var Bn;function yo(e){return typeof document>"u"?e:(p(Bn)||(Bn=document.createElement("a")),Bn.href=e,Bn.href)}var Ve;function lo(){if(p(Ve))return Ve;let e;if(typeof CESIUM_BASE_URL<"u"?e=CESIUM_BASE_URL:p(import.meta?.url)?e=mn(".",import.meta.url):typeof define=="object"&&p(define.amd)&&!define.amd.toUrlUndefined&&p(Sn.toUrl)?e=mn("..",Ye("Core/buildModuleUrl.js")):e=Dr(),!p(e))throw new j("Unable to determine Cesium base URL automatically, try defining a global variable called CESIUM_BASE_URL.");return Ve=new ke({url:yo(e)}),Ve.appendForwardSlash(),Ve}function Nr(e){return yo(Sn.toUrl(`../${e}`))}function wo(e){return lo().getDerivedResource({url:e}).url}var Qn;function Ye(e){return p(Qn)||(typeof define=="object"&&p(define.amd)&&!define.amd.toUrlUndefined&&p(Sn.toUrl)?Qn=Nr:Qn=wo),Qn(e)}Ye._cesiumScriptRegex=mo;Ye._buildModuleUrlFromBaseUrl=wo;Ye._clearBaseResource=function(){Ve=void 0};Ye.setBaseUrl=function(e){Ve=ke.DEFAULT.getDerivedResource({url:e})};Ye.getCesiumBaseUrl=lo;var bo=Ye;function kr(e,n,t){this.x=e,this.y=n,this.s=t}var Wn=kr;function ut(e){e=O(e,O.EMPTY_OBJECT),this._xysFileUrlTemplate=ke.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=O(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=O(e.sampleZeroJulianEphemerisDate,24423965e-1),this._sampleZeroDateTT=new pe(this._sampleZeroJulianEphemerisDate,0,$.TAI),this._stepSizeDays=O(e.stepSizeDays,1),this._samplesPerXysFile=O(e.samplesPerXysFile,1e3),this._totalSamples=O(e.totalSamples,27426),this._samples=new Array(this._totalSamples*3),this._chunkDownloadsInProgress=[];let n=this._interpolationOrder,t=this._denominators=new Array(n+1),o=this._xTable=new Array(n+1),i=Math.pow(this._stepSizeDays,n);for(let r=0;r<=n;++r){t[r]=i,o[r]=r*this._stepSizeDays;for(let a=0;a<=n;++a)a!==r&&(t[r]*=r-a);t[r]=1/t[r]}this._work=new Array(n+1),this._coef=new Array(n+1)}var Fr=new pe(0,0,$.TAI);function at(e,n,t){let o=Fr;return o.dayNumber=n,o.secondsOfDay=t,pe.daysDifference(o,e._sampleZeroDateTT)}ut.prototype.preload=function(e,n,t,o){let i=at(this,e,n),r=at(this,t,o),a=i/this._stepSizeDays-this._interpolationOrder/2|0;a<0&&(a=0);let u=r/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);let d=a/this._samplesPerXysFile|0,m=u/this._samplesPerXysFile|0,l=[];for(let w=d;w<=m;++w)l.push(ft(this,w));return Promise.all(l)};ut.prototype.computeXysRadians=function(e,n,t){let o=at(this,e,n);if(o<0)return;let i=o/this._stepSizeDays|0;if(i>=this._totalSamples)return;let r=this._interpolationOrder,a=i-(r/2|0);a<0&&(a=0);let u=a+r;u>=this._totalSamples&&(u=this._totalSamples-1,a=u-r,a<0&&(a=0));let d=!1,m=this._samples;if(p(m[a*3])||(ft(this,a/this._samplesPerXysFile|0),d=!0),p(m[u*3])||(ft(this,u/this._samplesPerXysFile|0),d=!0),d)return;p(t)?(t.x=0,t.y=0,t.s=0):t=new Wn(0,0,0);let l=o-a*this._stepSizeDays,w=this._work,T=this._denominators,C=this._coef,P=this._xTable,A,q;for(A=0;A<=r;++A)w[A]=l-P[A];for(A=0;A<=r;++A){for(C[A]=1,q=0;q<=r;++q)q!==A&&(C[A]*=w[q]);C[A]*=T[A];let k=(a+A)*3;t.x+=C[A]*m[k++],t.y+=C[A]*m[k++],t.s+=C[A]*m[k]}return t};function ft(e,n){if(e._chunkDownloadsInProgress[n])return e._chunkDownloadsInProgress[n];let t,o=e._xysFileUrlTemplate;p(o)?t=o.getDerivedResource({templateValues:{0:n}}):t=new ke({url:bo(`Assets/IAU2006_XYS/IAU2006_XYS_${n}.json`)});let i=t.fetchJson().then(function(r){e._chunkDownloadsInProgress[n]=!1;let a=e._samples,u=r.samples,d=n*e._samplesPerXysFile*3;for(let m=0,l=u.length;m<l;++m)a[d+m]=u[m]});return e._chunkDownloadsInProgress[n]=i,i}var Oo=ut;function R(e,n,t,o){this.x=O(e,0),this.y=O(n,0),this.z=O(t,0),this.w=O(o,0)}var gn=new _;R.fromAxisAngle=function(e,n,t){s.typeOf.object("axis",e),s.typeOf.number("angle",n);let o=n/2,i=Math.sin(o);gn=_.normalize(e,gn);let r=gn.x*i,a=gn.y*i,u=gn.z*i,d=Math.cos(o);return p(t)?(t.x=r,t.y=a,t.z=u,t.w=d,t):new R(r,a,u,d)};var Lr=[1,2,0],xr=new Array(3);R.fromRotationMatrix=function(e,n){s.typeOf.object("matrix",e);let t,o,i,r,a,u=e[Q.COLUMN0ROW0],d=e[Q.COLUMN1ROW1],m=e[Q.COLUMN2ROW2],l=u+d+m;if(l>0)t=Math.sqrt(l+1),a=.5*t,t=.5/t,o=(e[Q.COLUMN1ROW2]-e[Q.COLUMN2ROW1])*t,i=(e[Q.COLUMN2ROW0]-e[Q.COLUMN0ROW2])*t,r=(e[Q.COLUMN0ROW1]-e[Q.COLUMN1ROW0])*t;else{let w=Lr,T=0;d>u&&(T=1),m>u&&m>d&&(T=2);let C=w[T],P=w[C];t=Math.sqrt(e[Q.getElementIndex(T,T)]-e[Q.getElementIndex(C,C)]-e[Q.getElementIndex(P,P)]+1);let A=xr;A[T]=.5*t,t=.5/t,a=(e[Q.getElementIndex(P,C)]-e[Q.getElementIndex(C,P)])*t,A[C]=(e[Q.getElementIndex(C,T)]+e[Q.getElementIndex(T,C)])*t,A[P]=(e[Q.getElementIndex(P,T)]+e[Q.getElementIndex(T,P)])*t,o=-A[0],i=-A[1],r=-A[2]}return p(n)?(n.x=o,n.y=i,n.z=r,n.w=a,n):new R(o,i,r,a)};var go=new R,_o=new R,pt=new R,So=new R;R.fromHeadingPitchRoll=function(e,n){return s.typeOf.object("headingPitchRoll",e),So=R.fromAxisAngle(_.UNIT_X,e.roll,go),pt=R.fromAxisAngle(_.UNIT_Y,-e.pitch,n),n=R.multiply(pt,So,pt),_o=R.fromAxisAngle(_.UNIT_Z,-e.heading,go),R.multiply(_o,n,n)};var Hn=new _,ht=new _,Ee=new R,Ro=new R,$n=new R;R.packedLength=4;R.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e.x,n[t++]=e.y,n[t++]=e.z,n[t]=e.w,n};R.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new R),t.x=e[n],t.y=e[n+1],t.z=e[n+2],t.w=e[n+3],t};R.packedInterpolationLength=3;R.convertPackedArrayForInterpolation=function(e,n,t,o){R.unpack(e,t*4,$n),R.conjugate($n,$n);for(let i=0,r=t-n+1;i<r;i++){let a=i*3;R.unpack(e,(n+i)*4,Ee),R.multiply(Ee,$n,Ee),Ee.w<0&&R.negate(Ee,Ee),R.computeAxis(Ee,Hn);let u=R.computeAngle(Ee);p(o)||(o=[]),o[a]=Hn.x*u,o[a+1]=Hn.y*u,o[a+2]=Hn.z*u}};R.unpackInterpolationResult=function(e,n,t,o,i){p(i)||(i=new R),_.fromArray(e,0,ht);let r=_.magnitude(ht);return R.unpack(n,o*4,Ro),r===0?R.clone(R.IDENTITY,Ee):R.fromAxisAngle(ht,r,Ee),R.multiply(Ee,Ro,i)};R.clone=function(e,n){if(p(e))return p(n)?(n.x=e.x,n.y=e.y,n.z=e.z,n.w=e.w,n):new R(e.x,e.y,e.z,e.w)};R.conjugate=function(e,n){return s.typeOf.object("quaternion",e),s.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=e.w,n};R.magnitudeSquared=function(e){return s.typeOf.object("quaternion",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};R.magnitude=function(e){return Math.sqrt(R.magnitudeSquared(e))};R.normalize=function(e,n){s.typeOf.object("result",n);let t=1/R.magnitude(e),o=e.x*t,i=e.y*t,r=e.z*t,a=e.w*t;return n.x=o,n.y=i,n.z=r,n.w=a,n};R.inverse=function(e,n){s.typeOf.object("result",n);let t=R.magnitudeSquared(e);return n=R.conjugate(e,n),R.multiplyByScalar(n,1/t,n)};R.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x+n.x,t.y=e.y+n.y,t.z=e.z+n.z,t.w=e.w+n.w,t};R.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x-n.x,t.y=e.y-n.y,t.z=e.z-n.z,t.w=e.w-n.w,t};R.negate=function(e,n){return s.typeOf.object("quaternion",e),s.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=-e.w,n};R.dot=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),e.x*n.x+e.y*n.y+e.z*n.z+e.w*n.w};R.multiply=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e.x,i=e.y,r=e.z,a=e.w,u=n.x,d=n.y,m=n.z,l=n.w,w=a*u+o*l+i*m-r*d,T=a*d-o*m+i*l+r*u,C=a*m+o*d-i*u+r*l,P=a*l-o*u-i*d-r*m;return t.x=w,t.y=T,t.z=C,t.w=P,t};R.multiplyByScalar=function(e,n,t){return s.typeOf.object("quaternion",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x*n,t.y=e.y*n,t.z=e.z*n,t.w=e.w*n,t};R.divideByScalar=function(e,n,t){return s.typeOf.object("quaternion",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,t.w=e.w/n,t};R.computeAxis=function(e,n){s.typeOf.object("quaternion",e),s.typeOf.object("result",n);let t=e.w;if(Math.abs(t-1)<E.EPSILON6||Math.abs(t+1)<E.EPSILON6)return n.x=1,n.y=n.z=0,n;let o=1/Math.sqrt(1-t*t);return n.x=e.x*o,n.y=e.y*o,n.z=e.z*o,n};R.computeAngle=function(e){return s.typeOf.object("quaternion",e),Math.abs(e.w-1)<E.EPSILON6?0:2*Math.acos(e.w)};var dt=new R;R.lerp=function(e,n,t,o){return s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o),dt=R.multiplyByScalar(n,t,dt),o=R.multiplyByScalar(e,1-t,o),R.add(dt,o,o)};var Eo=new R,mt=new R,yt=new R;R.slerp=function(e,n,t,o){s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o);let i=R.dot(e,n),r=n;if(i<0&&(i=-i,r=Eo=R.negate(n,Eo)),1-i<E.EPSILON6)return R.lerp(e,r,t,o);let a=Math.acos(i);return mt=R.multiplyByScalar(e,Math.sin((1-t)*a),mt),yt=R.multiplyByScalar(r,Math.sin(t*a),yt),o=R.add(mt,yt,o),R.multiplyByScalar(o,1/Math.sin(a),o)};R.log=function(e,n){s.typeOf.object("quaternion",e),s.typeOf.object("result",n);let t=E.acosClamped(e.w),o=0;return t!==0&&(o=t/Math.sin(t)),_.multiplyByScalar(e,o,n)};R.exp=function(e,n){s.typeOf.object("cartesian",e),s.typeOf.object("result",n);let t=_.magnitude(e),o=0;return t!==0&&(o=Math.sin(t)/t),n.x=e.x*o,n.y=e.y*o,n.z=e.z*o,n.w=Math.cos(t),n};var Br=new _,Qr=new _,_n=new R,sn=new R;R.computeInnerQuadrangle=function(e,n,t,o){s.typeOf.object("q0",e),s.typeOf.object("q1",n),s.typeOf.object("q2",t),s.typeOf.object("result",o);let i=R.conjugate(n,_n);R.multiply(i,t,sn);let r=R.log(sn,Br);R.multiply(i,e,sn);let a=R.log(sn,Qr);return _.add(r,a,r),_.multiplyByScalar(r,.25,r),_.negate(r,r),R.exp(r,_n),R.multiply(n,_n,o)};R.squad=function(e,n,t,o,i,r){s.typeOf.object("q0",e),s.typeOf.object("q1",n),s.typeOf.object("s0",t),s.typeOf.object("s1",o),s.typeOf.number("t",i),s.typeOf.object("result",r);let a=R.slerp(e,n,i,_n),u=R.slerp(t,o,i,sn);return R.slerp(a,u,2*i*(1-i),r)};var Wr=new R,To=1.9011074535173003,Vn=un.supportsTypedArrays()?new Float32Array(8):[],Yn=un.supportsTypedArrays()?new Float32Array(8):[],Ue=un.supportsTypedArrays()?new Float32Array(8):[],ze=un.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){let n=e+1,t=2*n+1;Vn[e]=1/(n*t),Yn[e]=n/t}Vn[7]=To/(8*17);Yn[7]=To*8/17;R.fastSlerp=function(e,n,t,o){s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o);let i=R.dot(e,n),r;i>=0?r=1:(r=-1,i=-i);let a=i-1,u=1-t,d=t*t,m=u*u;for(let C=7;C>=0;--C)Ue[C]=(Vn[C]*d-Yn[C])*a,ze[C]=(Vn[C]*m-Yn[C])*a;let l=r*t*(1+Ue[0]*(1+Ue[1]*(1+Ue[2]*(1+Ue[3]*(1+Ue[4]*(1+Ue[5]*(1+Ue[6]*(1+Ue[7])))))))),w=u*(1+ze[0]*(1+ze[1]*(1+ze[2]*(1+ze[3]*(1+ze[4]*(1+ze[5]*(1+ze[6]*(1+ze[7])))))))),T=R.multiplyByScalar(e,w,Wr);return R.multiplyByScalar(n,l,o),R.add(T,o,o)};R.fastSquad=function(e,n,t,o,i,r){s.typeOf.object("q0",e),s.typeOf.object("q1",n),s.typeOf.object("s0",t),s.typeOf.object("s1",o),s.typeOf.number("t",i),s.typeOf.object("result",r);let a=R.fastSlerp(e,n,i,_n),u=R.fastSlerp(t,o,i,sn);return R.fastSlerp(a,u,2*i*(1-i),r)};R.equals=function(e,n){return e===n||p(e)&&p(n)&&e.x===n.x&&e.y===n.y&&e.z===n.z&&e.w===n.w};R.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(e.x-n.x)<=t&&Math.abs(e.y-n.y)<=t&&Math.abs(e.z-n.z)<=t&&Math.abs(e.w-n.w)<=t};R.ZERO=Object.freeze(new R(0,0,0,0));R.IDENTITY=Object.freeze(new R(0,0,0,1));R.prototype.clone=function(e){return R.clone(this,e)};R.prototype.equals=function(e){return R.equals(this,e)};R.prototype.equalsEpsilon=function(e,n){return R.equalsEpsilon(this,e,n)};R.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Xe=R;var V={},lt={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},an={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},wt={},ge={east:new _,north:new _,up:new _,west:new _,south:new _,down:new _},Fe=new _,Le=new _,xe=new _;V.localFrameToFixedFrameGenerator=function(e,n){if(!lt.hasOwnProperty(e)||!lt[e].hasOwnProperty(n))throw new j("firstAxis and secondAxis must be east, north, up, west, south or down.");let t=lt[e][n],o,i=e+n;return p(wt[i])?o=wt[i]:(o=function(r,a,u){if(!p(r))throw new j("origin is required.");if(isNaN(r.x)||isNaN(r.y)||isNaN(r.z))throw new j("origin has a NaN component");if(p(u)||(u=new G),_.equalsEpsilon(r,_.ZERO,E.EPSILON14))_.unpack(an[e],0,Fe),_.unpack(an[n],0,Le),_.unpack(an[t],0,xe);else if(E.equalsEpsilon(r.x,0,E.EPSILON14)&&E.equalsEpsilon(r.y,0,E.EPSILON14)){let d=E.sign(r.z);_.unpack(an[e],0,Fe),e!=="east"&&e!=="west"&&_.multiplyByScalar(Fe,d,Fe),_.unpack(an[n],0,Le),n!=="east"&&n!=="west"&&_.multiplyByScalar(Le,d,Le),_.unpack(an[t],0,xe),t!=="east"&&t!=="west"&&_.multiplyByScalar(xe,d,xe)}else{a=O(a,Ie.default),a.geodeticSurfaceNormal(r,ge.up);let d=ge.up,m=ge.east;m.x=-r.y,m.y=r.x,m.z=0,_.normalize(m,ge.east),_.cross(d,m,ge.north),_.multiplyByScalar(ge.up,-1,ge.down),_.multiplyByScalar(ge.east,-1,ge.west),_.multiplyByScalar(ge.north,-1,ge.south),Fe=ge[e],Le=ge[n],xe=ge[t]}return u[0]=Fe.x,u[1]=Fe.y,u[2]=Fe.z,u[3]=0,u[4]=Le.x,u[5]=Le.y,u[6]=Le.z,u[7]=0,u[8]=xe.x,u[9]=xe.y,u[10]=xe.z,u[11]=0,u[12]=r.x,u[13]=r.y,u[14]=r.z,u[15]=1,u},wt[i]=o),o};V.eastNorthUpToFixedFrame=V.localFrameToFixedFrameGenerator("east","north");V.northEastDownToFixedFrame=V.localFrameToFixedFrameGenerator("north","east");V.northUpEastToFixedFrame=V.localFrameToFixedFrameGenerator("north","up");V.northWestUpToFixedFrame=V.localFrameToFixedFrameGenerator("north","west");var Hr=new Xe,$r=new _(1,1,1),Vr=new G;V.headingPitchRollToFixedFrame=function(e,n,t,o,i){s.typeOf.object("HeadingPitchRoll",n),o=O(o,V.eastNorthUpToFixedFrame);let r=Xe.fromHeadingPitchRoll(n,Hr),a=G.fromTranslationQuaternionRotationScale(_.ZERO,r,$r,Vr);return i=o(e,t,i),G.multiply(i,a,i)};var Yr=new G,Xr=new Q;V.headingPitchRollQuaternion=function(e,n,t,o,i){s.typeOf.object("HeadingPitchRoll",n);let r=V.headingPitchRollToFixedFrame(e,n,t,o,Yr),a=G.getMatrix3(r,Xr);return Xe.fromRotationMatrix(a,i)};var Zr=new _(1,1,1),Jr=new _,Co=new G,Gr=new G,Kr=new Q,ei=new Xe;V.fixedFrameToHeadingPitchRoll=function(e,n,t,o){s.defined("transform",e),n=O(n,Ie.default),t=O(t,V.eastNorthUpToFixedFrame),p(o)||(o=new xn);let i=G.getTranslation(e,Jr);if(_.equals(i,_.ZERO))return o.heading=0,o.pitch=0,o.roll=0,o;let r=G.inverseTransformation(t(i,n,Co),Co),a=G.setScale(e,Zr,Gr);a=G.setTranslation(a,_.ZERO,a),r=G.multiply(r,a,r);let u=Xe.fromRotationMatrix(G.getMatrix3(r,Kr),ei);return u=Xe.normalize(u,u),xn.fromQuaternion(u,o)};var ni=6*3600+41*60+50.54841,ti=8640184812866e-6,oi=.093104,ri=-62e-7,ii=11772758384668e-32,ci=72921158553e-15,si=E.TWO_PI/86400,Xn=new pe;V.computeIcrfToCentralBodyFixedMatrix=function(e,n){let t=V.computeIcrfToFixedMatrix(e,n);return p(t)||(t=V.computeTemeToPseudoFixedMatrix(e,n)),t};V.computeTemeToPseudoFixedMatrix=function(e,n){if(!p(e))throw new j("date is required.");Xn=pe.addSeconds(e,-pe.computeTaiMinusUtc(e),Xn);let t=Xn.dayNumber,o=Xn.secondsOfDay,i,r=t-2451545;o>=43200?i=(r+.5)/ce.DAYS_PER_JULIAN_CENTURY:i=(r-.5)/ce.DAYS_PER_JULIAN_CENTURY;let u=(ni+i*(ti+i*(oi+i*ri)))*si%E.TWO_PI,d=ci+ii*(t-24515455e-1),m=(o+ce.SECONDS_PER_DAY*.5)%ce.SECONDS_PER_DAY,l=u+d*m,w=Math.cos(l),T=Math.sin(l);return p(n)?(n[0]=w,n[1]=-T,n[2]=0,n[3]=T,n[4]=w,n[5]=0,n[6]=0,n[7]=0,n[8]=1,n):new Q(w,T,0,-T,w,0,0,0,1)};V.iau2006XysData=new Oo;V.earthOrientationParameters=ho.NONE;var gt=32.184,ai=2451545;V.preloadIcrfFixed=function(e){let n=e.start.dayNumber,t=e.start.secondsOfDay+gt,o=e.stop.dayNumber,i=e.stop.secondsOfDay+gt;return V.iau2006XysData.preload(n,t,o,i)};V.computeIcrfToFixedMatrix=function(e,n){if(!p(e))throw new j("date is required.");p(n)||(n=new Q);let t=V.computeFixedToIcrfMatrix(e,n);if(p(t))return Q.transpose(t,n)};var fi=32.184,ui=2451545,Zn=new xn,pi=new Q,hi=new pe;V.computeMoonFixedToIcrfMatrix=function(e,n){if(!p(e))throw new j("date is required.");p(n)||(n=new Q);let t=pe.addSeconds(e,fi,hi),o=pe.totalDays(t)-ui,i=E.toRadians(12.112)-E.toRadians(.052992)*o,r=E.toRadians(24.224)-E.toRadians(.105984)*o,a=E.toRadians(227.645)+E.toRadians(13.012)*o,u=E.toRadians(261.105)+E.toRadians(13.340716)*o,d=E.toRadians(358)+E.toRadians(.9856)*o;return Zn.pitch=E.toRadians(180)-E.toRadians(3.878)*Math.sin(i)-E.toRadians(.12)*Math.sin(r)+E.toRadians(.07)*Math.sin(a)-E.toRadians(.017)*Math.sin(u),Zn.roll=E.toRadians(66.53-90)+E.toRadians(1.543)*Math.cos(i)+E.toRadians(.24)*Math.cos(r)-E.toRadians(.028)*Math.cos(a)+E.toRadians(.007)*Math.cos(u),Zn.heading=E.toRadians(244.375-90)+E.toRadians(13.17635831)*o+E.toRadians(3.558)*Math.sin(i)+E.toRadians(.121)*Math.sin(r)-E.toRadians(.064)*Math.sin(a)+E.toRadians(.016)*Math.sin(u)+E.toRadians(.025)*Math.sin(d),Q.fromHeadingPitchRoll(Zn,pi)};V.computeIcrfToMoonFixedMatrix=function(e,n){if(!p(e))throw new j("date is required.");p(n)||(n=new Q);let t=V.computeMoonFixedToIcrfMatrix(e,n);if(p(t))return Q.transpose(t,n)};var di=new Wn(0,0,0),mi=new hn(0,0,0,0,0,0),bt=new Q,Ot=new Q;V.computeFixedToIcrfMatrix=function(e,n){if(!p(e))throw new j("date is required.");p(n)||(n=new Q);let t=V.earthOrientationParameters.compute(e,mi);if(!p(t))return;let o=e.dayNumber,i=e.secondsOfDay+gt,r=V.iau2006XysData.computeXysRadians(o,i,di);if(!p(r))return;let a=r.x+t.xPoleOffset,u=r.y+t.yPoleOffset,d=1/(1+Math.sqrt(1-a*a-u*u)),m=bt;m[0]=1-d*a*a,m[3]=-d*a*u,m[6]=a,m[1]=-d*a*u,m[4]=1-d*u*u,m[7]=u,m[2]=-a,m[5]=-u,m[8]=1-d*(a*a+u*u);let l=Q.fromRotationZ(-r.s,Ot),w=Q.multiply(m,l,bt),T=e.dayNumber,C=e.secondsOfDay-pe.computeTaiMinusUtc(e)+t.ut1MinusUtc,P=T-2451545,A=C/ce.SECONDS_PER_DAY,q=.779057273264+A+.00273781191135448*(P+A);q=q%1*E.TWO_PI;let k=Q.fromRotationZ(q,Ot),L=Q.multiply(w,k,bt),F=Math.cos(t.xPoleWander),B=Math.cos(t.yPoleWander),W=Math.sin(t.xPoleWander),H=Math.sin(t.yPoleWander),ee=o-ai+i/ce.SECONDS_PER_DAY;ee/=36525;let re=-47e-6*ee*E.RADIANS_PER_DEGREE/3600,Z=Math.cos(re),oe=Math.sin(re),J=Ot;return J[0]=F*Z,J[1]=F*oe,J[2]=W,J[3]=-B*oe+H*W*Z,J[4]=B*Z+H*W*oe,J[5]=-H*F,J[6]=-H*oe-B*W*Z,J[7]=H*Z-B*W*oe,J[8]=B*F,Q.multiply(L,J,n)};var yi=new qe;V.pointToWindowCoordinates=function(e,n,t,o){return o=V.pointToGLWindowCoordinates(e,n,t,o),o.y=2*n[5]-o.y,o};V.pointToGLWindowCoordinates=function(e,n,t,o){if(!p(e))throw new j("modelViewProjectionMatrix is required.");if(!p(n))throw new j("viewportTransformation is required.");if(!p(t))throw new j("point is required.");p(o)||(o=new Oe);let i=yi;return G.multiplyByVector(e,qe.fromElements(t.x,t.y,t.z,1,i),i),qe.multiplyByScalar(i,1/i.w,i),G.multiplyByVector(n,i,i),Oe.fromCartesian4(i,o)};var li=new _,wi=new _,bi=new _;V.rotationMatrixFromPositionVelocity=function(e,n,t,o){if(!p(e))throw new j("position is required.");if(!p(n))throw new j("velocity is required.");let i=O(t,Ie.default).geodeticSurfaceNormal(e,li),r=_.cross(n,i,wi);_.equalsEpsilon(r,_.ZERO,E.EPSILON6)&&(r=_.clone(_.UNIT_X,r));let a=_.cross(r,n,bi);return _.normalize(a,a),_.cross(n,a,r),_.negate(r,r),_.normalize(r,r),p(o)||(o=new Q),o[0]=n.x,o[1]=n.y,o[2]=n.z,o[3]=r.x,o[4]=r.y,o[5]=r.z,o[6]=a.x,o[7]=a.y,o[8]=a.z,o};var vo=new G(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),Ao=new Me,_t=new _,Oi=new _,gi=new Q,St=new G,jo=new G;V.basisTo2D=function(e,n,t){if(!p(e))throw new j("projection is required.");if(!p(n))throw new j("matrix is required.");if(!p(t))throw new j("result is required.");let o=G.getTranslation(n,Oi),i=e.ellipsoid,r;if(_.equals(o,_.ZERO))r=_.clone(_.ZERO,_t);else{let l=i.cartesianToCartographic(o,Ao);r=e.project(l,_t),_.fromElements(r.z,r.x,r.y,r)}let a=V.eastNorthUpToFixedFrame(o,i,St),u=G.inverseTransformation(a,jo),d=G.getMatrix3(n,gi),m=G.multiplyByMatrix3(u,d,t);return G.multiply(vo,m,t),G.setTranslation(t,r,t),t};V.ellipsoidTo2DModelMatrix=function(e,n,t){if(!p(e))throw new j("projection is required.");if(!p(n))throw new j("center is required.");if(!p(t))throw new j("result is required.");let o=e.ellipsoid,i=V.eastNorthUpToFixedFrame(n,o,St),r=G.inverseTransformation(i,jo),a=o.cartesianToCartographic(n,Ao),u=e.project(a,_t);_.fromElements(u.z,u.x,u.y,u);let d=G.fromTranslation(u,St);return G.multiply(vo,r,t),G.multiply(d,t,t),t};var Mo=V;function x(e,n,t,o){this.west=O(e,0),this.south=O(n,0),this.east=O(t,0),this.north=O(o,0)}Object.defineProperties(x.prototype,{width:{get:function(){return x.computeWidth(this)}},height:{get:function(){return x.computeHeight(this)}}});x.packedLength=4;x.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e.west,n[t++]=e.south,n[t++]=e.east,n[t]=e.north,n};x.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new x),t.west=e[n++],t.south=e[n++],t.east=e[n++],t.north=e[n],t};x.computeWidth=function(e){s.typeOf.object("rectangle",e);let n=e.east,t=e.west;return n<t&&(n+=E.TWO_PI),n-t};x.computeHeight=function(e){return s.typeOf.object("rectangle",e),e.north-e.south};x.fromDegrees=function(e,n,t,o,i){return e=E.toRadians(O(e,0)),n=E.toRadians(O(n,0)),t=E.toRadians(O(t,0)),o=E.toRadians(O(o,0)),p(i)?(i.west=e,i.south=n,i.east=t,i.north=o,i):new x(e,n,t,o)};x.fromRadians=function(e,n,t,o,i){return p(i)?(i.west=O(e,0),i.south=O(n,0),i.east=O(t,0),i.north=O(o,0),i):new x(e,n,t,o)};x.fromCartographicArray=function(e,n){s.defined("cartographics",e);let t=Number.MAX_VALUE,o=-Number.MAX_VALUE,i=Number.MAX_VALUE,r=-Number.MAX_VALUE,a=Number.MAX_VALUE,u=-Number.MAX_VALUE;for(let d=0,m=e.length;d<m;d++){let l=e[d];t=Math.min(t,l.longitude),o=Math.max(o,l.longitude),a=Math.min(a,l.latitude),u=Math.max(u,l.latitude);let w=l.longitude>=0?l.longitude:l.longitude+E.TWO_PI;i=Math.min(i,w),r=Math.max(r,w)}return o-t>r-i&&(t=i,o=r,o>E.PI&&(o=o-E.TWO_PI),t>E.PI&&(t=t-E.TWO_PI)),p(n)?(n.west=t,n.south=a,n.east=o,n.north=u,n):new x(t,a,o,u)};x.fromCartesianArray=function(e,n,t){s.defined("cartesians",e),n=O(n,Ie.default);let o=Number.MAX_VALUE,i=-Number.MAX_VALUE,r=Number.MAX_VALUE,a=-Number.MAX_VALUE,u=Number.MAX_VALUE,d=-Number.MAX_VALUE;for(let m=0,l=e.length;m<l;m++){let w=n.cartesianToCartographic(e[m]);o=Math.min(o,w.longitude),i=Math.max(i,w.longitude),u=Math.min(u,w.latitude),d=Math.max(d,w.latitude);let T=w.longitude>=0?w.longitude:w.longitude+E.TWO_PI;r=Math.min(r,T),a=Math.max(a,T)}return i-o>a-r&&(o=r,i=a,i>E.PI&&(i=i-E.TWO_PI),o>E.PI&&(o=o-E.TWO_PI)),p(t)?(t.west=o,t.south=u,t.east=i,t.north=d,t):new x(o,u,i,d)};var _i=new _,Si=new _,Ri=new _,Ei=new _,Ti=new _,Rt=new Array(5);for(let e=0;e<Rt.length;++e)Rt[e]=new _;x.fromBoundingSphere=function(e,n,t){s.typeOf.object("boundingSphere",e);let o=e.center,i=e.radius;if(p(n)||(n=Ie.default),p(t)||(t=new x),_.equals(o,_.ZERO))return x.clone(x.MAX_VALUE,t),t;let r=Mo.eastNorthUpToFixedFrame(o,n,_i),a=G.multiplyByPointAsVector(r,_.UNIT_X,Si);_.normalize(a,a);let u=G.multiplyByPointAsVector(r,_.UNIT_Y,Ri);_.normalize(u,u),_.multiplyByScalar(u,i,u),_.multiplyByScalar(a,i,a);let d=_.negate(u,Ti),m=_.negate(a,Ei),l=Rt,w=l[0];return _.add(o,u,w),w=l[1],_.add(o,m,w),w=l[2],_.add(o,d,w),w=l[3],_.add(o,a,w),l[4]=o,x.fromCartesianArray(l,n,t)};x.clone=function(e,n){if(p(e))return p(n)?(n.west=e.west,n.south=e.south,n.east=e.east,n.north=e.north,n):new x(e.west,e.south,e.east,e.north)};x.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(e.west-n.west)<=t&&Math.abs(e.south-n.south)<=t&&Math.abs(e.east-n.east)<=t&&Math.abs(e.north-n.north)<=t};x.prototype.clone=function(e){return x.clone(this,e)};x.prototype.equals=function(e){return x.equals(this,e)};x.equals=function(e,n){return e===n||p(e)&&p(n)&&e.west===n.west&&e.south===n.south&&e.east===n.east&&e.north===n.north};x.prototype.equalsEpsilon=function(e,n){return x.equalsEpsilon(this,e,n)};x._validate=function(e){s.typeOf.object("rectangle",e);let n=e.north;s.typeOf.number.greaterThanOrEquals("north",n,-E.PI_OVER_TWO),s.typeOf.number.lessThanOrEquals("north",n,E.PI_OVER_TWO);let t=e.south;s.typeOf.number.greaterThanOrEquals("south",t,-E.PI_OVER_TWO),s.typeOf.number.lessThanOrEquals("south",t,E.PI_OVER_TWO);let o=e.west;s.typeOf.number.greaterThanOrEquals("west",o,-Math.PI),s.typeOf.number.lessThanOrEquals("west",o,Math.PI);let i=e.east;s.typeOf.number.greaterThanOrEquals("east",i,-Math.PI),s.typeOf.number.lessThanOrEquals("east",i,Math.PI)};x.southwest=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.west,n.latitude=e.south,n.height=0,n):new Me(e.west,e.south)};x.northwest=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.west,n.latitude=e.north,n.height=0,n):new Me(e.west,e.north)};x.northeast=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.east,n.latitude=e.north,n.height=0,n):new Me(e.east,e.north)};x.southeast=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.east,n.latitude=e.south,n.height=0,n):new Me(e.east,e.south)};x.center=function(e,n){s.typeOf.object("rectangle",e);let t=e.east,o=e.west;t<o&&(t+=E.TWO_PI);let i=E.negativePiToPi((o+t)*.5),r=(e.south+e.north)*.5;return p(n)?(n.longitude=i,n.latitude=r,n.height=0,n):new Me(i,r)};x.intersection=function(e,n,t){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",n);let o=e.east,i=e.west,r=n.east,a=n.west;o<i&&r>0?o+=E.TWO_PI:r<a&&o>0&&(r+=E.TWO_PI),o<i&&a<0?a+=E.TWO_PI:r<a&&i<0&&(i+=E.TWO_PI);let u=E.negativePiToPi(Math.max(i,a)),d=E.negativePiToPi(Math.min(o,r));if((e.west<e.east||n.west<n.east)&&d<=u)return;let m=Math.max(e.south,n.south),l=Math.min(e.north,n.north);if(!(m>=l))return p(t)?(t.west=u,t.south=m,t.east=d,t.north=l,t):new x(u,m,d,l)};x.simpleIntersection=function(e,n,t){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",n);let o=Math.max(e.west,n.west),i=Math.max(e.south,n.south),r=Math.min(e.east,n.east),a=Math.min(e.north,n.north);if(!(i>=a||o>=r))return p(t)?(t.west=o,t.south=i,t.east=r,t.north=a,t):new x(o,i,r,a)};x.union=function(e,n,t){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",n),p(t)||(t=new x);let o=e.east,i=e.west,r=n.east,a=n.west;o<i&&r>0?o+=E.TWO_PI:r<a&&o>0&&(r+=E.TWO_PI),o<i&&a<0?a+=E.TWO_PI:r<a&&i<0&&(i+=E.TWO_PI);let u=E.negativePiToPi(Math.min(i,a)),d=E.negativePiToPi(Math.max(o,r));return t.west=u,t.south=Math.min(e.south,n.south),t.east=d,t.north=Math.max(e.north,n.north),t};x.expand=function(e,n,t){return s.typeOf.object("rectangle",e),s.typeOf.object("cartographic",n),p(t)||(t=new x),t.west=Math.min(e.west,n.longitude),t.south=Math.min(e.south,n.latitude),t.east=Math.max(e.east,n.longitude),t.north=Math.max(e.north,n.latitude),t};x.contains=function(e,n){s.typeOf.object("rectangle",e),s.typeOf.object("cartographic",n);let t=n.longitude,o=n.latitude,i=e.west,r=e.east;return r<i&&(r+=E.TWO_PI,t<0&&(t+=E.TWO_PI)),(t>i||E.equalsEpsilon(t,i,E.EPSILON14))&&(t<r||E.equalsEpsilon(t,r,E.EPSILON14))&&o>=e.south&&o<=e.north};var Ci=new Me;x.subsample=function(e,n,t,o){s.typeOf.object("rectangle",e),n=O(n,Ie.default),t=O(t,0),p(o)||(o=[]);let i=0,r=e.north,a=e.south,u=e.east,d=e.west,m=Ci;m.height=t,m.longitude=d,m.latitude=r,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.longitude=u,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.latitude=a,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.longitude=d,o[i]=n.cartographicToCartesian(m,o[i]),i++,r<0?m.latitude=r:a>0?m.latitude=a:m.latitude=0;for(let l=1;l<8;++l)m.longitude=-Math.PI+l*E.PI_OVER_TWO,x.contains(e,m)&&(o[i]=n.cartographicToCartesian(m,o[i]),i++);return m.latitude===0&&(m.longitude=d,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.longitude=u,o[i]=n.cartographicToCartesian(m,o[i]),i++),o.length=i,o};x.subsection=function(e,n,t,o,i,r){if(s.typeOf.object("rectangle",e),s.typeOf.number.greaterThanOrEquals("westLerp",n,0),s.typeOf.number.lessThanOrEquals("westLerp",n,1),s.typeOf.number.greaterThanOrEquals("southLerp",t,0),s.typeOf.number.lessThanOrEquals("southLerp",t,1),s.typeOf.number.greaterThanOrEquals("eastLerp",o,0),s.typeOf.number.lessThanOrEquals("eastLerp",o,1),s.typeOf.number.greaterThanOrEquals("northLerp",i,0),s.typeOf.number.lessThanOrEquals("northLerp",i,1),s.typeOf.number.lessThanOrEquals("westLerp",n,o),s.typeOf.number.lessThanOrEquals("southLerp",t,i),p(r)||(r=new x),e.west<=e.east){let u=e.east-e.west;r.west=e.west+n*u,r.east=e.west+o*u}else{let u=E.TWO_PI+e.east-e.west;r.west=E.negativePiToPi(e.west+n*u),r.east=E.negativePiToPi(e.west+o*u)}let a=e.north-e.south;return r.south=e.south+t*a,r.north=e.south+i*a,n===1&&(r.west=e.east),o===1&&(r.east=e.east),t===1&&(r.south=e.north),i===1&&(r.north=e.north),r};x.MAX_VALUE=Object.freeze(new x(-Math.PI,-E.PI_OVER_TWO,Math.PI,E.PI_OVER_TWO));var mf=x;function D(e,n,t,o){this[0]=O(e,0),this[1]=O(t,0),this[2]=O(n,0),this[3]=O(o,0)}D.packedLength=4;D.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e[0],n[t++]=e[1],n[t++]=e[2],n[t++]=e[3],n};D.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new D),t[0]=e[n++],t[1]=e[n++],t[2]=e[n++],t[3]=e[n++],t};D.packArray=function(e,n){s.defined("array",e);let t=e.length,o=t*4;if(!p(n))n=new Array(o);else{if(!Array.isArray(n)&&n.length!==o)throw new j("If result is a typed array, it must have exactly array.length * 4 elements");n.length!==o&&(n.length=o)}for(let i=0;i<t;++i)D.pack(e[i],n,i*4);return n};D.unpackArray=function(e,n){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!==0)throw new j("array length must be a multiple of 4.");let t=e.length;p(n)?n.length=t/4:n=new Array(t/4);for(let o=0;o<t;o+=4){let i=o/4;n[i]=D.unpack(e,o,n[i])}return n};D.clone=function(e,n){if(p(e))return p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n):new D(e[0],e[2],e[1],e[3])};D.fromArray=D.unpack;D.fromColumnMajorArray=function(e,n){return s.defined("values",e),D.clone(e,n)};D.fromRowMajorArray=function(e,n){return s.defined("values",e),p(n)?(n[0]=e[0],n[1]=e[2],n[2]=e[1],n[3]=e[3],n):new D(e[0],e[1],e[2],e[3])};D.fromScale=function(e,n){return s.typeOf.object("scale",e),p(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=e.y,n):new D(e.x,0,0,e.y)};D.fromUniformScale=function(e,n){return s.typeOf.number("scale",e),p(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=e,n):new D(e,0,0,e)};D.fromRotation=function(e,n){s.typeOf.number("angle",e);let t=Math.cos(e),o=Math.sin(e);return p(n)?(n[0]=t,n[1]=o,n[2]=-o,n[3]=t,n):new D(t,-o,o,t)};D.toArray=function(e,n){return s.typeOf.object("matrix",e),p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n):[e[0],e[1],e[2],e[3]]};D.getElementIndex=function(e,n){return s.typeOf.number.greaterThanOrEquals("row",n,0),s.typeOf.number.lessThanOrEquals("row",n,1),s.typeOf.number.greaterThanOrEquals("column",e,0),s.typeOf.number.lessThanOrEquals("column",e,1),e*2+n};D.getColumn=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("result",t);let o=n*2,i=e[o],r=e[o+1];return t.x=i,t.y=r,t};D.setColumn=function(e,n,t,o){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=D.clone(e,o);let i=n*2;return o[i]=t.x,o[i+1]=t.y,o};D.getRow=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("result",t);let o=e[n],i=e[n+2];return t.x=o,t.y=i,t};D.setRow=function(e,n,t,o){return s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=D.clone(e,o),o[n]=t.x,o[n+2]=t.y,o};var vi=new Oe;D.setScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t);let o=D.getScale(e,vi),i=n.x/o.x,r=n.y/o.y;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*r,t[3]=e[3]*r,t};var Ai=new Oe;D.setUniformScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t);let o=D.getScale(e,Ai),i=n/o.x,r=n/o.y;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*r,t[3]=e[3]*r,t};var Po=new Oe;D.getScale=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n.x=Oe.magnitude(Oe.fromElements(e[0],e[1],Po)),n.y=Oe.magnitude(Oe.fromElements(e[2],e[3],Po)),n};var Uo=new Oe;D.getMaximumScale=function(e){return D.getScale(e,Uo),Oe.maximumComponent(Uo)};var ji=new Oe;D.setRotation=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let o=D.getScale(e,ji);return t[0]=n[0]*o.x,t[1]=n[1]*o.x,t[2]=n[2]*o.y,t[3]=n[3]*o.y,t};var Mi=new Oe;D.getRotation=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=D.getScale(e,Mi);return n[0]=e[0]/t.x,n[1]=e[1]/t.x,n[2]=e[2]/t.y,n[3]=e[3]/t.y,n};D.multiply=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e[0]*n[0]+e[2]*n[1],i=e[0]*n[2]+e[2]*n[3],r=e[1]*n[0]+e[3]*n[1],a=e[1]*n[2]+e[3]*n[3];return t[0]=o,t[1]=r,t[2]=i,t[3]=a,t};D.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t};D.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t};D.multiplyByVector=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=e[0]*n.x+e[2]*n.y,i=e[1]*n.x+e[3]*n.y;return t.x=o,t.y=i,t};D.multiplyByScalar=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t};D.multiplyByScale=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t),t[0]=e[0]*n.x,t[1]=e[1]*n.x,t[2]=e[2]*n.y,t[3]=e[3]*n.y,t};D.multiplyByUniformScale=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t};D.negate=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n};D.transpose=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[0],o=e[2],i=e[1],r=e[3];return n[0]=t,n[1]=o,n[2]=i,n[3]=r,n};D.abs=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n};D.equals=function(e,n){return e===n||p(e)&&p(n)&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[3]===n[3]};D.equalsArray=function(e,n,t){return e[0]===n[t]&&e[1]===n[t+1]&&e[2]===n[t+2]&&e[3]===n[t+3]};D.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(e[0]-n[0])<=t&&Math.abs(e[1]-n[1])<=t&&Math.abs(e[2]-n[2])<=t&&Math.abs(e[3]-n[3])<=t};D.IDENTITY=Object.freeze(new D(1,0,0,1));D.ZERO=Object.freeze(new D(0,0,0,0));D.COLUMN0ROW0=0;D.COLUMN0ROW1=1;D.COLUMN1ROW0=2;D.COLUMN1ROW1=3;Object.defineProperties(D.prototype,{length:{get:function(){return D.packedLength}}});D.prototype.clone=function(e){return D.clone(this,e)};D.prototype.equals=function(e){return D.equals(this,e)};D.prototype.equalsEpsilon=function(e,n){return D.equalsEpsilon(this,e,n)};D.prototype.toString=function(){return`(${this[0]}, ${this[2]})
(${this[1]}, ${this[3]})`};var _f=D;export{qe as a,G as b,De as c,ke as d,bo as e,Xe as f,Mo as g,mf as h,_f as i};
