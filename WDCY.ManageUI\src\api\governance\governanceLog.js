import request from '@/utils/request'

export const governanceLogList = (data) =>
request({
    url: '/governanceLog/list',
    method: 'get',
    params: data
})
export const governanceLog = (id) =>
	request({
		url: '/governanceLog/'+id,
		method: 'get',
	})
export const governanceLogAdd = (data) =>
request({
    url: '/governanceLog',
    method: 'post',
    data: data
})
export const governanceLogEdit = (data) =>
request({
    url: '/governanceLog',
    method: 'put',
    data: data
})
export const governanceLogDelete = (id) =>
request({
    url: '/governanceLog/'+id,
    method: 'delete',

})