<template>
	<div>
		<el-dialog draggable width="30%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<el-row  :gutter="20">
			<el-col :span="18">
				<el-col>访客姓名&nbsp;&nbsp;&nbsp;&nbsp;{{formatName(dataList.visitorName)}}</el-col>
				<el-col>证件号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.visitorIdCard}}</el-col>
				<el-col>访客电话&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.visitorPhone}}</el-col>
				<el-col>访客车牌&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.plateNo}}</el-col>
				<el-col>证件类型&nbsp;&nbsp;&nbsp;&nbsp;{{formatcertificateType(certificateTypeTagList,dataList.visitorCertificateType)}}</el-col>
			</el-col>
			<el-col :span="6">
				<!-- <el-image style="width: 120px; height: 120px" src="https://img0.baidu.com/it/u=4117713405,2961605581&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400" :preview-src-list="['https://img0.baidu.com/it/u=4117713405,2961605581&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400']"></el-image> -->
				<el-image style="width: 120px; height: 120px" :src="imgServer + dataList.photo" :preview-src-list="[imgServer + dataList.photo]">
					<template #error>
						<el-image preview-teleported fit="contain" style="height: 120px; width:120px" :src="errorHeadImg" :preview-src-list="[errorHeadImg]"></el-image>
					</template>
				</el-image>
			</el-col>
		</el-row>
		<el-row  :gutter="20">
			<el-col :span="24">
				<el-col :span="14">被访人员&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.ownerName}}</el-col>
				<el-col :span="14">拜访地址&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.ownerAddress}}</el-col>
				<el-col :span="14">审核状态&nbsp;&nbsp;&nbsp;&nbsp;<span class="status-tag" :style="'background-color:'+ getDictCss(statusList, dataList.status)">{{ formatStatus(statusList,dataList.status) }}</span></el-col>
				<el-col :span="14">拜访原由&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.visitPurpose}}</el-col>
				<el-col :span="14">审核意见&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.suggestion}}</el-col>
			</el-col>
		</el-row>
		<el-row  :gutter="20">
			<el-col :span="24">
				<el-col :span="14">申请时间&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.createTime}}</el-col>
				<el-col :span="14">到访时间&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.visitTime}}</el-col>
				<el-col :span="14">离访时间&nbsp;&nbsp;&nbsp;&nbsp;{{dataList.visitEndTime}}</el-col>
			</el-col>
		</el-row>
	</el-dialog>
	</div>
	
</template>

<script>

	import mitt from "@/utils/mitt";
	import { getDictCss, formatDict } from "@/utils/dict"
	import errImg from "../../assets/img/defaultHead.png"

	export default {
		props: ["statusList", "certificateTypeTagList"],
		data() {
			return {
				loading: false,
				dataList: {},
				dialog: {},
				imgServer: import.meta.env.VITE_BASE_API,
				communityId: localStorage.getItem("communityId"),
				errorHeadImg: errImg,
			}
		},
		methods:{
			formatStatus(dicList, cellValue) {
				return formatDict(dicList, cellValue)
			},
			formatcertificateType(dicList, cellValue) {
				return formatDict(dicList, cellValue)
			},
			getDictCss(dicList, cellValue) {
				return getDictCss(dicList, cellValue)
			}, 
			formatName(name){
				if (name) {
					if (name.length == 2) {
						const xing = String(name).substring(0,1)
						return xing + '*'
					} else if ( name.length == 3 ) {
						const xing = String(name).substring(0,1)
						const ming = String(name).substring(2,3)
						return xing + '*' + ming
					} else if(name.length>3){
						return name.substring(0,1)+"*"+'*'+name.substring(3,name.length)
					}	
				}
			},
		},
		mounted() {
			mitt.on("openVehicleRecordDetail", (data) => {
				this.dataList = data;
				this.dialog.show = true;
				this.dialog.title = "详情";
			});
		},
		created() {
			mitt.off("openVehicleRecordDetail");
		},
	};
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	.el-col{
			margin-bottom: 5px;
	}
	/deep/.el-dialog__body {
		background-color: #f1f5ff;
  	}
	.status-tag {
		display: inline-block;
		width: 59px;
		border-radius: 10px;
		color: #fff;
		font-size: 12px;
		line-height: 14px;
		font-family: Microsoft YaHei;
		padding: 5px 5px 5px 5px;
		text-align: center;
	}
</style>
