<template>
	<info-release-edit :typeList="typeList" @search="search"></info-release-edit>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.title" @keydown.enter="search" placeholder="标题" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="12">
			<el-button style="float: right;" type="primary" @click="add">发 布 信 息</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="infoReleaseList" border style="width: 100%">
				<el-table-column prop="title" align="center" label="标题"/>
				<el-table-column prop="type" align="center" label="类型" :formatter="formatType" />
				<el-table-column prop="type" align="center" label="状态" />
				<el-table-column align="center" width="200" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row.id)">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>
<script>
import { listInfoRelease,deleteInfoRelease,getInfoRelease } from "@/api/base/infoRelease"
import mitt from "@/utils/mitt"
import { listDictByNameEn } from "@/api/admin/dict"
import infoReleaseEdit from "@/componts/base/infoReleaseEdit.vue"
export default {
	components:{ infoReleaseEdit },
	data() {
		return {
			searchModel: {},
			editor:{},
			infoReleaseList: [],
			typeList:[],
			statusList:[],
			total:0,
			pageSize:10
		}
	},
	methods: {
		search() {
			listInfoRelease(this.searchModel)
			.then(res => {
				this.infoReleaseList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		edit(id){
			getInfoRelease(id)
			.then(res =>{
				mitt.emit('openInfoReleaseEdit',res.data.result)
			})
		},
		add(){
			mitt.emit('openInfoReleaseAdd')
		},
		formatType(row, column, cellValue, index){
			let result = ''
			for(let item of this.typeList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		deleted(id){
			this.$confirm('删除信息, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteInfoRelease(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			mitt.off('openInfoReleaseEdit')
			mitt.off('openInfoReleaseAdd')
			try{
				let res = await listInfoRelease({pageNum:1})
				
				this.infoReleaseList = res.data.result.list
				console.log(this.infoReleaseList);
				this.total = res.data.result.total
				
				let res_type = await listDictByNameEn('release_type')
				this.typeList = res_type.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
