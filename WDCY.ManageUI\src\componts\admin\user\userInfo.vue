<template>
	<el-dialog draggable width="25%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="userForm" :model="userModel" label-width="80px">
			<el-row>
				<el-col>
					<el-form-item label="头像" prop="avatar">
						<el-dropdown>
							<el-avatar :size="50" :src="imgServer+userModel.avatar">
								<el-icon :size="50"><user-filled></user-filled></el-icon>
							</el-avatar>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item>
										<el-button type="text" size="default"><el-upload :show-file-list="false" :http-request="imgUpload">上传</el-upload></el-button>
									</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="用户名" prop="userName">
						<el-input readonly v-model="userModel.userName" placeholder="用户名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="昵称" prop="nickName">
						<el-input v-model="userModel.nickName" placeholder="昵称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-form-item label="性别" prop="sex">
				<el-select style="width: 100%;" v-model="userModel.sex" clearable placeholder="性别">
				    <el-option v-for="item in sexList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
				</el-select>
			</el-form-item>
			<el-row>
				<el-col>
					<el-form-item label="邮箱">
						<el-input v-model="userModel.email" placeholder="邮箱"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="手机号">
						<el-input v-model="userModel.mobile" placeholder="手机号"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
	
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit('userForm')">提 交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { fileUpload } from "@/api/admin/file"
import { updateUserInfo } from "@/api/admin/user"
import mitt from "@/utils/mitt"
import { listDictByNameEn } from "@/api/admin/dict"
export default {
	data() {
		return {
			loading: false,
			userModel: {},
			dialog:{},
			sexList:[],
			imgServer: import.meta.env.VITE_BASE_API,
			rules: {
				userName: [{
					required: true,
					message: '用户名不能为空',
					trigger: 'blur',
				}],
				nickName: [{
					required: true,
					message: '请输入昵称',
					trigger: 'blur',
				}]
			}
		}
	},
	methods: {
		imgUpload(files){
			let form  = new FormData()
			form.append("file",files.file)
			form.append("modulesName", 'admin');
			form.append("functionName", 'userInfo');
			form.append("communityId", localStorage.getItem('communityId'));
			fileUpload(form)
			.then(res =>{
				this.userModel.avatar = res.data.result.url
				if(res.data.code == 0){
					this.$message.success("上传成功")
				}
			})
		},
		onSubmit(formName){
			this.$refs[formName].validate((valid) => {
			  if (valid) {
			    updateUserInfo(this.userModel)
			    .then(res =>{
					mitt.emit("initUserInfo")
			    	this.$message.success(res.data.msg)
			    	this.dialog.show = false
			    })
			  }
			})
			
		}
	},
	mounted(){
		mitt.off("initUserInfo")
		mitt.on('openUserInfo', (user) => {
			this.userModel = user
			listDictByNameEn('sex')
			.then(res =>{
				this.sexList = res.data.result
			})
			this.dialog.show = true
			this.dialog.title = "个人信息"
		})
	}
}
</script>
