import request from '@/utils/request'

// 查询组件列表
export function listComponent(query) {
  return request({
    url: '/mapi/v3_8_0/assy/component/list',
    method: 'get',
    params: query
  })
}

// 查询组件详细
export function getComponent(id) {
  return request({
    url: '/mapi/v3_8_0/assy/component/' + id,
    method: 'get'
  })
}

// 新增组件
export function addComponent(data) {
  return request({
    url: '/mapi/v3_8_0/assy/component',
    method: 'post',
    data: data
  })
}

// 修改组件
export function updateComponent(data) {
  return request({
    url: '/mapi/v3_8_0/assy/component',
    method: 'put',
    data: data
  })
}

// 删除组件
export function delComponent(id) {
  return request({
    url: '/mapi/v3_8_0/assy/component/' + id,
    method: 'delete'
  })
}
