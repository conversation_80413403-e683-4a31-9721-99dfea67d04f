import axios from 'axios'
export var baseUrl = import.meta.env.VITE_BASE_API
import { ElLoading, ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'


//设置请求的次数
axios.defaults.retry = 2;
//设置请求的间隙(ms)
axios.defaults.retryDelay = 1000;

// 创建axios实例
const service = axios.create({
	baseURL: baseUrl,
	timeout: 300000
})

var tipErr = {};
let isRefreshing = false;
// 重试队列，每一项将是一个待执行的函数形式
let requests = [];

// request拦截器
service.interceptors.request.use(config => {
	if (localStorage.getItem("token")) {
		config.headers['Authorization'] = localStorage.getItem("token")
	}
	NProgress.configure({ showSpinner: false })
	NProgress.start();
	return config
}, error => {
	console.log("请求异常", error)
	Promise.reject(error)
})

//判断token是否过期
function isTokenExpired() {
    let expires_time = Number(localStorage.getItem("expires_time"));
    let curentTime = new Date().getTime();
	console.log(curentTime, expires_time, curentTime >= expires_time);
    if (curentTime >= expires_time) {
        return true;
    } else {
        return false;
    }
}

// respone拦截器
service.interceptors.response.use(
	async response => {
		NProgress.done();
		// console.log('service.interceptors.response: ' + response)
		// console.log(response)
		if (response.status == 500) {
			return Promise.reject(response.data)
		} else if (response.data.code == 401) {
			if (isTokenExpired()) {
				isTokenExpired()
					// localStorage.clear()
				console.log(isTokenExpired(),'token过期时间');
			}
			if (!isRefreshing) {
				if (!localStorage.getItem("refreshToken")) {
					// ElMessage({
					// 	message: '令牌无效',
					// 	type: 'error',
					// 	duration: 5 * 1000
					// })
					console.error('令牌无效');
					location.reload()
					localStorage.removeItem("refreshToken")
				}
				isRefreshing = true
				let config = response.config;
				if (!config || !config.retry) return Promise.reject(response.data);
				config.__retryCount = config.__retryCount || 0;
				if (config.__retryCount >= config.retry) {
					return Promise.reject(response.data);
				}
				config.__retryCount += 1;
				try {
					const res = await axios.post(baseUrl + "/oauth/login", {
						refreshToken: localStorage.getItem("refreshToken"),
						grantType: "refresh"
					})
					if(res.data.code == -1) {
						localStorage.clear()
						// location.reload()
					}
					localStorage.setItem("token", res.data.result.access_token)
					localStorage.setItem("refreshToken", res.data.result.refresh_token)
					// location.reload()
					requests.forEach(cb => cb())
					requests = []
					return service(config)
				} catch (error) {
					// location.reload()
					// localStorage.clear()
				} finally {
					isRefreshing = false
				}
			} else {
				requests.push(() => {
					resolve(service(response.config))
				})
			}
		} else if (response.data.code == 0) {
			return response
		}  else if (response.data.code == 2) {
			return response
		}else {
			console.log(response.config.url)
			if (response.data.code == -1 && response.config.url == '/push-server/push') {
			// 	var fullLoading = ElLoading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.7)' , text: '正在加载中，请不要刷新！！！'});
			// 	fullLoading.close()
			} else {
				ElMessage({
					message: response.data.msg,
					type: 'error',
					duration: 5 * 1000
				})
			}
			console.error(response.data.msg);
			return Promise.reject(response.data.msg)
		}
	},
	error => {
		console.log('service.interceptors.response.err: ', error)
		NProgress.done();

		let { message, response } = error;
		console.log('service.interceptors.response.err 解构对象: ', message, response)

		if (message == "Network Error") {
			message = "请求过程中出错";
		}
		else if (message.includes("timeout")) {
			message = "系统服务请求超时";
		}
		else if (response.status == 503) {
			message = "服务暂时不可用，请稍候重试";
		}
		else if (message.includes("Request failed with status code")) {
			message = "系统服务『" + message.substr(message.length - 3) + "』异常";
		}
		if (response) {
			tipErr[response.status] = message;
		} else {
			tipErr[-1] = message
		}
		setTimeout(() => {
			for (const [key, value] of Object.entries(tipErr)) {
				// ElMessage({
				// 	message: value,
				// 	type: 'error',
				// 	duration: 5 * 1000
				// })
				console.error(value);
			}
			tipErr = {};
		}, 1000);
		return Promise.reject(message)
	})

export default service
