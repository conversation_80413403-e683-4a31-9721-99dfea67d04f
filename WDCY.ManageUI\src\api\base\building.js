import request from '@/utils/request'

export const detailBuilding = (data) =>
	request({
		url: '/building/detail',
		method: 'get',
		params: data
	})
export const queryChildrenNodeBuilding = (data) =>
	request({
		url: '/building/queryChildrenNode',
		method: 'get',
		params: data
	})	
export const importData = (data) =>
	request({
		url: '/building/importData',
		method: 'post',
		data: data,
		timeout: 1000 * 60 * 30
	})		

export const listBuilding = (data) =>
	request({
		url: '/building',
		method: 'get',
		params: data
	})
export const getBuilding = (id) =>
	request({
		url: '/building/'+id,
		method: 'get'
	})
export const calibrateRoom = (data) =>
	request({
		url: '/room/calibrate',
		method: 'put',
		data: data
	})
export const addBuilding = (data) =>
	request({
		url: '/building',
		method: 'post',
		data: data
	})
export const editBuilding = (data) =>
	request({
		url: '/building',
		method: 'put',
		data: data
	})
export const deleteBuilding = (id) =>
	request({
		url: '/building',
		method: 'delete',
		params: {
			id: id
		}
	})
// 编辑地理围栏
export const updateByPolyCoords = (data) =>
	request({
		url: '/building/updateByPolyCoords',
		method: 'put',
		data: data
	})
//传buildingId  获取单元列表
export const getUnitList = (params) =>
	request({
		url: '/unit',
		method: 'get',
		params: params
	})

//传unitId	获取房间列表
export const getRoomList = (params) =>
	request({
		url: '/room',
		method: 'get',
		params: params
	})
