import request from '@/utils/request'

export const warnStrategyList = (data) =>
	request({
		url: '/warnStrategy',
		method: 'get',
		params: data
	})
export const warnStrategyListEdit = (data) =>
	request({
		url: '/warnStrategy',
		method: 'put',
		data: data
	})
  export const warnStrategyListAdd = (data) =>
	request({
		url: '/warnStrategy',
		method: 'post',
		data: data
	})
  export const warnStrategyListDelete = (id) =>
	request({
		url: '/warnStrategy',
		method: 'delete',
		params: {
			id: id
		}
	})
  export const warnStrategyStatusEdit = (data) =>
	request({
		url: '/warnStrategy/updateStatus',
		method: 'put',
		data: data
	})
  export const warnRuleList = (id) =>
	request({
		url: '/warnStrategy/queryWarnRule/'+id,
		method: 'get',
	})
  export const getWarnStrategy = (id) =>
	request({
		url: '/warnStrategy/'+id,
		method: 'get',
	})

	export const warnStrategyConditionAdd = (data) =>
	request({
		url: '/warnStrategyCondition',
		method: 'post',
		data: data
	})

	export const getWarnStrategyCondition = (id) =>
	request({
		url: '/warnStrategyCondition/'+id,
		method: 'get',
	})

	export const warnStrategyConditionEdit = (data) =>
	request({
		url: '/warnStrategyCondition',
		method: 'put',
		data: data
	})

	export const warnStrategyConditionDelete = (id) =>
	request({
		url: '/warnStrategyCondition',
		method: 'delete',
		params: {
			id: id
		}
	})

	export const warnStrategyExecuteAdd = (data) =>
	request({
		url: '/warnStrategyExecute',
		method: 'post',
		data: data
	})

	export const getWarnStrategyExecute = (id) =>
	request({
		url: '/warnStrategyExecute/'+id,
		method: 'get',
	})

	export const warnStrategyExecuteEdit = (data) =>
	request({
		url: '/warnStrategyExecute',
		method: 'put',
		data: data
	})

	export const warnStrategyExecuteDelete = (id) =>
	request({
		url: '/warnStrategyExecute',
		method: 'delete',
		params: {
			id: id
		}
	})

	export const updateStatus = (data) =>
	request({
		url: '/warnStrategy/updateStatus',
		method: 'put',
		data: data
	})
