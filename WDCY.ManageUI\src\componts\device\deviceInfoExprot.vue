<template>
    <el-dialog draggable width="45%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
      <el-form ref="formEl" :rules="rules" :model="searchModel" label-width="120px" style="padding-right: 25px;">
        <el-row :gutter="24">
            <el-col :span="24">
                <el-form-item label="小区名称" prop="communityIds">
                    <el-select style="width:100%"  v-model="searchModel.communityIds" multiple placeholder="请选择小区名称">
                        <el-option v-for="item in communityList" :key="item.id" :label="item.communityName" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="14">
                <el-form-item label="设施状态" prop="statusList">
                    <el-select style="width: 100%"  v-model="searchModel.statusList" multiple placeholder="状态">
                        <el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="14">
                <el-form-item label="导出类型" prop="exportType">
                    <el-select style="width: 100%" v-model="searchModel.exportType" placeholder="导出类型">
                        <el-option v-for="item in exportTypeList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
      </el-form>

    <el-row justify="center">
        <el-button type="primary" style="width: 100px; height: 30px; margin-top: 20px" @click="onSubmit">下 载</el-button>
        <el-button type="primary" style="width: 100px; height: 30px; margin-top: 20px" @click="close">关 闭</el-button>
    </el-row>



    </el-dialog>
  </template>
  
  <script>
  import { ElLoading } from 'element-plus'
  import * as XLSX from "xlsx"
  import { deviceInfoExport } from "@/api/device/device";
  import { accountCommunity } from "@/api/base/community";
  import { listDictByNameEn } from "@/api/admin/dict"
  import mitt from "@/utils/mitt";
  export default {
    props:['statusList'],
    data() {
      return {
        loading: false,
        dialog: {},
        exportTypeList: [],
        communityIds: [],
        communityList:[],
        searchModel: {},
        rules: {
            // statusList: [
            //     { type: 'array', required: true, message: '请至少选择一个状态', trigger: 'change' }
            // ],
            exportType: [
                { type: 'data', required: true, message: '请选择导出类型', trigger: 'change' }
            ],
        },
        fullLoading: null
      };
    },
    methods: {
      onSubmit() {
          
        this.fullLoading = ElLoading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.7)' , text: '正在导出，请不要刷新！！！'});
          deviceInfoExport(this.searchModel).then( res => {
            this.fullLoading.close()
              if(this.searchModel.exportType == 1){
                let res_data = res.data.result.deviceStatusDtoList

                for (const item in res_data) {
                  res_data[item].status = this.$parent.formatDict(this.statusList, res_data[item].status)
                }
                var json_data = JSON.stringify(res_data)
                .replaceAll('communityId', '小区ID')
                .replaceAll('communityName', '小区名称')
                .replaceAll('community', '社区')
                .replaceAll("devIp", "设施IP")
                .replaceAll("devMac", "设施Mac")
                .replaceAll("devModel", "设施型号")
                .replaceAll('deviceInfoId', '设施ID')
                .replaceAll('deviceName', '设施名称')
                .replaceAll('deviceType', '设施类型')
                .replaceAll('gbCode', '国标编码')
                .replaceAll('lat', '经度')
                .replaceAll('lng', '纬度')
                .replaceAll('alt', '海拔')
                .replaceAll('address', '安装地址')
                .replaceAll('installType', '安装类型')
                .replaceAll('createTime', '安装时间')
                .replaceAll('status', '状态')
                .replaceAll('devNote', '设施描述')
                .replaceAll('devNo', '设施编号')
                .replaceAll('polyCoords', '地理围栏')
                .replaceAll('devTag', '设施标签')
                json_data=JSON.parse(json_data)

                const data = XLSX.utils.json_to_sheet(json_data)//此处tableData.value为表格的数据
                const wb = XLSX.utils.book_new()
                XLSX.utils.book_append_sheet(wb, data, '状态设施')//test-data为自定义的sheet表名
                XLSX.writeFile(wb,'状态设施列表.xlsx')//test.xlsx为自定义的文件名
              } else if (this.searchModel.exportType == 0) {
                let res_data = res.data.result.deviceImportDtoList
                var json_data = JSON.stringify(res_data).replaceAll('deviceName', '设施名称').replaceAll('deviceInstallAdministrationArea', '设施安装行政区域').replaceAll("tradeCode", "行业编码").replaceAll('typeCode', '类型编码').replaceAll('webCode', '网络标识').replaceAll('deviceCode', '设施国标编码').replaceAll('deviceTheirType', '设施所属类型').replaceAll('deviceType', '设施类型').replaceAll('cameraFunctionType', '摄像机功能类型').replaceAll('cameraDetailFunction', '摄像机详细功能').replaceAll('lng', '经度').replaceAll('lat', '纬度').replaceAll('deviceSourceUnit', '设施来源单位').replaceAll('ContactInformation', '联系方式').replaceAll('operationsCompany', '运维公司').replaceAll('ipv4', 'IPV4').replaceAll('sectorIndustry', '所属部门行业').replaceAll('isNoMultichannelDevice', '是否多通道设施').replaceAll('installAddress', '安装位置').replaceAll('cameraPositionType', '摄像机位置类型').replaceAll('cameraAreaType', '摄像机区域类型').replaceAll('deviceManufacturer', '设施厂商').replaceAll('deviceModel', '设施型号').replaceAll('cameraMACAddress', '摄像机MAC地址').replaceAll('gatehouseDetailedAddress', '所属小区').replaceAll('channelNumber', '设施ID').replaceAll('projectName', '项目名称').replaceAll('creatTime', '安装时间').replaceAll('serialNumber', '流水号').replaceAll('row', '行')
                json_data=JSON.parse(json_data)

                const data = XLSX.utils.json_to_sheet(json_data)//此处tableData.value为表格的数据
                const wb = XLSX.utils.book_new()
                XLSX.utils.book_append_sheet(wb, data, '国标设施')//test-data为自定义的sheet表名
                XLSX.writeFile(wb,'国标设施列表.xlsx')//test.xlsx为自定义的文件名
              }
        })
      },
      close(){
        this.dialog.show = false
      },
      async init() {
        await accountCommunity().then(res => {
            this.communityList = res.data.result
        })
        // if (this.communityList.length < 5) {
        //     for (let index = 0; index < this.communityList.length; index++) {
        //         this.communityIds.push(this.communityList[index].id)
        //     }
        // } else{
        //     for (let index = 0; index < 5; index++) {
        //         this.communityIds.push(this.communityList[index].id)
        //     }
        // }
		// this.searchModel.communityIds = this.communityIds


        let export_type = await listDictByNameEn('dev_export_type')
        this.exportTypeList = export_type.data.result
    },
    },
    mounted() {
        this.init()



      mitt.on("deviceInfoExport", () => {
        this.dialog.show = true;
        this.dialog.title = "设施导出";
      });
    },

  };
  </script>
  <style scoped>
  .editor {
    width: 805px;
  }
  </style>
  