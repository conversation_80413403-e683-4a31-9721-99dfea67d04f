<template>
    <el-dialog draggable width="25vw" v-loading="loading" destroy-on-close v-model="dialog.show" title="绑定用户">
        <el-form :rules="rules" ref="form" :model="userModel">
            <el-row>
                <el-col>
                    <el-form-item prop="phone">
                        <el-input placeholder="请输入手机号" v-model="userModel.phone">
                            <template #prefix>
                                <el-icon :size="20">
                                    <user></user>
                                </el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col>
                    <el-form-item prop="code">
                        <div style="width:100%;display:flex;justify-content:space-between;align-items:center">
                            <el-input style="width:240px" placeholder="手机验证码" v-model="userModel.code">
                                <template #prefix>
                                    <el-icon :size="20">
                                        <Iphone></Iphone>
                                    </el-icon>
                                </template>
                            </el-input>
                            <el-button style="height:50px" :disabled="timerDisabled" @click="getVerifyCode"> <b
                                    v-if="timerDisabled">{{ timer }}</b> 获取验证码</el-button>
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row justify="center">
                <el-button type="primary" style="width: 100px; height: 30px; margin-top: 20px" @click="onSubmit">提 交
                </el-button>
            </el-row>
        </el-form>
    </el-dialog>
    <el-dialog draggable width="26%" v-model="verifyDialog.show" title="请验证">
        <div style="display:flex;margin-bottom:10px;align-items: center;">
            <el-row>
                <el-col>
                    <el-input placeholder="请输入验证码" v-model="userModel.verifyCodeValue"></el-input>
                </el-col>
            </el-row>
            <el-image :src="verifyCode"></el-image>
            <el-icon @click="getVerifyCode" :size="22" style="cursor:pointer"><RefreshLeft /></el-icon>
        </div>
    </el-dialog>
</template>


<script>
import { } from "@/api/admin/userGroup";
import mitt from "@/utils/mitt";
import { login, getAVerifyCode, loginCode } from '@/api/admin/auth'
import { ElLoading, ElMessage } from 'element-plus'

export default {
    data() {
        return {
            loading: false,
            dialog: { show: false },
            verifyDialog: { show: false },
            userModel: {
                phone:''
            },
            timerDisabled: false,
            timer: 120,
            fullLoading: null,
            verifyCode: "",
            verifyCodeId: "",
            band: null
        };
    },
    methods: {
        getVerifyCode() {
            if (this.userModel.phone.length != 11) {
                ElMessage.error({
                    message: "请输入正确的手机号"
                })
                return
            }
            this.verifyDialog.show = true
            getAVerifyCode().then(res => {
                this.verifyCode = "data:image/png;base64," + res.data.result.verifyCode
                this.verifyCodeId = res.data.result.key
            })
        },
        // 获取验证码
        getLoginCode() {
            this.fullLoading.close()
            let data = {
                phone: this.userModel.phone,
                verifyCodeKey: this.verifyCodeId,
                verifyCodeValue: this.userModel.verifyCodeValue
            }
            loginCode(data).then(res => {
                this.verifyDialog.show = false
                this.timerDisabled = true
                let time = 120
                const daojishi = setInterval(() => {
                    time--
                    this.timer = time
                    if (time == 0) {
                        this.timerDisabled = false
                        clearInterval(daojishi);
                    }
                }, 1000)

            }).catch(err => {
                this.userModel.verifyCodeValue = ''
                this.getVerifyCode()
            })

        },
        onSubmit() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    console.log(valid);
                    localStorage.clear()
                    let data = {
                        grantType: "bind",
                        phone: this.userModel.phone,
                        code: this.userModel.code,
                        openid: this.band,
                        clientId: "wx59c45955021001"
                    }
                    login(data)
                        .then(res => {
                            console.log(res);
                            localStorage.setItem("token", res.data.result.access_token)
					        localStorage.setItem("refreshToken", res.data.result.refresh_token)
                            this.$emit("loginSuccess");
                        }).catch(err => {
                        })
                }
            });
        },
    },
    mounted() {
        mitt.on("openBandAdmin", (res) => {
            this.band = res
            this.dialog.show = true;
            this.dialog.title = "绑定用户";
        });
    },
    watch:{
        "dialog.show"(newVal){
            if (newVal) {
                this.verifyDialog.show = false
            }
        },
        "verifyDialog.show"(newVal){
            if (newVal) this.dialog.show = false
            else this.dialog.show = true
        },
        "userModel.verifyCodeValue"(newVal){
            if (newVal && newVal.length == 4) {
		    this.fullLoading = ElLoading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.7)' , text: '正在获取验证码..请稍后'});
		    this.getLoginCode()
		    this.userModel.verifyCodeValue = ""
	}
        }
    }
};
</script>
<style scoped>
#card {
	width: 25vw;
	height: 40vh;
	margin-left: 57%;
	margin-top: 10%;
}

/deep/ .el-input__inner {
	height: 50px !important;
	font-size: 17px;
}

/deep/ .el-input--small {
	line-height: 50px;
}

/deep/ .el-input__prefix {
	display: flex;
	align-items: center;
}
/deep/ .el-dialog__header {
	background-color: #fff!important;
	box-shadow:none;
	color: #b91a1a;
}
/deep/ .el-dialog__headerbtn:hover {
	background-color: #fff!important;
	box-shadow:none;
	color: #b91a1a!important;
}
/deep/ .el-dialog__close {
	background-color: #fff!important;
	box-shadow:none;
	color: #b91a1a!important;
}
/deep/ .el-dialog__close:hover{
	color: #b91a1a!important;
}
/deep/  .el-dialog__headerbtn:hover{
	color: #b91a1a!important;
}
</style>
