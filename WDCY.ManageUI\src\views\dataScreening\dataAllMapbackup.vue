<template>
  <div style="height: 100%; position: relative">
    <div class="header">
      <div>
        <span @click="addClass(1)" :class="{ is_actice: 1 == current }"
          >小区</span
        >
        <span style="margin: 0 19px">|</span>
        <span @click="addClass(2)" :class="{ is_actice: 2 == current }"
          >小程序用户数量</span
        >
      </div>
      <div style="display: flex" v-if="current == 2">
        <div
          style="
            display: flex;
            align-items: center;
            margin-right: 38px;
            cursor: pointer;
          "
        >
          <span class="green-block"></span>
          在线 {{ online }}
        </div>
        <div style="display: flex; align-items: center; cursor: pointer">
          <span class="gray-block"></span>
          不在线 {{ unOnline }}
        </div>
      </div>
    </div>
    <div id="container"></div>
    <div class="air-view">
      <div
        style="margin-bottom: 12px; height: 16px; font-size: 16px"
        v-for="item in totalList"
        :key="item"
      >
        {{ item.title }} &nbsp;&nbsp;&nbsp;{{ item.total }}
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
import AMapLoader from "@amap/amap-jsapi-loader";
import {
  getMapUserStatus,
  getMapCommuntiyStatus,
  getCommunityTotal,
  getUserGroupTotal,
} from "@/api/dataScreening/dataAllMap.js";
import xiaoqu from "../../assets/img/xiaoqu.png";
import zaixian from "../../assets/img/zaixian.png";
import lixian from "../../assets/img/lixian.png";

export default {
  data() {
    return {
      current: 1,
      totalList: [],
      points: [],
      map: null,
      markers: [],
      online: 0,
      unOnline: 0,
      cluster: null,
      gridSize: 60,
      styles: [],
    };
  },
  methods: {
    async init() {
      await getUserGroupTotal({ nodeType: 4 }).then((res) => {
        this.totalList.push({
          total: res.data.result,
          title: "乡镇",
          icon: "icon-suoshuxiangzhen",
        });
      });
      await getUserGroupTotal({ nodeType: 5 }).then((res) => {
        this.totalList.push({
          total: res.data.result,
          title: "社区",
          icon: "icon-menu_sqgg",
        });
      });
      await getCommunityTotal().then((res) => {
        this.totalList.push({
          total: res.data.result,
          title: "小区",
          icon: "icon-fangzi",
        });
      });
    },
    initMap() {
      window._AMapSecurityConfig = {
        securityJsCode: "bae7a07d95fc5735f8f0cb8a05c224b7",
      };
      AMapLoader.reset();
      AMapLoader.load({
        key: "",
        version: "2.0",
        plugin: ["AMap.MarkerCluster"],
      })
        .then((AMap) => {
          this.map = new AMap.Map("container", {
            resizeEnable: true,
            center: [105, 34],
            zoom: 5,
            viewMode: "3D",
          });
        })
        .catch((e) => {
          console.log(e);
        });
    },
    userMarkerClusterer() {
      var that = this;
      this.map = new AMap.Map("container", {
        resizeEnable: true,
        center: [105, 34],
        zoom: 5,
        viewMode: "3D",
      });
      this.unOnline = 0;
      this.online = 0;
      for (var i = 0; i < this.points.length; i += 1) {
        if (this.points[i].status == 0) {
          this.unOnline += 1;
        } else {
          this.online += 1;
        }
        var content =
          this.points[i].status == 1
            ? `<div style="display:flex;flex-direction: column;"><img style='height:62px;width:39px' src="${zaixian}" alt=""><div style="height:16px;width:150px;font-size:16px;color:green">${this.points[i].userName}</div></div>`
            : `<div><img src="${lixian}" alt=""><div style="height:16px;width:150px;font-size:16px;color:gary">${this.points[i].userName}</div></div>`;
        // this.markers.push(new AMap.Marker({
        //     lnglat: this.points[i]['lnglat'],
        //     content: content,
        //     offset: new AMap.Pixel(-19.5, -31),
        //     title:'lixian'
        // }))
        this.markers.push({
          lnglat: this.points[i]["lnglat"],
          content: content,
        });
        // console.log(this.markers);
      }
      // let that = this
      // AMap.plugin(["AMap.MarkerClusterer"],function(){
      //     that.cluster = new AMap.MarkerClusterer(that.map, that.markers, {
      //         gridSize: 80 // 聚合网格像素大小
      //     });
      // });
      // console.log(that.cluster);

      // new AMap.MarkerClusterer(this.map, this.markers, {gridSize: this.gridSize});
      that.map.plugin(["AMap.MarkerCluster"], function () {
        that.cluster = new AMap.MarkerCluster(that.map, that.markers, {
          gridSize: 80,
          renderMarker: function (object) {
            // let icon = new AMap.Icon({
            // size: new AMap.Size(39, 62), // 图标尺寸
            // image: xiaoqu, // Icon的图像
            // imageSize: new AMap.Size(39, 62), // 根据所设置的大小拉伸或压缩图片
            // content: 'gaga',
            // text: 'gaga',
            // title: 'gaga'
            // });
            // object.marker.setIcon(icon);

            object.marker.setContent(object.data[0].content);
          },
        });
        console.log(that.cluster);
        that.cluster.on("click", (item) => {
          console.log(item);
          if (item.clusterData.length <= 1) {
            return;
          }
          let alllng = 0,
            alllat = 0;
          for (const mo of item.clusterData) {
            alllng += mo.lnglat.lng;
            alllat += mo.lnglat.lat;
          }
          const lat = alllat / item.clusterData.length;
          const lng = alllng / item.clusterData.length;
          that.map.setZoomAndCenter(that.map.getZoom() + 4, [lng, lat]);
        });
        // new AMap.MarkerCluster(that.map, that.markers, {gridSize: 80});
      });
    },
    communityMarkerClusterer() {
      let that = this;
      // window._AMapSecurityConfig = {
      //     securityJsCode: "bae7a07d95fc5735f8f0cb8a05c224b7",
      // };
      AMapLoader.reset();
      AMapLoader.load({
        key: "",
        version: "2.0",
        plugin: ["AMap.MarkerCluster"],
      })
        .then((AMap) => {
          that.map = new AMap.Map("container", {
            resizeEnable: true,
            center: [105, 34],
            zoom: 5,
            viewMode: "3D",
          });
          // this.map = new AMap.Map('container', {
          //     resizeEnable: true,
          //     center: [105, 34],
          //     zoom: 8,
          //     viewMode: '3D'
          // });

          // 给每个点位添加样式
          for (var i = 0; i < this.points.length; i += 1) {
            var content = `<div style="display:flex;flex-direction: column;"><img style='height:62px;width:39px' src="${xiaoqu}" alt=""><div style="height:16px;width:150px;font-size:16px;color:green">${this.points[i].communityName}</div></div>`;
            // this.markers.push(
            //     new AMap.Marker({
            //         lnglat: this.points[i]['lnglat'],
            //         content: content,
            //         offset: new AMap.Pixel(-19.5, -31),
            //         // title: 'lixian'
            //     })
            // )
            // this.styles.push({url:xiaoqu, size:new AMap.Size(39,62), offset: new AMap.Pixel(-19.5, -31)})
            console.log(this.points[i]);

            this.markers.push({
              lnglat: this.points[i]["lnglat"],
              content: content,
            });
          }
          that.map.plugin(["AMap.MarkerCluster"], function () {
            // 添加好样式的点位插入地图内
            that.cluster = new AMap.MarkerCluster(that.map, that.markers, {
              gridSize: 80,
              renderMarker: function (object) {
                // let icon = new AMap.Icon({
                // size: new AMap.Size(39, 62), // 图标尺寸
                // image: xiaoqu, // Icon的图像
                // imageSize: new AMap.Size(39, 62), // 根据所设置的大小拉伸或压缩图片
                // content: 'gaga',
                // text: 'gaga',
                // title: 'gaga'
                // });
                // object.marker.setIcon(icon);

                object.marker.setContent(object.data[0].content);
              },
            });
            console.log(that.cluster);
            that.cluster.on("click", (item) => {
              // console.log(item);
              if (item.clusterData.length <= 1) {
                return;
              }
              let alllng = 0,
                alllat = 0;
              for (const mo of item.clusterData) {
                alllng += mo.lnglat.lng;
                alllat += mo.lnglat.lat;
              }
              const lat = alllat / item.clusterData.length;
              const lng = alllng / item.clusterData.length;
              that.map.setZoomAndCenter(that.map.getZoom() + 2, [lng, lat]);
            });
          });
        })
        .catch((e) => {
          console.log(e);
        });
    },
    // 随机生成经纬度
    replacePos(strObj, pos, replacetext1, replacetext2) {
      return (
        strObj.substr(0, pos - 1) +
        replacetext1 +
        replacetext2 +
        strObj.substring(pos + 1, strObj.length)
      );
    },
    randNum() {
      return Math.floor(Math.random() * 10);
    },
    //经纬度不满10位补
    lnglatPushRandNum(lnglat) {
      if (lnglat.length < 10) {
        for (let i = 0; i < 10 - lnglat.length; i++) {
          lnglat = lnglat.concat(this.randNum());
        }
      }
      return lnglat;
    },
    async addClass(index) {
      let that = this;
      this.current = index;
      if (index == 2) {
        this.points = [];
        this.markers = [];
        await getMapUserStatus().then((res) => {
          let userList = res.data.result;
          let lat = JSON.parse(localStorage.getItem("lnglat"))[0];
          let lng = JSON.parse(localStorage.getItem("lnglat"))[1];
          console.log(userList, res.data.result);
          for (let i = 0; i < userList.length; i++) {
            console.log(userList[i]);
            // 有经纬度添加,没有则随机经纬度
            if (userList[i].lat && userList[i].lng) {
              this.points.push({
                lnglat: [userList[i].lat, userList[i].lng],
                status: userList[i].status,
                userName: userList[i].userName,
              });
            } else {
              userList[i].lat = this.replacePos(
                this.lnglatPushRandNum(String(lat)),
                this.lnglatPushRandNum(String(lat)).length - 3,
                this.randNum(),
                this.randNum()
              );
              userList[i].lng = this.replacePos(
                this.lnglatPushRandNum(String(lng)),
                this.lnglatPushRandNum(String(lng)).length - 3,
                this.randNum(),
                this.randNum()
              );
              if (!userList[i].userName) {
                this.points.push({
                  lnglat: [userList[i].lat, userList[i].lng],
                  status: userList[i].status,
                  userName: "微**户",
                });
              } else {
                this.points.push({
                  lnglat: [userList[i].lat, userList[i].lng],
                  status: userList[i].status,
                  userName: userList[i].userName,
                });
              }
            }
          }
        });
        console.log(this.points);
        this.userMarkerClusterer();
      } else if (index == 1) {
        this.points = [];
        this.markers = [];
        await getMapCommuntiyStatus().then((res) => {
          let communityList = res.data.result;
          let lat = JSON.parse(localStorage.getItem("lnglat"))[0];
          let lng = JSON.parse(localStorage.getItem("lnglat"))[1];
          for (let i = 0; i < communityList.length; i++) {
            // 有经纬度直接添加，否则随机生成
            if (communityList[i].lat && communityList[i].lng) {
              this.points.push({
                lnglat: [communityList[i].lng, communityList[i].lat],
                communityName: communityList[i].communityName,
              });
            } else {
              communityList[i].lat = this.replacePos(
                this.lnglatPushRandNum(String(lat)),
                this.lnglatPushRandNum(String(lat)).length - 3,
                this.randNum(),
                this.randNum()
              );
              communityList[i].lng = this.replacePos(
                this.lnglatPushRandNum(String(lng)),
                this.lnglatPushRandNum(String(lng)).length - 3,
                this.randNum(),
                this.randNum()
              );
              this.points.push({
                lnglat: [communityList[i].lat, communityList[i].lng],
                communityName: communityList[i].communityName,
              });
            }
          }
          // console.log(this.points);
        });
        this.communityMarkerClusterer();
      }
    },
  },
  mounted() {
    this.init();
    this.addClass(1);
  },
};
</script>
<style scoped lang="less">
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
#container {
  width: 100%;
  height: 100%;
  width: 100%;
  border-radius: 8px;
}
.header {
  display: flex;
  justify-content: space-between;
  height: 82px;
  background-color: #fff;
  line-height: 82px;
  padding: 0 46px;
  border-radius: 8px;
  position: absolute;
  width: 100%;
  z-index: 99;
  > div {
    > span {
      cursor: pointer;
    }
  }
}
.is_actice {
  color: #5097ff;
}
.air-view {
  width: 151px;
  height: 115px;
  border-radius: 8px;
  padding: 22px 62px 21px 19px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0px 2px 4px #000;
  position: absolute;
  right: 64px;
  top: 94px;
}
.green-block {
  width: 28px;
  height: 16px;
  display: inline-block;
  background-color: green;
  margin-right: 9px;
  border-radius: 2px;
}
.gray-block {
  width: 28px;
  height: 16px;
  display: inline-block;
  background-color: gray;
  margin-right: 9px;
  border-radius: 2px;
}
</style>