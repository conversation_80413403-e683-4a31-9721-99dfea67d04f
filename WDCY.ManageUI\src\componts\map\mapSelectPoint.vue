<template>
  <el-dialog
    draggable
    width="50%"
    :append-to-body="false"
    destroy-on-close
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form :rules="rules" :model="personModel" label-width="80px">
      <el-row>
        <div id="container"></div>
        <div v-show="isEditMode" id="search">
          <el-select
            @change="selectChange"
            style="width: 100%"
            v-model="value"
            filterable
            remote
            reserve-keyword
            placeholder="点位搜索"
            :remote-method="remoteMethod"
          >
            <el-option
              v-for="item in poiList"
              :key="item.id"
              :label="item.name + '-' + item.district"
              :value="item.id"
            />
          </el-select>
        </div>
        <div style="width: 100%; margin-top: 20px">
          <el-radio-group
            @change="change3D"
            v-model="status"
            style="float: right"
          >
            <el-radio-button :key="1" label="1">开启卫星地图</el-radio-button>
            <el-radio-button :key="0" label="0">关闭卫星地图</el-radio-button>
          </el-radio-group>
        </div>
      </el-row>
    </el-form>
    <el-row v-show="isEditMode" justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        >提 交
      </el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import mitt from "@/utils/mitt";
//引入cesium基础库
// import * as Cesium from "mars3d-cesium";
//导入mars3d主库
// import * as mars3d from "mars3d";
//导入mars3d插件（按需使用，需要先npm install）
// import "mars3d-space";

export default {
  emits: ["addressCall", "point"],
  data() {
    return {
      loading: false,
      isEditMode: true, //是否编辑模式
      value: null,
      map: null,
      aMap: null,
      marker: null,
      autoComplete: null,
      geocoder: null,
      dialog: {},
      poiList: [],
      lnglat: [],
      center: [],
      markerAddress: null,
      markerTitle: "Hi,World!",
      status: true,
    };
  },
  methods: {
    change3D(e) {
      if (e == 0) {
        this.value = "";
        this.poiList = [];
        let that = this;
        window._AMapSecurityConfig = {
          securityJsCode: "bae7a07d95fc5735f8f0cb8a05c224b7",
        };
        AMapLoader.reset();
        AMapLoader.load({
          version: "2.0",
          key: "",
        })
          .then((AMap) => {
            this.aMap = AMap;
            AMap.plugin("AMap.Autocomplete", function () {
              that.autoComplete = new AMap.Autocomplete({
                city: "全国",
              });
            });
            AMap.plugin("AMap.Geocoder", function () {
              that.geocoder = new AMap.Geocoder({
                city: "全国",
              });
            });
            this.map = new AMap.Map("container", {
              //设置地图容器id
              viewMode: "3D", //是否为3D地图模式
              expandZoomRange: true,
              zoom: 17, //初始化地图级别
              zooms: [3, 20],
              resizeEnable: true,
              pitch: 35,
              mapStyle: "amap://styles/blue",
              center: this.center, //初始化地图中心点位置
            });
            this.map
              .getLayers()
              .find((v) => v.CLASS_NAME == "AMap.SkyLayer")
              .hide();

            if (this.lnglat) {
              this.addMarker();
            }
            console.info("initMap.AMapLoader.load");
            //为地图注册click事件获取鼠标点击出的经纬度坐标
            if (this.isEditMode) {
              this.map.on("click", function (e) {
                console.log("map.click:", e);
                that.lnglat = [e.lnglat.getLng(), e.lnglat.getLat()];
                that.setMarkerInfo();
              });
            }
          })
          .catch((e) => {
            console.log(e);
          });
      } else this.initMap();
    },
    selectChange() {
      for (let item of this.poiList) {
        if (item.id == this.value) {
          console.log("selectChange:", item);
          if (item.location == "") {
            this.$message("请选择具体点位");
            return;
          }
          this.lnglat = [item.location.lng, item.location.lat];
          this.map.setCenter([item.location.lng, item.location.lat]);
          this.setMarkerInfo();
        }
      }
    },
    remoteMethod(keyword) {
      let that = this;
      console.log(keyword);
      this.autoComplete.search(keyword, function (status, result) {
        console.log(result);
        that.poiList = result.tips;
      });
    },
    onSubmit() {
      // let that = this;
      if (!this.lnglat || this.lnglat.length == 0) {
        this.$message("未选择点位");
      }
      this.$emit("addressCall", this.markerAddress);

      this.$emit("point", this.lnglat);
      this.dialog.show = false;
    },
    // 初始地图
    initMap() {
      this.value = "";
      this.poiList = [];
      let that = this;
      window._AMapSecurityConfig = {
        securityJsCode: "bae7a07d95fc5735f8f0cb8a05c224b7",
      };
      AMapLoader.reset();
      AMapLoader.load({
        version: "2.0",
        key: "",
      })
        .then((AMap) => {
          this.aMap = AMap;
          AMap.plugin("AMap.Autocomplete", function () {
            that.autoComplete = new AMap.Autocomplete({
              city: "全国",
            });
          });
          AMap.plugin("AMap.Geocoder", function () {
            that.geocoder = new AMap.Geocoder({
              city: "全国",
            });
          });
          this.map = new AMap.Map("container", {
            //设置地图容器id
            viewMode: "3D", //是否为3D地图模式
            expandZoomRange: true,
            zoom: 17, //初始化地图级别
            zooms: [3, 20],
            resizeEnable: true,
            pitch: 35,
            layers: [
              // 卫星
              new AMap.TileLayer.Satellite({
                color: "rgba(0,0,255,0.5)",
              }),
              // 路网
              new AMap.TileLayer.RoadNet(),
            ],
            mapStyle: "amap://styles/blue",
            center: this.center, //初始化地图中心点位置
          });

          if (this.lnglat) {
            this.addMarker();
          }
          console.info("initMap.AMapLoader.load");
          //为地图注册click事件获取鼠标点击出的经纬度坐标
          if (this.isEditMode) {
            this.map.on("click", function (e) {
              console.log("map.click:", e);
              that.lnglat = [e.lnglat.getLng(), e.lnglat.getLat()];
              that.setMarkerInfo();
            });
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    //获取地址及创建锚点
    setMarkerInfo() {
      let that = this;
      // 逆地理编码
      this.geocoder.getAddress(this.lnglat, function (status, result) {
        if (status === "complete" && result.info === "OK") {
          that.markerAddress = result;
          that.markerTitle = result.regeocode.formattedAddress;
          console.info("geocoder.getAddress:", result, that.markerTitle);
          that.clearMarker();
          that.addMarker();
        }
      });
    },
    // 实例化点标记
    addMarker() {
      this.marker = new AMap.Marker({
        position: this.lnglat,
        // offset: new AMap.Pixel(-13, -30),
        // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png"
      });
      // // 自定义点标记内容
      // var markerContent = document.createElement("div");

      // // 点标记中的图标
      // var markerImg = document.createElement("img");
      // markerImg.className = "markerlnglat";
      // markerImg.src =
      //   "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png";
      // markerImg.setAttribute("width", "25px");
      // markerImg.setAttribute("height", "34px");
      // markerContent.appendChild(markerImg);

      // // 点标记中的文本
      // var markerSpan = document.createElement("span");
      // markerSpan.className = "marker";
      // markerSpan.innerHTML = this.markerTitle;
      // markerContent.appendChild(markerSpan);

      // this.marker.setContent(markerContent); //更新点标记内容
      // this.marker.setTitle(this.markerTitle);
      this.marker.setLabel({
        direction: "right",
        offset: new AMap.Pixel(10, 0), //设置文本标注偏移量
        content: this.markerTitle, //设置文本标注内容
      });
      // 将创建的点标记添加到已有的地图实例
      this.marker.setMap(this.map);
      // this.map.add(this.marker);
      console.info("addMarker:", this.markerTitle);
    },
    // 清除 marker
    clearMarker() {
      if (this.marker) {
        this.marker.setMap(null);
        this.marker = null;
      }
    },
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openMap", ({ editMode, center, anchor, anchorName }) => {
        (this.status = 1), (this.lnglat = anchor);
        if (!anchor || anchor.length < 2 || !anchor[0] || !anchor[1]) {
          this.lnglat = null;
        }

        this.center = this.lnglat || center;
        this.markerTitle = anchorName;
        this.isEditMode = editMode;
        this.initMap();
        this.dialog.title = editMode ? "地图选点" : "查看点位";
        this.dialog.show = true;
        console.info("mitt.on.openMap", this.center, this.lnglat);
      });
    });
  },
};
</script>

<style scoped>
#container {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 550px;
  z-index: 1;
  border-radius: 5px;
}

div /deep/ #search {
  position: absolute;
  left: 35%;
  top: 5%;
  z-index: 9999;
  width: 30%;
}
</style>
<style>
.amap-marker-label {
  position: absolute;
  top: -20px;
  color: #fff;
  padding: 4px 10px;
  box-shadow: 1px 1px 1px rgb(10 10 10 / 20%);
  white-space: nowrap;
  font-size: 12px;
  font-family: "";
  background-color: #25a5f7;
  border-radius: 3px;
}
</style>
