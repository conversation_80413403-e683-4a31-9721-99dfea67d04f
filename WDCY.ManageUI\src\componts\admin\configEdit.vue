<template>
	<el-dialog draggable width="40%" destroy-on-close v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="configModel" label-width="120px">
			<el-form-item label="键" prop="key">
				<el-input :readonly="configModel.id != 0" v-model="configModel.key" placeholder="键"></el-input>
			</el-form-item>

			<el-form-item label="值" prop="value">
				<el-input v-model="configModel.value" placeholder="值"></el-input>
			</el-form-item>

			<el-form-item label="状态" prop="status">
				<el-radio-group v-model="configModel.status">
					<el-radio v-for="item in statusList" :key="item.nameEn" :label="parseInt(item.nameEn)"> {{item.nameCn}} </el-radio>
				</el-radio-group>
			</el-form-item>
			
			<el-form-item label="备注" prop="remark">
				<el-input
					v-model="configModel.remark"
					maxlength="200"
					placeholder="请解释配置项代表的意思"
					show-word-limit
					type="textarea"
				/>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<!-- <el-button @click="dialogVisible = false">Cancel</el-button> -->
				<el-button type="primary" @click="onSubmit">
					提 交
				</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script>
import { editConfig, addConfig } from "@/api/admin/config";
import mitt from "@/utils/mitt";

export default {
	props: ['statusList'],
	data() {
		return {
			loading: false,
			configModel: {},
			dialog: {},
			rules: {
				key: [{
					required: true,
					message: '请输入键',
					trigger: 'blur',
				}],
				value: [{
					required: true,
					message: '请输入值',
					trigger: 'blur',
				}]
			}
		}
	},
	methods: {
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.configModel.id == 0) {
						addConfig(this.configModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						editConfig(this.configModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		}
	},
	mounted() {
		mitt.on('openConfigEdit', (config) => {
			this.configModel = config
			this.dialog.show = true
			this.dialog.title = "修改信息"
		})
		mitt.on('openConfigAdd', (id) => {
			this.configModel = {
				id: 0,
				parentId: id,
				status: 1
			}
			this.dialog.show = true
			this.dialog.title = "添加菜单"
		})
	}
}
</script>
