<template>
	<device-type-edit :icoList="icoList" :statusList="statusList" :tagList="tagList" @search="search"></device-type-edit>
	<device-action-edit :icoList="icoList" :statusList="statusList" :tagList="tagList" @search="search"></device-action-edit>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.name" @keydown.enter="search" placeholder="类型名称" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="12">
			<el-button style="float: right;" type="primary" @click="add(0)" v-if="hasPerm('device:type:add')">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table :data="deviceTypeList" row-key="id" border style="width: 100%">
				<el-table-column prop="name" header-align="center" label="类型名称" />
				<el-table-column prop="code"  align="center" label="类型代号" />
				<el-table-column label="图标" align="center" prop="icoUrl" width="75">
                    <template #default="scope">
                        <el-image style="width:45px;height:45px;background: #aaa;" :src="imgServer+formatImg(scope.row.icoInfoId)" fit="contain"></el-image>
                    </template>
                </el-table-column>
				<el-table-column prop="alertType" :formatter="formatStatus" align="center" label="警报类别" />
				<el-table-column prop="extendParamsTpl" show-overflow-tooltip align="center" label="运行参数模板" />
				<el-table-column prop="sort" width="80" align="center" label="排序" />
				<el-table-column align="center" label="操作"  v-if="hasPerm('device:type:update') || hasPerm('device:type:delete') || hasPerm('device:type:add')">
					<template #default="scope">
						<el-button type="text" size="default" @click="action(scope.row.id)">动作</el-button>
						<el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('device:type:update')">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('device:type:delete')">删除</el-button>
						<el-button type="text" size="default" @click="add(scope.row.id)" v-if="hasPerm('device:type:add')">添加子级</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
	
</template>
<script>
import { deviceTypeList,deviceTypeListDelete,getDeviceType} from "@/api/device/device"
import {warnDeviceActionList} from "@/api/warn/warn"
import { listIcon } from "@/api/base/icon"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import deviceTypeEdit from "@/componts/device/deviceTypeEdit.vue"
import deviceActionEdit from "@/componts/device/deviceActionEdit.vue"
export default {
	components:{ deviceTypeEdit, deviceActionEdit },
	data() {
		return {
            imgServer: import.meta.env.VITE_BASE_API,
			searchModel: {},
			deviceTypeList: [],
			communityId:localStorage.getItem("communityId"),
			statusList:[],
			tagList:[],
			icoList:[],
			total:0,
			pageSize:10
		}
	},
	methods: {
		search() {
			this.searchModel.communityId=this.communityId
			this.searchModel.type = 0
			deviceTypeList(this.searchModel)
			.then(res => {
				this.deviceTypeList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		// 动作
		action(){
			mitt.emit('openActionEdit')
		},
		edit(id){
			getDeviceType(id)
			.then(res =>{
				mitt.emit('openDeviceTypeEdit',res.data.result)
			})
		},
		// warnAction(id){
		// 	warnDeviceActionList({devTypeId:id})
		// 	.then(res =>{
		// 		mitt.emit('openWarnActionEdit',{data:res.data.result.list,devTypeId:id})
		// 	})
		// },
		add(id){
			mitt.emit('openDeviceTypeAdd',id)
		},
		deleted(id){
			this.$confirm('删除设施类型, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deviceTypeListDelete(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		formatStatus(row, column, cellValue, index){
			let result = ''
			for(let item of this.statusList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		formatImg(icoId){
            let result = "";
            this.icoList.forEach(element => {
                if (element.id == icoId) {
                    result = element.icoUrl
                }
            })
            return result;
        },
		async init(){
			mitt.off('openDeviceTypeEdit')
			mitt.off('openDeviceTypeAdd')
			try{
				this.searchModel.communityId=this.communityId
				this.searchModel.type = 0
				let res = await deviceTypeList(this.searchModel)
				this.deviceTypeList = res.data.result.list
				this.total = res.data.result.total
				let resTypeList = await listDictByNameEn('alert_type')
				this.statusList = resTypeList.data.result
				let icoList = await listIcon({pageSize:9999,icoCategory:'ico_dev'})
                this.icoList = icoList.data.result.list
			}catch(err){
			}
		}
	},
	created() {
		this.init();
		this.deviceTypeList = [
		{
			id: 1,
			groupName: "测试数据1",
		},
		{
			id: 2,
			groupName: "测试数据2",
			children: [
			{
				id: 21,
				groupName: "测试数据21",
			}],
		}];
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	div /deep/ .el-table__placeholder{
		height: 0px;
		width: 0px;
	}
</style>
