<template>
	<el-dialog draggable width="60%" v-loading="loading" v-model="dialog.show" :title="dialog.title" append-to-body>
		<el-row>
			<room-edit :roomTypeList="roomTypeList" :roomStatusList="roomStatusList" @search="search"></room-edit>
			<el-col :span="5">
			  <el-button-group v-for="item in buildingDetailModel.unitList" :key="item.id" style=" width: 100%;margin-bottom: 10px;">
				<el-button style="border: 0;width: 70%;" type="primary" @click="choiceUnit(item.id)" v-if="hasPerm('base:unit:query')">{{item.unitNumber}}</el-button>
				<el-button style="border: 0;padding: 0 8px;width: 15%;" type="primary" @click="toEditUnit(item.id,item.unitNumber)"  v-if="hasPerm('base:unit:edit')">
					<el-icon :size="20">
					  <edit></edit>
					</el-icon>
				</el-button>
				<el-button style="border: 0;padding: 0 8px;width: 15%;" type="primary" @click="toDeleteUnit(item.id)" v-if="hasPerm('base:unit:delete')">
					<el-icon :size="20">
					  <delete></delete>
					</el-icon>
				</el-button>
			  </el-button-group>
			  <el-button style="width: 100%;" @click="toAddUnit" v-if="hasPerm('base:unit:add')">添加单元</el-button>
			</el-col>
			<el-col :push="1" :span="18">
				<el-row :gutter="20">
					<el-table stripe :data="buildingDetailModel.roomPageInfo.list" border style="width: 100%" v-if="hasPerm('base:room:query')">
						<el-table-column prop="roomNumber" align="center" label="房间编号" />
						<el-table-column prop="floor" align="center" label="楼层" />
						<el-table-column prop="type" align="center" :formatter="formatRoomType" label="户型" />
						<el-table-column prop="acreage" align="center" label="面积" />
						<el-table-column prop="isGroupOrientedLeasing" align="center" label="状态">
							<template #default="scope">
								<!-- <el-tag style="margin-right: 10px;" size="default" :type="scope.row.isGroupOrientedLeasing?'success':''">{{scope.row.isGroupOrientedLeasing?'是':'否'}}</el-tag> -->
								<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(roomStatusList, scope.row.status) }}</el-tag>
							</template>
						</el-table-column>
						<el-table-column align="center" width="230" label="操作">
							<template #default="scope">
								<el-button type="primary" @click="toViewRoom(scope.row.id)" v-if="hasPerm('base:room:edit')">
									<i class="iconfont icon-yanjing_xianshi" style="font-size:24px"></i>
								</el-button>
								<el-button type="primary" @click="toEditRoom(scope.row.id)" v-if="hasPerm('base:room:edit')">
									<el-icon :size="20">
									  <edit></edit>
									</el-icon>
								</el-button>
								<el-button type="primary" @click="toDeleteRoom(scope.row.id)" v-if="hasPerm('base:room:delete')" >
									<el-icon :size="20">
									  <delete></delete>
									</el-icon>
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</el-row>
				<el-row>
					<el-col :span="12"><el-button v-show="roomModel.unitId" @click="toAddRoom" v-if="hasPerm('base:room:add')">添加房间</el-button></el-col>
					<el-col style="display: flex;justify-content: flex-end;" :span="12">
						<el-pagination background v-model:page-size="searchModel.pageSize" :current-page='currentPage' :page-sizes="[10, 20, 50, 100]"
						layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange" @size-change="handleSizeChange"
						:total="Number(buildingDetailModel.roomPageInfo.total)"></el-pagination>
					</el-col>
				</el-row>
			</el-col>
		</el-row>
	</el-dialog>
</template>
<script setup>
import { Edit,Delete } from '@element-plus/icons-vue'
</script>
<script>
import { editBuilding,addBuilding,detailBuilding } from "@/api/base/building"
import roomEdit from "@/componts/base/roomEdit.vue"
import { listDictByNameEn } from "@/api/admin/dict"
import { addUnit,editUnit,deleteUnit } from "@/api/base/unit"
import { getRoom,deleteRoom } from "@/api/base/room"
import { getDictCss, formatDict } from "@/utils/dict"
import mitt from "@/utils/mitt";
export default {
	components:{ roomEdit },
	data() {
		return {
			loading: false,
			activeName:'first',
			roomTypeList:[],
			roomStatusList:[],
			searchModel:{},
			roomModel:{},
			buildingDetailModel: {},
			dialog:{},
			pageSize:10,
			currentPage: 1
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		onSubmit(){
		
		},
		search(){
			detailBuilding(this.searchModel)
			.then(res =>{
				this.buildingDetailModel = res.data.result
			})
		},
		toDeleteRoom(id){
			this.$confirm('删除房间, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteRoom(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		toEditRoom(id){
			getRoom(id)
			.then(res =>{
				mitt.emit('openRoomEdit',res.data.result)
			})
			.catch(err =>{
				this.$message.error(err)
			})
		},
		toViewRoom(id){
			getRoom(id)
			.then(res =>{
				mitt.emit('openRoomView',res.data.result)
			})
			.catch(err =>{
				this.$message.error(err)
			})
		},
		toAddRoom(){
			mitt.emit('openRoomAdd',this.roomModel)
		},
		choiceUnit(id){
			this.roomModel.unitId = id
			this.searchModel.unitId = id
			this.search()
		},
		toDeleteUnit(id){
			this.$confirm('删除单元, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then((res) => {
					deleteUnit(id)
					.then(res =>{
						this.search()
						this.roomModel.unitId = null
						this.searchModel.unitId = null
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		toEditUnit(id,unitNumber){
			this.$prompt('请输入单元编号', '修改', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  inputValue:unitNumber
				}).then((res) => {
					let val = res.value
					if(val == null || val == '' || val.trim() == ''){
						this.$message("单元编号为空")
						return
					}
					let unit = {
						id:id,
						unitNumber:val,
						buildingId:this.buildingDetailModel.id,
						communityId:this.buildingDetailModel.communityId
					}
					editUnit(unit)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		toAddUnit(){
			this.$prompt('请输入单元编号', '添加', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消'
				}).then((res) => {
					let val = res.value
					if(val == null || val == '' || val.trim() == ''){
						this.$message("单元编号为空")
						return
					}
					let unit = {
						unitNumber:val,
						buildingId:this.buildingDetailModel.id,
						communityId:this.buildingDetailModel.communityId
					}
					addUnit(unit)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.currentPage = num
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			console.log(num);
			this.currentPage = num
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			console.log(num);
			this.currentPage = num
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			console.log(num);
			this.currentPage = num
			this.searchModel.pageSize = num
			this.search()
		},
		formatRoomType(row, column, cellValue, index){
			let result = ''
			for(let item of this.roomTypeList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		async init(){
			
			try{
				let room_res = await listDictByNameEn('room_type')
				this.roomTypeList = room_res.data.result
				let room_status = await listDictByNameEn("room_status")
				this.roomStatusList = room_status.data.result
			}catch(err){
				
			}
		}
	},
	mounted(){
		this.$nextTick(function() {
			mitt.on('openBuildingDetail', (detail) => {
				console.log(this.currentPage,'123');
				this.currentPage = 1
				this.init()
				this.searchModel = {
					id:detail.id
				}
				this.buildingDetailModel = detail
				this.roomModel = {}
				if(this.buildingDetailModel.unitList.length>0){
					this.roomModel.unitId = this.buildingDetailModel.unitList[0].id
				}
				this.roomModel.id = 0
				this.roomModel.buildingId = detail.id,
				this.roomModel.communityId = detail.communityId
				this.dialog.show = true
				this.dialog.title = detail.buildingNumber
			})
		})
	},
	created() {
		mitt.off('openRoomAdd')
		mitt.off('openRoomEdit')
		mitt.off('openRoomView')
	}
}
</script>

<style scoped="scoped">
	.el-row {
		margin-bottom: 20px;
	}
	.el-dialog__body{
		height: 500px !important;
	}
</style>
