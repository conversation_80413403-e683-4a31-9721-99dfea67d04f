<template>
	<icon-edit :statusList="statusList" :typeList="typeList" :parkingList="parkingList" @search="search"></icon-edit>
	<input type="file" id="btn_file" @change="filechange" style="display:none">
	<el-row :gutter="20">
		<el-col :span="3">
			<el-input  v-model="searchModel.icoName" @keydown.enter="search" placeholder="名称关键字" clearable />
		</el-col>
		<el-col :span="3">
			<el-input  v-model="searchModel.icoCode" @keydown.enter="search" placeholder="代号关键字" clearable />
		</el-col>
		<el-col :span="3">
			<el-input  v-model="searchModel.note" @keydown.enter="search" placeholder="描述关键字" clearable />
		</el-col>
		<el-col :span="3">
			<el-select  v-model="searchModel.icoCategory" placeholder="图标分类" clearable>
				<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
			</el-select>
		</el-col>
		<!-- <el-col :span="2">
			<el-select  v-model="searchModel.status" placeholder="状态" clearable>
				<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
					:value="parseInt(item.nameEn)"></el-option>
			</el-select>
		</el-col> -->
		<el-col :span="2">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="2" :push="8">
			<el-button style="float: right;" type="primary" @click="add">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="iconList" border height="calc(100vh - 268px)" style="width: 100%">
				<el-table-column label="图标" align="center" prop="icoUrl" width="75">
                    <template #default="scope">
                        <el-image style="'width:45px;height:45px;background:rgba(136, 186, 255,.3);" :src="imgServer + scope.row.icoUrl" fit="contain"></el-image>
                    </template>
                </el-table-column>
				<el-table-column prop="id" align="center" label="图标ID" width="68"/>
				<el-table-column prop="icoName" align="center" label="图标名称"/>
				<el-table-column prop="icoCode" align="center" label="图标代号"/>
				<el-table-column prop="icoCategory" :formatter="formatType" align="center" label="图标分类" />
				<el-table-column prop="status" align="center" label="状态" width="95">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="createTime"  align="center" label="创建时间" width="168" />
				<el-table-column prop="updateTime"  align="center" label="更新时间" width="168" />
				<el-table-column prop="note" align="center" label="描述"/>
				<el-table-column align="center" width="100" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row.id)">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('base:vehicle:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
	</el-row>
</template>

<script>
import { listIcon,deleteIcon,getIcon,searchListIcon } from "@/api/base/icon"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import iconEdit from "@/componts/base/iconEdit.vue"

export default {
	components:{ iconEdit },
	data() {
		return {
			searchModel: {},
			iconList: [],
			statusList:[],
			typeList:[],
			parkingList:[],
			total:0,
			imgServer: import.meta.env.VITE_BASE_API,
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatType(row, column, cellValue, index) {
			return formatDict(this.typeList, cellValue)
		},
		search() {
			searchListIcon(this.searchModel)
			.then(res => {
				this.iconList = res.data.result
				this.total = res.data.result.total
			})
		},
		edit(id){
			getIcon(id)
			.then(res =>{
				mitt.emit('openIconEdit',res.data.result)
			})
		},
		add(){
			mitt.emit('openIconAdd')
		},
		deleted(id){
			this.$confirm('删除图标, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteIcon(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			mitt.off('openVehicleEdit')
			mitt.off('openVehicleAdd')
			try{
				let res = await searchListIcon(this.searchModel)
				this.iconList = res.data.result
				this.total = res.data.result.total
				let vehicle_status = await listDictByNameEn('common_status')
				this.statusList = vehicle_status.data.result
				let ico_type = await listDictByNameEn('ico_category')
				this.typeList = ico_type.data.result
				// let parking_type = await listDictByNameEn('parking_type')
				// this.parkingList = parking_type.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	.errMsg{
	width: 1056px;
	height: 534px;
	/* overflow-x: auto; */
	background-color: #fff;
	/* position: relative; */
	color: #666666;
	font-size: 14px;
	box-sizing: border-box;
}
.err_msg_title{
	padding-top: 18px;
	padding-bottom: 24px;
	padding-left: 19px;
}
.errMsgMain{
	height: 456px;
	overflow-x: auto;
}
.blue_icon {
	height: 16px;
	width: 4px;
	background-color: #4B96FF;
	display: inline-block;
	border-radius: 1px;
}
.everyErrMsg{
	height: 38px;
	width: 1019px;
	line-height: 38px;
	/* margin-bottom: 10px; */
	white-space: nowrap; 
	text-overflow: ellipsis;
	overflow: hidden;
	padding: 0 19px;
	cursor: pointer;
	user-select:none
}
.everyErrMsg:nth-of-type(2n+1){
	background-color: rgba(80, 158, 255,.1);
}
.nothing{
	text-align: center;
	display: block;
	margin: auto;
	/* margin-bottom: 140px; */
}
/deep/ .diaLogClass{
	height: 780px;
	width: 1124px;
	background-color: rgb(241, 245, 255);
	overflow-x: auto;
	position: relative;
	border-radius: 16px;
}
/deep/ .el-dialog__body{
	padding: 0 34px;
}
.importBox{
	background-color: #fff;
	width: 1056px;
	line-height: 48px;
	border-radius: 6px;
	margin: 27px auto 5px;
	font-size: 14px;
}
.importText{
	padding-left: 32px;
}
.button_box{
	margin: 32px auto 36px;
	width: 237px;
}
.button_box > button{
	width: 112px;
	height: 40px;
}
.button_box > button:nth-child(1){
	background-color: #3694FF;
	color: #fff;
}
.button_box > button:nth-child(2){
	color: #3694FF;
	border: 1px solid#3694FF;
}
	/* 隐藏滚动条 */
	::-webkit-scrollbar{
	display: none;
	}
</style>
