<template>
  <el-dialog
    draggable
    width="50%"
    destroy-on-close
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
    :close-on-click-modal="false"
  >
    <el-form
      :rules="rules"
      ref="form1"
      :model="governanceResponsibilityModel"
      label-width="100px"
    >
      <el-row>
        <el-col :span="9">
          <el-form-item label="姓名" prop="name">
            <el-select
              style="width: 100%"
              v-model="governanceResponsibilityModel.name"
              placeholder="姓名"
              @change="personHandleChange"
              filterable
              clearable
            >
              <el-option
                v-for="item in governanceResponsibilityModelList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
                <span style="float: left">{{ item.name }}</span>
                <span
                  style="
                    float: right;
                    color: var(--el-text-color-secondary);
                    font-size: 13px;
                  "
                  >{{ item.idCard }}</span
                >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-button
          style="float: right; margin-left: 10px"
          type="primary"
          v-if="submitType=='event'"
          @click="grvernanceEntry"
          >快捷录入</el-button
        >
        </el-col>

        <el-col :span="10">
          <el-form-item label="电话" prop="phone">
            <el-input
              disabled
              v-model="governanceResponsibilityModel.phone"
              placeholder="电话"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="9">
          <el-form-item label="证件类型" prop="certificateType">
            <el-select
              disabled
              style="width: 100%"
              v-model="governanceResponsibilityModel.certificateType"
              placeholder="证件类型"
              filterable
              clearable
            >
              <el-option
                v-for="item in certificateTypeList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="item.nameEn"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :push="3" style="float: right;">
          <el-form-item label="证件号" prop="idCard">
            <el-input
              disabled
              v-model="governanceResponsibilityModel.idCard"
              placeholder="证件号"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="23">
          <el-form-item v-if="submitType=='event'" label="职责描述" prop="dutyNote">
            <el-input
              v-model="governanceResponsibilityModel.dutyNote"
              maxlength="50"
              placeholder="请简单描述职责"
              show-word-limit
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        :disabled="isDisabled"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>

<script>
import mitt from "@/utils/mitt";
import { listDictByNameEn } from "@/api/admin/dict";
import JsonEditorVue from "json-editor-vue3";
import { queryChildrenNodeBuilding } from "@/api/base/building";
import { queryByIdCardOrPhone } from "@/api/base/person";

export default {
  components: { JsonEditorVue },
  props: [],
  data() {
    return {
      jsonVal: {},
      governanceResponsibilityModel: {},
      certificateTypeList: [],
      dialog: {},
      governanceResponsibilityModelList: [],
      tableIndex: null,
      responsibleId: null,
      isDisabled: false,
      rules: {
        name: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "change",
          },
        ],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          {
            required: true,
            pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
          },
        ],
        idCard: [
          {
            required: true,
            message: "身份证号码不能为空",
            trigger: "blur"
          }, {
            validator: (rule, value, callback) => {
            if (!value) {
              return callback(new Error('身份证不能为空'))
            }
            if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)) {
              callback(new Error('请输入正确的二代身份证号码'))
            } else {
              callback()
            }
          },
            trigger: 'blur'
          }, 
        ]
      },
      submitType:""
    };
  },
  methods: {
    personHandleChange(e) {
      this.governanceResponsibilityModelList.some(item=>{
        if (item.id == e) {
          this.governanceResponsibilityModel=JSON.parse(JSON.stringify(item))
        }
      })
    },
    onSubmit() {
      if(this.isDisabled){
        return;
      }
      this.isDisabled=true;
      this.$refs["form1"].validate((valid) => {
        if (valid) {
          if (this.dialog.title == "添加经办人员") {
            this.governanceResponsibilityModel.id=null;
            if (this.submitType == 'trace') {
              this.$emit(
                "searchTraceResponsibility",
                this.governanceResponsibilityModel
              );
            }
            if (this.submitType == 'event') {
              console.log('event',this.governanceResponsibilityModel);
              this.$emit(
                "searchResponsibility",
                this.governanceResponsibilityModel
              );
            }
            this.dialog.show = false;
          } else {
            this.governanceResponsibilityModel.id = this.responsibleId
            const params = {
              "data": this.governanceResponsibilityModel,
              "tableIndex": this.tableIndex
            }
            if (this.submitType == 'event') {
              this.$emit("searchResponsibility1", params);

            }
            if (this.submitType == 'trace') {
              this.$emit("searchTraceResponsibility1", params);

            }
            this.dialog.show = false;
          }
        }
      });
      setTimeout(() => {
        this.isDisabled = false; // 100ms后再次启用按钮（根据需求调整时间）
      }, 200);
    },
    searchResponsibility() {
      this.$emit("searchResponsibility");
    },
    async init() {
      try {
        let certificate_res = await listDictByNameEn("certificate_type");
        this.certificateTypeList = certificate_res.data.result;
        let governanceResponsibilityModelList_res = await queryByIdCardOrPhone({
          tags: "handled_by",
        });
        this.governanceResponsibilityModelList =
          governanceResponsibilityModelList_res.data.result;
      } catch (err) {}
    },
    // 快捷录入
    grvernanceEntry() {
      let communityId = localStorage.getItem("communityId");
      queryChildrenNodeBuilding({ communityId: communityId }).then((res) => {
        mitt.emit("quickEntry", {
          buildingList: res.data.result,
          communityId: communityId,
        });
      });
    },
  },

  mounted() {
    this.jsonVal = {};
    this.$nextTick(function () {
      // mitt.off("openGovernanceResponsibilityAdd")
      // mitt.off("openGovernanceResponsibilityEdit")
      mitt.on(
        "openGovernanceResponsibilityEdit",
        (data) => {
          this.tableIndex = data.index
          this.submitType = data.type
          this.governanceResponsibilityModel = JSON.parse(JSON.stringify(data.row));
          this.init();
          this.dialog.show = true;
          this.dialog.title = "修改经办人员";
          this.responsibleId = data.row.id
        }
      );
      mitt.on("openGovernanceResponsibilityAdd", (params) => {
        this.init();
        this.submitType = params.type
        this.governanceResponsibilityModel = {
          governanceEventId: params.governanceEventId,
        };
        this.dialog.show = true;
        this.dialog.title = "添加经办人员";
      });

      mitt.on("savePeople",()=>{
        this.init();
      })
    });
  },
  watch:{
    "governanceResponsibilityModel.dutyNote"(newVal,oldVal){
      if (newVal && newVal.length > 50) {
        this.governanceResponsibilityModel.dutyNote = String(newVal).slice(0,50);
      }
    }
  },
  created() {},
};
</script>

<style scoped>
.editor {
  width: 805px;
}
</style>
