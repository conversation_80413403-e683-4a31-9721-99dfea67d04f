import request from '@/utils/request'

export const login = (data) =>

	request({
		url: '/oauth/login',
		method: 'post',
		data: data
	})
export const mcpLogin = (data) =>
	request({
		url: '/oauth/mcp-auth',
		method: 'post',
		data: data
	})
export const createQrCode = (data) =>
	request({
		url: '/oauth/createQrCode',
		method: 'post',
		data: data
	})
export const getQrCode = (data) =>
	request({
		url: '/oauth/getQrCode',
		method: 'post',
		params: data
	})

	
export const getAVerifyCode = () =>

	request({
		url: '/oauth/verify-code',
		method: 'get'
	})

export const getLoginStatus = () =>

	request({
		url: '/oauth/systemParameter',
		method: 'post'
	})
export const loginCode = (data) =>

	request({
		url: '/oauth/login-code',
		method: 'post',
		data: data
	})
export const getCode = () =>

	request({
		url: '/oauth/getCode',
		method: 'get'
	})
export const loginOut = () =>

	request({
		url: '/oauth/loginOut',
		method: 'post'
	})

export const pushUser = (data) =>

	request({
		url: '/push-server/push',
		method: 'post',
		data: data
	})

export const getUserAgreement = (data) =>
	request({
		url: '/globalInfo/getUserAgreement',
		method: 'get',
		params:{
			type:data
		},
		
	})