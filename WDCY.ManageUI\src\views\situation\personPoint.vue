<template>

  <person-point-edit
    @search="search"
    :flowTypeList="flowTypeList"
  ></person-point-edit>
  <device-connect-edit
    @search="search"
    :deviceStatusList="deviceStatusList"
  ></device-connect-edit>
  <el-row :gutter="20">
    <el-col :span="4">
      <el-input
        v-model="searchModel.name"
        @keydown.enter="search"
        placeholder="人流信息名称"
        clearable
      />
    </el-col>
    <el-col :span="4">
      <el-button type="primary" @click="search">搜 索</el-button>
    </el-col>
    <el-col :span="4" :push="12">
      <el-button
        style="float: right"
        type="primary"
        @click="add"
        v-if="hasPerm('touristFlow:person:add')"
        >添 加</el-button
      >
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-table :data="personList" border style="width: 100%">
        <el-table-column prop="name" align="center" label="人流信息名称" />
        <el-table-column prop="note" align="center" label="人流信息描述" />
        <el-table-column
          prop="flowType"
          align="center"
          :formatter="formatFlowType"
          width="100"
          label="进出类型"
        />
        <el-table-column prop="lng" align="center" width="108" label="经度" />
        <el-table-column prop="lat" align="center" width="108" label="纬度" />
        <el-table-column
          prop="createTime"
          width="168"
          align="center"
          label="创建时间"
        >
        </el-table-column>
        <el-table-column
          prop="updateTime"
          width="168"
          align="center"
          label="修改时间"
        >
        </el-table-column>
        <el-table-column align="center" width="200" label="操作">
          <template #default="scope">
            <el-button
              type="text"
              size="default"
              @click="deviceConnect(scope.row.id)"
              v-if="hasPerm('device:relevance:detial')"
              >人流设施关联</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="edit(scope.row.id)"
              v-if="hasPerm('touristFlow:person:update')"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="deleted(scope.row.id)"
              v-if="hasPerm('touristFlow:person:delete')"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination
        background
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :total="Number(total)"
      ></el-pagination>
    </el-col>
  </el-row>
</template>
<script>
import { getDictCss, formatDict } from "@/utils/dict";
import {
  touristPointList,
  touristPointListDelete,
  getTouristPoint,
} from "@/api/situation/situation";
import { listDictByNameEn } from "@/api/admin/dict";
import mitt from "@/utils/mitt";
import personPointEdit from "@/componts/visitor/personPointEdit.vue";
import deviceConnectEdit from "@/componts/visitor/deviceConnectEdit.vue";


export default {
  components: { personPointEdit, deviceConnectEdit },
  data() {
    return {
      searchModel: {},
      flowTypeList: [],
      deviceStatusList: [],
      communityId: localStorage.getItem("communityId"),
      total: 0,
      personList: [],
      total: 0,
      pageSize: 10,
    };
  },
  methods: {
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    formatFlowType(row, column, cellValue, index) {
      return formatDict(this.flowTypeList, cellValue);
    },
    search() {
      this.searchModel.communityId = this.communityId;
      touristPointList(this.searchModel).then((res) => {
        this.personList = res.data.result.list;
        this.total = res.data.result.total;
      });
    },
    deviceConnect(id) {
      mitt.emit("openDeviceConnectEdit", { id: id, type: 1 });
    },
    edit(id) {
      getTouristPoint(id).then((res) => {
        mitt.emit("openPersonPointEdit", res.data.result);
      });
    },
    add() {
      mitt.emit("openPersonPointAdd");
    },
    deleted(id) {
      this.$confirm("删除人流点, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          touristPointListDelete(id).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
          });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    handleSizeChange(num) {
      this.searchModel.pageSize = num;
      this.search();
    },
    async init() {
      mitt.off("openPersonPointEdit");
      mitt.off("openPersonPointAdd");
      mitt.off("openDeviceConnectEdit");
      try {
        this.searchModel.communityId = this.communityId;
        let res = await touristPointList(this.searchModel);
        this.personList = res.data.result.list;
        this.total = res.data.result.total;
        let flowType = await listDictByNameEn("flow_type");
        this.flowTypeList = flowType.data.result;
        let deviceStatus = await listDictByNameEn("device_status");
        this.deviceStatusList = deviceStatus.data.result;
      } catch (err) {}
    },
  },
  created() {
    this.init();
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
</style>
