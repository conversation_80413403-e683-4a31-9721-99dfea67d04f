<template>
  <device-info-edit
    :typeList="typeNodeList"
    :oriTypeList="oriTypeList"
    :statusList="statusList"
    :tagList="tagList"
    @search="search"
  ></device-info-edit>
  <video-edit></video-edit>
  <monitor-view></monitor-view>
  <plugin-monitor-view></plugin-monitor-view>
  <monitor-pop></monitor-pop>

  <service-records></service-records>

  <door-open></door-open>
  <device-association
    :deviceType="deviceType"
    :dataList="deviceList"
    :typeNodeList="typeNodeList"
    :statusList="statusList"
    @search="search"
    :total="total"
  ></device-association>
  <device-info-exprot :statusList="statusList"></device-info-exprot>
  <geofencing></geofencing>

  <input type="file" id="btn_file" @change="filechange" style="display: none" />
  <el-row :gutter="20">
    <el-col :span="2">
      <el-input
        v-model="searchModel.name"
        @keydown.enter="search"
        placeholder="名称"
        clearable
      />
    </el-col>
    <el-col :span="2">
      <el-cascader
        style="width: 100%"
        :props="{ checkStrictly: true }"
        :options="typeNodeList"
        @change="handleChange"
        clearable
        placeholder="选择类型"
      />
    </el-col>
    <el-col :span="2">
      <el-select
        style="width: 100%"
        v-model="searchModel.status"
        placeholder="状态"
        clearable
      >
        <el-option
          v-for="item in statusList"
          :key="item.nameEn"
          :label="item.nameCn"
          :value="parseInt(item.nameEn)"
        ></el-option>
      </el-select>
    </el-col>
    <el-col :span="4">
      <el-button type="primary" @click="search">搜 索</el-button>
    </el-col>
    <el-col :span="5" :push="9">
      <el-button
        style="float: right"
        type="primary"
        @click="add"
        v-if="hasPerm('device:info:add')"
        >添 加</el-button
      >
      <el-button
        style="float: right; margin-right: 20px"
        type="primary"
        v-if="hasPerm('device:info:export')"
        @click="dropOut"
        >导出</el-button
      >
      <el-dropdown
        style="float: right; margin-right: 20px"
        split-button
        v-if="hasPerm('device:info:importExcel')"
        type="primary"
        @click="importExcel"
      >
        导 入
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              ><a
                :href="
                  imgServer +
                  '/assets/import/小区基础信息-设施国标编码导入模板.xlsx'
                "
                >导入模板下载</a
              >
            </el-dropdown-item>
            <!-- <el-dropdown-item command="b">Action 2</el-dropdown-item> -->
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-col>
  </el-row>
  <!-- <el-tabs v-model="activeName" @tab-click="handleClick" v-for="(item,index) in deviceTypeList">
    <el-tab-pane :label="item.name" :name="item.id"></el-tab-pane>
  </el-tabs> -->
  <el-row :gutter="20">
    <el-col :span="24">
      <el-table :data="deviceList" border style="width: 100%">
        <el-table-column
          prop="videoCover"
          width="100"
          align="center"
          label="图片"
        >
          <template #default="scope">
            <el-image
              preview-teleported
              style="
                width: 60px;
                height: 36px;
                display: flex;
                justify-content: center;
                align-items: center;
              "
              :src="getVC(scope.row)"
              :preview-src-list="[getVC(scope.row)]"
            >
              <template #error>
                <span style="display: flex; justify-content: center"
                  >暂无图片</span
                >
              </template>
            </el-image>
          </template>
        </el-table-column>

        <el-table-column
          prop="devNo"
          show-overflow-tooltip
          align="center"
          label="设施编号"
        />
        <el-table-column
          prop="gbCode"
          show-overflow-tooltip
          align="center"
          label="国标编码"
        />
        <el-table-column
          prop="name"
          show-overflow-tooltip
          align="left"
          header-align="center"
          label="名称"
        />
        <el-table-column
          prop="devIp"
          show-overflow-tooltip
          width="140"
          align="center"
          label="设施IP(mac地址)"
        >
          <template #default="scope">
            {{ scope.row.devIp }}
            <span v-if="scope.row.devMac">({{ scope.row.devMac }})</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="typeName"
          width="120"
          align="center"
          label="设施类型"
        />

        <el-table-column prop="status" width="120" align="center" label="状态">
          <template #default="scope">
            <el-tag :type="getDictCss(statusList, scope.row.status)">{{
              formatDict(statusList, scope.row.status)
            }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="installType"
          width="120"
          align="center"
          label="安装类型"
        >
          <template #default="scope">
            <el-tag
              :type="getDictCss(installTypeList, scope.row.installType)"
              >{{ formatDict(installTypeList, scope.row.installType) }}</el-tag
            >
          </template>
        </el-table-column>

        <el-table-column
          prop="address"
          align="left"
          header-align="center"
          label="地址"
        >
          <template #default="scope">
            <el-col :span="24" style="display: flex; align-items: center">
              <el-button
                v-show="isShow(scope.row)"
                type="text"
                style="font-size: 30px; padding: 0"
                @click="selectPoint(scope.row)"
              >
                <el-icon :size="30">
                  <location-filled></location-filled>
                </el-icon>
              </el-button>
              <el-tooltip :content="scope.row.address">
                <span
                  style="
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  "
                  >{{ scope.row.address }}</span
                >
              </el-tooltip>
            </el-col>
          </template>
        </el-table-column>

        <el-table-column
          prop="note"
          show-overflow-tooltip
          align="center"
          label="备注"
        />
        <el-table-column
          align="center"
          width="260"
          label="操作"
          v-if="
            hasPerm('device:protect:montior') ||
            hasPerm('device:regulate:openDoor') ||
            hasPerm('device:info:update') ||
            hasPerm('device:info:delete')
          "
        >
          <template #default="scope">
            <el-button
              type="text"
              size="default"
              @click="maintenanceRecords(scope.row)"
              >维护记录</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="deviceAssociation(scope.row)"
              v-if="hasPerm('device:info:association')"
              >设施关联</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="queryVideo(scope.row)"
              v-show="scope.row.seeVideo"
              v-if="hasPerm('device:protect:montior')"
              >查看监控</el-button
            >
            <el-button
              type="text"
              size="default"
              v-if="hasPerm('device:regulate:openDoor')"
              @click="openDoor(scope.row)"
              v-show="
                scope.row.typeName == '人闸' || scope.row.typeName == '车闸'
              "
              >开闸</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="selectArea(scope.row)"
              v-if="hasPerm('device:info:update')"
              >围栏
            </el-button>
            <el-button
              type="text"
              size="default"
              @click="edit(scope.row.id)"
              v-if="hasPerm('device:info:update')"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="deleted(scope.row.id)"
              v-if="hasPerm('device:info:delete')"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination
        background
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :total="Number(total)"
      ></el-pagination>
    </el-col>
    <el-dialog
      draggable
      v-model="errMsgStatus"
      :title="communityName"
      custom-class="diaLogClass"
    >
      <div class="importBox">
        <span class="importText"
          >已成功导入<strong style="color: #4b96ff">{{ successNum }}</strong
          >条</span
        >
        <span class="importText"
          >导入失败<strong style="color: red">{{ failNum }}</strong
          >条</span
        >
      </div>
      <div class="errMsg">
        <div class="err_msg_title">
          <span class="blue_icon"></span>
          <span style="margin-left: 8px; display: inline-block"
            >导入失败明细</span
          >
        </div>
        <div class="errMsgMain" v-if="failNum">
          <el-tooltip v-for="(item, index) in errMsgList" :key="index">
            <template #content>
              <div
                v-for="(item, index) in popUpMsg"
                :key="index"
                style="line-height: 26px"
              >
                {{ item }}
              </div>
            </template>
            <div @mouseover="hover(item)" class="everyErrMsg">
              {{ item }}
            </div>
          </el-tooltip>
        </div>
        <img v-else class="nothing" src="../../assets/img/nothing.png" alt="" />
      </div>
      <div class="button_box">
        <el-button @click="copyToClipboard($event)">复 制</el-button>
        <el-button @click="close">关 闭</el-button>
      </div>
    </el-dialog>
    <div id="mui-player"></div>
  </el-row>
</template>

<script>
import { ElLoading } from "element-plus";
// import * as XLSX from "xlsx"
import "mui-player/dist/mui-player.min.css";
// import MuiPlayer from 'mui-player'
// import Hls from 'hls.js'
import {
  deviceLink,
  deviceInfoList,
  openDoorInfo,
  openPeopleDoor,
  openVehicleDoor,
  deviceInfoListDelete,
  getDeviceInfo,
  deviceInfoAll2List,
  deviceTypeAll2List,
  queryMonitorByDevNo,
  deviceInfoPictureByOpenId,
  deviceTypeAllList,
  importData,
  queryVideoConfig,
  updateByPolyCoords,
} from "@/api/device/device";
import { listDictByNameEn } from "@/api/admin/dict";
import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict";
import { openGeofencingMap, openMap } from "@/utils/myUtils";
import DeviceInfoEdit from "@/componts/device/deviceInfoEdit.vue";
import DoorOpen from "@/componts/device/doorOpen.vue";
import DeviceInfoExprot from "@/componts/device/deviceInfoExprot.vue";
import DeviceAssociation from "@/componts/device/deviceAssociation.vue";
import serviceRecords from "@/componts/device/serviceRecords.vue";
import videoEdit from "@/componts/video/videoEdit.vue";
import monitorView from "@/componts/video/monitorView.vue";
import pluginMonitorView from "@/componts/video/pluginMonitorView.vue";
import monitorPop from "@/componts/video/monitorPop.vue";
import { getCommunity } from "@/api/base/community";
import geofencing from "@/componts/map/geofencing.vue";
export default {
  components: {
    DeviceInfoEdit,
    videoEdit,
    DoorOpen,
    DeviceInfoExprot,
    DeviceAssociation,
    monitorView,
    pluginMonitorView,
    serviceRecords,
    geofencing,
    monitorPop,
  },
  data() {
    return {
      communityId: localStorage.getItem("communityId"),
      searchModel: {},
      typeList: [],
      typeNodeList: [],
      imgServer: import.meta.env.VITE_BASE_API,
      deviceList: [],
      statusList: [],
      installTypeList: [],
      oriTypeList: [],
      tagList: [],
      total: 0,
      pageSize: 10,
      doorStatusList: [],
      errMsgList: [],
      errMsgStatus: false,
      popUpMsg: [],
      successNum: "",
      failNum: "",
      communityName: "",
      deviceType: [],
    };
  },
  mounted() {
    mitt.on("updateGeoPolyCoords", (data) => {
      if (data) {
        console.log("提交保存地理围栏信息", data);
        updateByPolyCoords(data).then((res) => {
          if (res.data.code == 0) {
            this.$message.success(res.data.msg);
          } else {
            this.$message.error(res.data.msg);
          }
        });
      }
    });
  },
  methods: {
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    search() {
      this.searchModel.type = 5;
      this.searchModel.communityId = this.communityId;
      deviceInfoAll2List(this.searchModel).then((res) => {
        this.deviceList = res.data.result.list;
        this.total = res.data.result.total;
      });
    },
    selectPoint(row) {
      var communityId = localStorage.getItem("communityId");
      getCommunity(communityId)
        .then((res) => {
          // var result = res.data.result;
          // var config = JSON.parse(result.expandParams);
          // var mode = config.map.mode;
          // if (mode == "amap" || !result.enabled3d) {
          //   openMap(mitt, false, [row.lng, row.lat], row.address);
          // } else if (mode == "mars3d") {
          //   console.log("aa");

          //   try {
          //     var center = {
          //       lng: config.map.sdgis.position.lng,
          //       lat: config.map.sdgis.position.lat,
          //       alt: config.map.sdgis.position.alt,
          //       heading: config.map.sdgis.view.heading,
          //       pitch: config.map.sdgis.view.pitch,
          //     };

          //     var rotationSet = { x: 0, y: 0, z: 0 };

          //     try {
          //       rotationSet = config.map.sdgis.rotation;
          //     } catch (error) {}

          //     var scaleSet = 1;
          //     try {
          //       scaleSet = config.map.sdgis.scale;
          //     } catch (error) {}

          //     var showBaseMap = false;
          //     try {
          //       showBaseMap = config.map.sdgis.showBaseMap;
          //     } catch (error) {}
          //
          //     var data = {
          //       edit: false,
          //       title:'查看点位',
          //       point: [row.lng, row.lat, row.alt],
          //       position: config.map.sdgis.position,
          //       center: center,
          //       modeUrl: config.map.sdgis.tdtile,
          //       contextMenuStatus: false,
          //       rotationSet,
          //       scaleSet,
          //       showBaseMap: showBaseMap,
          //     };
          //     console.log(data);
          //     mitt.emit("openMarsMap", data);
          //   } catch (error) {
          //     console.log(error);
          //   }
          // } else if (mode == "treejs") {
          // }

          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map.mode;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          if (result.enabled3d && mode == "mars3d") {
            position = config.map.sdgis.position;

            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (result.lng && result.lat) {
            center = {
              lng: result.lng,
              lat: result.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }
          var data = {
            enabled3d: result.enabled3d,
            edit: false,
            point: [row.lng, row.lat, row.alt],
            position: position,
            center: center,
            modeUrl: modeUrl,
            title: "地图选点",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          //console.log(data);
          mitt.emit("openMarsMap", data);
        })
        .catch((err) => {});
    },
    // 地理围栏编辑
    selectArea(row) {
      getCommunity(row.communityId)
        .then((res) => {
          var community = res.data.result;
          if (res.data.code == 0 && community) {
            getDeviceInfo(row.id).then((res1) => {
              var info = res1.data.result;
              if (res1.data.code == 0 && info) {
                let position = {
                  lng: info.lng,
                  lat: info.lat,
                  alt: info.alt,
                };
                openGeofencingMap(mitt, {
                  id: row.id,
                  title: info.name,
                  position: info.lng && info.lat ? position : null,
                  polyCoords: info.polyCoords,
                  enabled3d: community.enabled3d,
                  mapConfig: community.expandParams,
                });
              }
            });
          }
        })
        .catch((err) => {});
    },
    isShow(row) {
      if (
        row.lng != undefined &&
        row.lng != null &&
        row.lng != "" &&
        row.lat != undefined &&
        row.lat != null &&
        row.lat != ""
      ) {
        return true;
      }
      return false;
    },
    // 格式化图片
    getVC(row) {
      if (row.expandParams) {
        let obj = JSON.parse(row.expandParams);
        if (obj.picture) {
          return this.imgServer + obj.picture;
        } else {
          return false;
        }
      }
    },
    // getPicture(row) {
    // 	deviceInfoPictureByOpenId({ openId: row.devNo })
    // 		.then(res => {
    // 			if (res.data.result == null) {
    // 				this.$message("获取失败")
    // 			} else {
    // 				this.$message("获取成功")
    // 				this.search()
    // 			}
    // 		})
    // },
    /**设施关联按钮 */
    deviceAssociation(row) {
      console.log(row);
      // deviceLink({devId:row.id,LinkType:row.typeName}).then(res => {
      mitt.emit("openDeviceAssociation", row);
      // })
    },
    /**维护记录按钮 */
    maintenanceRecords(row) {
      mitt.emit("openRecords", row);
    },
    /**
     * 查看监控
     */
    queryVideo(row) {
      mitt.emit("openMonitorPop", row);

      // queryVideoConfig().then((res) =>
      // {
      //   let urlType =
      //     res.data.result.previewMode == "GBT"
      //       ? res.data.result.previewProtocolType
      //       : res.data.result.previewMode
      //       ? res.data.result.previewMode
      //       : "hls";
      //   let video = {
      //     communityId: String(localStorage.getItem("communityId")),
      //     code: row.devNo,
      //     urlType: urlType.toLowerCase(),
      //     playType: "previewURLs", //playbackURLs 回看
      //     id: row.id,
      //   };
      //   if (video.urlType == "plugin") {
      //     const data = {
      //       res: res.data.result,
      //       devNo: row.devNo,
      //     };
      //     mitt.emit("openPluginVideo", data);
      //   } else if (video.urlType == "ws") {
      //     var data = { video: video, name: row.name };
      //     mitt.emit("openWSVideo", data);
      //   } else {
      //     queryMonitorByDevNo(video).then((res) => {
      //       var data = {
      //         videoSrc: res.data.result,
      //         name: row.name,
      //         urlType: urlType.toLowerCase(),
      //       };

      //       mitt.emit("openVideoEdit", data);
      //     });
      //   }
      // });
    },
    // 远程开闸
    async openDoor(row) {
      let that = this;
      let searchModel = {};
      if (row.typeName == "车闸") {
        searchModel.id = row.id;
        searchModel.type = 2;
        await openDoorInfo(searchModel).then((res) => {
          searchModel.doorStatusList = that.doorStatusList;
          searchModel.data = res.data.result;
          mitt.emit("doorOpen", searchModel);
        });
        const data = {};
        data.id = String(row.id);
      } else if (row.typeName == "人闸") {
        searchModel.id = row.id;
        searchModel.type = 1;
        await openDoorInfo(searchModel).then((res) => {
          searchModel.doorStatusList = that.doorStatusList;
          searchModel.data = res.data.result;
          mitt.emit("doorOpen", searchModel);
        });

        const data = {};
        data.id = String(row.id);
      }
    },
    edit(id) {
      getDeviceInfo(id).then((res) => {
        mitt.emit("openDeviceInfoEdit", res.data.result);
      });
    },
    // 导入功能
    importExcel() {
      document.getElementById("btn_file").value = null;
      document.getElementById("btn_file").click();
      this.communityName = "导入设施数据";
    },
    // 导出
    dropOut() {
      mitt.emit("deviceInfoExport");
    },
    close() {
      this.errMsgStatus = false;
    },
    filechange() {
      this.fullLoading = ElLoading.service({
        fullscreen: true,
        background: "rgba(0, 0, 0, 0.7)",
        text: "正在执行导入，稍候将返回结果，请不要刷新！！！",
      });
      var file = document.getElementById("btn_file").files[0];
      var formdata = new FormData();
      formdata.append("file", file);
      formdata.append("communityId", localStorage.getItem("communityId"));
      importData(formdata).then((res) => {
        console.log(formdata);
        this.fullLoading.close();
        if (res.data.result) {
          this.errMsgStatus = true;
          this.errMsgList = res.data.result.fail;

          this.successNum = res.data.result.successCount;
          this.failNum = res.data.result.failCount;
          this.search();
        }
        mitt.emit("openPersonAdd");
      });
    },
    hover(item) {
      item = JSON.stringify(item);
      if (item) {
        if (item.includes("；")) {
          item = item.substring(item.indexOf(" ") + 1);
          this.popUpMsg = item.split("；");
        } else {
          this.popUpMsg = [item];
        }
      }
    },
    copyToClipboard() {
      // 复制
      if (!this.failNum) {
        return;
      }
      // navigator clipboard 需要https等安全上下文
      const that = this;
      if (navigator.clipboard && window.isSecureContext) {
        // navigator clipboard 向剪贴板写文本
        that.$message.success("复制成功!");
        return navigator.clipboard.writeText(this.errMsgList.join("\r\n"));
      } else {
        // 创建text area
        let textArea = document.createElement("textarea");
        textArea.value = this.errMsgList.join("\r\n");
        // 使text area不在viewport，同时设置不可见
        textArea.style.position = "absolute";
        textArea.style.opacity = 0;
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        that.$message.success("复制成功!");
        return new Promise((res, rej) => {
          // 执行复制命令并移除文本框
          document.execCommand("copy") ? res() : rej();
          textArea.remove();
        });
      }
    },
    handleChange(e) {
      if (e == null) {
        this.searchModel.devTypeId = null;
        return;
      } else {
        this.searchModel.devTypeId = e[e.length - 1];
      }
    },
    add() {
      mitt.emit("openDeviceInfoAdd");
    },
    deleted(id) {
      this.$confirm("删除设施, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deviceInfoListDelete(id).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
          });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    handleSizeChange(num) {
      this.searchModel.pageSize = num;
      this.search();
    },
    async init() {
      mitt.off("openDeviceInfoEdit");
      mitt.off("openDeviceInfoAdd");
      mitt.off("openPluginVideo");
      mitt.off("openVideoEdit");
      mitt.off("openWSVideo");
      mitt.off("doorOpen");

      try {
        this.searchModel.type = 5;
        this.searchModel.communityId = this.communityId;
        let res = await deviceInfoAll2List(this.searchModel);
        let type_res = await deviceTypeAll2List({
          communityId: this.communityId,
        });
        let ori_type_res = await deviceTypeAllList({
          communityId: this.communityId,
        });

        this.typeList = type_res.data.result;
        this.oriTypeList = ori_type_res.data.result;
        var typeStr = JSON.stringify(type_res.data.result)
          .replaceAll("name", "label")
          .replaceAll("id", "value");
        this.typeNodeList = JSON.parse(typeStr);
        console.log("分级", this.typeNodeList);
        this.deviceList = res.data.result.list;
        this.total = res.data.result.total;

        let deviceStatus = await listDictByNameEn("device_status");
        this.statusList = deviceStatus.data.result;

        let deviceType = await listDictByNameEn("Link_Type");
        this.deviceType = deviceType.data.result;

        let doorStatus = await listDictByNameEn("door_status");
        this.doorStatusList = doorStatus.data.result;

        let installTypes = await listDictByNameEn("install_type");
        this.installTypeList = installTypes.data.result;
      } catch (err) {}
    },
  },
  created() {
    this.init();
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
div /deep/ .diaLogClass {
  height: 780px;
  width: 1124px;
  background-color: rgb(241, 245, 255);
  overflow-x: auto;
  position: relative;
  border-radius: 16px;
}
div /deep/ .el-dialog__body {
  padding: 0 34px;
}
.importBox {
  background-color: #fff;
  width: 1056px;
  line-height: 48px;
  border-radius: 6px;
  margin: 27px auto 5px;
  font-size: 14px;
}

.importText {
  padding-left: 32px;
}

.button_box {
  margin: 32px auto 36px;
  width: 237px;
}

.button_box > button {
  width: 112px;
  height: 40px;
}

.button_box > button:nth-child(1) {
  background-color: #3694ff;
  color: #fff;
}
.everyErrMsg:nth-of-type(2n + 1) {
  background-color: rgba(80, 158, 255, 0.1);
}

.nothing {
  text-align: center;
  display: block;
  margin: auto;
  /* margin-bottom: 140px; */
}
.blue_icon {
  height: 16px;
  width: 4px;
  background-color: #4b96ff;
  display: inline-block;
  border-radius: 1px;
}

.everyErrMsg {
  height: 38px;
  width: 1019px;
  line-height: 38px;
  /* margin-bottom: 10px; */
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 19px;
  cursor: pointer;
  user-select: none;
}
.err_msg_title {
  padding-top: 18px;
  padding-bottom: 24px;
  padding-left: 19px;
}

.errMsgMain {
  height: 456px;
  overflow-x: auto;
}
.errMsg {
  width: 1056px;
  height: 534px;
  /* overflow-x: auto; */
  background-color: #fff;
  /* position: relative; */
  color: #666666;
  font-size: 14px;
  box-sizing: border-box;
}
</style>
