<!-- 海康冲突 -->

<template>
  <el-dialog :close-on-click-modal="false" draggable :style="'width:' + (routePointsStatus ? '70%' : '50%')"
    style="height:585px" :append-to-body="false" destroy-on-close v-loading="loading" v-model="dialog.show"
    :title="dialog.title" @close="close">
    <div>
      <div class="div-bottom">
        <div v-if="tilesetLayer != null" class="bottom-btn" @click="showTilesetLayer">
          显示/隐藏模型
        </div>
        <div class="bottom-btn" @click="openBaseMap(0)">天地图影像</div>
        <div class="bottom-btn" @click="openBaseMap(1)">高德基础</div>
        <div class="bottom-btn" @click="openBaseMap(2)">高德影像</div>
      </div>
      <div id="mars3dContainer" class="mars3d-container"></div>

      <el-button v-if="routePointsStatus" type="primary" style="
          position: absolute;
          left: 59%;
          bottom: 100px;
          width: 100px;
          height: 30px;
          z-index: 102;
        " @click="view">{{ roamingText }}</el-button>
      <div v-if="routePointsStatus" style="
          height: 460px;
          padding:15px;
          background:white;
          

          position: absolute;
          width: 250px;
        ">
        <div style="flex: 1; height: 100%">
          <el-button style="font-size: 25px; width: 100%; border-radius: 0;" size="default" @click="addRouteLine">
            <el-icon :size="20">
              <plus></plus>
            </el-icon>
          </el-button>
          <!-- <el-scrollbar height="400px"> -->
          <el-menu unique-opened style="height: 93%; overflow-x: auto" active-text-color="#409EFF">
            <template v-for="(item, index) in routePoints" :key="index">
              <el-sub-menu @click="handleNodeClick(item, index)" :index="index"
                style=" margin-left: -10px; border-bottom:1px solid  #E7E7E7">
                <template #title>
                  <el-icon>
                    <Guide />
                  </el-icon>
                  <span v-if="item.editNameStatus" style="user-select: none">{{
                    item.routeName
                  }}</span>
                  <el-input v-else v-model="item.routeName" @click.stop style="width: 60%"></el-input>
                  <div class="eidt--remove" style=" position: absolute; left: 65%">
                    <el-icon v-if="item.editNameStatus" @click="editName(item)">
                      <EditPen />
                    </el-icon>
                    <el-icon v-else @click="editName(item)">
                      <Check />
                    </el-icon>
                    <el-icon @click="removeLine(index)" style="margin-left: 7px">
                      <Delete />
                    </el-icon>
                  </div>
                </template>
                <el-menu-item v-for="secondItem in item.viewPoints" :key="secondItem"
                  @click="sonHandleNodeClick(secondItem)" style="user-select: none">
                  <el-icon>
                    <Location />
                  </el-icon>{{ secondItem.name }}
                </el-menu-item>
              </el-sub-menu>
            </template>
          </el-menu>
          <!-- </el-scrollbar> -->
          <!-- <div style="position:absolute;left:21.5%;top:117px;font-size:18px;width:43px;">
								<div v-for="(item, index) in routePoints" :key="index" style="line-height:56px">
									<el-icon v-if="item.editNameStatus" @click="editName(item)"><EditPen /></el-icon>
									<el-icon v-else @click="editName(item)"><Check /></el-icon>
									<el-icon @click="removeLine(index)" style="margin-left:7px"><Delete /></el-icon>
								</div>
							</div> -->
          <!-- <div>
					创建路线
				</div>
				<div v-for="item in routePoints" @click="changeRouteLine(item)" @dblclick="editRouteLineName(item)" :key="item">
					{{item.routeName}}
				</div> -->
        </div>
        <!-- <div style="width:40px"></div> -->

        <div style="
            height: 500px;
            width: 250px;
            overflow: hidden;
            position: absolute;
            top: 0;
            left: 280px;
          ">
          <div v-if="pointObjStatus" class="alert-marsk">
            <el-form :rules="rules" ref="form" :model="pointObj" label-width="80x">
              <el-form-item label="名 称" prop="communityName">
                <el-input v-model="pointObj.name" placeholder="" />
              </el-form-item>
              <el-form-item label="经 度" prop="communityName">
                <el-input v-model="pointObj.lat" placeholder="" />
              </el-form-item>
              <el-form-item label="纬 度 " prop="communityName">
                <el-input v-model="pointObj.lng" placeholder="" />
              </el-form-item>
              <el-form-item label="视  高" prop="communityName">
                <el-input v-model="pointObj.alt" placeholder="" />
                <span style="position: absolute; right: 10px">米</span>
              </el-form-item>
              <el-form-item label="俯仰角" prop="communityName">
                <el-input v-model="pointObj.pitch" placeholder="" />
              </el-form-item>
              <el-form-item label="方 向" prop="communityName">
                <el-input v-model="pointObj.heading" placeholder="" />
              </el-form-item>
              <el-form-item label="时 长" prop="communityName">
                <el-input v-model="pointObj.duration" placeholder="" />
                <span style="position: absolute; right: 10px">秒</span>
              </el-form-item>
            </el-form>
            <el-button type="primary" @click="savePoint">确定</el-button>
            <!-- <el-icon size="35" @click="savePoint" style="float:right;margin-right:10px"><Check /></el-icon> -->
          </div>

          <!-- <el-button type="primary" style="position:absolute;right:30px;bottom:100px" @click="stop" v-else>停止</el-button> -->
          <!-- <el-button type="primary" style="position:absolute;right:40px;bottom:100px" @click="saveRoteLine">暂存线路</el-button> -->
        </div>
      </div>

      <div v-else style="height: 480px; overflow: hidden">
        <div class="search-container" v-if="isEditMode && !polyCoordsEdit">
          <el-input class="input-box" v-model="lnglat[0]" placeholder="经度:120.xxxx" />
          <el-input class="input-box" v-model="lnglat[1]" placeholder="纬度:31.xxxx" />
          <el-input class="input-box" v-model="lnglat[2]" placeholder="海拔" />
          <el-button type="primary" style="background: white; width: 50px; height: 25px; color: black"
            @click="flyToPoint">定 位
          </el-button>
        </div>

        <div class="search-container" v-if="polyCoordsEdit">
          <el-button type="primary" style="background: white; width: 100px; height: 25px; color: black"
            @click="startDrawGraphic">绘制围栏
          </el-button>
        </div>
      </div>
    </div>

    <el-row v-show="!contextMenuStatus">
      <div style="
          display: flex;
          width: 50%;
          justify-content: space-between;
          align-items: space-between;
        " v-if="0">
        <div>经度:{{ lnglat[0] }}</div>
        <div>纬度:{{ lnglat[1] }}</div>
        <div>海拔:{{ lnglat[2] }}</div>
        <!-- <div>经度:{{position.lat}}</div>
				<div>纬度:{{position.lng}}</div>
				<div>海拔:{{position.alt}}</div> -->
      </div>
    </el-row>

    <el-row v-show="isEditMode" justify="center" style="position: absolute; bottom: 100px; left: 40%">
      <el-button type="primary" style="width: 100px; height: 30px; z-index: 101" @click="onSubmit"
        v-if="hasPerm('base:community:updateByRoutePoints')">提 交
      </el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import mitt from "@/utils/mitt";
import { updateByRoutePoints } from "@/api/base/community";
import { TweensRoaming } from "./TweensRoaming";
//引入cesium基础库
// import * as Cesium from "mars3d-cesium";
//导入mars3d主库
// import * as mars3d from "mars3d";
//导入mars3d插件（按需使用，需要先npm install）
// import "mars3d-space";

export default {
  emits: ["point"],
  data() {
    return {
      loading: false,
      isEditMode: true, //是否编辑模式
      dialog: {
        show: false,
        title: "巡游线路编辑",
      },
      lnglat: [],
      position: {},
      center: {},

      modeUrl: "",
      mapSource: null,
      routePoints: null,
      routePointsStatus: null,
      routeLine: [],
      keyPonit: {},
      treeClickCount: 0,
      editNameStatus: true,
      routeLineIndex: 0,
      comunityData: {}, //当前编辑小区的数据
      pointObj: {},
      pointObjStatus: false,
      initPointStatus: false, //初始化标点是否显示
      contextMenuStatus: false, //显示右键菜单
      graphicLayer: null,
      movePoint: {}, // 正在移动的点位对象 lng lat alt
      backPoint: {},
      roamingState: null,
      duration: null, //第一个点到第二个点的移动时间
      roamingText: "预 览",

      enabled3d: false,
      rotationSet: { x: 0, y: 0, z: 0 },
      scaleSet: 1,
      showBaseMap: false,
      tilesetLayer: null,
      polyCoords: [],
      scrollWall: null,
      polyCoordsEdit: false,
      polyCoordsGraphic: null,
    };
  },

  mounted() {
    this.$nextTick(function () {
      /**
       * center:中心点
       * isEditMode:是否可提交
       * contextMenuStatus:显示右键菜单
       */
      mitt.on("openMarsMap", (e) => {
        if (this.dialog.show) {
          return;
        }

        this.dialog.show = true;
        this.isEditMode = e.edit;
        this.enabled3d = e.enabled3d;
        this.rotationSet = e.rotationSet;
        this.scaleSet = e.scaleSet;
        this.showBaseMap = e.showBaseMap;

        if (e.title) this.dialog.title = e.title;

        var point = e.point;

        this.lnglat = point;

        if (e.position) {
          this.position = e.position;
        }

        var center = e.center;

        this.initPointStatus = e.lineStatus != undefined ? e.lineStatus : false;

        this.center = center;

        this.modeUrl = e.modeUrl != undefined ? e.modeUrl : "";

        this.routePoints = e.routePoints != undefined ? e.routePoints : null;

        this.routePointsStatus =
          e.routePointsStatus != undefined ? e.routePointsStatus : null;

        this.polyCoordsEdit =
          e.polyCoordsEdit != undefined ? e.polyCoordsEdit : false;

        try {
          this.polyCoords = e.polyCoords;
        } catch (error) { }

        this.comunityData = e.comunityData != undefined ? e.comunityData : {};

        this.contextMenuStatus =
          e.contextMenuStatus != undefined ? e.contextMenuStatus : false;

        this.flag = false;

        for (const key in this.routePoints) {
          this.routePoints[key].editNameStatus = true;
        }

        setTimeout(() => {
          this.pointObjStatus = false;

          this.initMap();

          if (this.routePoints) {
            this.handleNodeClick(this.routePoints[0], 0);
          }
        }, 200);
      });
    });
  },
  methods: {
    showTilesetLayer() {
      console.log("this.tilesetLayer.show", this.tilesetLayer.show);

      if (this.tilesetLayer) {
        this.tilesetLayer.show = !this.tilesetLayer.show;
      }
    },

    openBaseMap(index) {
      const layersss = this.mapSource.getLayers({
        layers: false, // 不包含layers中配置的所有图层
      });
      layersss[0].show = false;
      layersss[1].show = false;
      layersss[2].show = false;
      layersss[index].show = true;
    },

    flyToPoint() {
      if (this.lnglat[0] && this.lnglat[1]) {
        var position = {
          lng: this.lnglat[0],
          lat: this.lnglat[1],
          alt: this.lnglat[2],
        };

        var options = {
          radius: 500,
          heading: 0,
          pitch: -90,
        };

        this.mapSource.flyToPoint(position, options);

        this.graphicLayer.clear();
        const graphic = new mars3d.graphic.BillboardEntity({
          position: position,
          style: {
            image: "/image/point.png",
            width: 30,
            height: 30,
            scaleByDistance: true,
            scale: 1,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
        });
        this.graphicLayer.addGraphic(graphic);
      }
    },

    async startDrawGraphic() {
      if (this.polyCoordsGraphic)
        this.graphicLayer.removeGraphic(this.polyCoordsGraphic);

      if (this.scrollWall)
        this.graphicLayer.removeGraphic(this.scrollWall);
        this.polyCoords = [];
        this.polyCoordsGraphic = await this.graphicLayer.startDraw({
          type: "polygon",
          style: {
            color: "#29cf34",
            opacity: 0.5,
            outline: true,
            outlineWidth: 3,
            outlineColor: "#ffffff",
          },
        });

        this.polyCoordsGraphic.points.forEach((item) => {
          var polyCoord = [item._lng, item._lat, item._alt];
          this.polyCoords.push(polyCoord);
        });

        console.log("polyCoords", this.polyCoords);
    },

    initMap() {
      var that = this;
      var mapOptions = {
        scene: {
          center: that.center,
          shadows: false, // 是否启用日照阴影
          removeDblClick: true, // 是否移除Cesium默认的双击事件
          // 以下是Cesium.Viewer所支持的options【控件相关的写在另外的control属性中】
          sceneMode: 3, // 3等价于Cesium.SceneMode.SCENE3D,
          // 以下是Cesium.Scene对象相关参数
          showSun: true, // 是否显示太阳
          showMoon: true, // 是否显示月亮
          showSkyBox: true, // 是否显示天空盒
          showSkyAtmosphere: true, // 是否显示地球大气层外光圈
          fog: true, // 是否启用雾化效果
          fxaa: true, // 是否启用抗锯齿

          // 以下是Cesium.Globe对象相关参数
          globe: {
            // depthTestAgainstTerrain: false, // 是否启用深度监测
            baseColor: "#033447", // 地球默认背景色
            // showGroundAtmosphere: true, // 是否在地球上绘制的地面大气
            // enableLighting: false, // 是否显示昼夜区域
          },

          // 以下是Cesium.ScreenSpaceCameraController对象相关参数
          cameraController: {
            zoomFactor: 3.0, // 鼠标滚轮放大的步长参数
            minimumZoomDistance: 1, // 地球放大的最小值（以米为单位）
            maximumZoomDistance: 1115000, // 地球缩小的最大值（以米为单位）
            enableRotate: true, // 2D和3D视图下，是否允许用户旋转相机
            enableTranslate: true, // 2D和哥伦布视图下，是否允许用户平移地图
            enableTilt: true, // 3D和哥伦布视图下，是否允许用户倾斜相机
            enableZoom: true, // 是否允许 用户放大和缩小视图
            enableCollisionDetection: true, // 是否允许 地形相机的碰撞检测
          },
        },
        control: {
          // geocoder: true,
          defaultContextMenu: true, // 右击菜单
          contextmenu: {
            hasDefault: !that.contextMenuStatus,
          }, // 右键菜单
          // homeButton: true, // 视角复位按钮
          // navigationHelpButton: true, //帮助按钮
          // fullscreenButton: true, //全屏按钮
          // baseLayerPicker: true, //basemaps底图切换按钮
          // sceneModePicker: true //二三维切换按钮
        },
        basemaps: [
          {
            name: "天地图卫星",
            icon: "img/basemaps/tdt_img.png",
            type: "tdt",
            layer: "img_d",
            show: false,
          },

          {
            name: "高德电子",
            icon: "/img/basemaps/blackMarble.png",
            type: "gaode",
            id: 2017,
            filterColor: "#003A4A",
            layer: "vec",
            show: false,
            brightness: 0.6,
            chinaCRS: "GCJ02",
            contrast: 1.8,
            gamma: 0.3,
            hue: 1,
            invertColor: false,
            name: "暗色底图",
            pid: 10,
            saturation: 0,
          },
          {
            name: "高德影像",
            icon: "img/basemaps/gaode_img.png",
            type: "group",
            layers: [
              { name: "底图", type: "gaode", layer: "img_d" },
              { name: "注记", type: "gaode", layer: "img_z" },
            ],
            show: true,
          },
        ],
      };

      var map = new mars3d.Map("mars3dContainer", mapOptions); //支持的参数请看API文档：http://mars3d.cn/api/Map.html
      this.mapSource = map;
      
      if (that.modeUrl && that.enabled3d) {
        var tiles3dLayer = new mars3d.layer.TilesetLayer({
          url: that.modeUrl,
          maximumScreenSpaceError: 16,
          maximumMemoryUsage: 1024,
          dynamicScreenSpaceError: true,
          cullWithChildrenBounds: false,
          skipLevelOfDetail: true,
          preferLeaves: true,
          flyTo: true,
          position: that.position,
          show: true,
          rotation: that.rotationSet,
          scale: that.scaleSet,
        });

        map.addLayer(tiles3dLayer);

        this.tilesetLayer = tiles3dLayer;
      }

      var graphicLayer = new mars3d.layer.GraphicLayer();
      this.graphicLayer = graphicLayer;
      map.addLayer(graphicLayer);
      if (this.polyCoords && this.polyCoords.length > 0) {
        for (var polyCoord of this.polyCoords) {
          this.scrollWall = new mars3d.graphic.ScrollWall({
            positions: polyCoord.coords,
            style: {
              diffHeight: 100, // 高度
              color: "#00FF00",
              style: 2, // 效果2，默认是1
              speed: 2,
            },
          });
          this.graphicLayer.addGraphic(this.scrollWall);
        }
      }

      const arr = [];
      map.getDefaultContextMenu().map((i) => {
        if (["查看此处坐标", "查看当前视角"].includes(i.text)) {
          arr.push(i);
        }
      });

      arr.push({
        text: "保存当前视角",
        show: function () {
          return !this.pointObjStatus;
        },
        callback: (e) => {
          // map.onlyPickModelPosition = false
          console.log(map.getCameraView(e.cartesian));
          if (this.pointObjStatus) {
            this.pointObj.heading = map.getCameraView(e.cartesian).heading;
            this.pointObj.alt = map.getCameraView(e.cartesian).alt;
            this.pointObj.lat = map.getCameraView(e.cartesian).lat;
            this.pointObj.lng = map.getCameraView(e.cartesian).lng;
            this.pointObj.pitch = map.getCameraView(e.cartesian).pitch;
          }
        },
      });
      arr.push({
        text: "添加漫游点位",
        show: function () {
          return !map.onlyPickModelPosition;
        },
        callback: (e) => {
          // map.onlyPickModelPosition = false
          // console.log(map.getCameraView(e.cartesian));
          this.keyPonit = map.getCameraView(e.cartesian);
          this.keyPonit.alt = map.getCameraView(e.cartesian).alt + 3;

          var graphicLayer = new mars3d.layer.GraphicLayer();
          this.mapSource.addLayer(graphicLayer);

          const point = [
            this.keyPonit.lng,
            this.keyPonit.lat,
            this.keyPonit.alt,
          ];
          // console.log(point);

          const graphic = new mars3d.graphic.BillboardEntity({
            position: point,
            style: {
              image: "/image/point.png",
              width: 30,
              height: 30,
              scaleByDistance: true,
              scale: 1,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            },
          });

          if (this.routeLine.length != 0) {
            const lat1 = this.routeLine[this.routeLine.length - 1].lat;
            const lng1 = this.routeLine[this.routeLine.length - 1].lng;
            const lat2 = this.keyPonit.lat;
            const lng2 = this.keyPonit.lng;
            this.duration = this.getDistances(lat1, lng1, lat2, lng2).m / 4;
          }

          this.graphicLayer.addGraphic(graphic);
          this.keyPonit.name = "关键点" + this.routeLine.length;
          this.keyPonit.id = this.routeLine.length;
          // this.keyPonit.pitch = -45
          console.log(this.keyPonit);
          this.routeLine.push(this.keyPonit);
          if (this.routeLine.length >= 2) {
            this.routeLine[this.routeLine.length - 2].duration =
              this.duration.toFixed();
          }
          console.log(this.routeLine);
        },
      });

      if (that.contextMenuStatus) that.mapSource.bindContextMenu(arr);
      else that.mapSource.unbindContextMenu();
      // map.changeMouseModel(true)

      // 绑定右键菜单
      graphicLayer.bindContextMenu([
        {
          text: "开始编辑对象",
          icon: "fa fa-edit",
          show: function (e) {
            console.log(e);
            const graphic = e.graphic;
            if (!graphic || !graphic.hasEdit) {
              return false;
            }
            return !graphic.isEditing;
          },
          callback: (e) => {
            const graphic = e.graphic;
            if (!graphic) {
              return false;
            }
            if (graphic) {
              graphicLayer.startEditing(graphic);
            }
          },
        },
      ]);
      
      if (this.lnglat.length == 3 && this.lnglat[0] && !this.initPointStatus) {
        const graphic = new mars3d.graphic.BillboardEntity({
          position: that.lnglat,
          style: {
            image: "/image/point.png",
            width: 30,
            height: 30,
            scaleByDistance: true,
            scale: 1,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
        });
        graphicLayer.addGraphic(graphic);
      }
      // if (that.isEditMode&& !that.routeLine) {
      if (that.isEditMode && !that.polyCoordsEdit) {
        //绑定监听事件
        map.on(mars3d.EventType.dblClick, function (e) {
          graphicLayer.clear();
          const mpt = mars3d.LngLatPoint.fromCartesian(e.cartesian);

          var point = [mpt._lng, mpt._lat, mpt._alt];
          console.log(point);
          that.lnglat = point;
          const graphic = new mars3d.graphic.BillboardEntity({
            position: point,
            style: {
              image: "/image/point.png",
              width: 30,
              height: 30,
              scaleByDistance: true,
              scale: 1,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            },
          });
          graphicLayer.addGraphic(graphic);
        });

        //绑定监听事件
        map.on(mars3d.EventType.editMouseMove, function (e) {
          const mpt = mars3d.LngLatPoint.fromCartesian(e.cartesian);
          const camera = map.getCameraView(e.cartesian);
          var point = [mpt._lng, mpt._lat, mpt._alt];

          that.lnglat = point;
          if (that.routeLine) {
            that.movePoint = {
              lng: point[0],
              lat: point[1],
              alt: point[2],
            };
            that.pointObj.lng = that.movePoint.lng;
            that.pointObj.lat = that.movePoint.lat;
            that.pointObj.alt = that.movePoint.alt;
            that.pointObj.heading = camera.heading;

            console.log(point, that.movePoint);
          }
        });
      }

      if (that.polyCoordsEdit) {
      }
      
    },

    addRouteLine() {
      console.log(this.routePoints);
      if (!this.routePoints) {
        this.routePoints = [];
      }
      this.routePoints.push({
        routeName: "线路" + (this.routePoints.length + 1),
        viewPoints: [],
      });
    },
    saveRoteLine() {
      console.log(this.routePoints, this.routePoints[this.routeLineIndex]);
      this.routePoints[this.routeLineIndex].viewPoints = [];
      this.routePoints[this.routeLineIndex].viewPoints = this.routeLine;
      this.savePoint(true);
    },
    view() {
      // const eventTarget = new mars3d.BaseClass()
      console.log(this.roaming, this.roamingState);
      // if (this.roamingState && this.roaming) this.stop();

      console.log(this.roamingState);
      if (!this.roamingState) {
        // if (!this.roaming) {
        this.roaming = new TweensRoaming({
          points: this.routeLine,
        });
        this.mapSource.addThing(this.roaming);
        // }
        setTimeout(() => {
          this.roaming.start();
          this.roamingText = "停 止";
          this.roamingState = true;
        }, 3000);
      } else {
        this.stop();
        this.roamingText = "预 览";
      }

      // this.roaming = new TweensRoaming({
      // 	points:this.routeLine
      // })
      // this.mapSource.addThing(this.roaming)
      // setTimeout(() => {
      // 	this.roaming.start();
      // 	this.roamingState = false;
      // }, 3000);

      // this.mapSource.setCameraViewList(this.routeLine)
    },
    stop() {
      if (this.roamingState && this.roaming) {
        this.roamingText = "预览";
        this.roaming.stop();
        this.roamingState = false;
      }
    },
    editName(item, e) {
      console.log(e);
      if (e && e.stopPropagation) {
        e.stopPropagation();
      } else {
        window.event.cancelBubble = true;
      }
      if (this.routePoints.length == 1) {
        this.handleNodeClick(this.routePoints[0], 0);
      }
      item.editNameStatus = !item.editNameStatus;
      console.log(item);
    },
    removeLine(index, e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      } else {
        window.event.cancelBubble = true;
      }
      this.routePoints.splice(index, 1);
    },
    handleNodeClick(row, index) {
      console.log(row, index);
      this.routeLineIndex = index;
      console.log(this.routeLine, row.viewPoints);
      console.log(this.graphicLayer);
      if (this.graphicLayer) {
        this.graphicLayer.clear();
      }
      this.graphicLayer = new mars3d.layer.GraphicLayer();

      this.graphicLayer.bindContextMenu([
        {
          text: "开始编辑对象",
          icon: "fa fa-edit",
          show: function (e) {
            console.log(e);
            const graphic = e.graphic;
            if (!graphic || !graphic.hasEdit) {
              return false;
            }
            return !graphic.isEditing;
          },
          callback: (e) => {
            this.backPoint = this.mapSource.getCameraView(e.cartesian);
            const graphic = e.graphic;
            console.log(e);
            if (!graphic) {
              return false;
            }
            if (graphic) {
              this.graphicLayer.startEditing(graphic);
              this.pointObjStatus = true;
              this.pointObj = this.routeLine.find((item) => {
                if (
                  item.lat == e.graphic._point.lat &&
                  item.lng == e.graphic._point.lng &&
                  item.alt == e.graphic._point.alt
                ) {
                  return item;
                }
              });
              this.mapSource.setCameraView(this.pointObj);
              console.log(this.pointObj);
            }
          },
        },
        {
          text: "删除",
          icon: "fa fa-edit",
          show: function (e) {
            console.log(e);
            const graphic = e.graphic;
            if (!graphic || !graphic.hasEdit) {
              return false;
            }
            return !graphic.isEditing;
          },
          callback: (e) => {
            const graphic = e.graphic;
            console.log(e);
            if (!graphic) {
              return false;
            }
            if (graphic) {
              this.routeLine.splice(
                this.routeLine.findIndex(
                  (item) =>
                    item.lat == e.graphic._point.lat &&
                    item.lng == e.graphic._point.lng &&
                    item.alt == e.graphic._point.alt
                ),
                1
              );
              this.savePoint(true);
            }
          },
        },
      ]);
      this.mapSource.addLayer(this.graphicLayer);
      this.routeLine = row.viewPoints;

      for (let i = 0; i < this.routeLine.length; i++) {
        const point = [
          this.routeLine[i].lng,
          this.routeLine[i].lat,
          this.routeLine[i].alt,
        ];
        console.log(point);

        const graphic = new mars3d.graphic.BillboardEntity({
          position: point,
          style: {
            image: "/image/point.png",
            width: 30,
            height: 30,
            scaleByDistance: true,
            scale: 1,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            label: {
              text: this.routeLine[i].name,
              scaleByDistance: true,
              scale: 0.7,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              background: true,
              // verticalOrigin: 5,
              color: "#ffffff",
              visibleDepth: false,
            },
          },
        });
        this.graphicLayer.addGraphic(graphic);
      }
      let positionsArr = [];
      for (let i = 0; i < this.routeLine.length; i++) {
        positionsArr.push([
          this.routeLine[i].lng,
          this.routeLine[i].lat,
          this.routeLine[i].alt,
        ]);
      }
      console.log(positionsArr);
      let lineEntity = new mars3d.graphic.PolylineEntity({
        positions: positionsArr,
        style: {
          color: "#fff",
          width: 2,
        },
      });
      this.graphicLayer.addGraphic(lineEntity);

      // var line = new mars3d.graphic.BasePolyEntity({
      // 	positions: this.routeLine,
      // 	style: {
      // 	fill: false,
      // 	outline: true,
      // 	outlineWidth: 3,
      // 	outlineColor: "#ff0000"
      // 	}})
      // graphicLayer.addGraphic(line)
    },
    sonHandleNodeClick(index) {
      console.log(this.position);
      this.backPoint = this.position;
      this.backPoint.alt = 430;
      this.backPoint.heading = 0;
      this.backPoint.pitch = -90;
      this.pointObj = index;
      this.pointObjStatus = true;
      this.mapSource.setCameraView(this.pointObj);
    },
    savePoint(removeStatus) {
      this.pointObjStatus = false;

      if (removeStatus) {
        // if (!this.backPoint) {
        // this.backPoint = {"lat":31.136326,"lng":120.652548,"alt":430.1,"heading":0,"pitch":-90}
        // }
        console.log(this.backPoint);
        this.mapSource.setCameraView(this.backPoint);
      }
      // this.pointObj = this.mapSource.getCameraView(e.cartesian)
      this.graphicLayer.clear();
      for (let i = 0; i < this.routeLine.length; i++) {
        const point = [
          this.routeLine[i].lng,
          this.routeLine[i].lat,
          this.routeLine[i].alt,
        ];
        console.log(point);

        const graphic = new mars3d.graphic.BillboardEntity({
          position: point,
          style: {
            image: "/image/point.png",
            width: 30,
            height: 30,
            scaleByDistance: true,
            scale: 1,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            label: {
              text: this.routeLine[i].name,
              scaleByDistance: true,
              scale: 0.7,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              background: true,
              // verticalOrigin: 5,
              color: "#ffffff",
              visibleDepth: false,
            },
          },
        });
        this.graphicLayer.addGraphic(graphic);
      }
      //绘制两点之间的线
      let positionsArr = [];
      for (let i = 0; i < this.routeLine.length; i++) {
        positionsArr.push([
          this.routeLine[i].lng,
          this.routeLine[i].lat,
          this.routeLine[i].alt,
        ]);
      }
      let lineEntity = new mars3d.graphic.PolylineEntity({
        positions: positionsArr,
        style: {
          color: "#fff",
          width: 2,
        },
      });
      this.graphicLayer.addGraphic(lineEntity);
    },

    close() {
      console.log("销毁");

      if (this.tilesetLayer) this.tilesetLayer = null;

      if (this.mapSource) {
        this.stop();
        this.mapSource.destroy();
      }
    },
    onSubmit() {
      if (this.lnglat.length == 0) {
        this.$message("未选择点位");
      }
      var point = this.lnglat;

      if (this.routePoints) {
        console.log(this.routePoints);
        this.comunityData.routePoints = JSON.stringify(this.routePoints);
        console.log(this.comunityData);
        updateByRoutePoints(this.comunityData).then((res) => {
          this.$message.success(res.data.msg);
        });
      }

      mitt.emit("polyCoords", this.polyCoords);
      this.$emit("point", point);
      this.dialog.show = false;
    },
    getDistances(lat1, lng1, lat2, lng2) {
      let EARTH_RADIUS = 6378.137; // 地球半径
      let radLat1 = (lat1 * Math.PI) / 180.0; //lat1 * Math.PI / 180.0=>弧度计算
      let radLat2 = (lat2 * Math.PI) / 180.0;
      let a = radLat1 - radLat2;
      let b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
      let s =
        2 *
        Math.asin(
          Math.sqrt(
            Math.pow(Math.sin(a / 2), 2) +
            Math.cos(radLat1) *
            Math.cos(radLat2) *
            Math.pow(Math.sin(b / 2), 2)
          )
        );
      s = s * EARTH_RADIUS;
      s = Math.round(s * 10000) / 10000; // 输出为公里
      return { m: s * 1000, km: Number(s.toFixed(2)) };
    },
  },
};
</script>
<style scoped lang="less">
.mars3d-container {
  position: absolute;
}

#mars3dContainer {
  height: 485px;

  width: 95%;
}

/deep/ div .el-menu {
  border: none;
}

::-webkit-scrollbar {
  display: none;
}

/deep/ .is-opened>div {
  color: #568efe !important;
  background-color: rgba(221, 221, 221, 0.7);
}

/deep/ .el-menu--inline>li {
  background-color: rgba(238, 238, 238, 0.7);
}

.alert-marsk {
  display: flex;
  width: 175px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 21px 23px 12px 18px;

  /deep/.el-input {
    background: rgba(255, 255, 255, 0.5);
  }

  /deep/ label {
    color: #ccc;
    width: 54px !important;
  }

  /deep/ .el-input__wrapper {
    height: 25px;
    background-color: rgba(255, 255, 255, 0.2);
  }

  /deep/ button {
    height: 25px;
    margin-top: 10px;
    width: 70px;
  }

  /deep/ .el-form-item {
    margin-bottom: 0px;
  }
}

.search-container {
  position: absolute;
  display: flex;
  align-items: center;
  top: 15px;
  left: 130px;

  .input-box {
    width: 120px;
    height: 25px;
    margin-right: 10px;
  }
}

.div-bottom {
  position: absolute;
  width: 95%;
  height: 40px;
  background: white;
  z-index: 100;
  bottom: 0px;
  justify-content: flex-end;
  /* 子元素靠右 */
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;

  .bottom-btn {
    width: 80px;
    height: 30px;
    border: 1px solid RGB(75, 150, 255);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 12px;
  }

  .bottom-btn:hover {
    color: RGB(75, 150, 255);
  }
}
</style>
