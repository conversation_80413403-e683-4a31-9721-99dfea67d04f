<template>
  <div style="height: 100%; position: relative">
    <div class="header">
      <div>
        <span @click="addClass(1)" :class="{ is_actice: 1 == current }"
          >小区</span
        >
        <span style="margin: 0 19px">|</span>
        <span @click="addClass(2)" :class="{ is_actice: 2 == current }"
          >小程序用户数量</span
        >
      </div>
      <div style="display: flex" v-if="current == 2">
        <div
          style="
            display: flex;
            align-items: center;
            margin-right: 38px;
            cursor: pointer;
          "
        >
          <span class="green-block"></span>
          在线 {{ online }}
        </div>
        <div style="display: flex; align-items: center; cursor: pointer">
          <span class="gray-block"></span>
          不在线 {{ unOnline }}
        </div>
      </div>
    </div>
    <div id="mars3dContainer" class=""></div>
    <div class="air-view" v-if="current == 1">
      <div
        style="margin-bottom: 12px; height: 16px; font-size: 16px"
        v-for="item in totalList"
        :key="item"
      >
        {{ item.title }} &nbsp;&nbsp;&nbsp;{{ item.total }}
      </div>
    </div>
  </div>
</template>
<script type="text/javascript">
import {
  getMapUserStatus,
  getMapCommuntiyStatus,
  getCommunityTotal,
  getUserGroupTotal,
} from "@/api/dataScreening/dataAllMap.js";
import xiaoqu from "../../assets/img/xiaoqu.png";
import zaixian from "../../assets/img/zaixian.png";
import lixian from "../../assets/img/lixian.png";

// 生成聚合图标，支持异步
async function getClusterImage(count) {
  let colorIn;
  if (count < 10) {
    colorIn = "rgba(110, 204, 57, 0.6)";
  } else if (count < 100) {
    colorIn = "rgba(240, 194, 12,  0.6)";
  } else {
    colorIn = "rgba(241, 128, 23,  0.6)";
  }

  const radius = 40;
  const thisSize = radius * 2;

  const circleCanvas = document.createElement("canvas");
  circleCanvas.width = thisSize;
  circleCanvas.height = thisSize;
  const circleCtx = circleCanvas.getContext("2d", { willReadFrequently: true });

  circleCtx.fillStyle = "#ffffff00";
  circleCtx.globalAlpha = 0.0;
  circleCtx.fillRect(0, 0, thisSize, thisSize);

  // 圆形底色
  circleCtx.globalAlpha = 1.0;
  circleCtx.beginPath();
  circleCtx.arc(radius, radius, radius, 0, Math.PI * 2, true);
  circleCtx.closePath();
  circleCtx.fillStyle = colorIn;
  circleCtx.fill();

  // 数字文字
  const text = count + "个";
  circleCtx.font = radius * 0.6 + "px bold normal"; // 设置字体
  circleCtx.fillStyle = "#ffffff"; // 设置颜色
  circleCtx.textAlign = "center"; // 设置水平对齐方式
  circleCtx.textBaseline = "middle"; // 设置垂直对齐方式
  circleCtx.fillText(text, radius, radius); // 绘制文字（参数：要写的字，x坐标，y坐标）

  return circleCanvas.toDataURL("image/png"); // getImage方法返回任意canvas的图片即可
}

export default {
  data() {
    return {
      current: 1,
      totalList: [],
      points: [],
      mapSource: null,
      graphicLayer: null,
      markers: [],
      online: 0,
      unOnline: 0,
      cluster: null,
      gridSize: 60,
      styles: [],
    };
  },

  mounted() {
    this.$nextTick(() => {
      this.init();
      this.addClass(1);
    });
  },

  unmounted() {
    if (this.mapSource) {
      this.mapSource.destroy();
    }
  },

  methods: {
    async init() {
      await getUserGroupTotal({ nodeType: 4 }).then((res) => {
        this.totalList.push({
          total: res.data.result,
          title: "乡镇",
          icon: "icon-suoshuxiangzhen",
        });
      });
      await getUserGroupTotal({ nodeType: 5 }).then((res) => {
        this.totalList.push({
          total: res.data.result,
          title: "社区",
          icon: "icon-menu_sqgg",
        });
      });
      await getCommunityTotal().then((res) => {
        this.totalList.push({
          total: res.data.result,
          title: "小区",
          icon: "icon-fangzi",
        });
      });
    },
    async addClass(index) {
      if (this.mapSource) {
        this.mapSource.destroy();
      }
      this.initMap();
      let that = this;
      this.current = index;
      if (index == 2) {
        this.points = [];
        this.markers = [];
        await getMapUserStatus().then((res) => {
          let userList = res.data.result;
          let lat = JSON.parse(localStorage.getItem("lnglat"))[0];
          let lng = JSON.parse(localStorage.getItem("lnglat"))[1];

          for (let i = 0; i < userList.length; i++) {
            // 有经纬度添加,没有则随机经纬度
            if (userList[i].lat && userList[i].lng) {
              this.points.push({
                lnglat: [userList[i].lat, userList[i].lng],
                status: userList[i].status,
                userName: userList[i].userName,
              });
            } else {
              userList[i].lat = this.replacePos(
                this.lnglatPushRandNum(String(lat)),
                this.lnglatPushRandNum(String(lat)).length - 3,
                this.randNum(),
                this.randNum()
              );
              userList[i].lng = this.replacePos(
                this.lnglatPushRandNum(String(lng)),
                this.lnglatPushRandNum(String(lng)).length - 3,
                this.randNum(),
                this.randNum()
              );
              if (!userList[i].userName) {
                this.points.push({
                  lnglat: [userList[i].lat, userList[i].lng],
                  status: userList[i].status,
                  userName: "微**户",
                });
              } else {
                this.points.push({
                  lnglat: [userList[i].lat, userList[i].lng],
                  status: userList[i].status,
                  userName: userList[i].userName,
                });
              }
            }
          }
        });

        this.userMarkerClusterer();
      } else if (index == 1) {
        this.points = [];
        this.markers = [];
        await getMapCommuntiyStatus().then((res) => {
          let communityList = res.data.result;
          let lat = JSON.parse(localStorage.getItem("lnglat"))[0];
          let lng = JSON.parse(localStorage.getItem("lnglat"))[1];
          for (let i = 0; i < communityList.length; i++) {
            // 有经纬度直接添加，否则随机生成
            if (communityList[i].lat && communityList[i].lng) {
              this.points.push({
                lnglat: [communityList[i].lng, communityList[i].lat],
                communityName: communityList[i].communityName,
              });
            } else {
              communityList[i].lat = this.replacePos(
                this.lnglatPushRandNum(String(lat)),
                this.lnglatPushRandNum(String(lat)).length - 3,
                this.randNum(),
                this.randNum()
              );
              communityList[i].lng = this.replacePos(
                this.lnglatPushRandNum(String(lng)),
                this.lnglatPushRandNum(String(lng)).length - 3,
                this.randNum(),
                this.randNum()
              );
              this.points.push({
                lnglat: [communityList[i].lat, communityList[i].lng],
                communityName: communityList[i].communityName,
              });
            }
          }
        });
        this.communityMarkerClusterer();
      }
    },

    initMap() {
      var that = this;

      var center = {
        lat: 30.688611,
        lng: 119.260277,
        alt: 2073759,
        heading: 0,
        pitch: -90,
      };

      var mapOptions = {
        scene: {
          center: center,
          shadows: false, // 是否启用日照阴影
          removeDblClick: true, // 是否移除Cesium默认的双击事件
          // 以下是Cesium.Viewer所支持的options【控件相关的写在另外的control属性中】
          sceneMode: 3, // 3等价于Cesium.SceneMode.SCENE3D,
          // 以下是Cesium.Scene对象相关参数
          showSun: true, // 是否显示太阳
          showMoon: true, // 是否显示月亮
          showSkyBox: true, // 是否显示天空盒
          showSkyAtmosphere: true, // 是否显示地球大气层外光圈
          fog: true, // 是否启用雾化效果
          fxaa: true, // 是否启用抗锯齿

          // 以下是Cesium.Globe对象相关参数
          globe: {
            // depthTestAgainstTerrain: false, // 是否启用深度监测
            baseColor: "#033447", // 地球默认背景色
            // showGroundAtmosphere: true, // 是否在地球上绘制的地面大气
            // enableLighting: false, // 是否显示昼夜区域
          },

          // 以下是Cesium.ScreenSpaceCameraController对象相关参数
          cameraController: {
            zoomFactor: 3.0, // 鼠标滚轮放大的步长参数
            minimumZoomDistance: 1, // 地球放大的最小值（以米为单位）
            maximumZoomDistance: 6115000, // 地球缩小的最大值（以米为单位）
            enableRotate: true, // 2D和3D视图下，是否允许用户旋转相机
            enableTranslate: true, // 2D和哥伦布视图下，是否允许用户平移地图
            enableTilt: true, // 3D和哥伦布视图下，是否允许用户倾斜相机
            enableZoom: true, // 是否允许 用户放大和缩小视图
            enableCollisionDetection: true, // 是否允许 地形相机的碰撞检测
          },
        },
        control: {
          // geocoder: true,
          defaultContextMenu: true, // 右击菜单
          contextmenu: {
            hasDefault: !that.contextMenuStatus,
          }, // 右键菜单
          // homeButton: true, // 视角复位按钮
          // navigationHelpButton: true, //帮助按钮
          // fullscreenButton: true, //全屏按钮
          // baseLayerPicker: true, //basemaps底图切换按钮
          // sceneModePicker: true //二三维切换按钮
        },
        basemaps: [
          {
            name: "天地图卫星",
            icon: "img/basemaps/tdt_img.png",
            type: "tdt",
            layer: "img_d",
            key: ["9ae78c51a0a28f06444d541148496e36"],
            show: false,
          },

          {
            name: "高德电子",
            icon: "/img/basemaps/blackMarble.png",
            type: "gaode",
            id: 2017,
            filterColor: "#003A4A",
            layer: "vec",
            show: true,
            brightness: 0.6,
            chinaCRS: "GCJ02",
            contrast: 1.8,
            gamma: 0.3,
            hue: 1,
            invertColor: true,
            name: "暗色底图",
            pid: 10,
            saturation: 0,
          },
          {
            name: "高德影像",
            icon: "img/basemaps/gaode_img.png",
            type: "group",
            layers: [
              { name: "底图", type: "gaode", layer: "img_d" },
              { name: "注记", type: "gaode", layer: "img_z" },
            ],
            show: false,
          },
        ],
      };

      var map = new mars3d.Map("mars3dContainer", mapOptions); //支持的参数请看API文档：http://mars3d.cn/api/Map.html

      this.mapSource = map;
    },

    // 随机生成经纬度
    replacePos(strObj, pos, replacetext1, replacetext2) {
      return (
        strObj.substr(0, pos - 1) +
        replacetext1 +
        replacetext2 +
        strObj.substring(pos + 1, strObj.length)
      );
    },
    randNum() {
      return Math.floor(Math.random() * 10);
    },
    //经纬度不满10位补
    lnglatPushRandNum(lnglat) {
      if (lnglat.length < 10) {
        for (let i = 0; i < 10 - lnglat.length; i++) {
          lnglat = lnglat.concat(this.randNum());
        }
      }
      return lnglat;
    },

    communityMarkerClusterer() {
      var data = [];

      this.points.forEach((element) => {
        data.push({
          lng: element.lnglat[0],
          lat: element.lnglat[1],
          alt: 0,
          communityName: element.communityName,
        });
      });

      var listAll = {
        data: data,
      };

      console.log(listAll);
      if (this.graphicLayer) this.graphicLayer.clear();

      const singleDigitPins = {};

      // 创建矢量数据图层（业务数据图层）
      var graphicLayer = new mars3d.layer.BusineDataLayer({
        data: listAll,
        dataColumn: "data", // 数据接口中对应列表所在的取值字段名
        lngColumn: "lng",
        latColumn: "lat",
        altColumn: "alt",
        // 点的聚合配置
        clustering: {
          enabled: true,
          pixelRange: 20,
          minChanged: 0.01,
          clampToGround: true,
          addHeight: 1000,
          opacity: 1,
          // getImage是完全自定义方式
          getImage: async function (count, result) {
            const key = "type1-" + count; // 唯一标识，不同图层需要设置不一样

            let image = singleDigitPins[key];
            if (image) {
              return image; // 当前页面变量有记录
            }

            // image = await localforage.getItem(key);
            if (image) {
              singleDigitPins[key] = image;
              return image; // 浏览器客户端缓存有记录
            }

            image = await getClusterImage(count); // 生成图片
            singleDigitPins[key] = image; // 记录到当前页面变量，未刷新页面时可直接使用
            // localforage.setItem(key, image); // 记录到浏览器客户端缓存，刷新页面后也可以继续复用
            return image;
          },
        },
        symbol: {
          type: "billboardP",
          styleOptions: {
            image: xiaoqu,
            width: 45,
            height: 64, // billboard聚合必须有width、height
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            scaleByDistance: new Cesium.NearFarScalar(1000, 0.7, 5000000, 0.3),
            label: {
              text: "{communityName}",
              font_size: 19,
              color: Cesium.Color.AZURE,
              outline: true,
              outlineColor: Cesium.Color.BLACK,
              outlineWidth: 2,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              pixelOffset: new Cesium.Cartesian2(0, 20), // 偏移量
              distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                0.0,
                80000
              ),
            },
          },
        },
        // 自定义创建对象，可替代symbol、
        // onCreateGraphic: function (e) {
        //   const graphic = new mars3d.graphic.BillboardEntity({
        //     position: e.position,
        //     style: {
        //       image: "img/marker/lace-blue.png",
        //       width: 25,
        //       height: 34, // 聚合必须有width、height
        //       horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        //       verticalOrigin: Cesium.VerticalOrigin.BOTTOM
        //     },
        //     attr: e.attr
        //   })
        //   graphicLayer.addGraphic(graphic)
        // },
      });

      this.mapSource.addLayer(graphicLayer);
      this.graphicLayer = graphicLayer;
    },

    userMarkerClusterer() {
      var data = [];
      this.unOnline = 0;
      this.online = 0;
      this.points.forEach((element) => {
        if (element.status == 0) {
          this.unOnline += 1;
        } else {
          this.online += 1;
        }

        data.push({
          lng: element.lnglat[0],
          lat: element.lnglat[1],
          alt: 0,
          status: element.status,
          userName: element.userName,
        });
      });

      var listAll = {
        data: data,
      };

      console.log(listAll);
      if (this.graphicLayer) this.graphicLayer.clear();

      const singleDigitPins = {};

      // 创建矢量数据图层（业务数据图层）
      var graphicLayer = new mars3d.layer.BusineDataLayer({
        data: listAll,
        dataColumn: "data", // 数据接口中对应列表所在的取值字段名
        lngColumn: "lng",
        latColumn: "lat",
        altColumn: "alt",
        // 点的聚合配置
        clustering: {
          enabled: true,
          pixelRange: 20,
          minChanged: 0.01,
          clampToGround: true,
          addHeight: 1000,
          opacity: 1,
          // getImage是完全自定义方式
          getImage: async function (count, result) {
            const key = "type1-" + count; // 唯一标识，不同图层需要设置不一样

            let image = singleDigitPins[key];
            if (image) {
              return image; // 当前页面变量有记录
            }

            // image = await localforage.getItem(key);
            if (image) {
              singleDigitPins[key] = image;
              return image; // 浏览器客户端缓存有记录
            }

            image = await getClusterImage(count); // 生成图片
            singleDigitPins[key] = image; // 记录到当前页面变量，未刷新页面时可直接使用
            // localforage.setItem(key, image); // 记录到浏览器客户端缓存，刷新页面后也可以继续复用
            return image;
          },
        },
        symbol: {
          type: "billboardP",
          styleOptions: {
            image: "{status}" ? lixian : zaixian,
            width: 45,
            height: 64, // billboard聚合必须有width、height
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            scaleByDistance: new Cesium.NearFarScalar(1000, 0.7, 5000000, 0.3),
            label: {
              text: "{userName}",
              font_size: 19,
              color: Cesium.Color.AZURE,
              outline: true,
              outlineColor: Cesium.Color.BLACK,
              outlineWidth: 2,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              pixelOffset: new Cesium.Cartesian2(0, 20), // 偏移量
              distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                0.0,
                80000
              ),
            },
          },
        },
        // 自定义创建对象，可替代symbol、
        // onCreateGraphic: function (e) {

        //
        //         var arrt=e.arrt

        //
        //   const graphic = new mars3d.graphic.BillboardEntity({
        //     name: e.arrt.userName,
        //     position:e.position,
        //     attr: e.arrt,
        //     style: {
        //       image:e.arrt.status?zaixian:lixian,
        //       scale: 1,
        //       horizontalOrigin: this.Cesium.HorizontalOrigin.CENTER,
        //       verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
        //       clampToGround: true, // 是否贴地
        //       visibleDepth: false, // 是否被遮挡
        //       label: {
        //         text: e.arrt.userName,
        //         visibleDepth: false,
        //         clampToGround: true,
        //         opacity: 0.8, // 透明度
        //         font_size: 40,
        //         scale: 0.8,
        //         color: "#ffffff",
        //         // pixelOffsetX: 110,
        //         pixelOffsetY: -70,
        //         outlineColor: "#ffffff", // 边框颜色
        //         outlineWidth: 1, // 边框宽度
        //         innerHeight: 20,
        //         background: true,
        //         backgroundColor: "rgba(255,215,0,0.5)",
        //         distanceDisplayCondition:
        //           new this.Cesium.DistanceDisplayCondition(10.0, 2000.0),
        //         scaleByDistance: new this.Cesium.NearFarScalar(
        //           500,
        //           1,
        //           1400,
        //           0.0
        //         ),
        //         translucencyByDistance: new this.Cesium.NearFarScalar(
        //           500,
        //           1,
        //           1400,
        //           0.0
        //         ),
        //       },
        //     },
        //   });
        //   graphicLayer.addGraphic(graphic);
        // },
      });

      this.mapSource.addLayer(graphicLayer);
      this.graphicLayer = graphicLayer;
    },
  },
};
</script>
<style scoped lang="less">
#mars3dContainer {
  position: absolute;
  height: 90%;
  top: 10%;
  width: 100%;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
#container {
  width: 100%;
  height: 100%;
  width: 100%;
  border-radius: 8px;
}
.header {
  display: flex;
  justify-content: space-between;
  height: 82px;
  background-color: #fff;
  line-height: 82px;
  padding: 0 46px;
  border-radius: 8px;
  position: absolute;
  width: 100%;
  z-index: 99;
  > div {
    > span {
      cursor: pointer;
    }
  }
}
.is_actice {
  color: #5097ff;
}
.air-view {
  width: 151px;
  height: 115px;
  border-radius: 8px;
  padding: 22px 62px 21px 19px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0px 2px 4px #000;
  position: absolute;
  right: 64px;
  top: 94px;
}
.green-block {
  width: 28px;
  height: 16px;
  display: inline-block;
  background-color: green;
  margin-right: 9px;
  border-radius: 2px;
}
.gray-block {
  width: 28px;
  height: 16px;
  display: inline-block;
  background-color: gray;
  margin-right: 9px;
  border-radius: 2px;
}
</style>