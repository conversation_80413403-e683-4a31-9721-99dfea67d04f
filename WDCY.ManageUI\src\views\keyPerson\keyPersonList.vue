<template>
	<key-person-edit @search="search" :sexList="sexList" :certificateTypeTagList="certificateTypeTagList"></key-person-edit>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.name" @keydown.enter="search" placeholder="姓名" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="12">
			<el-button style="float: right;" type="primary" @click="add" v-if="hasPerm('keyPerson:keyPerson:add')">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table :data="personList" border height="calc(100vh - 300px)" style="width: 100%">
				<el-table-column prop="photo" width="100" align="center" label="照片">
					<template #default="scope">
						<el-image preview-teleported fit="contain" style="width: 50px; height: 50px"
							:src="imgServer + scope.row.photo" :preview-src-list="[imgServer + scope.row.photo]"></el-image>
					</template>
				</el-table-column>
				<!-- <el-table-column prop="communityName" width="180" align="center" label="所属小区" /> -->
				<el-table-column prop="name" width="100" align="center" label="姓名" />
				<el-table-column prop="age" width="100" align="center" label="年龄" />
				<el-table-column prop="idCard" width="160" align="center" label="身份证" />
				<el-table-column prop="certificateType" width="160" align="center" :formatter="formatCertificateType" label="证件类型" />

				<el-table-column prop="address" align="left" label="地址" />
				<el-table-column prop="personType" width="240" align="left" label="人员类型">
					<template #default="scope">
						<el-tag v-for="item in scope.row.keyPersonTypeVOList" :key="item.personTypeId" >
							{{ item.name }} 
						</el-tag>&nbsp;
					</template>
				</el-table-column>
				<el-table-column prop="status" width="100" align="center" label="状态">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column align="center" width="100" label="操作" v-if="hasPerm('keyPerson:keyPerson:edit') || hasPerm('keyPerson:keyPerson:delete')">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('keyPerson:keyPerson:edit')">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('keyPerson:keyPerson:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" 
				:page-sizes="[10, 20, 50, 100]"
				:total="Number(total)"
				layout="total, sizes, prev, pager, next, jumper" 
				@current-change="currentChange" 
				@size-change="handleSizeChange" >
			</el-pagination>
		</el-col>
	</el-row>
</template>

<script>
import { keyPersonList, keyPersonListDelete, getKeyPerson } from "@/api/keyPerson/keyPerson"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import keyPersonEdit from "@/componts/keyPerson/keyPersonEdit.vue"

export default {
	components: { keyPersonEdit },
	data() {
		return {
			searchModel: {},
			imgServer: import.meta.env.VITE_BASE_API,
			communityId: localStorage.getItem("communityId"),
			personList: [],
			statusList: [],
			sexList: [],
			total: 0,
			pageSize: 10,
			certificateTypeTagList: []
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatCertificateType(row, column, cellValue, index) {
			return formatDict(this.certificateTypeTagList, cellValue)
		},
		search() {
			this.searchModel.communityId = this.communityId
			keyPersonList(this.searchModel)
				.then(res => {
					this.personList = res.data.result.list
					this.total = res.data.result.total
				})
		},
		edit(id) {
			getKeyPerson(id)
				.then(res => {
					mitt.emit('openKeyPersonEdit', res.data.result)
				})
		},
		add() {
			mitt.emit('openKeyPersonAdd')
		},
		deleted(id) {
			this.$confirm('删除重点人员, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				keyPersonListDelete(id)
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num) {
			this.searchModel.pageSize = num
			this.search()
		},
		async init() {
			mitt.off('openKeyPersonEdit')
			mitt.off('openKeyPersonAdd')
			try {
				this.searchModel.communityId = this.communityId
				let res = await keyPersonList(this.searchModel)
				this.personList = res.data.result.list
				this.total = res.data.result.total
				
				let resTypeList = await listDictByNameEn('common_status')
				this.statusList = resTypeList.data.result

				let sex_res = await listDictByNameEn('sex')
				this.sexList = sex_res.data.result

				let certificateType_res = await listDictByNameEn('certificate_type')
				this.certificateTypeTagList = certificateType_res.data.result
			} catch (err) {
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
.el-row {
	margin-bottom: 20px;
	background-color: #fff;
	padding: 20px 10px;
	border-radius: 5px;
}

.el-tag{
	margin-right: 5px;
}

</style>
