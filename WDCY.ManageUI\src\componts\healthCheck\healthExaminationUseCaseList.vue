<template>
	<health-examination-useCase-edit @search="search" :statusList="statusList"></health-examination-useCase-edit>
    <el-dialog draggable
		top="3vh"
		width="70%"
		v-loading="loading"
		v-model="dialog.show"
		:title="dialog.title"
	>
	<el-row :gutter="20">
		<el-col :span="3">
			<el-input v-model="searchModel.caseName" placeholder="用例名称" clearable />
		</el-col>
		<el-col :span="2">
			<el-select style="width: 100%;" v-model="searchModel.status" placeholder="状态" clearable>
				<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
					:value="item.nameEn"></el-option>
			</el-select>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
        <el-col :span="4" :push="11">
			<el-button style="float: right;" type="primary" @click="add">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table :data="checkCaseList" border style="width: 100%">
				<el-table-column prop="sort" width="70" align="center" label="排序" />
				<el-table-column prop="id" width="180" align="center" label="用例ID" />
				<el-table-column prop="caseName" show-overflow-tooltip width="280" align="center" label="用例名称" />
				<el-table-column prop="sceneName" show-overflow-tooltip width="120" align="center" label="场景名称" />
				<el-table-column prop="status" align="center" label="状态" width="80">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
						<!-- <el-switch :active-value="1" :inactive-value="0" v-model="scope.row.status" @change="editStatus(scope.row)" /> -->
					</template>
				</el-table-column>
				<el-table-column prop="createTime" align="center" width="170" label="创建时间"/>
				<el-table-column prop="updateTime" align="center" width="170" label="修改时间"/>
				<el-table-column prop="note" show-overflow-tooltip align="center" label="备注"/>

				<el-table-column align="center" width="100" label="操作">
					<template #default="scope">
							<el-button type="text" size="default" @click="edit(scope.row.id)">编辑</el-button>
							<el-button type="text" size="default" @click="deleted(scope.row.id)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</el-dialog>


</template>
<script>
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import { listTestCase, getTestCase, addTestCase, editTestCase, deleteTestCase } from "@/api/healthCheck/useCaseScenario"
import healthExaminationUseCaseEdit from "@/componts/healthCheck/healthExaminationUseCaseEdit.vue"
export default {
	props: ['statusList'],
	components:{ healthExaminationUseCaseEdit },
	data() {
		return {
			searchModel: {
				name:""
			},
			checkCaseList: [],
			sysFuncId: "",
			funcName: "",
			imgServer: import.meta.env.VITE_BASE_API,
			total:0,
			pageSize: 10,
            dialog: {
				show:false,
				title:''
			},
			expandParams: {},
			note: ''
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatThirdSystem(list,value){
			let result = ""
			list.forEach(item => {
				if (item.id == value) {
					result = item.sysName
				}
			})
			return result
		},
		search() {
			this.searchModel.sysFuncId = this.sysFuncId
			listTestCase(this.searchModel).then( res => {
				this.checkCaseList = res.data.result.list
				this.total = res.data.result.total

			})
		},
		add(){
			const data = {funcName: this.funcName, sysFuncId: this.sysFuncId, expandParams: this.expandParams, note:this.note}
			mitt.emit('openHealthExaminaCaseCheckAdd', data)
			this.dialog.show = false
		},
		edit(id){
			getTestCase(id).then(res => {
				mitt.emit('openHealthExaminaCaseCheckEdit',res.data.result)
			})
			this.dialog.show = false
			
		},
		deleted(id){
			this.$confirm('删除信息, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteTestCase(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		formatStatus(row, column, cellValue, index){
			let result = ''
			for(let item of this.statusList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		async init(){
            console.log(this.$route);
			try{
				this.searchModel.pageSize = 10
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	},
    mounted(){
        this.$nextTick(function(){
            mitt.on("openUseCase",(res)=>{
				this.checkCaseList = res.list
				this.note = res.note
				this.sysFuncId = res.sysFuncId
				this.funcName = res.title
				this.total = res.total
				this.expandParams = res.expandParams
                this.dialog.show = true
                this.dialog.title = "所属系统功能---"+res.title
            })

        })
    }
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
