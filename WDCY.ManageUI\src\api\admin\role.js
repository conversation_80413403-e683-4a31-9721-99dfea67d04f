import request from '@/utils/request'

export const pagingRole = (data) =>
	request({
		url: '/role',
		method: 'get',
		params: data
	})
export const listRole = (data) =>
	request({
		url: '/role/list',
		method: 'get',
		params: data
	})
export const getRole = (id) =>
	request({
		url: '/role/'+id,
		method: 'get'
	})
export const saveRole = (data) =>
	request({
		url: '/role',
		method: 'post',
		data: data
	})
export const deleteRole = (id) =>
	request({
		url: '/role',
		method: 'delete',
		params: {
			id: id
		}
	})
export const roleAuthorization = () =>
	request({
		url: '/role/roleAuthorization',
		method: 'get'
	})
	
export const scopeList = (data) =>
	request({
		url: '/role/scope',
		method: 'get',
		params: data
	})
export const scopeEdit = (data) =>
	request({
		url: '/role/scope',
		method: 'put',
		data:data
	})
