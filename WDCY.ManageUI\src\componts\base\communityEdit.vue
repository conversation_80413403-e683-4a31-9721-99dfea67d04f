<template>
  <el-dialog
    draggable
    width="50%"
    destroy-on-close
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form
      :rules="rules"
      ref="form"
      :model="communityModel"
      label-width="100px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="小区名称" prop="communityName">
            <el-input
              v-model="communityModel.communityName"
              placeholder="小区名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="可视化名" prop="visualAlias">
            <el-input
              maxlength="15"
              show-word-limit
              v-model="communityModel.visualAlias"
              placeholder="可视化名"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="小区面积(m²)" label-width="" prop="acreage">
            <el-input
              v-model="communityModel.acreage"
              placeholder="小区面积(平方米)"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地址" prop="address">
            <el-input
              v-model="communityModel.address"
              placeholder="地址"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户组" prop="groupId">
            <el-cascader
              filterable
              v-model="communityModel.groupId"
              :show-all-levels="false"
              style="width: 100%"
              :props="{ checkStrictly: true }"
              :options="groupList"
              @change="handleChange"
              clearable
              placeholder="选择组"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="经纬度" prop="lng">
            <el-input
              style="width: 45%"
              v-model="communityModel.lng"
              placeholder="经度"
            ></el-input
            >&nbsp;-&nbsp;<el-input
              style="width: 45%"
              v-model="communityModel.lat"
              placeholder="纬度"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-form-item label-width="0">
            <el-button
              type="text"
              style="font-size: 30px; padding: 0"
              @click="selectPoint"
            >
              <el-icon :size="30">
                <location-filled></location-filled>
              </el-icon>
            </el-button>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="2">
          <el-button style="height: 30px;" @click="map3d" >巡 游</el-button>
        </el-col> -->
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="开放式ID" prop="openId">
            <el-input
              readonly
              disabled
              v-model="communityModel.openId"
              placeholder="小区开放式ID"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select
              style="width: 100px"
              v-model="communityModel.status"
              placeholder="状态"
            >
              <el-option
                v-for="item in statusList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="parseInt(item.nameEn)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="地理围栏" prop="note">
            <el-input
              v-model="communityModel.polyCoords"
              placeholder="地理围栏"
              type="textarea"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="2">
          <el-form-item label-width="0">
            <el-button
              type="text"
              style="font-size: 30px; padding: 0"
              @click="selectArea"
            >
              <el-icon :size="30">
                <location-filled></location-filled>
              </el-icon>
            </el-button>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input
              v-model="communityModel.note"
              maxlength="200"
              placeholder="请简单说明小区及其硬件配置情况"
              show-word-limit
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="接入时间" prop="joinTime">
            <el-date-picker
              v-model="communityModel.joinTime"
              type="date"
              placeholder="接入时间"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="验收时间" prop="beginTime">
            <el-date-picker
              v-model="communityModel.beginTime"
              type="date"
              placeholder="验收时间"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="维护期限" prop="endTime">
            <el-date-picker
              v-model="communityModel.endTime"
              type="date"
              placeholder="维护期限"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="扩展参数" prop="expandParams">
            <!-- <json-editor-vue class="editor" language="cn" v-model="jsonVal" /> -->
            <JsonEditorVue
              language="cn"
              class="editor"
              :modelValue="jsonVal"
              @update:modelValue="changeJson"
            />
            <!-- <JsonEditorVue class="editor" modelValue="data" @update:modelValue="changeJson" @blur="validate" /> -->
            <!-- <json-editor-vue class="editor" v-model="jsonVal" /> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>

<script>
import { editCommunity, addCommunity } from "@/api/base/community";
import mitt from "@/utils/mitt";
import { openMap, openMarsMap } from "@/utils/myUtils";
import JsonEditorVue from "json-editor-vue3";
import { getCommunity } from "@/api/base/community";
export default {
  components: { JsonEditorVue },
  props: ["statusList", "groupList"],
  data() {
    return {
      loading: false,
      jsonVal: {},
      communityModel: {},
      dialog: {},
      rules: {
        communityName: [
          {
            required: true,
            message: "请输入小区名",
            trigger: "blur",
          },
        ],
        visualAlias: [
          {
            required: true,
            message: "请输入可视化名",
            trigger: "blur",
          },
        ],
        groupId: [
          {
            required: true,
            message: "请选择所属组",
            trigger: "change",
          },
        ],
      },
    };
  },
  mounted() {
    this.jsonVal = {};
    this.$nextTick(function () {
      mitt.on("openCommunityEdit", (community) => {
        this.communityModel = community;
        this.dialog.show = true;
        this.dialog.title = "修改信息";
        this.jsonVal = JSON.parse(this.communityModel.expandParams);
      });
      mitt.on("openCommunityAdd", () => {
        this.communityModel = {
          id: 0,
          status: 1,
        };
        this.dialog.show = true;
        this.dialog.title = "添加小区";
      });
      mitt.on("setPointValue", (e) => {
        console.log(e);
        this.communityModel.lng = e[0];
        this.communityModel.lat = e[1];
      });
      mitt.on("setAddress", (e) => {
        this.communityModel.address = e.regeocode.formattedAddress;
      });

      mitt.on("polyCoords", (e) => {
        if (e && e.length > 0) {
          console.log("polyCoords", JSON.stringify(e));
          this.communityModel.polyCoords = JSON.stringify(e);
        }
      });
    });
  },
  created() {
    mitt.off("openMarsMap");
  },
  methods: {
    changeJson(json) {
      // console.info("changeJson: ", json);
      this.jsonVal = json;
    },
    handleChange(e) {
      if (e == null) {
        this.communityModel.groupId = null;
        return;
      }
      this.communityModel.groupId = e[e.length - 1];
    },

    selectArea() {
      var communityId = this.communityModel.id;
      getCommunity(communityId)
        .then((res) => {
          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map.mode;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          var polyCoords = [];

          if (result.polyCoords) {
            polyCoords = JSON.parse(result.polyCoords);
          }

          if (result.enabled3d && mode == "mars3d") {
            position = config.map.sdgis.position;
            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (this.communityModel.lng && this.communityModel.lat) {
            center = {
              lng: this.communityModel.lng,
              lat: this.communityModel.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }

          var data = {
            enabled3d: result.enabled3d,
            edit: true,
            polyCoords: polyCoords,
            polyCoordsEdit: true,
            point: [center.lng, center.lat, 0],
            position: position,
            center: center,
            modeUrl: modeUrl,
            title: "地理围栏",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);

          openMarsMap(mitt, data);
        })
        .catch((err) => {});
    },

    selectPoint() {
      var communityId = this.communityModel.id;
      getCommunity(communityId)
        .then((res) => {
          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map.mode;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          if (result.enabled3d && mode == "mars3d") {
            position = config.map.sdgis.position;

            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (this.communityModel.lng && this.communityModel.lat) {
            center = {
              lng: this.communityModel.lng,
              lat: this.communityModel.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }

          var data = {
            enabled3d: result.enabled3d,
            edit: true,
            point: [center.lng, center.lat, 0],
            position: position,
            center: center,
            modeUrl: modeUrl,
            title: "地图选点",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);
          openMarsMap(mitt, data);
        })
        .catch((err) => {});
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.communityModel.expandParams = JSON.stringify(this.jsonVal);

          if (this.communityModel.id == 0) {
            addCommunity(this.communityModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          } else {
            editCommunity(this.communityModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          }
        }
      });
    },
  },
};
</script>

<style scoped>
.editor {
  width: 805px;
}
</style>
