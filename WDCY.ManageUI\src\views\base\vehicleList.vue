<template>
	<vehicle-edit :statusList="statusList" :typeList="typeList" :parkingList="parkingList" :plateColorList="plateColorList" @search="search"></vehicle-edit>
	<vehicle-view @search="search" :tagList="tagList"></vehicle-view>
	<open-id-edit @search="search"></open-id-edit>
	<input type="file" id="btn_file" @change="filechange" style="display:none">
	<el-row :gutter="20">
		<el-col :span="8" style="display:flex;width:500px">
			<el-input style="margin-right:20px" v-model="searchModel.vehicleNumber" @keydown.enter="search" placeholder="车牌号" clearable />
			<el-input style="margin-right:20px" v-model="searchModel.userName" @keydown.enter="search" placeholder="姓名" clearable />
			<el-input  v-model="searchModel.idCard " @keydown.enter="search" placeholder="身份证" clearable />
		</el-col>
		<el-col :span="2">
			<el-select style="width: 100%;" v-model="searchModel.status" placeholder="状态" clearable>
				<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
					:value="parseInt(item.nameEn)"></el-option>
			</el-select>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="6">
			<el-button style="float: right;" type="primary" @click="add" v-if="hasPerm('base:vehicle:add')">添 加</el-button>
			<!-- <el-button style="float: right;margin-right: 20px;" type="primary" @click="importExcel">导 入</el-button> -->
			
			<el-dropdown style="float: right; margin-right: 20px;" split-button type="primary" @click="importExcel">
				导 入
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item ><a :href="imgServer + '/assets/import/小区基础信息-车辆导入模板.xlsx'">导入模板下载</a> </el-dropdown-item>
						<!-- <el-dropdown-item command="b">Action 2</el-dropdown-item> -->
					</el-dropdown-menu>
				</template>
			</el-dropdown>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="vehicleList" border height="calc(100vh - 300px)" style="width: 100%">
				<el-table-column prop="vehicleNumber" align="center" label="车牌号"/>
				<el-table-column prop="personName" align="center" label="车主姓名"/>
				<el-table-column prop="idCard" align="center" width="160" label="身份证号"/>
				<el-table-column prop="vehicleType" :formatter="formatType" align="center" label="车型" />
				<el-table-column prop="plateColor" :formatter="formatPlateColor" align="center" label="车牌颜色" />
				<el-table-column prop="parkingType" :formatter="formatParking" align="center" label="车位类型" />
				<el-table-column prop="parkingNumber" align="center" label="车位编号" />
				<el-table-column prop="createTime"  align="center" label="创建时间" width="168" />
				<el-table-column prop="updateTime"  align="center" label="更新时间" width="168" />

				<!-- <el-table-column prop="status" width="100" :formatter="formatStatus" align="center" label="状态" /> -->
				<el-table-column prop="status" align="center" label="状态" width="95">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>
				
				<el-table-column align="center" width="200" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="view(scope.row.personId)">查看</el-button>
						<el-button type="text" size="default" @click="viewLog(scope.row.vehicleNumber)">查看日志</el-button>
						<el-button type="text" size="default" @click="openId(scope.row)" v-if="hasPerm('base:person:update-openid:edit')">OpenId</el-button>
						<el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('base:vehicle:edit')">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('base:vehicle:delete')">删除</el-button>
						<el-button type="text" size="default" @click="update(scope.row)">更新</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="searchModel.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>

		<el-dialog draggable v-model="errMsgStatus" :title="communityName" custom-class="diaLogClass">
			<div class="importBox">
				<span class="importText">已成功导入<strong style="color:#4B96FF">{{successNum}}</strong>条</span>
				<span class="importText">导入失败<strong style="color:red">{{failNum}}</strong>条</span>
			</div>
			<div class="errMsg">
				<div class="err_msg_title">
					<span class="blue_icon"></span>
					<span style="margin-left:8px; display:inline-block">导入失败明细</span>
				</div>
				<div class="errMsgMain" v-if="failNum">
					<el-tooltip v-for="(item, index) in errMsgList" :key="index">
						<template #content>
							<div v-for="(item, index) in popUpMsg" :key="index" style="line-height: 26px">{{item}}</div>
						</template>
						<div @mouseover="hover(item)" class="everyErrMsg">
							{{item}}
						</div>
					</el-tooltip>
				</div>
				<img v-else class="nothing" src="../../assets/img/nothing.png" alt="">
			</div>
			<div class="button_box">
				<el-button @click="copyToClipboard($event)">复 制</el-button>
				<el-button @click="close">确 定</el-button>
			</div>
		</el-dialog>
	</el-row>
</template>

<script>
import { ElLoading } from 'element-plus'
import { listVehicle,deleteVehicle,updateVehicle,getVehicle,importData } from "@/api/base/vehicle"
import { getPerson } from "@/api/base/person"
import { listPerson } from "@/api/base/person"
import { listDictByNameEn } from "@/api/admin/dict"
import { accountCommunity } from "@/api/base/community"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import vehicleView from "@/componts/base/vehicleView.vue"
import vehicleEdit from "@/componts/base/vehicleEdit.vue"
import openIdEdit from "@/componts/base/openIdEdit.vue"

export default {
	components:{ vehicleEdit, vehicleView, openIdEdit },
	data() {
		return {
			searchModel: {
				communityId:localStorage.getItem("communityId"),
				pageSize:10
			},
			vehicleList: [],
			statusList:[],
			plateColorList:[],
			typeList:[],
			parkingList:[],
			tagList:[],
			total:0,
			errMsgList:[],
			imgServer: import.meta.env.VITE_BASE_API,
			errMsgStatus: false,
			popUpMsg: [],
			successNum: "",
			failNum: "",
			communityName: "",
			fullLoading: null
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatType(row, column, cellValue, index){
			return formatDict(this.typeList, cellValue)
		},
		formatParking(row, column, cellValue, index){
			return formatDict(this.parkingList, cellValue)
		},
		formatPlateColor(row, column, cellValue, index){
			return formatDict(this.plateColorList, cellValue)
		},
		// 导入功能
		filechange(){
			this.fullLoading = ElLoading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.7)' , text: '正在执行导入，稍候将返回结果，请不要刷新！！！'});
			var file = document.getElementById("btn_file").files[0]
			var formdata = new FormData();
			formdata.append("file",file);
			formdata.append("communityId",localStorage.getItem("communityId"));
			importData(formdata)
			.then(res =>{
				this.fullLoading.close()
				if (res.data.result) {
					this.errMsgStatus = true
					this.errMsgList = res.data.result.errList
					this.successNum = res.data.result.success
					this.failNum = res.data.result.fail
					this.search()
				}
			})
		},
		close(){
			this.errMsgStatus = false
		},
		hover(item){
			if (item) {
				if (item.includes('；')) {
					item = item.substring(item.indexOf(" ") + 1)
					this.popUpMsg = item.split('；')
				}else {
					this.popUpMsg = [item]
				}
			}		
		},
		copyToClipboard(){ // 复制
			if (!this.failNum) {
				return
			}
            // navigator clipboard 需要https等安全上下文
			const that = this
            if (navigator.clipboard && window.isSecureContext) {
                // navigator clipboard 向剪贴板写文本
				that.$message.success("复制成功!")
                return navigator.clipboard.writeText(this.errMsgList.join('\r\n'));
            } else {
                // 创建text area
                let textArea = document.createElement("textarea");
                textArea.value = this.errMsgList.join('\r\n');
                // 使text area不在viewport，同时设置不可见
                textArea.style.position = "absolute";
                textArea.style.opacity = 0;
                textArea.style.left = "-999999px";
                textArea.style.top = "-999999px";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
				that.$message.success("复制成功!")
                return new Promise((res, rej) => {
                    // 执行复制命令并移除文本框
                    document.execCommand('copy') ? res() : rej();
                    textArea.remove()
                })
            }
        },
		importExcel(){
			document.getElementById("btn_file").value = null
			document.getElementById("btn_file").click()
		},
		search() {
			listVehicle(this.searchModel)
			.then(res => {
				this.vehicleList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		view(id){
			getPerson('particular/'+id)
				.then(pres =>{
					listVehicle({ personId: id, communityId: localStorage.getItem("communityId") })
					.then(vres =>{
							mitt.emit('openVehicleView', { person: pres.data.result, vehicle: vres.data.result})
						})
				})
		},
		viewLog(name){
			console.log(name);
			this.$router.push({path:"/log",query:{body:name}})
		},
		openId(row){
			const data = {
				id: row.id,
				openId: row.openId,
				type: "vehicle"
			}
			mitt.emit('openOpenIdEdit',data)
		},
		edit(id){
			getVehicle(id)
			.then(res =>{
				mitt.emit('openVehicleEdit',res.data.result)
			})
		},
		add(){
			mitt.emit('openVehicleAdd')
		},
		deleted(id){
			this.$confirm('删除车辆, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteVehicle(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		update(row){
			console.log(row);
			const searchData = {
				id: row.id,
				openId: row.openId,
				communityId: localStorage.getItem("communityId"),
			}
			updateVehicle(searchData).then( res => {
				// this.search()
				window.location.reload();
				this.$message.success(res.data.msg)
			})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			mitt.off('openVehicleEdit')
			mitt.off('openVehicleAdd')
			mitt.off('openVehicleView')
			try{
				
				let res = await listVehicle(this.searchModel)
				this.vehicleList = res.data.result.list
				this.total = res.data.result.total
				let vehicle_status = await listDictByNameEn('vehicle_status')
				this.statusList = vehicle_status.data.result
				let plate_color = await listDictByNameEn('plate_color')
				this.plateColorList = plate_color.data.result
				let vehicle_type = await listDictByNameEn('vehicle_type')
				this.typeList = vehicle_type.data.result
				let parking_type = await listDictByNameEn('parking_type')
				this.parkingList = parking_type.data.result
				let tagRes = await listDictByNameEn('person_tag')

				// 格式化标签
				let tagList = []
				for (let item of tagRes.data.result) {
					var nameEn = item.nameEn.split(".")
					var type = ''
					if (nameEn.length > 1) {
						type = nameEn[1]
					}
					tagList.push({
						nameEn: nameEn[0],
						nameCn: item.nameCn,
						type: type,
					})
				}
				this.tagList = tagList
			}catch(err){
			}
		}
	},
	created() {
		this.init()
		accountCommunity().then(res => {
			const communityId = localStorage.getItem("communityId")
			const communityName = res.data.result.filter(item => {
				return communityId == item.id
			})
			this.communityName = `导入${communityName[0].communityName}小区基础数据`
		})
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	.errMsg{
	width: 1056px;
	height: 534px;
	/* overflow-x: auto; */
	background-color: #fff;
	/* position: relative; */
	color: #666666;
	font-size: 14px;
	box-sizing: border-box;
}
.err_msg_title{
	padding-top: 18px;
	padding-bottom: 24px;
	padding-left: 19px;
}
.errMsgMain{
	height: 456px;
	overflow-x: auto;
}
.blue_icon {
	height: 16px;
	width: 4px;
	background-color: #4B96FF;
	display: inline-block;
	border-radius: 1px;
}
.everyErrMsg{
	height: 38px;
	width: 1019px;
	line-height: 38px;
	/* margin-bottom: 10px; */
	white-space: nowrap; 
	text-overflow: ellipsis;
	overflow: hidden;
	padding: 0 19px;
	cursor: pointer;
	user-select:none
}
.everyErrMsg:nth-of-type(2n+1){
	background-color: rgba(80, 158, 255,.1);
}
.nothing{
	text-align: center;
	display: block;
	margin: auto;
	/* margin-bottom: 140px; */
}
/deep/ .diaLogClass{
	height: 780px;
	width: 1124px;
	background-color: rgb(241, 245, 255);
	overflow-x: auto;
	position: relative;
	border-radius: 16px;
}
/deep/ .el-dialog__body{
	padding: 0 34px;
}
.importBox{
	background-color: #fff;
	width: 1056px;
	line-height: 48px;
	border-radius: 6px;
	margin: 27px auto 5px;
	font-size: 14px;
}
.importText{
	padding-left: 32px;
}
.button_box{
	margin: 32px auto 36px;
	width: 237px;
}
.button_box > button{
	width: 112px;
	height: 40px;
}
.button_box > button:nth-child(1){
	background-color: #3694FF;
	color: #fff;
}
.button_box > button:nth-child(2){
	color: #3694FF;
	border: 1px solid#3694FF;
}
	/* 隐藏滚动条 */
	::-webkit-scrollbar{
	display: none;
	}
</style>
