<template>
    <we-chat-user-view :certificateTypeTagList="certificateTypeTagList" :sexList="sexList"></we-chat-user-view>
	<wx-bind-menu @search="search" :roleList="roleList" :userGroupList="userGroupList"></wx-bind-menu>
	<el-row :gutter="20">
		<el-col :span="16" style="display:flex">
			<el-input style="margin-right:10px" v-model="searchModel.name" placeholder="姓名" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.phone" placeholder="手机号" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.idCard" placeholder="身份证" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.address" placeholder="证件类型" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.openId" placeholder="用户唯一标识" clearable />
			<el-select style="width: 100%;margin-right:10px" v-model="searchModel.roleId" placeholder="选择角色" clearable>
				<el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
			</el-select>
			<el-cascader :show-all-levels="false" style="width: 100%;" v-model="searchModel.groupId" :props="{ checkStrictly: true }" :options="userGroupList" @change="handleChange" clearable placeholder="选择组"/>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table :data="personList" border height="calc(100vh - 300px)" style="width: 100%">
                <el-table-column prop="photo" width="100" align="center" label="照片">
                    <template #default="scope">
							<el-image preview-teleported fit="contain" style="height: 60px; width:60px" :src="imgServer+scope.row.photo" :preview-src-list="[imgServer+scope.row.photo]">
								<template #error>
									<el-image preview-teleported fit="contain" style="height: 60px; width:60px" :src="errorHeadImg" :preview-src-list="[errorHeadImg]"></el-image>
								</template>
							</el-image>
					</template>
                </el-table-column>
				<el-table-column prop="name" width="90px" align="center" label="姓名" />
				<el-table-column prop="gender" width="54px" align="center" :formatter="formatSex" label="性别" />
				<el-table-column prop="openId" width="290" align="center" label="用户唯一标识" />
				<el-table-column prop="phone" align="center" label="电话" />
				<el-table-column prop="idCard" align="center" label="证件号" />
				<el-table-column prop="certificateType" width="150" :formatter="formatCertificateType" align="center" label="证件类型" />
                <el-table-column prop="idType" width="150" align="center" label="是否关注公众号" >
					<template #default="scope">
						{{ scope.row.mpOpenId ?"是":"否" }}
					</template>
				</el-table-column>
				<el-table-column prop="createTime" width="165" align="center" label="创建时间" />
				<el-table-column prop="collectionTime" width="165" align="center" label="所属小区" >
					<template #default="scope">
						<div @mouseleave="communityShow = true">
							<div v-for="item in scope.row.communityNameList" :key="item">{{item}}</div>
						</div>
					</template>
				</el-table-column>
				<el-table-column align="center" width="100" label="操作" v-if="hasPerm('weChatUser:weChatUser:detial')">
					<template #default="scope">
							<el-button type="text" size="default" @click="bind(scope.row)">绑定</el-button>
							<el-button type="text" size="default" @click="view(scope.row)" v-if="hasPerm('weChatUser:weChatUser:detial')">查看</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>
<script>
import { weChatList, getWeChat } from "@/api/weChat/weChatUser"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { listRole } from "@/api/admin/role"
import { listUserGroup } from "@/api/admin/userGroup"
import { getDictCss, formatDict } from "@/utils/dict"
import weChatUserView from "@/componts/weChat/weChatUserView.vue"
import wxBindMenu from "@/componts/weChat/wxBindMenu.vue"
import errImg from "../../assets/img/defaultHead.png"
export default {
    components:{ weChatUserView, wxBindMenu },
	data() {
		return {
			searchModel: {},
			personList: [],
			imgServer: import.meta.env.VITE_BASE_API,
			communityId:localStorage.getItem("communityId"),
			statusList:[],
			sexList: [],
			roleList: [],
			userGroupList: [],
			certificateTypeTagList: [],
			total:0,
			pageSize: 10,
			errorHeadImg: errImg,
			communityShow: true
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		handleChange(e){
			if(e == null){
			this.searchModel.groupId = null
			return
			}
			this.searchModel.groupId = e[e.length-1]
		},
		formatCertificateType(row, column, cellValue, index){
			return formatDict(this.certificateTypeTagList, cellValue)
		},
		formatSex(row, column, cellValue, index) {
			return formatDict(this.sexList, cellValue)
		},
		search() {
			// this.searchModel.communityId=this.communityId
			weChatList(this.searchModel)
			.then(res => {
				this.personList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		bind(row){
			mitt.emit('openWxBind', row)
		},
        view(row){
			getWeChat({id:row.id}).then(res => {
				mitt.emit('weChatUserView', { userInfo: res.data.result, weChatView: row})
			})
        },
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			try{
				let res = await weChatList(this.searchModel)
				this.personList = res.data.result.list
				this.total = res.data.result.total

				let role_res = await listRole({status:0})
				this.roleList = role_res.data.result

				let resGroup = await listUserGroup({totalize:99999})
				var groupStr = JSON.stringify(resGroup.data.result.list).replaceAll('groupName','label').replaceAll('id','value')
				this.userGroupList = JSON.parse(groupStr)

				// let deviceStatus = await listDictByNameEn('flow_type')
				// this.statusList = deviceStatus.data.result
				let certificateType_res = await listDictByNameEn('certificate_type')
				this.certificateTypeTagList = certificateType_res.data.result

				let sex_res = await listDictByNameEn('sex')
				this.sexList = sex_res.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
