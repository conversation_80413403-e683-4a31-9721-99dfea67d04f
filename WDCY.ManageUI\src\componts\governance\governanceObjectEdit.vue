<template>
  <governance-entry-edit
    :sexList="sexList"
    :statusList="statusList"
    :tagList="tagList"
    :certificateTypeList="certificateTypeList"
    :liveTypeList="liveTypeList"
    :personTypeList="personTypeList"
    @search="search"
  ></governance-entry-edit>
  <el-dialog
    draggable
    width="50%"
    destroy-on-close
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
    :close-on-click-modal="false"
  >
    <el-scrollbar height="300px">
      <el-form
        :rules="rules"
        ref="form"
        :model="governanceObjectModel"
        label-width="100px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="人员姓名" prop="name">
              <el-select
                style="width: 100%"
                v-model="governanceObjectModel.name"
                filterable
                remote
                clearable
                reserve-keyword
                placeholder="输入关键字搜索"
                :remote-method="remoteMethod"
                @change="personHandleChange"
                :loading="loading"
                allow-create
                default-first-option
                @blur="onTypeBlur($event)"
              >
                <el-option
                  v-for="item in governanceObjectModelList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  <span style="float: left">{{ item.name }}</span>
                  <span
                    style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                    >{{ item.idCard }}</span
                  >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-button
            type="primary"
            @click="grvernanceEntry"
            style="float: right; margin-left: 25px"
            >快捷录入</el-button
          >
          <el-col :span="11">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="governanceObjectModel.phone"
                placeholder="联系电话"
                clearable
                style="float: right"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row> </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="证件类型" prop="certificateType">
              <el-select
                style="width: 100%"
                v-model="governanceObjectModel.certificateType"
                placeholder="证件类型"
                filterable
                clearable
              >
                <el-option
                  v-for="item in certificateTypeList"
                  :key="item.nameEn"
                  :label="item.nameCn"
                  :value="item.nameEn"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="证件号" prop="idCard">
              <el-input
                v-model="governanceObjectModel.idCard"
                placeholder="证件号"
                clearable
                style="float: right"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="籍贯" prop="nativePlace">
              <el-input
                v-model="governanceObjectModel.nativePlace"
                placeholder="籍贯"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="住址" prop="address">
              <el-input
                v-model="governanceObjectModel.address"
                placeholder="住址"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="11">
            <el-form-item label="小区" prop="communityId">
              <el-cascader
                style="width: 100%"
                v-model="governanceObjectModel.communityId"
                :options="communityList"
                @change="communityHandleChange"
                clearable
                filterable
                placeholder="选择小区"
              ></el-cascader>
            </el-form-item>
          </el-col>

          <el-col :span="11">
            <el-form-item label="楼栋" prop="buildingId">
              <el-cascader
                style="width: 100%"
                v-model="governanceObjectModel.buildingId"
                :options="buildingList"
                @change="buildingHandleChange"
                clearable
                filterable
                placeholder="选择楼栋"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item label="单元" prop="unitId">
              <el-cascader
                style="width: 100%"
                v-model="governanceObjectModel.unitId"
                :options="unitList"
                @change="unitHandleChange"
                clearable
                filterable
                placeholder="选择单元"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="房屋" prop="roomId">
              <el-cascader
                style="width: 100%"
                v-model="governanceObjectModel.roomId"
                :options="roomList"
                @change="roomHandleChange"
                clearable
                filterable
                placeholder="选择房屋"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        :disabled="isDisabled"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>

<script>
import mitt from "@/utils/mitt";
import JsonEditorVue from "json-editor-vue3";
import { getBuildingList, getAnchorPoint } from "@/api/base/community";
import { getUnitList, getRoomList } from "@/api/base/building";
import { listDictByNameEn } from "@/api/admin/dict";
import { queryChildrenNodeBuilding } from "@/api/base/building";
import { queryByIdCardOrPhone } from "@/api/base/person";
import governanceEntryEdit from "@/componts/governance/governanceEntryEdit.vue";

export default {
  components: { JsonEditorVue },
  props: ["statusList", "typeList", "levelList"],
  data() {
    return {
      imgServer: import.meta.env.VITE_BASE_API,
      loading: false,
      governanceObjectModel: {
        groupId: JSON.parse(localStorage.getItem("userInfo")).groupId,
      },
      dialog: {},
      isDisabled: false,
      sexList: [],
      statusList: [],
      tagList: [],
      buildingList: [],
      unitList: [],
      roomList: [],
      certificateTypeList: [],
      personTypeList: [],
      liveTypeList: [],
      viewStatus: true,
      governanceObjectModelList: [],
      personId: "",
      tableIndex: null,
      rules: {
        name: [
          {
            required: true,
            message: "请输入关键字",
            trigger: "change",
          },
        ],
        phone: [
          { required: true, message: "请输入手机号码", trigger: "blur" },
          {
            required: true,
            pattern: /^[1][3,4,5,7,8,9][0-9]{9}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            required: true,
            message: "身份证号码不能为空",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                return callback(new Error("身份证不能为空"));
              }
              if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)) {
                callback(new Error("请输入正确的二代身份证号码"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    onTypeBlur(e) {
      if (e.target.value.trim() !== "") {
        //e.target.value就是录入的内容
        this.governanceObjectModel.name = e.target.value;
        // 如果是对象，要使用this.$set方法
        // this.$set('typeObj', 'name', e.target.value)
      }
    },
    // 输入字查人员
    personHandleChange(e) {
      this.governanceObjectModelList.some((item) => {
        if (item.id == e) {
          this.governanceObjectModel = item;
          if (this.governanceObjectModel.groupId) {
            getAnchorPoint({
              groupId: this.governanceObjectModel.groupId,
            }).then((res) => {
              this.communityList = JSON.parse(
                JSON.stringify(res.data.result)
                  .replaceAll("communityName", "label")
                  .replaceAll("id", "value")
              );
            });
          }
          if (this.governanceObjectModel.communityId) {
            getBuildingList({
              communityId: this.governanceObjectModel.communityId,
              pageSize: 99999999,
            }).then((res) => {
              this.buildingList = JSON.parse(
                JSON.stringify(res.data.result.list)
                  .replaceAll("buildingNumber", "label")
                  .replaceAll("id", "value")
              );
            });
          }
          if (this.governanceObjectModel.buildingId) {
            getUnitList({
              buildingId: this.governanceObjectModel.buildingId,
              pageSize: 99999999,
            }).then((res) => {
              this.unitList = JSON.parse(
                JSON.stringify(res.data.result.list)
                  .replaceAll("unitNumber", "label")
                  .replaceAll("id", "value")
              );
            });
          }
          if (this.governanceObjectModel.unitId) {
            getRoomList({
              unitId: this.governanceObjectModel.unitId,
              pageSize: 99999999,
            }).then((res) => {
              this.roomList = JSON.parse(
                JSON.stringify(res.data.result.list)
                  .replaceAll("roomNumber", "label")
                  .replaceAll("id", "value")
              );
            });
          }
        }
      });
    },
    remoteMethod(data) {
      if (data.trim() != "") {
        queryByIdCardOrPhone({ name: data }).then((res) => {
          this.governanceObjectModelList = res.data.result;
        });
      }
    },
    communityHandleChange(e) {
      if (!e) {
        this.governanceObjectModel.communityId = null;
        this.governanceObjectModel.buildingId = null;
        this.governanceObjectModel.unitId = null;
        this.governanceObjectModel.roomId = null;
        this.buildingList = [];
        this.unitList = [];
        this.roomList = [];
        return;
      }
      this.unitList = [];
      this.roomList = [];
      this.governanceObjectModel.communityId = e[0];
      getBuildingList({
        communityId: e[0],
        pageSize: 99999999,
      }).then((res) => {
        this.buildingList = JSON.parse(
          JSON.stringify(res.data.result.list)
            .replaceAll("buildingNumber", "label")
            .replaceAll("id", "value")
        );
      });
    },
    buildingHandleChange(e) {
      if (!e) {
        this.governanceObjectModel.buildingId = null;
        this.governanceObjectModel.unitId = null;
        this.governanceObjectModel.roomId = null;
        this.unitList = [];
        this.roomList = [];
        return;
      }
      this.roomList = [];
      this.governanceObjectModel.buildingId = e[0];
      getUnitList({
        buildingId: e[0],
        pageSize: 99999999,
      }).then((res) => {
        this.unitList = JSON.parse(
          JSON.stringify(res.data.result.list)
            .replaceAll("unitNumber", "label")
            .replaceAll("id", "value")
        );
      });
    },
    unitHandleChange(e) {
      if (!e) {
        this.governanceObjectModel.unitId = null;
        this.governanceObjectModel.roomId = null;
        this.roomList = [];
        return;
      }
      this.governanceObjectModel.unitId = e[0];
      getRoomList({
        unitId: e[0],
        pageSize: 99999999,
      }).then((res) => {
        this.roomList = JSON.parse(
          JSON.stringify(res.data.result.list)
            .replaceAll("roomNumber", "label")
            .replaceAll("id", "value")
        );
      });
    },
    roomHandleChange(e) {
      if (!e) {
        this.governanceObjectModel.roomId = null;
        return;
      }
      this.governanceObjectModel.roomId = e[0];
    },
    // 快捷录入
    grvernanceEntry() {
      let communityId = localStorage.getItem("communityId");
      queryChildrenNodeBuilding({ communityId: communityId }).then((res) => {
        mitt.emit("quickEntry", {
          buildingList: res.data.result,
          communityId: communityId,
        });
      });
    },
    onSubmit() {
      if (this.isDisabled) {
        return;
      }
      this.isDisabled = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.governanceObjectModel.communityId == null) {
            this.governanceObjectModel.communityName = null;
          }
          if (this.governanceObjectModel.buildingId == null) {
            this.governanceObjectModel.buildingNumber = null;
          }
          if (this.governanceObjectModel.unitId == null) {
            this.governanceObjectModel.unitNumber = null;
          }
          if (this.governanceObjectModel.roomId == null) {
            this.governanceObjectModel.roomNumber = null;
          }
          this.communityList.findIndex((item) => {
            if (item.value == this.governanceObjectModel.communityId) {
              this.governanceObjectModel.communityName = item.label;
            }
          });
          this.buildingList.findIndex((item) => {
            if (item.value == this.governanceObjectModel.buildingId) {
              this.governanceObjectModel.buildingNumber = item.label;
            }
          });
          this.unitList.findIndex((item) => {
            if (item.value == this.governanceObjectModel.unitId) {
              this.governanceObjectModel.unitNumber = item.label;
            }
          });
          this.roomList.findIndex((item) => {
            if (item.value == this.governanceObjectModel.roomId) {
              this.governanceObjectModel.roomNumber = item.label;
            }
          });
          if (this.dialog.title == "添加事件对象") {
            this.governanceObjectModel.id = null;
            this.$emit("searchObject", this.governanceObjectModel);
            this.dialog.show = false;
          } else {
            const params = {
              data: this.governanceObjectModel,
              tableIndex: this.tableIndex,
            };
            this.$emit("searchObject1", params);
            this.dialog.show = false;
          }
        }
      });
      setTimeout(() => {
        this.isDisabled = false; // 200ms后再次启用按钮（根据需求调整时间）
      }, 200);
    },
    async getPersonTag() {
      let tagRes = await listDictByNameEn("person_tag");
      this.tagList = tagRes.data.result;
    },
    async init() {
      try {
        this.getPersonTag();
        let sex_res = await listDictByNameEn("sex");
        this.sexList = sex_res.data.result;
        let certificate_res = await listDictByNameEn("certificate_type");
        this.certificateTypeList = certificate_res.data.result;
        let person_res = await listDictByNameEn("person_type");
        let live_res = await listDictByNameEn("live_type");
        this.personTypeList = person_res.data.result;
        this.liveTypeList = live_res.data.result;
        let status_res = await listDictByNameEn("person_status");
        this.statusList = status_res.data.result;
      } catch (err) {
        console.log(err);
      }
      this.communityList = [];
      this.buildingList = [];
      this.unitList = [];
      this.roomList = [];
      getAnchorPoint({
        groupId: this.governanceObjectModel.groupId,
      }).then((res) => {
        this.communityList = JSON.parse(
          JSON.stringify(res.data.result)
            .replaceAll("communityName", "label")
            .replaceAll("id", "value")
        );
      });
      if (this.governanceObjectModel.communityId) {
        getBuildingList({
          communityId: this.governanceObjectModel.communityId,
          pageSize: 99999999,
        }).then((res) => {
          this.buildingList = JSON.parse(
            JSON.stringify(res.data.result.list)
              .replaceAll("buildingNumber", "label")
              .replaceAll("id", "value")
          );
        });
      }
      if (this.governanceObjectModel.buildingId) {
        getUnitList({
          buildingId: this.governanceObjectModel.buildingId,
          communityId: this.communityId,
          pageSize: 99999999,
        }).then((res) => {
          this.unitList = JSON.parse(
            JSON.stringify(res.data.result.list)
              .replaceAll("unitNumber", "label")
              .replaceAll("id", "value")
          );
        });
      }
      if (this.governanceObjectModel.unitId) {
        getRoomList({
          unitId: this.governanceObjectModel.unitId,
          communityId: this.communityId,
          pageSize: 99999999,
        }).then((res) => {
          this.roomList = JSON.parse(
            JSON.stringify(res.data.result.list)
              .replaceAll("roomNumber", "label")
              .replaceAll("id", "value")
          );
        });
      }
    },
  },
  mounted() {
    this.$nextTick(function () {
      this.init();
      mitt.on("openGovernanceObjectEdit", (data) => {
        this.tableIndex = data.index;
        this.governanceObjectModel = JSON.parse(JSON.stringify(data.row));
        this.governanceObjectModel.groupId = JSON.parse(
          localStorage.getItem("userInfo")
        ).groupId;
        this.init();
        this.dialog.show = true;
        this.dialog.title = "修改事件对象";
      });
      mitt.on("openGovernanceObjectAdd", (governanceEventId) => {
        this.governanceObjectModel = {};
        this.governanceObjectModel.groupId = JSON.parse(
          localStorage.getItem("userInfo")
        ).groupId;
        this.governanceObjectModel.governanceEventId = governanceEventId;
        this.dialog.show = true;
        this.dialog.title = "添加事件对象";
        this.init();
      });
    });
  },
  created() {},
};
</script>

<style scoped lang="less">
div /deep/.picView .el-dialog__header {
  background-color: #fff;
  box-shadow: none;
}
div /deep/ .picView .el-dialog__close {
  color: #ccc;
}

div /deep/ .el-upload-list {
  display: flex;
  width: 100%;
  flex-wrap: nowrap;
  > li,
  div {
    display: flex;
    flex-shrink: 0;
  }
}
</style>
