<template>
	<third-sys-account-edit :thirdSystemList="thirdSystemList" :userList="userList"
		:roleList="roleList"></third-sys-account-edit>
	<el-dialog draggable width="70%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<el-row :gutter="20">
			<el-col :span="8" style="display:flex">
				<el-input style="margin-right:10px" v-model="searchModel.userName" placeholder="用户名" clearable />
			</el-col>
			<el-col :span="4">
				<el-button type="primary" @click="search">搜 索</el-button>
			</el-col>
			<el-col :span="4" :push="8">
				<el-button style="float: right;" type="primary" @click="add">添 加</el-button>
			</el-col>
		</el-row>
		<el-row :gutter="20" style="margin-top: -30px;">
			<el-col :span="24">
				<el-table :data="accountList" border style="width: 100%;height: 450px;">
					<!-- <el-table-column prop="id"  align="center" label="ID" /> -->
					<el-table-column prop="thirdSystemId" align="center" label="第三方系统">
						<template #default="scope">
							{{ formatThirdSystem(thirdSystemList, scope.row.thirdSystemId) }}
						</template>
					</el-table-column>
					<el-table-column prop="userName" align="center" label="用户名" />
					<el-table-column prop="userId" align="center" label="归属账号">
						<template #default="scope">
							{{ formatUser(userList, scope.row.userId) }}
						</template>
					</el-table-column>
					<el-table-column prop="roleId" align="center" label="归属角色">
						<template #default="scope">
							{{ formatRole(roleList, scope.row.roleId) }}
						</template>
					</el-table-column>
					<el-table-column label="创建时间" align="center" prop="createTime" width="180">
						<template #default="scope">
							<span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
						</template>
					</el-table-column>
					<el-table-column label="创建人" align="center" prop="createUser" />
					<el-table-column label="修改时间" align="center" prop="updateTime" width="180">
						<template #default="scope">
							<span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}') }}</span>
						</template>
					</el-table-column>
					<el-table-column label="修改人" align="center" prop="updateUser" />
					<el-table-column align="center" width="100" label="操作">
						<template #default="scope">
							<el-button type="text" size="default" @click="edit(scope.row.id)">编辑</el-button>
							<el-button type="text" size="default" @click="deleted(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
			<!-- <el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[5, 10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col> -->
		</el-row>
	</el-dialog>


</template>
<script>
import { getDictCss, formatDict } from "@/utils/dict"
import { listThirdSystem, getThirdSysAccount, deleteThirdSysAccount, listThirdSysConf, listThirdSysAccount } from "@/api/admin/thirdSystem"
import { listDictByNameEn } from "@/api/admin/dict"
import { listRole } from "@/api/admin/role"
import { listUser } from "@/api/admin/user"
import mitt from "@/utils/mitt"
import thirdSysAccountEdit from "@/componts/admin/thirdSysAccountEdit.vue"
export default {
	props: ['thirdSystemList'],
	components: { thirdSysAccountEdit },
	data() {
		return {
			searchModel: {},
			statusList: [],
			confList: [],
			accountList: [],
			userList: [],
			roleList: [],
			imgServer: import.meta.env.VITE_BASE_API,
			total: 0,
			pageSize: 5,
			dialog: {
				show: false,
				title: ''
			},
			dialog: {
				show: false,
				title: ''
			},
			loading: false,
		}
	},
	methods: {
		formatThirdSystem(list, value) {
			let result = ""
			list.forEach(item => {
				if (item.id == value) {
					result = item.sysName
				}
			})
			return result
		},
		formatRole(list, value) {
			let result = ""
			list.forEach(item => {
				if (item.id == value) {
					result = item.roleName
				}
			})
			return result
		},
		formatUser(list, value) {
			let result = ""
			list.forEach(item => {
				if (item.id == value) {
					result = item.nickName
				}
			})
			return result
		},
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		},
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			if (this.searchModel.userName) {
				let accountList = []
				this.accountList.forEach(element => {
					if (element.userName && element.userName.indexOf(this.searchModel.userName) != -1) {
						accountList.push(element)
					}
				});
				this.accountList = accountList
			} else {
				this.$parent.account(this.accountId)
			}
		},
		add() {
			mitt.emit('openThirdSysAccountAdd', this.accountId)
			this.dialog.show = false
		},
		edit(id) {
			getThirdSysAccount(id).then(res => {
				mitt.emit('openThirdSysAccountEdit', res.data.result)
			})
			this.dialog.show = false

		},
		deleted(id) {
			this.$confirm('删除信息, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteThirdSysAccount(id)
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num) {
			this.searchModel.pageSize = num
			this.search()
		},
		formatStatus(row, column, cellValue, index) {
			let result = ''
			for (let item of this.statusList) {
				if (item.nameEn == cellValue) {
					result = item.nameCn
				}
			}
			return result
		},
		async init() {
			try {
				this.searchModel.pageSize = 5
				let deviceStatus = await listDictByNameEn('flow_type')
				this.statusList = deviceStatus.data.result
			} catch (err) {
			}
		}
	},
	created() {
		this.init()
	},
	mounted() {
		this.$nextTick(function () {
			listUser({ status: 0 }).then(res => {
				this.userList = res.data.result
			})
			listRole({ status: 0 }).then(res => {
				this.roleList = res.data.result
			})
			mitt.on("account", (res) => {
				this.accountList = res.list
				//this.total = res.list.total
				this.accountId = res.accountId
				this.dialog.show = true
				this.dialog.title = "第三方系统账户"
				// this.search()
			})

		})
	}
}
</script>

<style scoped>
.el-row {
	margin-bottom: 20px;
	background-color: #fff;
	padding: 20px 10px;
	border-radius: 5px;
}
</style>
