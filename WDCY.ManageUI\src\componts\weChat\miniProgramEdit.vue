<template>
  <el-dialog draggable width="50%" top="5vh" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
    <el-form ref="form" :model="miniProgramModel" :rules="rules" label-width="100px">
      <el-form-item label="系统名称" prop="appName">
        <el-input v-model="miniProgramModel.appName" placeholder="请输入系统名称" maxlength="5" show-word-limit />
      </el-form-item>

      <el-form-item label="小程序ID" prop="appId">
        <el-input v-model="miniProgramModel.appId" placeholder="请输入小程序ID" />
      </el-form-item>

      <el-form-item label="导航路径" prop="navigatePath">
        <el-input v-model="miniProgramModel.navigatePath" placeholder="请输入小程序导航路径" />
      </el-form-item>

      <el-form-item label="额外数据" prop="extraData">
        <el-input v-model="miniProgramModel.extraData" type="textarea" placeholder="请输入小程序额外数据" maxlength="1000"
          show-word-limit />
      </el-form-item>

      <el-form-item label="小程序图标" prop="icoUrl">
        <el-upload :class="[miniProgramModel.icoUrl ? '' : 'upload']" class="avatar-uploader"
          :action="imgServer + miniProgramModel.icoUrl" :show-file-list="false" :http-request="loadingImg">
          <img v-if="miniProgramModel.icoUrl" :src="imgServer + miniProgramModel.icoUrl" @mouseenter="mask=true" @mouseleave="mask=false" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon">
            <Plus />
          </el-icon>
          <div @mouseenter="mask=true" @mouseleave="mask=false" v-if="mask" style="background:rgba(0, 0, 0, .2);width: 178px; height: 178px;position: absolute;"></div>
        </el-upload>
        <!-- <el-button type="primary"> -->
        <!-- <el-upload
                id="uploadApp"
                :show-file-list="false"
                :http-request="loadingImg"
                v-if="!miniProgramModel.icoUrl"
                >小程序图标上传</el-upload
                >
                <el-image
                v-else
                    id="canvas"
                    fit="contain"
                    @click="uploadAppImg"
                    style="height: 200px; width: 200px"
                    :src="imgServer + miniProgramModel.icoUrl"
                  >
                  </el-image> -->
        <!-- <imageUpload v-model="miniProgramModel.icoUrl" :limit="1" /> -->
        <!-- </el-button> -->
      </el-form-item>

      <el-form-item label="小程序" required>
        <el-col :span="12">
          <el-form-item label="分类" prop="appCategory">
            <el-select v-model="miniProgramModel.appCategory" placeholder="请选择..." clearable style="width:116px">
              <el-option v-for="item in categoryList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="appType">
            <el-select v-model="miniProgramModel.appType" placeholder="请选择..." clearable style="width:116px">
              <el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-form-item>

      <el-form-item label="备注" prop="note">
        <el-input v-model="miniProgramModel.note" type="textarea" placeholder="请输入备注" maxlength="200" show-word-limit />
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="miniProgramModel.sort" :min="1" style="width:126px"></el-input-number>
      </el-form-item>

      <el-form-item label="状态" prop="state">
        <el-select v-model="miniProgramModel.state" placeholder="请选择..." clearable style="width:116px">
          <el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
            :value="parseInt(item.nameEn)" />
        </el-select>
      </el-form-item>

    </el-form>
    <el-row justify="center">
      <el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import mitt from "@/utils/mitt"
import { miniProgramList, getMiniProgram, addMiniProgram, editMiniProgram, deleteMiniProgram } from "@/api/weChat/miniProgram"
import { fileUpload } from "@/api/admin/file";
export default {
  props: ['typeList', 'categoryList', 'statusList'],
  data() {
    return {
      loading: false,
      dialog: {},
      mask: false,
      miniProgramModel: {},
      imgServer: import.meta.env.VITE_BASE_API,
      rules: {
        appName: [
          { required: true, message: "系统名称不能为空", trigger: "blur" }
        ],
        appId: [
          { required: true, message: "小程序ID不能为空", trigger: "blur" }
        ],
        icoUrl: [
          { required: true, message: "小程序图标不能为空", trigger: "blur" }
        ],
        navigatePath: [
          { required: true, message: "导航路径不能为空", trigger: "blur" }
        ],
        appType: [
          { required: true, message: "小程序类型不能为空", trigger: "blur" }
        ],
        appCategory: [
          { required: true, message: "小程序分类不能为空", trigger: "blur" }
        ],
        sort: [
          { required: true, message: "排序不能为空", trigger: "blur" }
        ],
        state: [
          { required: true, message: "状态不能为空", trigger: "blur" }
        ]
      }
    }
  },
  methods: {
    mousein(){
      console.log('enter');
    },
    uploadAppImg() {
      const uploadButton = document.querySelector("#uploadApp");
      this.miniProgramModel.icoUrl = null
    },
    loadingImg(files) {
      console.log(files);
      let form = new FormData();
      form.append("file", files.file);
      form.append("modulesName", 'wxUser');
      form.append("functionName", 'miniProgram');
      form.append("communityId", localStorage.getItem('communityId'));
      fileUpload(form).then((res) => {
        this.miniProgramModel.icoUrl = res.data.result.url;
        if (res.data.code == 0) {
          this.$message.success("上传成功");
        }
      });
    },
    onSubmit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.miniProgramModel.id == 0) {
            addMiniProgram(this.miniProgramModel)
              .then(res => {
                this.$message.success(res.data.msg)
                this.$emit("search")
                this.dialog.show = false
                delete (this.miniProgramModel.id)

              })
          } else {
            editMiniProgram(this.miniProgramModel)
              .then(res => {
                console.log(res);
                this.$message.success(res.data.msg)
                this.$emit("search")
                this.dialog.show = false
              })
          }
        }
      });
    }
  },
  mounted() {
    // console.log(this.typeList);
    // console.log(this.categoryList);
    this.$nextTick(function () {
      mitt.on("openMiniProgramEdit", (resault) => {
        this.miniProgramModel = resault
        this.dialog.show = true
        this.dialog.title = "修改小程序"
      })
      mitt.on("openMiniProgramAdd", (sort) => {
        this.miniProgramModel = {
          id: 0,
          state: 1,
          sort: sort,
          appType: "native"
        }
        this.dialog.show = true
        this.dialog.title = "新增小程序"
      })
    })
  },
}
</script>
<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>