<template>
	<bound-func-list></bound-func-list>
	<el-dialog draggable width="40%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="caseTestModel" label-width="100px">
			<el-row>
				<el-col :span="9">
					<el-form-item label="场景ID" prop="checkSceneId">
						<el-input disabled v-model="caseTestModel.checkSceneId" placeholder="场景ID"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="场景名称" prop="sceneName">
						<el-input disabled v-model="caseTestModel.sceneName" placeholder="场景名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="9">
					<el-form-item label="功能ID" prop="sysFuncId">
						<el-input disabled v-model="caseTestModel.sysFuncId" placeholder="功能ID"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="功能名称" prop="funcName">
						<el-input disabled v-model="caseTestModel.funcName" placeholder="功能名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="3">
					<el-button style="float: right;" @click="bindingFuc">绑 定</el-button>
				</el-col>
				<el-col :span="24">
					<el-form-item label="用例名称" prop="caseName">
						<el-input v-model="caseTestModel.caseName" placeholder="用例名称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="caseTestModel.status">
                            <el-radio-button  v-for="item in statusList" :key="item.nameEn"
                                :label="parseInt(item.nameEn)">{{ item.nameCn }}</el-radio-button >
                        </el-radio-group>
                    </el-form-item>
            	</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="排序" prop="sort">
						<el-input-number v-model="caseTestModel.sort" :min="1" @change="handleChange" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
				<el-form-item label="备注" prop="note">
					<el-input
					v-model="caseTestModel.note"
					maxlength="200"
					placeholder="请简单说明场景情况"
					show-word-limit
					type="textarea"
					/>
				</el-form-item>
				</el-col>
			</el-row>
			
            <el-row>
                <el-col :span="24">
                <el-form-item label="扩展参数" prop="expandParams">
                    <JsonEditorVue
                    language="cn"
                    class="editor"
                    :modelValue="jsonVal"
                    @update:modelValue="changeJson"
                    />
                </el-form-item>
                </el-col>
            </el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交
			</el-button>
		</el-row>
		<!-- <vehicle-number></vehicle-number> -->
	</el-dialog>
</template>

<script>
import {  addTestCase, editTestCase } from "@/api/healthCheck/useCaseScenario"
import JsonEditorVue from "json-editor-vue3";
import mitt from "@/utils/mitt";
import boundFuncList from "@/componts/healthCheck/boundFuncList.vue"
export default {
    components: { JsonEditorVue, boundFuncList },
	props: ['statusList'],
	data() {
		return {
			loading: false,
			caseTestModel: {},
			dialog: {},
			imgServer: import.meta.env.VITE_BASE_API,
            jsonVal: {},
		}
	},
	methods: {
        changeJson(json) {
        	this.jsonVal = json;
        },
		bindingFuc(){
			mitt.emit('openFuncList1', (data) => {
				
			})
		},
		onSubmit() {
			this.dialog.show = false
			this.$parent.dialog.show = true
			this.$refs['form'].validate((valid) => {
				if (valid) {
					this.caseTestModel.expandParams = JSON.stringify(this.jsonVal);
					if (this.caseTestModel.id == 0) {
						delete this.caseTestModel.id
						addTestCase(this.caseTestModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						editTestCase(this.caseTestModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		}
	},
	mounted() {
		this.jsonVal = {};
		this.$nextTick(function () {
			mitt.on('openCaseCheckEdit', (data) => {
				this.caseTestModel = data
				this.dialog.show = true
				this.dialog.title = "修改用例"
				this.jsonVal = JSON.parse(this.caseTestModel.expandParams);
			})
			mitt.on('openCaseCheckAdd', (data) => {
				this.jsonVal = {}
				this.caseTestModel = {
					id: 0,
					sort:this.$parent.checkCaseList.length + 1,
					checkSceneId: data.id,
					sceneName: data.checkSceneName,
					status: 1
				}
				this.dialog.show = true
				this.dialog.title = "添加用例"
			})
		})
	},
	watch:{
		"dialog.show"(newVal,oldVal){
			if (newVal == false) {
				this.$parent.dialog.show = true
			}
		}
	}
}
</script>
<style scoped lang="less">
.editor {
  width: 100%;
}
.avatar-uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}

.el-select-dropdown__item{
	display: flex;align-items: center;text-align:center;
	>img{
		background-color: rgba(136, 186, 255,.3);
		width: 20px;
		margin:0 auto;
		margin-right: 10px;
	}
}
.el-select-dropdown__item:hover{
	background-color: rgba(170, 170, 170,.3);
}
</style>
