<template>
  <warn-strategy-edit @search="search" :eventLevelList="eventLevelList" :eventCodeList="eventCodeList"></warn-strategy-edit>
  <warn-rule-edit @search="search"></warn-rule-edit>
  <el-row :gutter="20">
    <el-col :span="4">
      <el-input v-model="searchModel.name" placeholder="策略名称" clearable />
    </el-col>
    <el-col :span="4">
      <el-button type="primary" @click="search">搜 索</el-button>
    </el-col>
    <el-col :span="4" :push="12">
      <el-button style="float: right" type="primary" @click="add" v-if="hasPerm('warn:warnStrategy:add')">添 加</el-button
      >
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col>
      <el-table :data="personList" border style="width: 100%">
        <el-table-column
          prop="name"
          width="160"
          align="left" header-align="center"
          label="预警策略名称"
        />
        <el-table-column prop="note" align="left" header-align="center" label="预警策略简介" />
        <el-table-column
          prop="conditionType"
          align="center"
          width="120"
          label="条件类型"
        />
        <el-table-column prop="eventCode" align="center" label="事件标识" width="95">
					<template #default="scope">
						{{ formatDict(eventCodeList, scope.row.eventCode) }}
					</template>
				</el-table-column>
        <el-table-column prop="eventLevel" width="90" align="center" label="事件等级" >
					<template #default="scope">
					  	<div :style="'background-color:' + getDictCss(eventLevelList, scope.row.eventLevel)">{{ formatDict(eventLevelList, scope.row.eventLevel) }}</div>
					</template>
				</el-table-column>
        <el-table-column
          v-if="hasPerm('warn:warnStrategy:updateStatus')"
          prop="status"
          width="100"
          align="center"
          label="开关策略"
        >
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatus(scope.row.id, scope.row.status)"
            />
          </template>
        </el-table-column>

        <el-table-column width="120" align="center" label="预警规则">
          <template #default="scope">
            <el-button
              type="text"
              size="default"
              @click="editWarnRule(scope.row.id, scope.row.conditionType)"
              >编辑预警规则</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="startTime"
          width="165"
          align="center"
          label="生效开始时间"
        />
        <el-table-column
          prop="endTime"
          width="165"
          align="center"
          label="生效结束时间"
        />
        <el-table-column
          prop="createTime"
          width="165"
          align="center"
          label="添加时间"
        />
        <el-table-column
          prop="updateTime"
          width="165"
          align="center"
          label="修改时间"
        />
        <el-table-column align="center" width="100" label="操作">
          <template #default="scope">
            <el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('warn:warnStrategy:update')">编辑</el-button
            >
            <el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('warn:warnStrategy:delete')">删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination
        background
        v-model:page-size="pageSize" 
        :page-sizes="[10, 20, 50, 100]" 
        layout="total, sizes, prev, pager, next, jumper"  
        @current-change="currentChange" 
        @size-change="handleSizeChange"
        :total="Number(total)"
      ></el-pagination>
    </el-col>
  </el-row>
</template>
<script>
import {
  updateStatus,
  warnStrategyList,
  warnStrategyListDelete,
  warnRuleList,
  getWarnStrategy,
} from "@/api/warn/warnStrategy";
import mitt from "@/utils/mitt";
import { listDictByNameEn } from "@/api/admin/dict"
import warnStrategyEdit from "@/componts/warn/warnStrategyEdit.vue";
import warnRuleEdit from "@/componts/warn/warnRuleEdit.vue";
import { getDictCss, formatDict } from "@/utils/dict"
export default {
  components: { warnStrategyEdit, warnRuleEdit },
  data() {
    return {
      searchModel: {},
      personList: [],
      eventLevelList: [],
      eventCodeList: [],
      communityId: localStorage.getItem("communityId"),
      total: 0,
      pageSize: 10
    };
  },
  methods: {
    getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
    formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
    search() {
      this.searchModel.communityId = this.communityId;
      warnStrategyList(this.searchModel)
        .then((res) => {
          this.personList = res.data.result.list;
          this.total = res.data.result.total;
        })
    },
    edit(id) {
      getWarnStrategy(id).then((res) => {
        mitt.emit("openWarnStrategyEdit", res.data.result);
      });
    },
    // 编辑预警规则
    editWarnRule(id, conditionType) {
      warnRuleList(id).then((res) => {
        mitt.emit("openWarnRuleEdit", {
          data: res.data.result,
          conditionType: conditionType,
        });
      });
    },
    add() {
      mitt.emit("openWarnStrategyAdd");
    },
    handleStatus(id, status) {
      updateStatus({
        id: id,
        status: status,
      })
        .then((res) => {
          this.$message.success(res.data.msg);
        })
    },
    deleted(id) {
      this.$confirm("删除策略, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          warnStrategyListDelete(id)
            .then((res) => {
              this.search();
              this.$message.success(res.data.msg);
            })
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
    async init() {
      mitt.off("openWarnStrategyEdit");
      mitt.off("openWarnStrategyAdd");
      mitt.off("openWarnRuleEdit");
      try {
        this.searchModel.communityId = this.communityId;
        let res = await warnStrategyList(this.searchModel);
        this.personList = res.data.result.list;
        this.total = res.data.result.total;

        let eventLevel_res = await listDictByNameEn('event_level')
				this.eventLevelList = eventLevel_res.data.result
        let subscribe_event_family = await listDictByNameEn('subscribe_event_family')
				let eventFamilyList = subscribe_event_family.data.result

        let subscribe_event_community = await listDictByNameEn('subscribe_event_community')
				let eventCommunityList = subscribe_event_community.data.result

        this.eventCodeList = [...eventFamilyList, ...eventCommunityList]
      } catch (err) {
      }
    },
  },
  created() {
    this.init();
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
</style>
