/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.119
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as o}from"./chunk-KCNAH63E.js";import"./chunk-JDQIUPWT.js";import"./chunk-3HJM4DNA.js";import"./chunk-6APME2VO.js";import"./chunk-ZVFSUDGR.js";import"./chunk-QJHYMZPL.js";import"./chunk-EDMSGCXM.js";import"./chunk-ZLFFYFO6.js";import"./chunk-4WSWBQPX.js";import"./chunk-PSM2OZFQ.js";import"./chunk-TPVKDNQ2.js";import"./chunk-L2WB5XBS.js";import"./chunk-MKOJDBDR.js";import"./chunk-6IEKOAAO.js";import"./chunk-FI7FQWFK.js";import"./chunk-QU7IDCXZ.js";import{a as t,d as c}from"./chunk-A37TTN4T.js";import"./chunk-U4UUGLXG.js";import"./chunk-3GUOVF7B.js";import"./chunk-ZWNHW2OC.js";import"./chunk-SQHFMRD4.js";import"./chunk-GDH3Q66C.js";import{e as n}from"./chunk-IWAQ2DE4.js";function a(r,i){return n(i)&&(r=o.unpack(r,i)),r._center=t.clone(r._center),r._ellipsoid=c.clone(r._ellipsoid),o.createGeometry(r)}var u=a;export{u as default};
