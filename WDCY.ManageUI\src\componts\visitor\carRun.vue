<template>
    <el-dialog draggable
    width="70%"
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
  <el-row :gutter="20">
		<el-col :span="8" style="display:flex">
			<el-input style="margin-right:10px" v-model="searchModel.name" readonly placeholder="车牌号" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.deviceName" placeholder="车闸设施" clearable />
			<el-input v-model="searchModel.address" placeholder="进出地点" clearable />
		</el-col>
		<el-col :span="8" style="display:flex">
			<el-date-picker
				v-model="searchModel.startTime"
				type="datetime"
				placeholder="选择开始日期"
				value-format="YYYY-MM-DD HH:mm:ss"
				:size="size"
				style="margin-right:10px"
			/>
			<el-date-picker
				style="margin-right:10px"
				v-model="searchModel.endTime"
				type="datetime"
				placeholder="选择结束日期"
				value-format="YYYY-MM-DD HH:mm:ss"
				:size="size"
			/>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20" style="height:450px">
		<el-col :span="24">
			<el-table :data="personList" border style="width: 100%">
				<el-table-column prop="plateNumber" width="100" align="center" label="车牌号" />
				<el-table-column prop="collectionTime" width="180" align="center" label="时间" />
				<el-table-column prop="flowType" width="100" :formatter="formatStatus" align="center" label="进出类型" />
				<el-table-column prop="address" align="left" label="进出地点" />
				<el-table-column prop="deviceName" width="300" align="center" label="车闸设施">
				</el-table-column>
				<el-table-column prop="capturePhoto" width="100" align="center" label="照片">
					<template #default="scope">
						<el-image preview-teleported fit="contain" style="width: 50px; height: 50px; z-index: 9999;" :src="imgServer+scope.row.capturePhoto" :preview-src-list="[imgServer+scope.row.capturePhoto]"></el-image>
					</template>
				</el-table-column>
				<el-table-column align="center" width="100" label="操作">
					<template #default="scope">
							<el-button type="text" size="default" @click="deleted(scope.row.id)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<section v-if="loadingAnimas" @click="closeLoading">
      <div class="dots">
        <span style="--i:1"></span>
        <span style="--i:2"></span>
        <span style="--i:3"></span>
        <span style="--i:4"></span>
        <span style="--i:5"></span>
        <span style="--i:6"></span>
        <span style="--i:7"></span>
        <span style="--i:8"></span>
        <span style="--i:9"></span>
        <span style="--i:10"></span>
        <span style="--i:11"></span>
        <span style="--i:12"></span>
        <span style="--i:13"></span>
        <span style="--i:14"></span>
        <span style="--i:15"></span>
        <text style="position:absolute;left:calc( 50% - 66px);top:calc(50% + 58px); color:yellow; font-size: larger;">正在加载车行，请稍后...</text>
      </div>
    </section>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[5, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</el-dialog>
</template>
<script>
import { vehicleList,vehicleListDelete} from "@/api/situation/situation"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
export default {
	data() {
		return {
			searchModel: {},
			statusList:[],
			personList: [],
			imgServer: import.meta.env.VITE_BASE_API,
			communityId:localStorage.getItem("communityId"),
			total:0,
			pageSize:5,
            dialog: {},
            loading: false,
			loadingAnimas: true
		}
	},
	methods: {
		search() {
			console.log(123);
			this.searchModel.communityId=this.communityId
			vehicleList(this.searchModel)
			.then(res => {
				this.personList = res.data.result.list
				this.total = res.data.result.total
				this.loadingAnimas = false
			})
		},
		deleted(id){
			this.$confirm('删除信息, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					vehicleListDelete(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		formatStatus(row, column, cellValue, index){
			let result = ''
			for(let item of this.statusList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		async init(){
			try{
				this.searchModel.communityId=this.communityId
				this.searchModel.pageSize = 5
				// let res = await vehicleList(this.searchModel)
				// this.personList = res.data.result.list
				// this.total = res.data.result.total
				let deviceStatus = await listDictByNameEn('flow_type')
				this.statusList = deviceStatus.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	},
    mounted(){
        this.$nextTick(function(){
            mitt.on("carRun",(res)=>{
				if (res.plateNo) {
					this.searchModel.name = res.plateNo
					this.search()
				} else{
					this.searchModel.name = ' '
					this.personList = []
					this.search()
				}
				
                this.dialog.show = true
                this.dialog.title = "车行"
            })
        })
    }
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	section {
  z-index: 99999;
  position: absolute;
  display: flex;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  align-items: center;
  justify-content: center;
  background: #0000004b;
}

section .dots span {
  position: absolute;
  height: 10px;
  width: 10px;
  background-color: rgb(7, 241, 132);
  border-radius: 50%;
  transform: rotate(calc(var(--i) * (360deg / 15))) translateY(35px);
  animation: animate 1.5s linear infinite;
  animation-delay: calc(var(--i) * 0.1s);
  opacity: 0;
}

@keyframes animate {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
</style>
