<template>
    <thirdSystem-edit :statusList="statusList" :formLoginTypeList="formLoginTypeList"
        :encryptPwdTypeList="encryptPwdTypeList" :sysTypeList="sysTypeList" :sysCategoryList="sysCategoryList"
        :showModeList="showModeList" :icoList="icoList" @search="search"></thirdSystem-edit>
    <third-sys-account-List :thirdSystemList="dataList" @search="search"></third-sys-account-List>
    <third-sys-conf-List :thirdSystemList="dataList" @search="search"></third-sys-conf-List>
    <el-row :gutter="20">
        <el-col :span="6" style="display:flex">
            <el-input style="margin-right:10px;width:50%" v-model="searchModel.keyWord" placeholder="请输入系统名称"
                clearable />
            <el-button type="primary" @click="search(searchModel.keyWord)">搜 索</el-button>
        </el-col>
        <el-col :span="4" :push="14">
            <el-button style="float: right;" type="primary" @click="add">添 加</el-button>
        </el-col>
    </el-row>
    <el-row :gutter="20">
        <el-col :span="24">
            <el-table stripe :data="dataList" border height="calc(100vh - 300px)" style="width: 100%">
                <el-table-column label="图标" align="center" prop="icoUrl" width="100">
                    <template #default="scope">
                        <el-image style="width:70px;height:70px" :src="imgServer + formatImg(scope.row.icoInfoId)"
                            fit="contain"></el-image>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="id" align="center" label="ID" /> -->
                <el-table-column prop="sysName" align="center" label="系统名称" />
                <el-table-column prop="sysCode" align="center" label="系统代号" />
                <el-table-column prop="runUrl" align="center" label="运行URL" />
                <el-table-column prop="showMode" align="center" label="展示方式" width="85">
                    <template #default="scope">
                        <el-tag :type="getDictCss(showModeList, scope.row.showMode)">
                            {{ formatDict(showModeList, scope.row.showMode) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="typeId" align="center" label="系统类型" width="85">
                    <template #default="scope">
                        <el-tag :type="getDictCss(sysTypeList, scope.row.typeId)">
                            {{ formatDict(sysTypeList, scope.row.typeId) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="sysCategory" align="center" label="系统分类" width="85">
                    <template #default="scope">
                        <el-tag :type="getDictCss(sysCategoryList, scope.row.sysCategory)">
                            {{ formatDict(sysCategoryList, scope.row.sysCategory) }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="formLoginType" align="center" label="登录方式" width="85">
                    <template #default="scope">
                        <el-tag :type="getDictCss(formLoginTypeList, scope.row.formLoginType)">
                            {{ formatDict(formLoginTypeList, scope.row.formLoginType) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="encryptPwdType" align="center" label="加密方式" width="100">
                    <template #default="scope">
                        <el-tag :type="getDictCss(encryptPwdTypeList, scope.row.encryptPwdType)">
                            {{ formatDict(encryptPwdTypeList, scope.row.encryptPwdType) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="status" align="center" label="状态" width="80">
                    <template #default="scope">
                        <el-tag :type="getDictCss(statusList, scope.row.status)">
                            {{ formatDict(statusList, scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="note" align="center" label="描述" />
                <el-table-column prop="createTime"  align="center" label="创建时间" />
				<el-table-column prop="updateTime"  align="center" label="更新时间" />
                <el-table-column align="center" label="操作" width="200">
                    <template #default="scope">
                        <el-button type="text" size="default" @click="conf(scope.row.id)">配置</el-button>
                        <el-button type="text" size="default" @click="account(scope.row.id)">账户</el-button>
                        <el-button type="text" size="default" @click="edit(scope.row.id)">编辑</el-button>
                        <el-button type="text" size="default" @click="deleted(scope.row.id)"
                            v-if="hasPerm('base:vehicle:delete')">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-col>
        <el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
            <el-pagination background v-model:page-size="searchModel.pageSize" :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange"
                @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
        </el-col>
    </el-row>
</template>

<script>
import { listDictByNameEn } from "@/api/admin/dict"
import { getDictCss, formatDict } from "@/utils/dict"
import mitt from "@/utils/mitt"
import { ElLoading } from 'element-plus'
import { listThirdSystem, getThirdSystem, deleteThirdSystem, listThirdSysConf, listThirdSysAccount } from "@/api/admin/thirdSystem"
import { listIcon } from "@/api/base/icon"
import thirdSystemEdit from "@/componts/admin/thirdSystemEdit.vue"
import thirdSysAccountList from "@/componts/admin/thirdSysAccountList.vue"
import thirdSysConfList from "@/componts/admin/thirdSysConfList.vue"
export default {
    components: { thirdSystemEdit, thirdSysAccountList, thirdSysConfList },
    data() {
        return {
            imgServer: import.meta.env.VITE_BASE_API,
            searchModel: {},
            icoList: [],
            dataList: [],//table展示列表
            statusList: [],
            formLoginTypeList: [],
            encryptPwdTypeList: [],
            sysCategoryList: [],
            sysTypeList: [],
            showModeList: [],
            loading: false,
            total: ""
        }
    },
    methods: {
        getDictCss(dicList, cellValue) {
            return getDictCss(dicList, cellValue)
        },
        formatDict(dicList, cellValue) {
            return formatDict(dicList, cellValue)
        },
        formatImg(icoId) {
            let result = "";
            this.icoList.forEach(element => {
                if (element.id == icoId) {
                    result = element.icoUrl
                }
            })
            return result;
        },
        /*** 搜索 */
        search(keyWord) {
            // listThirdSystem(this.searchModel)
            // .then((res) => {
            //     this.dataList = res.data.result.list
            //     this.total = res.data.result.total
            // })
            if (keyWord) {
                let result = []
                this.dataList.forEach(item => {
                    if (item.sysName.search(keyWord) != -1) {
                        result.push(item)
                    }
                })
                this.dataList = result
            } else {
                listThirdSystem(this.searchModel)
                    .then((res) => {
                        this.dataList = res.data.result.list
                        this.total = res.data.result.total
                    })
            }
        },
        edit(id) {
            getThirdSystem(id)
                .then(res => {
                    mitt.emit('openThirdSystemEdit', res.data.result)
                })
        },
        add() {
            mitt.emit('openThirdSystemAdd')
        },
        deleted(id) {
            this.$confirm('删除图标, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteThirdSystem(id)
                    .then(res => {
                        this.search()
                        this.$message.success(res.data.msg)
                    })
            }).catch(() => { })
        },
        conf(id) {
            //console.log(id);
            listThirdSysConf({ thirdSystemId: id }).then(res => {
                mitt.emit("conf", { confId: id, list: res.data.result })
            })
        },
        account(id) {
            listThirdSysAccount({ thirdSystemId: id }).then(res => {
                mitt.emit("account", { accountId: id, list: res.data.result })
            })
        },
        async init() {
            try {
                let icoList = await listIcon({ pageSize: 9999, icoCategory: "ico_third_sys" })
                this.icoList = icoList.data.result.list

                let res = await listThirdSystem();
                this.dataList = res.data.result.list
                this.total = res.data.result.total

                let status = await listDictByNameEn('common_status')
                this.statusList = status.data.result
                let loginType = await listDictByNameEn('form_login_type')
                this.formLoginTypeList = loginType.data.result
                let pwdType = await listDictByNameEn('encrypt_pwd_type')
                this.encryptPwdTypeList = pwdType.data.result
                let sysType = await listDictByNameEn('type_id')
                this.sysTypeList = sysType.data.result
                let sysCategory = await listDictByNameEn('sys_category')
                this.sysCategoryList = sysCategory.data.result
                let showMode = await listDictByNameEn('show_mode')
                this.showModeList = showMode.data.result
            } catch (err) {
            }
        },
    },
    created() {
        this.init();
    },

}
</script>
<style scoped lang="scss">
.el-row {
    margin-bottom: 20px;
    background-color: #fff;
    padding: 20px 10px;
    border-radius: 5px;
}

.title_des {
    font-size: 15px;
    display: flex;
    align-items: flex-end;
}

.log-list:nth-child(2n-1) {
    background-color: rgba(248, 248, 248);
}

.log-list {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 10px;
    height: 35px;
    line-height: 35px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
}

.log-list:hover {
    background-color: rgb(227, 233, 241);
}

.input-ta {
    border: none;
}

.scroll {
    height: vh(850) !important;
    // height:vh(850);
    width: 22% !important;
    margin-right: 15px;
}

.dialog-content {


    height: vh(850);
    width: vw(1480);
    display: flex;
    flex-direction: row;

    >div {
        height: vh(850);
        width: 78%;
    }
}
</style>