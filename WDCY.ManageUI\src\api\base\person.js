import request from '@/utils/request'

export const listPerson = (data) =>
	request({
		url: '/person',
		method: 'get',
		params: data
	})
export const queryDistrict = (data) =>
	request({
		url: '/userGroup/queryDistrict',
		method: 'get',
		params: data
	})
export const queryList = (data) =>
	request({
		url: '/person/list',
		method: 'get',
		params: data
	})
	
export const getIdCard = (data) =>
	request({
		url: '/person/idCard',
		method: 'get',
		params: data
	})
export const getPerson = (id) =>
	request({
		url: '/person/'+id,
		method: 'get',
	})
export const personExamine = (data) =>
	request({
		url: '/person/examine',
		method: 'post',
		data:data
	})

export const addPerson = (data) =>
	request({
		url: '/person',
		method: 'post',
		data: data
	})
export const editPerson = (data) =>
	request({
		url: '/person',
		method: 'put',
		data: data
	})
export const editPersonOpenId = (data) =>
	request({
		url: '/person/updateByIdAndOpenId',
		method: 'put',
		data: data
	})
export const editCard = (data) =>
request({
	url: '/person/update-card',
	method: 'post',
	data: data
})
export const deletePerson = (data) =>
	request({
		url: '/person',
		method: 'delete',
		params: data
	})

	export const importPersonImg = (data) =>
	request({
		url: '/person/batch/image',
		method: 'post',
		data: data,
		timeout: 1000 * 60 * 30
	})
export const queryByIdCardOrPhone = (data) =>
	request({
		url: '/person/idCardOrPhone',
		method: 'get',
		params: data
	})	
	export const addQuickEntry = (data) =>
	request({
		url: '/person/quickEntry',
		method: 'post',
		data: data
	})		