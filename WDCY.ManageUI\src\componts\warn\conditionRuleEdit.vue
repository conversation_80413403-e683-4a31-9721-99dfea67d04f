<template>
  <el-dialog draggable
    width="50%"
    destroy-on-close
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form :rules="rules" :model="conditionModel" label-width="120px">
      <el-row>
        <el-col :span="18">
          <el-form-item label="触发类型" prop="triggerType">
            <el-radio
              v-model="conditionModel.triggerType"
              :label="0"
              @click="refresh()"
              >设施状态</el-radio
            >
            <el-radio
              v-model="conditionModel.triggerType"
              :label="1"
              @click="refresh()"
              >事件感知</el-radio
            >
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="事件名称" prop="name">
            <el-select
              style="width: 100%"
              @change="triggerChange"
              v-model="conditionModel.id"
              placeholder="事件名称"
            >
              <el-option
                v-if="conditionModel.triggerType != null"
                v-for="item in conditionModel.triggerType == 0
                  ? deviceTypeList
                  : eventTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="事件规则" prop="type">
            <el-row>
              <!-- <span
                style="
                  display: flex;
                  align-items: center;
                  justify-content: flex-start;
                  font-size: 15px;
                "
                v-for="(item, index) in checkRuleList"
                :key="index"
              >
                <span>{{ item.text }}</span>
                <span v-show="item.value">『{{ item.value }}』</span>
              </span> -->
              <span style="font-size: 15px">{{getDetectRuleNote()}}</span>
            </el-row>
          </el-form-item>
        </el-col>
				<el-col :span="18">
					<el-form-item label="重复过滤时间(秒)" prop="repeatFilterDuration">
						<el-input-number
							v-model="conditionModel.repeatFilterDuration"
							:min="0"
							:step="1"
							controls-position="right"
						/>
					</el-form-item>
				</el-col>
      </el-row>
    </el-form>
    <el-row justify="end">
      <el-button type="primary" @click="onConditionSubmit">提 交</el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import {
  warnStrategyConditionAdd,
  warnStrategyConditionEdit,
} from "@/api/warn/warnStrategy";
import {
  getWarnEventSourceInfoType,
  getWarnEventSourceInfo,
} from "@/api/warn/warn";
import { getWarnDetectRuleNote } from "@/utils/myUtils";
import mitt from "@/utils/mitt";
export default {
  data() {
    return {
      loading: false,
      communityId: localStorage.getItem("communityId"),
      id: 0,
      conditionId: 0,
      conditionModel: {},
      conditionModel1: {},
      type: "",
      deviceTypeList: [],
      eventTypeList: [],
      dialog: {},
      strategyId: null,
      checkRuleList: [], //按占位符分解模板
      rules: {
        triggerType: [
          {
            required: true,
            message: "请选择触发类型",
            trigger: "blur",
          },
        ],
        name: [
          {
            required: true,
            message: "请选择事件名称",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    refresh() {
      this.conditionModel = {};
      this.checkRuleList = [];
    },
    // 事件名称改变
    triggerChange(id) {
      getWarnEventSourceInfo(id)
        .then((res) => {
          this.conditionModel = res.data.result;
          // this.loadWarnEventDetectRules();
          if (!this.conditionModel.repeatFilterDuration)
            this.conditionModel.repeatFilterDuration = 0;
        })
    },
    onConditionSubmit() {
      this.conditionModel1.warnEventSourceId = this.conditionModel.id;
      this.conditionModel1.warnStrategyId = this.id;
      this.conditionModel1.repeatFilterDuration = this.conditionModel.repeatFilterDuration;
      console.log("onConditionSubmit: ", this.conditionModel);
      if (this.type === "add") {
        this.conditionModel1.id = 0;
        warnStrategyConditionAdd(this.conditionModel1)
          .then((res) => {
            this.$message.success(res.data.msg);
            this.$emit("search");
            this.dialog.show = false;
          })
      } else {
        this.conditionModel1.id = this.conditionId;
        warnStrategyConditionEdit(this.conditionModel1)
          .then((res) => {
            this.$message.success(res.data.msg);
            this.$emit("search");
            this.dialog.show = false;
          })
      }
    },
    // 初始化数据加载
    loadTriggerType() {
      getWarnEventSourceInfoType({
        communityId: this.communityId,
        type: 0,
      })
        .then((res) => {
          this.deviceTypeList = res.data.result;
        })
      getWarnEventSourceInfoType({
        communityId: this.communityId,
        type: 1,
      })
        .then((res) => {
          this.eventTypeList = res.data.result;
        })
    },
    // loadWarnEventDetectRules() {
    //   let checkRuleNote = this.conditionModel.note;
    //   if (!checkRuleNote) return;

    //   let ruleList = checkRuleNote.split(/#\d\#/);
    //   // for (var i = 0; i < ruleList.length; i++) {
    //   //   if (
    //   //     ruleList[i] == undefined ||
    //   //     ruleList[i] == null ||
    //   //     ruleList[i] == ""
    //   //   ) {
    //   //     ruleList.splice(i, 1);
    //   //   }
    //   // }
    //   this.checkRuleList = ruleList;
    //   let ruleIndexList = checkRuleNote.match(/#\d\#/gi);
    //   ruleIndexList = Array.from(ruleIndexList, (element) => {
    //     return Number(element.replace(/#/gi, ""));
    //   });

    //   let list = [];
    //   let index = 1;
    //   for (let temp of this.checkRuleList) {
    //     let itemTemp = this.conditionModel.warnEventDetectRules.find(
    //       (m) => m.sort == ruleIndexList[index - 1]
    //     );
    //     // for (let item of this.conditionModel.warnEventDetectRules) {
    //     //   if (item.sort == ruleIndexList[index-1]) {
    //     //     itemTemp = item;
    //     //     break;
    //     //   }
    //     // }
    //     // debugger
    //     let value = "";
    //     if (itemTemp) {
    //       if (itemTemp.valDataType == "BOOL") {
    //         // value = itemTemp.optionVal.split("|")[Math.abs(parseInt(itemTemp.thresholdVal) - 1)];
    //         value = itemTemp.optionVal.split("|").reverse()[
    //           parseInt(itemTemp.thresholdVal)
    //         ];
    //       } else {
    //         value = itemTemp.thresholdVal;
    //       }
    //     }
    //     list.push({ text: temp, value: value });
    //     index++;
    //     console.log("conditionModel.warnEventDetectRules.find:", itemTemp);
    //   }
    //   console.log("loadWarnEventDetectRules.list:", list);
    //   this.checkRuleList = list;
    // },
    // 事件规则
    getDetectRuleNote() {
      return getWarnDetectRuleNote(this.conditionModel.note, this.conditionModel.warnEventDetectRules);
    },
  },
  mounted() {
    this.$nextTick(function () {
      this.loadTriggerType();
      mitt.on("openConditionRuleEdit", (condition) => {
        this.type = "edit";
        this.id = condition.id;
        this.conditionModel = condition.data;
        this.conditionId = condition.conditionId;
        if (!this.conditionModel.repeatFilterDuration && condition.repeatFilterDuration)
            this.conditionModel.repeatFilterDuration = condition.repeatFilterDuration;
        console.log("openConditionRuleEdit:", condition, this.conditionModel);
        // this.loadWarnEventDetectRules();
        this.dialog.show = true;
        this.dialog.title = "修改";
      });
      mitt.on("openConditionRuleAdd", (condition) => {
        this.type = "add";
        this.id = condition.id;
        this.checkRuleList = [];
        this.conditionModel = {
          id: null,
          // triggerType: 1     进入添加页面初始值:   0/1
          repeatFilterDuration: 0
        };
        this.dialog.show = true;
        this.dialog.title = "添加";
      });
    });
  },
};
</script>
