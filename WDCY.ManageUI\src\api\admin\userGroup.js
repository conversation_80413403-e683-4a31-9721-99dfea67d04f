import request from '@/utils/request'

export const listUserGroup = (data) =>
	request({
		url: '/userGroup',
		method: 'get',
		params: data
	})
export const getUserGroup = (id) =>
	request({
		url: '/userGroup/'+id,
		method: 'get'
	})
export const currentUserGroup = () =>
	request({
		url: '/userGroup/uList',
		method: 'get'
	})
export const addUserGroup = (data) =>
	request({
		url: '/userGroup',
		method: 'post',
		data: data
	})
export const editUserGroup = (data) =>
	request({
		url: '/userGroup',
		method: 'put',
		data: data
	})
export const deleteUserGroup = (id) =>
	request({
		url: '/userGroup',
		method: 'delete',
		params: {
			id: id
		}
	})
