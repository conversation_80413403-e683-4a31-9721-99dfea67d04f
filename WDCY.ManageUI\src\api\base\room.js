import request from '@/utils/request'

export const listRoom = (data) =>
	request({
		url: '/room',
		method: 'get',
		params: data
	})
export const getRoom = (id) =>
	request({
		url: '/room/'+id,
		method: 'get',
	})
export const addRoom = (data) =>
	request({
		url: '/room',
		method: 'post',
		data: data
	})
export const editRoom = (data) =>
	request({
		url: '/room',
		method: 'put',
		data: data
	})
export const deleteRoom = (id) =>
	request({
		url: '/room',
		method: 'delete',
		params: {
			id: id
		}
	})
