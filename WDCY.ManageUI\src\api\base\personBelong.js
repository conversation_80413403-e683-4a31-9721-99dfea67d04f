import request from '@/utils/request'

export const getPersonBelong = (id) =>
	request({
		url: '/personBelong/'+id,
		method: 'get'
	})
	
export const listPersonBelong = (data) =>
	request({
		url: '/personBelong',
		method: 'get',
		params: data
	})
	//查看本户
export const personViewAccount = (data) =>
	request({
		url: '/person/viewAccount',
		method: 'get',
		params: data
	})
export const addPersonBelong = (data) =>
	request({
		url: '/personBelong',
		method: 'post',
		data: data
	})
export const editPersonBelong = (data) =>
	request({
		url: '/personBelong',
		method: 'put',
		data: data
	})
export const deletePersonBelong = (id) =>
	request({
		url: '/personBelong',
		method: 'delete',
		params: {
			id: id
		}
	})
