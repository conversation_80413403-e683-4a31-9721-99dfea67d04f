import request from '@/utils/request'

export const getCommunityTotal = (data) =>
	request({
		url: '/community/total',
		method: 'get',
		params: data
	})
export const getPersonTotal = (data) =>
	request({
		url: '/person/total',
		method: 'get',
		params: data
	})
export const getVehicleTotal = (data) =>
	request({
		url: '/vehicle/total',
		method: 'get',
		params: data
	})
export const getRoomTotal = (data) =>
	request({
		url: '/room/total',
		method: 'get',
		params: data
	})
export const getDeviceInfoTotal = (data) =>
	request({
		url: '/deviceInfo/total',
		method: 'get',
		params: data
	})
export const getVisitorsTotal = (data) =>
	request({
		url: '/visitors/total',
		method: 'get',
		params: data
	})
export const getWxTotal = (data) =>
	request({
		url: 'wx/total',
		method: 'get',
		params: data
	})
export const getUserGroupTotal = (data) =>
	request({
		url: '/userGroup/total',
		method: 'get',
		params: data
	})

export const getPersonSituation = (data) =>
	request({
		url: '/situation/sts/person-total',
		method: 'post',
		data: data
	})
export const getVehicleSituation = (data) =>
	request({
		url: '/situation/sts/vehicle-total',
		method: 'post',
		data: data
	})
export const getDeviceEventSituation = (data) =>
	request({
		url: '/deviceInfo/total-device-event',
		method: 'post',
		data: data
	})
export const getWarnTotalSituation = (data) =>
	request({
		url: '/warn/Statistics/total-warn-group',
		method: 'post',
		data: data
	})
export const getWarningSituation = (data) =>
	request({
		url: '/community/total-list',
		method: 'get',
		params: data
	})