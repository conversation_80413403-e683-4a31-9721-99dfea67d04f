<template>
	<el-dialog draggable width="25%" destroy-on-close v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" :model="roleModel" label-width="100px">
			<el-row>
				<el-col>
					<el-form-item label="数据范围" prop="scope">
						<el-select style="width: 100%;" v-model="roleModel.scope" clearable placeholder="数据范围">
						    <el-option v-for="item in scopeList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col v-show="roleModel.scope == 1">
					<el-form-item label="自定义范围" prop="ids">
						<el-tree check-strictly @check="menuCheck" :default-checked-keys="roleModel.scopeIds" :data="groupList" show-checkbox node-key="id" :props="permissionProps" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { listUserGroup } from "@/api/admin/userGroup"
import { scopeEdit } from "@/api/admin/role"
import mitt from "@/utils/mitt";
export default {
	props:['scopeList'],
	data() {
		return {
			loading: false,
			roleModel: {},
			groupList:[],
			dialog:{},
			permissionProps:{
				children: 'children',
				label: 'groupName',
			},
		}
	},
	methods: {
		onSubmit(){
			scopeEdit(this.roleModel)
			.then(res =>{
				this.$message.success(res.data.msg)
				this.dialog.show = false
			})
		},
		menuCheck(node,checked){
			this.roleModel.scopeIds = checked.checkedKeys
		}
	},
	mounted(){
		this.$nextTick(function() {
			mitt.on('openRoleScopeEdit', (role) => {
				this.roleModel = role
				this.roleModel.roleId = role.id
				listUserGroup({pageSize:999999})
				.then(res =>{
					this.groupList = res.data.result.list
				})
				this.dialog.show = true
				this.dialog.title = "数据范围"
			})
		})
	}
}
</script>
