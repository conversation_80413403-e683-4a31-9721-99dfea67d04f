import request from '@/utils/request'

export const listVehicle = (data) =>
	request({
		url: '/vehicle',
		method: 'get',
		params: data
	})
export const getVehicle = (id) =>
	request({
		url: '/vehicle/'+id,
		method: 'get',
	})
export const importData = (data) =>
	request({
		url: '/vehicle/importData',
		method: 'post',
		data: data,
		timeout: 1000 * 60 * 30
	})	
export const addVehicle = (data) =>
	request({
		url: '/vehicle',
		method: 'post',
		data: data
	})
export const editVehicle = (data) =>
	request({
		url: '/vehicle',
		method: 'put',
		data: data
	})
export const editVehicleOpenId = (data) =>
	request({
		url: '/vehicle/updateByIdAndOpenId',
		method: 'put',
		data: data
	})
export const deleteVehicle = (id) =>
	request({
		url: '/vehicle',
		method: 'delete',
		params: {
			id: id
		}
	})
export const updateVehicle = (data) =>
	request({
		url: '/vehicle/renewal',
		method: 'put',
		data: data
	})