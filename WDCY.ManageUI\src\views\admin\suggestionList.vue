<template>
    <suggestion-edit @search="search" :openList="openList"></suggestion-edit>
    <el-row :gutter="20">
			<el-col :span="6" style="display:flex">
				<el-input style="margin-right:10px;width:50%" @keydown.enter="search" v-model="searchModel.keyWord" placeholder="请输入..." clearable />
				<el-button type="primary" @click="search">搜 索</el-button>
			</el-col>
			<el-col :span="4" :push="14">
				<el-button style="float: right;" :disabled="ids.length == 0" type="primary" @click="deleted" v-if="hasPerm('sys:suggestion:delete')">删 除</el-button>
				<!-- <el-button style="float: right; margin-right: 20px;" :disabled="ids.length == 0" type="primary" @click="push">下发</el-button> -->
			</el-col>
		</el-row>
		<el-row :gutter="20">
			<el-col :span="24">
				<el-table row-key="id" stripe :data="dataList" border height="calc(100vh - 300px)" style="width: 100%; " @selection-change="handleSelectionChange">
					<el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
					<el-table-column prop="ID" align="center" label="ID"/>
					<el-table-column prop="wxUserId" align="center" label="微信ID" />
					<el-table-column prop="content" align="center" label="内容详情" />
					<el-table-column prop="replyContent" align="center" label="回复详情" />
					<el-table-column prop="publicType" align="center" label="是否公开" >
						<template #default="scope">
							{{ formatDict(openList, scope.row.publicType) }}
                        </template>
					</el-table-column>
					<el-table-column prop="createTime" align="center" label="创建时间" />
						
                    <el-table-column align="center"  label="操作">
                        <template #default="scope">
                                <el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('sys:suggestion:edit')">回 复</el-button>
                        </template>
                    </el-table-column>
				</el-table>
			</el-col>
			<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
				<el-pagination background :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
			</el-col>
		</el-row>
</template>
<script>
import { listSuggestion, getSuggestion, editSuggestion, deleteSuggestion} from "@/api/admin/opinion"
import { getDictCss, formatDict } from "@/utils/dict"
import { listDictByNameEn } from "@/api/admin/dict"
import suggestionEdit from "@/componts/admin/suggestionEdit.vue"
import mitt from "@/utils/mitt"
export default {
	components:{ suggestionEdit },
	data() {
		return {
			searchModel: {
				// communityId:localStorage.getItem("communityId")
			},
			index:0,
			dataList:[],
			ids:[],
			total:0,
			pageSize:10,
			openList:[]
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		handleSelectionChange(val){
			let list = []
			for(let item of val){
				list.push(item.id)
			}
			this.ids = list
		},
		search(){
			listSuggestion(this.searchModel)
			.then(res =>{
				this.dataList = res.data.result.list
				this.total = res.data.result.total
			})
		},
        edit(id){
           
            getSuggestion(id).then(res =>{
                mitt.emit("openSuggestionEdit", res.data.result)
            })
            // editSuggestion({id :id,content:"a按时发放sd"}).then(res =>{
            //     console.log(res);
            // })
        },
		deleted(){
			this.$confirm(`删除意见建议, 是否继续?`, '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(()=>{
					deleteSuggestion(this.ids)
			.then(res =>{
				this.search()
				this.$message.success(res.data.msg)
			})
				})
			
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
            listSuggestion().then( res => {
                this.dataList = res.data.result.list
            })
			let status_res = await listDictByNameEn('data_type')
			this.statusList = status_res.data.result
			let open_res = await listDictByNameEn('open')
			this.openList = open_res.data.result
			
			// this.search()
		}
	},
	created() {
		this.init()
	}
}
</script>
<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	.title_des{
		font-size: 15px;
		display: flex;
		align-items: flex-end;
	}
</style>