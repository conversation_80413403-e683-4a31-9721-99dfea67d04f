<template>
	<el-dialog draggable width="40%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="healthExaminaModel" label-width="100px">
            <el-row>
				<el-col :span="24">
					<el-form-item label="系统功能ID" prop="funcId">
						<el-input maxlength="18" show-word-limit v-model="healthExaminaModel.funcId" placeholder="系统功能ID"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
            <el-row>
                <el-col :span="24">
                <el-form-item label="请求参数" prop="expandParams">
                    <JsonEditorVue
                    language="cn"
                    class="editor"
                    :modelValue="jsonVal"
                    @update:modelValue="changeJson"
                    />
                </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                <el-form-item label="响应结果" prop="expandParams">
                    <JsonEditorVue
                    language="cn"
                    class="editor"
                    :modelValue="jsonValed"
                    @update:modelValue="changeJson"
                    />
                </el-form-item>
                </el-col>
            </el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="submit">生 成</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { getProductionAssert } from "@/api/healthCheck/healthExamination"
import mitt from "@/utils/mitt";
import JsonEditorVue from "json-editor-vue3";
export default {
    components: { JsonEditorVue },
	data() {
		return {
			loading: false,
			healthExaminaModel: {},
			dialog: {},
            jsonVal: {},
            jsonValed: {},
			personId: "",
			startToEndTime: [],
			imgServer: import.meta.env.VITE_BASE_API,
		}
	},
	methods: {
        changeJson(json) {
        	this.jsonVal = json;
        },
        changeJsoned(json) {
        	this.jsonValed = json;
        },
        close(){
            this.dialog.show = false
        },
        submit(){
            this.jsonVal.funcId = this.healthExaminaModel.funcId
            getProductionAssert(this.jsonVal).then(res => {
                this.jsonValed = JSON.parse(JSON.stringify(res.data));
                console.log(res.data);
            }).catch(()=>{
                this.$message.error('生成失败')
            })
        },
        init(){
        }
	},
	mounted() {
        this.jsonVal = {};
		this.$nextTick(function () {
			mitt.on('openProductionAssert', (id) => {
                if (id) {
                    console.log(id);
                    this.healthExaminaModel.funcId = id
                }
				this.dialog.show = true
				this.dialog.title = "断言生成"
                // this.jsonVal = JSON.parse(this.healthExaminaModel.expandParams);
			})
		})
	},
    created(){
        this.init()
    }
}
</script>
<style scoped>

.editor {
  width: 805px;
}
.avatar-uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}
</style>
