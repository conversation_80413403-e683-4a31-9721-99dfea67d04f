<template>
    <mini-program-edit :categoryList="categoryList" :typeList="typeList" :statusList="statusList" @search="search"></mini-program-edit>
    <mini-app-bind-menu @search="search"></mini-app-bind-menu>
    <el-row :gutter="20">
		<el-col :span="12" style="display:flex">
			<!-- <el-input style="margin-right:10px" v-model="searchModel.name" placeholder="姓名" clearable /> -->
            <el-select style="margin-right:10px" v-model="searchModel.appCategory" placeholder="小程序分类" clearable>
                <el-option v-for="item in categoryList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"/>
            </el-select>
            <el-select style="margin-right:10px" v-model="searchModel.state" placeholder="小程序状态" clearable>
                <el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"/>
            </el-select>
            <el-select style="margin-right:10px" v-model="searchModel.appType" placeholder="小程序类型" clearable>
                <el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
            </el-select>
            <el-input style="width:200px" v-model="searchModel.keyword" placeholder="模糊匹配小程序名称" clearable/>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
        <el-col :span="4" :push="4">
			<el-button style="float: right;" type="primary" @click="add" v-if="hasPerm('miniProgram:app:add')">新 增</el-button>
		</el-col>
	</el-row>
    <el-row :gutter="20">
		<el-col :span="24">
			<el-table :data="personList" border height="calc(100vh - 300px)" style="width: 100%">
                <el-table-column label="ID" align="center" prop="id" width="100"/>
                <el-table-column label="小程序图标" align="center" prop="icoUrl" width="100">
                    <template #default="scope">
                        <el-image :src="imgServer+scope.row.icoUrl" fit="contain"></el-image>
                    </template>
                </el-table-column>

                <el-table-column label="系统名称" align="center" prop="appName" />
                <el-table-column label="小程序ID" align="center" prop="appId" />
                <el-table-column label="小程序导航路径" align="center" prop="navigatePath" />
                <el-table-column label="备注" align="center" prop="note" />

                <el-table-column label="小程序分类" align="center" prop="appCategory">
                    <template #default="scope">
						<el-tag :type="getDictCss(categoryList, scope.row.appCategory)">{{ formatDict(categoryList, scope.row.appCategory) }}</el-tag>
                    </template>
                </el-table-column>

                <el-table-column label="小程序类型" align="center" prop="appType">
                    <template #default="scope">
						<el-tag :type="getDictCss(typeList, scope.row.appType)">{{ formatDict(typeList, scope.row.appType) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="排序" sortable align="center" prop="sort" />

                <el-table-column label="发布状态" align="center" prop="state">
                    <template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.state)">{{ formatDict(statusList, scope.row.state) }}</el-tag>
                    </template>
                </el-table-column>
                
				<el-table-column align="center"  label="操作" width="170">
					<template #default="scope">
							<el-button type="text" size="default" @click="bind(scope.row)">绑定菜单</el-button>
							<el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('miniProgram:app:update')">修改</el-button>
							<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('miniProgram:app:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>

<script>
import { miniProgramList, getMiniProgram, deleteMiniProgram } from "@/api/weChat/miniProgram"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import miniProgramEdit from "@/componts/weChat/miniProgramEdit.vue"
import miniAppBindMenu from "@/componts/weChat/miniAppBindMenu.vue"
import errImg from "../../assets/img/defaultHead.png"
export default {
	components: { miniProgramEdit, miniAppBindMenu },
	data() {
		return {
			searchModel: {},
			personList: [],
			imgServer: import.meta.env.VITE_BASE_API,
			communityId: localStorage.getItem("communityId"),
			statusList: [],
			categoryList: [],
			typeList: [],
			total: 0,
			pageSize: 10,
			errorHeadImg: errImg,
			communityShow: true
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			console.log(this.searchModel);
			if (this.searchModel.appCategory == '') {
				delete (this.searchModel.appCategory)
			}
			if (this.searchModel.appType == '') {
				delete (this.searchModel.appType)
			}
			miniProgramList(this.searchModel)
				.then(res => {
					this.personList = res.data.result.list
					this.total = res.data.result.total
				})
		},
		add() {
			mitt.emit('openMiniProgramAdd', Number(this.total) +1)
		},
		deleted(id) {
			this.$confirm('删除信息, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteMiniProgram(id)
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
		},
		// 绑定
		bind(row){
			mitt.emit('openMiniAppBind', row)
		},
		edit(id) {
			getMiniProgram(id).then(res => {
				mitt.emit('openMiniProgramEdit', res.data.result)
			})
		},
		view(id) {
			getWeChat({ id: id }).then(res => {
				mitt.emit('weChatUserView', res.data.result)
			})
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num) {
			this.searchModel.pageSize = num
			this.search()
		},
		async init() {
			try {
				let res = await miniProgramList(this.searchModel)
				this.personList = res.data.result.list
				this.total = res.data.result.total

				let appType = await listDictByNameEn('app_type')
				this.typeList = appType.data.result

				let appCategory = await listDictByNameEn('app_category')
				this.categoryList = appCategory.data.result

				let appStatus = await listDictByNameEn('app_status')
				this.statusList = appStatus.data.result
			} catch (err) {
			}
		}
	},
	created() {
		this.init()
	}
}
</script>
<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>