import request from '@/utils/request'

export const keyPersonList = (data) =>
	request({
		url: '/keyPerson',
		method: 'get',
		params: data
	})
export const getKeyPerson = (id) =>
	request({
		url: '/keyPerson/'+id,
		method: 'get',
	})
export const keyPersonListAdd = (data) =>
	request({
		url: '/keyPerson',
		method: 'post',
		data: data
	})
export const keyPersonListEdit = (data) =>
	request({
		url: '/keyPerson',
		method: 'put',
		data: data
	})
export const keyPersonListDelete = (id) =>
	request({
		url: '/keyPerson',
		method: 'delete',
		params: {
			id: id
		}
	})
export const typeList = (data) =>
	request({
		url: '/warnEventType/queryAll',
		method: 'get',
		params: data
})	

