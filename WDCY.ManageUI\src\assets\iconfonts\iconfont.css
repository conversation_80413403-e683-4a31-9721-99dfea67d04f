@font-face {
  font-family: "iconfont"; /* Project id 3867651 */
  src: url('iconfont.woff2?t=1674110138034') format('woff2'),
       url('iconfont.woff?t=1674110138034') format('woff'),
       url('iconfont.ttf?t=1674110138034') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-zcpt-weixinguanli:before {
  content: "\e6b6";
}

.icon-xitongguanli:before {
  content: "\e648";
}

.icon-shuju:before {
  content: "\e8c6";
}

.icon-xiaoqu:before {
  content: "\100de";
}

.icon-xiaoqu1:before {
  content: "\100e3";
}

