<template>
    <el-dialog draggable
    top="3vh"
    width="1000px"
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title">
    <el-form :rules="rules" ref="form" :model="warnEventSourceModel" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="11.5">
          <el-date-picker
            v-model="searchModel.startTime"
            type="date"
            placeholder="选择开始日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            :size="size"
            style="margin-right:10px"
          />
          <el-date-picker
            style="margin-right:10px"
            v-model="searchModel.endTime"
            type="date"
            placeholder="选择结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            :size="size"
          />
          <el-input style="width:170px" v-model="searchModel.keyWord" @keydown.enter="search" placeholder="关键字" clearable />
        </el-col>
        <el-col :span="4">
			    <el-button style="margin-left:10px" type="primary" @click="search">搜 索</el-button>

        </el-col>
        <el-col :span="2" :push="2">
          <el-button type="primary" @click="add">新 增</el-button>
        </el-col>
          
      </el-row>
        <el-row>
        <el-col style="height: 500px;margin-top:10px;overflow:auto">
          <!-- <el-form-item label="关联设施" prop="note" style=" height: 380px;width:100%"> -->
            <el-row>
              <!-- <el-col :span="24">
                <el-table
                  :data="dataList"
                  style="width: 100%;height: 335px; overflow:auto"
                  border
                >
                  <el-table-column prop="id" align="center" label="ID" width="200"></el-table-column>
                  <el-table-column prop="note" align="center" label="描述"></el-table-column>
                  <el-table-column prop="maintenanceTime" align="center" label="维护时间"></el-table-column>
                  <el-table-column align="center" label="操作" width="240">
                    <template #default="scope">
                      <el-button type="text" size="default" @click="view(scope.row.id,'video')">查看视频</el-button>
                      <el-button type="text" size="default" @click="view(scope.row.id,'photo')">查看图片</el-button>
                      <el-button type="text" size="default" @click="edit(scope.row.id)">编辑</el-button>
                      <el-button type="text" size="default" @click="remove(scope.row.id)">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col> -->
              <el-col style="margin-top:10px;height:calc(100% );overflow:auto">
              <div v-for="(activity, index) in dataList" style="padding-left:30px;" :key="index">
                <div style="line-height:32px;border-left:2px solid #e0e4ea;padding:0 15px">
                  <div style="height:10px;width:10px;background: #568efe;border-radius:5px;position:relative;right:21px;top:21px">
                    <div style="height:12px;width:12px;border:1px solid #568efe;border-radius:6px;position:relative;left:-2px;top:-2px"></div>
                  </div>
                  <div style="display:flex;justify-content:space-between;align-items:center; line-height:32px;background:#e6edff;padding:0 15px">
                    <div>{{activity.maintenanceTime}}</div>
                    <div>
                      <el-button type="text" size="default" @click="edit(activity.id)">编辑</el-button>
                      <el-button type="text" size="default" @click="remove(activity.id)">移除</el-button>
                    </div>
                  </div>
                  <div style="background:#f5f5f5;padding:10px 15px">
                    <div style="display:flex;">
                      <div style="width:70px">相关描述:</div>
                      <div style="flex:1;background:#fff;border-radius:5px;padding:0 15px">{{activity.note}}</div>
                    </div>
                  </div>
                  <div style="">
                    <div style="background:#f5f5f5;padding:0 15px 10px;width:96.7%;display:flex;flex-direction:column;">
                      <div style="display:flex;" v-if="activity.photoList">
                        <div style="width:70px">相关图片:</div>
                        <!-- <div style="flex:1;background:#fff;border-radius:5px;padding:0 15px">{{activity.photoList.url}}</div> -->
                        <!-- <div style="display:flex;flex-wrap:wrap;overflow:auto; width:100%;max-height:300px;padding-left:15px">
                          <div class="img-box" v-for="(item, index) in activity.photoList" :key="index" style="width:32%;">
                            <el-image :preview-src-list="formatPreviewList(activity.photoList)" :initial-index="index" :src="imgServer + item.url" style="height:120px;"></el-image>
                            <div style="position:relative ;bottom:13px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{item.name}}</div>
                          </div>
                        </div> -->
                        <el-scrollbar
                          class="pic--video"
                          style="height: 198px; flex:1"
                        >
                        <div style="display: flex">
                  <div
                    v-for="(item, idx) in activity.photoList"
                    :key="idx"
                    style="
                      width: 148px;
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                      margin-right: 10px;
                    "
                  >
                    <div
                      class="mask"
                      style="
                        height: 146px;
                        width: 146px;
                        display: flex;
                        align-items: center;
                        justify-content:center;
                        border: 1px dashed #ccc;
                        border-radius: 5px;
                      "
                    >
                      <el-image
                      style="max-height:146px;max-width:146px"
                        :initial-index="idx"
                        :preview-src-list="formatPreviewList(activity.photoList)"
                        :src="imgServer + item.url"
                      ></el-image>
                    </div>
                    <div style="width: 100%; overflow: hidden; height: 32px">
                      {{ item.name }}
                    </div>
                  </div>
                </div>
                        </el-scrollbar>
                      </div>
                    </div>
                    <div style="">
                      <div style="background:#f5f5f5;padding:0 15px 10px;height:100%;display:flex;flex-direction:column;">
                        <div style="padding-bottom:5px;display:flex;height:100%" v-if="activity.videoList">
                          <div style="width:70px">相关视频:</div>
                          <!-- <div style="display:flex;flex:1;background:#fff;border-radius:5px;max-height:300px;min-height:50px;overflow:auto;line-height:28px;padding-left:10px;">
                            <div class="video-box" v-for="(item, index) in activity.videoList" @click="openVideo(item)" style="width:49%;cursor:pointer" :key="index">
                              <video :src="imgServer+''+item.url" style="height:120px;width:100%"></video>
                              <div style="position:relative ;bottom:13px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{item.name}}</div>
                            </div>
                          </div> -->
                          <el-scrollbar
                          class="pic--video"
                          style="height: 198px; flex:1"
                        >
                        <div style="display: flex;height:100%;">
                  <div
                    v-for="(item, idx) in activity.videoList"
                    @click="openVideo(item)"
                    :key="idx"
                    style="
                      width: 148px;
                      display: flex;
                      height:100%;
                      flex-direction: column;
                      align-items: space-between;
                      justify-content: center;
                      flex-shrink: 0;
                      margin-right: 10px;
                    "
                  >
                      <video
                      style="height:146px;max-width:146px;"
                        :src="imgServer+''+item.url"
                      ></video>
                    <div style="width: 100%; overflow: hidden; height: 32px">
                      {{ item.name }}
                    </div>
                  </div>
                </div>
                        </el-scrollbar>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>
          </el-col>
            </el-row>
          <!-- </el-form-item> -->
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center">
      <el-button style="width: 100px;margin-top: 10px;" type="primary" @click="close"
        >关 闭</el-button
      >
    </el-row>
    </el-dialog>
    <el-dialog draggable
    top="5vh"
    width="840px"
    v-loading="loading"
    v-model="secondDialog.show"
    :title="secondDialog.title">
			<el-form ref="formEl" :rules="rulesEdit" :model="recordsModel" label-width="120px" style="padding-right: 25px;">
        <el-form-item label="维护时间" prop="time">
          
          <el-date-picker
              v-model="recordsModel.maintenanceTime"
              type="datetime"
              placeholder="选择维护时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :size="size"
              style="margin-right:10px"
            />
        </el-form-item>
        <el-form-item label="维护图片">
          <el-scrollbar style="height:198px">
						<el-upload
							multiple
							accept="image/jpeg,image/jpg,image/png"
							v-model:file-list="fileList"
							action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
							list-type="picture-card"
							:on-preview="handlePictureCardPreview"
							:on-remove="deletePic"
							:http-request="loadingImg"
              :before-upload="beforeUploadImg"
						>
							<el-icon style="display: flex;flex-wrap: nowrap;"><Plus /></el-icon>
						</el-upload>
<div style="display:flex;position: relative;top:-10px">
  <div v-for="(item, idx) in fileList" :key="idx" style="width:146px;display:flex;flex-shrink:0;margin-right:10px">
    <el-input
      v-model="item.name"
      style="width: 100%"
      placeholder="请输入文件名"
    ></el-input>
  </div>
</div>
          </el-scrollbar>
						<el-dialog v-model="dialogVisible" class="picView">
							<img w-full style="width:100%" :src="dialogImageUrl" alt="Preview Image" />
						</el-dialog>
					</el-form-item>


          <el-form-item label="维护视频">
          <el-scrollbar style="height:198px">
            <el-upload
							multiple
              accept= 'video/*'
							v-model:file-list="fileList1"
							:action="imgServer + '/file/upload'"
							list-type="picture-card"
							:on-preview="handleVideoCardPreview"
							:on-remove="deleteVideo"
							:http-request="loadingVideo"
              :before-upload="beforeUploadVideo"
              :on-success="getVideoCover"
						>
							<el-icon style="display: flex;flex-wrap: nowrap;"><Plus /></el-icon>
						</el-upload>
<div style="display:flex;position: relative;top:-10px">
  <div v-for="(item, idx) in fileList1" :key="idx" style="width:146px;display:flex;flex-shrink:0;margin-right:10px">
    <!-- <video
          style="width: 100px; height: 100px"
          :src="item.url"
        ></video> -->
    <el-input
      v-model="item.name"
      style="width: 100%"
      placeholder="请输入文件名"
    ></el-input>
  </div>
</div>
          </el-scrollbar>
          <el-dialog v-model="dialogVisibleVideo" class="picView videoView">
							<video :src="videoUrl" style="width:100%;max-height:520px" controls>
                <!-- <source v-if="videoUrl" :src="videoUrl" type="video/mp4">
                您的浏览器不支持视频标签。 -->
              </video>
						</el-dialog>

</el-form-item>


        <el-form-item label="备注" prop="note">
          <el-input
            v-model="recordsModel.note"
            maxlength="200"
            placeholder="请填写维护描述"
            show-word-limit
            type="textarea"
          />
        </el-form-item>
      </el-form>
      <el-row justify="center">
        <el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
      </el-row>
    </el-dialog>
    
    <el-dialog
      draggable
      class="picView"
      top="5vh"
      width="1000px"
      v-loading="loading"
      v-model="videoShow"
      title="查看视频">
      <div class="view_contoiner">
          <video
            :src="videoUrl"
            style="max-width:100%;height:100%"
            controls>
          </video>
      </div>
  </el-dialog>
</template>
<script>
import { deviceLinkDelete, recordsList, recordsPush, recordsEdit, recordsQuery, recordsRemove } from "@/api/device/device"
import mitt from "@/utils/mitt";
import { fileUpload, fileRemove } from "@/api/admin/file";
import { getDictCss, formatDict } from "@/utils/dict"

export default {
    props: ["statusList", "dataList", "typeNodeList", 'total', 'deviceType'],
  data() {
    return {
			imgServer: import.meta.env.VITE_BASE_API,
      loading: false,
      warnEventSourceModel: {warnEventDetectRules:[]},
      dataList: [],
      communityId: localStorage.getItem("communityId"),
      dialog: {},
      secondDialog: {},
      viewDialog: {show:false,title:"查看"},
      videoShow: false,
      deviceLinkDtoList: [],
      searchModel: {},
      recordsModel: {},
			fileList: [],
			list: [],
			fileList1: [],
      tableList:[],
			dialogVisible: false,
			dialogVisibleVideo: false,
      devId:"",
      videoUrl:"",
      viewUrl:"",
      uploadImgType:['.jpg','.jpeg','.png','.gif','.JPG','.JPEG','.PNG','.GIF'],
      uploadVideoType:['.mp4'],
      viewStatus: true,
      rulesEdit: {
        note: [
          {
            required: true,
            message: "字数不能为空且不可超过200",
            min:0,
            max:200
          },
        ],
        // time: [
        //   {
        //     required: true,
        //     message: "请输入电话",
        //     trigger: "change",
        //   },
        // ],
      },
    };
  },
  methods: {
    getDictCss(dicList, cellValue) {
        return getDictCss(dicList, cellValue)
    }, 
    formatDict(dicList, cellValue) {
        return formatDict(dicList, cellValue)
    },
    formatPreviewList(data){
      const list = []
      if (data.length) {
        for (const key in data) {
          list.push(this.imgServer+''+data[key].url)
        }
      }
      return list
    },
    // 查看监控
    openVideo(row){
      this.videoShow = true
      this.videoUrl = this.imgServer + row.url
      console.log(this.videoUrl);
    },
    close(){
      this.dialog.show = false
    },
    
		handlePictureCardPreview (uploadFile) {
			console.log(uploadFile);
			this.dialogImageUrl = uploadFile.url
			this.dialogVisible = true
		},
    handleVideoCardPreview(uploadFile){
      this.videoUrl = []
      this.videoUrl = uploadFile.videoUrl
      console.log(uploadFile);
			this.dialogVisibleVideo = true
    },
		deletePic(uploadFile, uploadFiles){
			fileRemove({fileUrl:uploadFile.url.replace(this.imgServer,"")})
      		this.recordsModel.photoList = uploadFiles
		},
		deleteVideo(uploadFile, uploadFiles){
      console.log(uploadFile,this.recordsModel.videoList);
      if (uploadFile.videoUrl.includes("base64")) {
        return
      }
			fileRemove({fileUrl:uploadFile.videoUrl.replace(this.imgServer,"")})
      for (let i = 0; i < this.recordsModel.videoList.length; i++) {
        if (uploadFile.videoUrl.includes(this.recordsModel.videoList[i].url)) {
          this.recordsModel.videoList.splice(i,1)
        }
      }
      console.log(this.recordsModel.videoList);
		},
		loadingImg(files) {
			let form = new FormData();
      form.append("needCompress", false)
			form.append("file", files.file);
			form.append("modulesName", 'device');
			form.append("functionName", 'device');
			form.append("communityId", localStorage.getItem('communityId'));
			fileUpload(form).then((res) => {
				this.recordsModel.photoList.push(res.data.result);
					if (res.data.code == 0) {
					}
			});
		},
    beforeUploadImg(file) {
      const type = file.name.substring(file.name.lastIndexOf('.')) // 获取文件后缀，取文件格式
      const isLt10M = file.size / 1024 / 1024 < 25
      if (!this.uploadImgType.includes(type)) {
        this.$message({ type: 'error', message: '只支持jpg,jpeg,png,gif,JPG,JPEG,PNG,GIF文件格式！' })
        return false
      }
      if (!isLt10M) {
        this.$message({
          message: '上传文件大小不能超过 25MB!',
          type: 'warning'
        })
        return false
      }
    },
    beforeUploadVideo(file) {
      const type = file.name.substring(file.name.lastIndexOf('.')) // 获取文件后缀，取文件格式
      const isLt10M = file.size / 1024 / 1024 < 100
      if (!this.uploadVideoType.includes(type)) {
        this.$message({ type: 'error', message: '只支持mp4文件格式！' })
        return false
      }
      if (!isLt10M) {
        this.$message({
          message: '上传文件大小不能超过 100MB!',
          type: 'warning'
        })
        return false
      }
    },

    // 上传视频
    loadingVideo(files) {
			let form = new FormData();
			form.append("file", files.file);
			form.append("modulesName", 'device');
			form.append("functionName", 'device');
			form.append("communityId", localStorage.getItem('communityId'));
			fileUpload(form).then((res) => {
        if(res.data.code != 0){
          this.$message({
          type: "error",
          message: "视频上传失败",
        });
        return false;
      }
				this.recordsModel.videoList.push(res.data.result);
			});
      console.log(1,files,'files@@@@@@@@@@@@@@@@@@@@');
      files.onSuccess(res => {
        console.log(res,'123');
      })
      this.setFileList(this.fileList1)
		},

    getVideoCover(file){
  const video = document.createElement("video"); // 也可以自己创建video
  video.src = file.url; // url地址 url跟 视频流是一样的
 
  var canvas = document.createElement("canvas"); // 获取 canvas 对象
  const ctx = canvas.getContext("2d"); // 绘制2d
  video.crossOrigin = "anonymous"; // 解决跨域问题，也就是提示污染资源无法转换视频
  video.currentTime = 1; // 第一帧
 
  video.oncanplay = () => {
    canvas.width = video.clientWidth ? video.clientWidth : 320; // 获取视频宽度
    canvas.height = video.clientHeight ? video.clientHeight : 320; //获取视频高度
    // 利用canvas对象方法绘图
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    // 转换成base64形式
    let videoFirstimgsrc = canvas.toDataURL("image/png"); // 截取后的视频封面
    let videoUrl = file.url;
    file.url = videoFirstimgsrc; // file的url储存封面图片
    file.videoUrl = videoUrl; // file的videoUrl储存视频
 
    video.remove();
    canvas.remove();
  };
  return file;
},
setFileList(_fileList) {
  console.log(_fileList);
                for (let obj of _fileList) {
                  //视频附件，获取第一帧画面作为 封面展示
                  this.getVideoCover(obj);
                }
            this.fileList1 = _fileList;     //fileList 为 Element file-list 参数值
        },
    search() {
        // this.searchModel.type = 5
        // this.searchModel.pageSize = this.total
        // this.searchModel.communityId = this.communityId
        recordsList(this.searchModel)
            .then(res => {
              console.log(res.data.result.list);
                this.dataList = res.data.result.list
                // this.total = res.data.result.total
            })
    },
    add() {
				this.fileList = []
				this.fileList1 = []
        this.recordsModel = {
          devId: this.devId,
          videoList: [],
          photoList: []
        }
        this.secondDialog.show = true
        this.secondDialog.title='新增记录'
        this.search()
    },
    edit(id,text){
      this.secondDialog.title='编辑记录'
      this.secondDialog.show = true
      recordsQuery(id).then(res => {
        this.recordsModel = res.data.result

        // 图片
        if (!this.recordsModel.photoList) {
					this.recordsModel.photoList = []
				} else {
					this.fileList = []
					for (const item of this.recordsModel.photoList) {
						if (item.url.includes(this.imgServer)) {
							this.fileList.push({name:item.name,url:item.url})
						}else {
							this.fileList.push({name:item.name,url:this.imgServer + '' + item.url})
						}
					}
				}
        console.log(this.fileList);

        //视频
        if (!this.recordsModel.videoList) {
					this.recordsModel.videoList = []
				} else {
					this.fileList1 = []
					for (const item of this.recordsModel.videoList) {
						if (item.url.includes(this.imgServer)) {
							this.fileList1.push({name:item.name,url:item.url})
						}else {
							this.fileList1.push({name:item.name,url:this.imgServer + '' + item.url})
						}
					}
				}
        console.log(this.fileList1);
        this.setFileList(this.fileList1)
      })
      this.search()
    },
    remove(id) {
      this.$confirm('删除维护记录, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				recordsRemove(id)
					.then(res => {
            this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
    },
    onSubmit(){
			this.$refs['formEl'].validate((valid) =>{
				if(valid){
          // 处理视频、图片 上传
					const list = []
					for (const key in this.fileList) {
            console.log(this.fileList[key],key,'submit');
						if (this.fileList[key].url.includes(this.imgServer)) {
							list.push({name:this.fileList[key].name.split(".")[0], url:this.recordsModel.photoList[key].url.replace(this.imgServer,"")})
						}else{
							list.push({name:this.fileList[key].name.split(".")[0], url:this.recordsModel.photoList[key].url})
						}
					}
          
					const list1 = []
					for (const key in this.fileList1) {
            console.log(this.fileList1[key]);
						if (this.fileList1[key].url.includes(this.imgServer)) {
							list1.push({name:this.fileList1[key].name.split(".")[0], url:this.recordsModel.videoList[key].url.replace(this.imgServer,"")})
						}else{
							list1.push({name:this.fileList1[key].name.split(".")[0], url:this.recordsModel.videoList[key].url})
						}
					}
          
					this.recordsModel.photoList = list
					this.recordsModel.videoList = list1
					if(this.secondDialog.title=='新增记录'){
            console.log('add');
						recordsPush(this.recordsModel)
						.then(res =>{
							this.$message.success(res.data.msg)
							this.$emit("search")
							this.secondDialog.show = false
      this.search()
						})
					}else{
            console.log('edit');
						recordsEdit(this.recordsModel)
						.then(res =>{
							this.$message.success(res.data.msg)
							this.$emit("search")
							this.secondDialog.show = false
      this.search()
						})
					}
				}
			})
		}
  },
  watch:{
    'secondDialog.show'(newValue) {
            if (!newValue) {
                this.recordsModel = {
                  devId: this.devId
                }
                this.fileList = []
                this.fileList1 = []
            }
        },
        dialogVisibleVideo(newValue){
          if (!newValue) {
            console.log(123123);
            this.videoUrl = ''
          }
        },
        "recordsModel.note"(newVal){
          if (newVal && newVal.length > 200) {
            this.recordsModel.note = String(newVal).slice(0,200);
          }
        }
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openRecords", (data) => {
        this.devId = data.id
        this.searchModel = {
          devId: this.devId
        }
        recordsList(this.searchModel).then(res => {
         this.dataList =  res.data.result.list
        })
        this.dialog.show = true;
        this.dialog.title = data.name+"--维护记录";
        // this.search()
      });
    });
  },
}
</script>
<style scoped lang="less">
div /deep/ .el-upload-list{
  display: flex;
  width:100%;
  flex-wrap: nowrap;
  >li,div{
    display: flex;
    flex-shrink: 0;
  }
}

div /deep/.picView .el-dialog__header{
	background-color: #fff;
	box-shadow: none;
}
div /deep/ .picView .el-dialog__close{
	color: #ccc;
}
.view_contoiner{
  height:570px;
  width: 100%;
  display:flex;
  justify-content:space-between;
  > .view_left{
    width: 28%;
    height: 100%;
    border: 1px solid #ccc;
    // background-color: pink;
     .view_left_son{
      line-height: 40px;
      border: 1px solid #ccc;
      margin: 3px;
      border-radius: 2px;
      padding-left: 5px;
      cursor: pointer;
    }
    .view_left_son:hover{
      background-color: #eee;
    }
  }
  > .view_right{
    flex: 1;
    height: 100%;
    // background-color: black;
    margin-left: 5px;
    border: 1px solid #ccc;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

.img-box:not(:nth-child(3n)){
  margin-right: calc(6% / 3);
}
.video-box:not(:nth-child(2n)){
  margin-right: calc(6% / 3);
}

.pic--video div /deep/ .el-upload-list {
  display: flex;
  width: 100%;
  flex-wrap: nowrap;
  > li,
  div {
    display: flex;
    flex-shrink: 0;
  }
}
</style>