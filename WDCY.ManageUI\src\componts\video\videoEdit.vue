<template>
	<el-dialog draggable width="50%" append-to-body destroy-on-close v-model="dialog.show" :before-close="close"
		:title="dialog.title">
		<div style='width: 0;height: 0;' id="container" ref="container"></div>
		<video ref="vd" id="video" controls style="width: 100%;height: 100%;"></video>
	</el-dialog>
</template>

<script>
	import Hls from 'hls.js'
	import mitt from "@/utils/mitt"
	export default {
	data() {
		return {
			dialog:{
				show: false,
				title: ""
			},
			hls: null,
			vd: null,
			jessibuca: null,
			err: "",
			playUrl:'',
			urlType: ''

		}
	},
	methods: {
		close() {
			if (this.hls) this.hls.destroy()
			
			this.dialog.show = false
			if (this.jessibuca) this.jessibuca.destroy()
		},
		async init(options) {
			var _this = this;
			options = options || {};
			this.jessibuca = new window.Jessibuca(
				Object.assign(
					{
						
						container: this.$refs.container,
						videoBuffer: Number(1), // 缓存时长
						isResize: false,
						useWCS: this.useWCS,
						useMSE: this.useMSE,
						text: "",
						// background: "bg.jpg",
						loadingText: "疯狂加载中...",
						// hasAudio:false,
						debug: true,
						supportDblclickFullscreen: true,
						showBandwidth: this.showBandwidth, // 显示网速
						operateBtns: {
							fullscreen: this.showOperateBtns,
							screenshot: this.showOperateBtns,
							play: this.showOperateBtns,
							audio: this.showOperateBtns,
						},
						vod: this.vod,
						forceNoOffscreen: !this.useOffscreen,
						isNotMute: true,
						timeout: 10,
						decoder: '/lib/gbt/decoder.js',


						// 打包路径
						// decoder: '/lib/gbt/decoder.js'

						// decoder:'../../../public/lib/gbt/decoder.js'
					},
					options
				)
			);
			
			this.jessibuca.on("pause", function () {
				console.log("on pause");
				_this.playing = false;
			});
			this.jessibuca.on("play", function () {
				console.log("on play");
				_this.playing = true;
			});
			this.jessibuca.on("mute", function (msg) {
				console.log("on mute", msg);
				_this.quieting = msg;
			});
			this.jessibuca.on("performance", function (performance) {
				var show = "卡顿";
				if (performance === 2) {
				show = "非常流畅";
				} else if (performance === 1) {
				show = "流畅";
				}
				_this.performance = show;
			});

			this.jessibuca.on("play", () => {
				this.playing = true;
				this.loaded = true;
				this.quieting = this.jessibuca.isMute();
			});
		},
		play(playUrl) {
			this.jessibuca.play(playUrl);
		},
	},
	created() {
		
	},
	beforeDestroy() {
    },
	mounted(){
		var _this = this
		mitt.on('openVideoEdit', (data) => {
			console.log(data);
			this.playUrl = data.videoSrc.url
			window.onerror = (msg) => (_this.err = msg);
			
			if (data.urlType == 'hls') {
				_this.urlType = "hls"
			}else {
				_this.urlType = data.urlType
			}
			console.log(_this.urlType);
			setTimeout(function() {
				_this.init()
				console.log(data.urlType,data);


				var video = _this.$refs.vd
				if (Hls.isSupported()) {
					_this.hls = new Hls()
					_this.hls.loadSource(data.videoSrc.url)
					_this.hls.attachMedia(video)
					_this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
						video.play()
					})
					_this.hls=_this.hls
				} else if (video.canPlayType('application/vnd.apple.mpegurl')) {
					video.src = data.videoSrc
					video.play()
				}
			}, 500)

			_this.dialog.show = true
			_this.dialog.title = "视频监控("+data.name+')'
			// _this.dialog.title = "视频监控"


			setTimeout(() => {
					_this.play(this.playUrl)
			}, 1000);
		})

	
	

	},
	unmounted(){
		if(this.jessibuca) this.jessibuca.destroy();
	}
}
</script>


<!-- <script setup>
	import Hls from 'hls.js'
	import mitt from "@/utils/mitt"
	import videojs from "video.js"
	import {
		ref,
		reactive,
		onMounted,
		onUnmounted
	} from 'vue'
	const vd = ref(null)
	const dialog = reactive({
		show: false,
		title: ""
	})
	var hls = null;

	function close() {
		hls.destroy()
		dialog.show = false
	}
	onMounted(() => {
		mitt.on('openVideoEdit', (data) => {
			setTimeout(function() {
				var video = vd.value
				if (Hls.isSupported()) {
					hls = new Hls()
					hls.loadSource(data.videoSrc.url)
					hls.attachMedia(video)
					hls.on(Hls.Events.MANIFEST_PARSED, () => {
						video.play()
					})
					hls = hls
					console.log(hls);
				} else if (video.canPlayType('application/vnd.apple.mpegurl')) {
					video.src = data.videoSrc
					video.play()
				}
			}, 500)
			dialog.show = true
			dialog.title = "视频监控——"+data.name
		})
	})
</script> -->
