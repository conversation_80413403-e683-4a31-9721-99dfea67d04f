import request from '@/utils/request'

export const listGovernanceObject = (data) =>
	request({
		url: '/governanceObject/page',
		method: 'get',
		params: data
	})

export const getGovernanceObject = (id) =>
	request({
		url: '/governanceObject/'+id,
		method: 'get'
	})	

export const addGovernanceObject = (data) =>
	request({
		url: '/governanceObject',
		method: 'post',
		data: data
	})

export const editGovernanceObject = (data) =>
	request({
		url: '/governanceObject',
		method: 'put',
		data: data
	})	

export const deleteGovernanceObject = (id) =>
	request({
		url: '/governanceObject/'+id,
		method: 'delete'
	})		