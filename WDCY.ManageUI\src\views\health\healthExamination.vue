<template>
	<health-examination-edit :statusList="statusList" :categoryList="categoryList" :methodList="methodList" :hasRightList="hasRightList" @search="search" ></health-examination-edit>
	<!-- <examine-one-edit :statusList="statusList" :categoryList="categoryList" :methodList="methodList" :hasRightList="hasRightList" @search="search" ></examine-one-edit> -->
	<production-assert-create></production-assert-create>
	<!-- 用例组件 -->
	<health-examination-use-case-list :statusList="statusList"></health-examination-use-case-list>
	<init-scan :categoryList="categoryList" @search="search" ></init-scan>
	<el-row :gutter="20">
		<el-col :span="2">
			<el-input v-model="searchModel.funcName" @keydown.enter="search" placeholder="功能名称" clearable />
		</el-col>
		<el-col :span="2">
			<el-input v-model="searchModel.apiPath" @keydown.enter="search" placeholder="接口路径" clearable />
		</el-col>
		<el-col :span="2">
			<el-select style="width: 100%;" v-model="searchModel.funcCategory" placeholder="功能分类" clearable>
				<el-option v-for="item in categoryList" :key="item.nameEn" :label="item.nameCn"
					:value="item.nameEn"></el-option>
			</el-select>
		</el-col>
		<el-col :span="2">
			<el-select style="width: 100%;" v-model="searchModel.status" placeholder="状态" clearable>
				<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
					:value="item.nameEn"></el-option>
			</el-select>
		</el-col>
		<el-col :span="2">
			<el-select style="width: 100%;" v-model="searchModel.method" placeholder="请求方式" clearable>
				<el-option v-for="item in methodList" :key="item.nameEn" :label="item.nameCn"
					:value="item.nameEn"></el-option>
			</el-select>
		</el-col>
		<el-col :span="2">
			<el-select style="width: 100%;" v-model="searchModel.hasRight" placeholder="权限控制" clearable>
				<el-option v-for="item in hasRightList" :key="item.nameEn" :label="item.nameCn"
					:value="item.nameEn"></el-option>
			</el-select>
		</el-col>
		<el-col :span="1">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="7" :push="4">
			<el-button style="float: right;" type="primary" @click="add" v-if="hasPerm('sys:healthCheck:insert')">添 加</el-button>
			<el-button style="float: right;margin-right: 10px;" type="primary" @click="initScan">初始扫描</el-button>
			<el-button style="float: right;" type="primary" @click="productionAssert">生成断言</el-button>
			<el-button style="float: right;" type="primary" @click="batch">批量</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table row-key="id" stripe :data="regionalSystemList" border ref="multipleTable" height="calc(100vh - 300px)" style="width: 100%" @selection-change="handleSelectionChange">
        		<el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
				<el-table-column prop="funcName" align="left" label="功能名称" />
				<el-table-column prop="apiPath" align="left" label="接口路径"/>
				<el-table-column prop="funcCategory" width="130" align="center" label="功能分类">
                    <template #default="scope">
						<el-tag :type="getDictCss(categoryList, scope.row.funcCategory)">{{ formatDict(categoryList, scope.row.funcCategory) }}</el-tag>
                    </template>
                </el-table-column>
				<el-table-column prop="method" width="130" align="center" label="请求方式">
                    <template #default="scope">
						<el-tag :type="getDictCss(methodList, scope.row.method)">{{ formatDict(methodList, scope.row.method) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="hasRight" align="center" label="权限控制" width="88">
					<template #default="scope">
						<el-tag :type="getDictCss(hasRightList,scope.row.hasRight)">{{ formatDict(hasRightList, scope.row.hasRight) }}</el-tag>
					</template>
				</el-table-column>
                
				<!-- <el-table-column prop="checkTime" align="center" label="检测时间"/> -->
				<!-- <el-table-column prop="sex" align="center" label="性别" :formatter="formatSex" width="88" /> -->
				<!-- <el-table-column prop="status" align="center" :formatter="formatStatus" label="状态" /> -->

				<el-table-column prop="status" align="center" label="状态" width="100">
					<template #default="scope">
						<!-- <el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag> -->
						<el-switch :active-value="1" :inactive-value="0" v-model="scope.row.status" @change="editStatus(scope.row)" />
					</template>
				</el-table-column>
				<el-table-column prop="note" align="left" label="备注"/>
				<el-table-column prop="createTime" align="center" width="170" label="创建时间"/>
				<el-table-column prop="updateTime" align="center" width="170" label="修改时间"/>

				<el-table-column align="center" width="200" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="useCase(scope.row)">用例</el-button>
						<el-button type="text" size="default" @click="examineOne(scope.row)">检查</el-button>
						<el-button type="text" size="default" @click="edit(scope.row)" v-if="hasPerm('sys:healthCheck:put')">编辑</el-button>
						<el-button style="margin-right: 10px;" type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('sys:healthCheck:delete')">删除</el-button>
                    </template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
	<el-dialog draggable width="25%" v-model="dialog.show" destroy-on-close title="选择场景">
    	<el-form :rules="rules" ref="form" :model="batchModel" >
			<!-- <el-row> -->
				<el-col :span="24">
                    <el-form-item label="场景名称" prop="checkSceneId">
						<el-select style="width: 100%" v-model="batchModel.checkSceneId" placeholder="场景名称">
							<el-option v-for="item in sceneList" :key="item.id" :label="item.sceneName" :value="item.id"></el-option>
						</el-select>
                    </el-form-item>
            	</el-col>
			<!-- </el-row> -->
		</el-form>
		<el-row justify="center" style="padding:0;margin:0">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="batchSubmit">保 存
			</el-button>
		</el-row>
  </el-dialog>
</template>
<script>
import { listHealthExamina, deleteHealthExamina, getHealthExamina, editHealthExamina, toExaminaOne, getProductionAssert } from "@/api/healthCheck/healthExamination"
import { listAllScene, batchCheckCase, listTestCase } from "@/api/healthCheck/useCaseScenario"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import healthExaminationEdit from "@/componts/healthCheck/healthExaminationEdit.vue"
import examineOneEdit from "@/componts/healthCheck/examineOneEdit.vue"
import productionAssertCreate from "@/componts/healthCheck/productionAssertCreate.vue"
import initScan from "@/componts/healthCheck/initScan.vue"
import healthExaminationUseCaseList from "@/componts/healthCheck/healthExaminationUseCaseList.vue"

export default {
	components:{ healthExaminationEdit, examineOneEdit, initScan ,productionAssertCreate, healthExaminationUseCaseList },
	data() {
		return {
			searchModel: {},
			batchModel: {},
			dialog: {},
			regionalSystemList: [],
			hasRightList: [
                {
                    nameCn:"是",
                    nameEn:true,

                },{
                    nameCn:"否",
                    nameEn:false,

                }
            ],
			statusList: [],
			categoryList: [],
			methodList: [],
			total: 0,
			pageSize: 10,
			ids: [],
			sceneList:[]
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			listHealthExamina(this.searchModel)
			.then(res => {
				this.regionalSystemList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		// 批量操作
		handleSelectionChange(val) {
			let list = []
			for (let i = 0; i < val.length; i++) {
				list.push({
					funcName: val[i].funcName,
					expandParams: val[i].expandParams,
					sort: i+1,
					id: val[i].id,
					note: val[i].note
				})
			}
			this.ids = list
		},
		batch(){
			if (this.ids.length != 0) {
				this.dialog.show = true
			}
		},
		// 场景提交
		batchSubmit(){
			console.log(this.batchModel);
			this.batchModel.funcBoList = this.ids
			batchCheckCase(this.batchModel).then(res => {
				this.$message.success(res.data.msg)
				this.ids = []
				this.dialog.show = false
			})
		},
		async editStatus(data) {
			let searchModel = {
				id: data.id,
				status: data.status
			}
			editHealthExamina(searchModel)
		},
		edit(row){
            getHealthExamina(row.id).then(res => {
				mitt.emit('openRegionalSystemEdit',res.data.result)
			})
		},
		// 单个数据检查
		examineOne(row){
			this.$confirm('正在执行检查, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					toExaminaOne(row.id)
					.then(res =>{
						mitt.emit('openExamineOneEdit',res.data.result)
						this.search()
					})
				}).catch(()=>{})
		},
		useCase(row){
			const sendData = {
				sysFuncId: row.id
			}
			listTestCase(sendData).then( res => {
				/**
				 * {sysFuncId,
				 *  list,
				 *  total,
				 *  title,
				 *  expandParams
				 * }
				 */
				const data  = {...sendData,...{list: res.data.result.list},...{total:res.data.result.total},...{title:row.funcName,note:row.note},...{expandParams:row.expandParams}}
				console.log(data);
				mitt.emit('openUseCase',data)
			})
		},
		// 生成断言
		productionAssert(){
			mitt.emit('openProductionAssert')
		},
		deleted(id){
			this.$confirm('删除区域系统, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteHealthExamina(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		add() {
			mitt.emit('openRegionalSystemAdd',this.searchModel)
		},
		// 初始扫描
		initScan(){
			mitt.emit('openInitScan',this.categoryList)
		},
		async init(){
			try{
				// let status_res = await listDictByNameEn('user_status')
				// this.statusList = status_res.data.result
				let resStatus = await listDictByNameEn('func_status')
				this.statusList = resStatus.data.result
				let resCategory = await listDictByNameEn('func_category')
				this.categoryList = resCategory.data.result
				let resRequestMethod = await listDictByNameEn('request_method')
				this.methodList = resRequestMethod.data.result
				let res = await listHealthExamina(this.searchModel)
				this.regionalSystemList = res.data.result.list
				this.total = res.data.result.total
				let resSceneList = await listAllScene()
				this.sceneList = resSceneList.data.result

			}catch(err){
			}
		}
	},
	created() {
		mitt.off('openUserEdit')
		mitt.off('openUserRegister')
		mitt.on('refreshUserList',()=>{this.search()})
		this.init()
		// this.search()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	div /deep/ .el-tree-node__content{
		height: 35px;
	}
	div /deep/ .el-tree-node__content:hover{
		color: #fff;
		background-color: var(--el-color-primary-light-5);
	}

	div /deep/ .el-tree-node__expand-icon{
		font-size: 18px;
		margin-right: 15px;
	}
</style>