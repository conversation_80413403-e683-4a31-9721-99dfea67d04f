<template>
	<el-dialog draggable width="50%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="dataList" label-width="80px">
			<el-form-item label="插件名称" prop="pluginName">
				<el-input v-model="dataList.pluginName" placeholder="插件名称"></el-input>
			</el-form-item>

			<el-form-item label="插件代号" prop="pluginCode">
				<el-input v-model="dataList.pluginCode" placeholder="插件代号"></el-input>
			</el-form-item>

			<!-- multiple el-select属性：可多选 -->
			<el-form-item label="插件类型" prop="pluginType">
				<el-select style="width: 30%;" v-model="dataList.pluginType" placeholder="插件类型">
					<el-option v-for="item in pluginTypeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="插件状态" v-if="dialog.title !== '添加插件'" prop="status">
				<el-select style="width: 30%" v-model="dataList.status" placeholder="状态">
					<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
						:value="parseInt(item.nameEn)"></el-option>
				</el-select>
			</el-form-item>
          <el-form-item label="扫描路径" prop="packagePath">
            <el-input v-model="dataList.packagePath" placeholder="扫描路径"></el-input>
          </el-form-item>
          <el-form-item label="插件路径" prop="pluginPath">
            <el-input v-model="dataList.pluginPath" placeholder="插件路径"></el-input>
          </el-form-item>
          <el-form-item label="版本" prop="version">
            <el-input style="width:30%" v-model="dataList.version" placeholder="版本"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="note">
			<el-input
				v-model="dataList.note"
				maxlength="200"
				placeholder="请填写插件备注"
				show-word-limit
				type="textarea"
			/>
			</el-form-item>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交
			</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { addPlugIn, editPlugIn } from "@/api/admin/plugIn"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt";
export default {
	props: ["pluginTypeList", "statusList"],
	data() {
		return {
			loading: false,
			dataList: {},
			dialog: {},
		}
	},
	methods: {
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				this.dataList.communityId = String(localStorage.getItem("communityId"))
				if (valid) {
					if (this.dataList.id == 0) {
						addPlugIn(this.dataList)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						editPlugIn(this.dataList)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		},
		async init(){
		}
	},
	mounted() {
		this.init()
		this.$nextTick(function () {
			mitt.on('openPlugInEdit', (res) => {
        console.log(res,'1212');
				this.dataList = res
				this.dialog.show = true
				this.dialog.title = "修改信息"
			})
			mitt.on('openPlugInAdd', () => {
				this.dataList = {
					id: 0
				}
				this.startToEndTime = []
				this.dialog.show = true
				this.dialog.title = "添加插件"
			})
		})
	}
}
</script>
