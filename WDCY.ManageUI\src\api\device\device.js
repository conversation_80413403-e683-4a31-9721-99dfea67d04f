import request from '@/utils/request'

export const deviceInfoList = (data) =>
	request({
		url: '/deviceInfo',
		method: 'get',
		params: data
	})
export const deviceInfoAllList = (data) =>
	request({
		url: '/deviceInfo/queryAll',
		method: 'get',
		params: data
	})
export const deviceInfoPictureByOpenId = (data) =>
	request({
		url: '/deviceInfo/picture',
		method: 'get',
		params: data
	})
export const deviceInfoExport = (data) =>
	request({
		url: '/deviceInfo/export',
		method: 'post',
		data: data
	})
//设备导入
export const importData = (data) =>
	request({
		url: '/deviceInfo/importData',
		method: 'post',
		data: data
	})

export const deviceInfoAll2List = (data) =>
	request({
		url: '/deviceInfo/queryAll2',
		method: 'get',
		params: data
	})
export const getDeviceInfo = (id) =>
	request({
		url: '/deviceInfo/' + id,
		method: 'get',
	})
export const deviceInfoListAdd = (data) =>
	request({
		url: '/deviceInfo',
		method: 'post',
		data: data
	})
export const deviceInfoListEdit = (data) =>
	request({
		url: '/deviceInfo',
		method: 'put',
		data: data
	})
export const deviceInfoListDelete = (id) =>
	request({
		url: '/deviceInfo',
		method: 'delete',
		params: {
			id: id
		}
	})

// 编辑地理围栏
export const updateByPolyCoords = (data) =>
	request({
		url: '/deviceInfo/updateByPolyCoords',
		method: 'put',
		data: data
	})
	
export const deviceTypeAllList = (data) =>
	request({
		url: '/deviceType/queryList',
		method: 'get',
		params: data
	})

export const deviceTypeList = (data) =>
	request({
		url: '/deviceType',
		method: 'get',
		params: data
	})
export const deviceTypeAll2List = (data) =>
	request({
		url: '/deviceType/queryAll2',
		method: 'get',
		params: data
	})

export const getDeviceType = (id) =>
	request({
		url: '/deviceType/' + id,
		method: 'get',
	})
export const deviceTypeListAdd = (data) =>
	request({
		url: '/deviceType',
		method: 'post',
		data: data
	})
export const openPeopleDoor = (data) =>
	request({
		url: '/deviceRegulate/openDoor',
		method: 'post',
		data: data
	})
export const openVehicleDoor = (data) =>
	request({
		url: '/deviceRegulate/openBrake',
		method: 'post',
		data: data
	})
export const openDoorInfo = (data) =>
	request({
		url: '/deviceRegulate/lookDoorStatus',
		method: 'post',
		data: data
	})

export const deviceTypeListEdit = (data) =>
	request({
		url: '/deviceType',
		method: 'put',
		data: data
	})
export const deviceTypeListDelete = (id) =>
	request({
		url: '/deviceType',
		method: 'delete',
		params: {
			id: id
		}
	})
export const deviceInfoByIdsList = (data) =>
	request({
		url: '/deviceInfo/queryByIds',
		method: 'get',
		params: data
	})

export const queryMonitorByDevNo = (data) =>
	request({
		url: '/monitor/info',
		method: 'get',
		params: data
	})
export const queryVideoConfig = () =>
	request({
		url: '/community/queryVideoConfig',
		method: 'get',
		params: {
			communityId: String(localStorage.getItem("communityId"))
		}
	})
export const deviceLink = (data) =>
	request({
		url: '/deviceLink',
		method: 'get',
		params: data
	})
export const getDeviceTypeList = (data) =>
	request({
		url: '/deviceInfo/getDeviceTypeList',
		method: 'get',
		params: data
	})
export const deviceLinkAdd = (data) =>
	request({
		url: '/deviceLink',
		method: 'post',
		data: data
	})
export const deviceLinkEdit = (data) =>
	request({
		url: '/deviceLink',
		method: 'put',
		data: data
	})
export const deviceLinkDelete = (id) =>
	request({
		url: '/deviceLink',
		method: 'delete',
		params: {
			id: id
		}
	})
//维护记录分页
export const recordsList = (data) =>
	request({
		url: '/dataDeviceMaintenance',
		method: 'get',
		params: data
	})
//维护记录新增
export const recordsPush = (data) =>
	request({
		url: '/dataDeviceMaintenance',
		method: 'post',
		data: data
	})
//维护记录修改
export const recordsEdit = (data) =>
	request({
		url: '/dataDeviceMaintenance',
		method: 'put',
		data: data
	})
//主键查询维护记录
export const recordsQuery = (id) =>
	request({
		url: '/dataDeviceMaintenance/' + id,
		method: 'get'
	})
//维护记录删除
export const recordsRemove = (id) =>
	request({
		url: '/dataDeviceMaintenance/' + id,
		method: 'delete'
	})

