import request from '@/utils/request'

export const listMenuByRoleId = (id) =>
	request({
		url: '/roleMenu/'+id,
		method: 'get'
	})
export const addRoleMenu = (data) =>
	request({
		url: '/roleMenu',
		method: 'post',
		data: data
	})
export const editRoleMenu = (data) =>
	request({
		url: '/roleMenu',
		method: 'put',
		data: data
	})
export const deleteRoleMenu = (id) =>
	request({
		url: '/roleMenu',
		method: 'delete',
		params: {
			id: id
		}
	})
