import { ref, toRefs } from 'vue'
// import { getDicts } from '@/api/system/dict/data'
import { listDictByNameEn } from "@/api/admin/dict";

/**
 * 获取字典数据
 * @param  {...any} args 字典类型列表
 * @returns 字典列表
 */
export function useDict(...args) {
  const res = ref({});
  return (() => {
    args.forEach((d, index) => {
      res.value[d] = [];
      listDictByNameEn(d).then(resp => {
        //  
        // res.value[d] = resp.data.result.map(p => ({ label: p.dictLabel, value: p.dictValue, elTagType: p.listClass }))
        res.value[d] = resp.data.result.map(p => ({
          label: p.nameCn,
          value: p.nameEn,
          css: p.cssClass
        }))
      })
    })
    return toRefs(res.value);
  })()
}

/**
 * 回显数据字典,逗号拼接多项
 * @param {*} datas 字典列表
 * @param {*} value 字典键值
 * @returns 字典名称
 */
// export function selectDictLabel(datas, value) {
//   var actions = [];
//   Object.keys(datas).some((key) => {
//     if (datas[key].value == ('' + value)) {
//       actions.push(datas[key].label);
//       return true;
//     }
//   })
//   return actions.join(',');
// }
export function selectDictLabel(datas, value) {
  var actions = [];
  datas.value.some((key) => {
    console.log(key);
    if (key.value == value) {
      actions.push(key.label);
      return true;
    }
  })
  return actions.join(',');
}

/**
 * 获取字典样式
 * @param {*} dicList 字典项列表
 * @param {*} cellValue 字典值
 */
export function getDictCss(dicList, cellValue) {
  if(!dicList) return ''
  let item = dicList.find(m => m.nameEn == cellValue)
  let result = item ? item.cssClass : ''
  return result
}

/**
 * 回显字典名称
 * @param {*} dicList 字典项列表
 * @param {*} cellValue 字典值
 * @returns 字典名称
 */
export function formatDict(dicList, cellValue) {
  if(!dicList) return ''
  let item = dicList.find(m => m.nameEn == cellValue)
  let result = item ? item.nameCn : ''
  return result
}