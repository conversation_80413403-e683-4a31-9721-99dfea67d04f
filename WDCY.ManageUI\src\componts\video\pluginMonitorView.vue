<template>
  <el-dialog v-model="dialog.show" :before-close="close" width="1000px" title="视频播放">
    <div class="monitor-view-container" v-if="dialog.show">
      <title-box>
        <div class="title">
          <div class="tsgz-right-people-monitor-title-image" alt="" />
          <!-- <span>视频画面</span> -->
        </div>
      </title-box>
      <div class="afjk-right-monitor-play-container">
        <div v-if="dialogVisible" class="plugin-tips">
          <span>{{ downloadText }}</span>
          <div></div>
          <a @click="handleDownloadExe" style="cursor: pointer;">下载视频播放插件</a>
        </div>
        <div class="video-view" id="playerPreview" ref="playerPreview"></div>
      </div>
    </div>

  </el-dialog>
</template>

<script>
import mitt from "@/utils/mitt"
import initPlugin from "@/componts/hkVideo/lib/initPlugin";

export default {
  data() {
    return {
      dialog:{show:false},
      previewArray: [null, null, null, null],

      argument: {
        appkey: "", //API网关提供的appkey
        secret: "", //API网关提供的secret
        ip: "", //API网关IP地址
        windId: 1, //当前窗口号
      },
      cameraIndexCode: "c78bf2dba3204b42988819d49a980b49",

      curWindIndex: 1, //当前选中窗口号，从1开始
      oWebControl: null, //插件实例

      iWidth: 958,
      playback_date: "", // 录像回放时间
      playbackStartTime: "", // 录像回放时间
      playbackEndTime: "", // 录像回放时间

      options: {
        appkey: "", //API网关提供的appkey
        secret: "", //API网关提供的secret
        ip: "", //API网关IP地址
        playMode: 0, //播放模式（决定显示预览还是回放界面）
        port: 443, //端口
        snapDir: "D:\\SnapDir", //抓图存储路径
        videoDir: "D:\\VideoDir", //紧急录像或录像剪辑存储路径
        layout: "1x1", //布局
        enableHTTPS: 1, //是否启用HTTPS协议
        encryptedFields: "secret", //加密字段
        showToolbar: 1,
        showSmart: 1,
        buttonIDs: "0,16,256,257,258,259,260,512,513,514,515,516,517,768,769", //自定义工具条按钮
      },
      rate: 0, //组件宽度与窗口宽度比例
      isInitComolete: false, //第一次初始化是否完成
      dialogVisible: false,

      //未安装插件时候是否显示插件下载对话框提示用户下载功能
      downloadDialog: true,
      //海康插件下载url
      downloadUrl:
        import.meta.env.VITE_BASE_API + "/assets/WebPlugin/VideoWebPlugin.exe",
      downloadText:
        "插件启动失败，请检查插件是否安装,如果未安装请点击下载安装,安装后刷新页面",
      autoResize: true,
      playMode: 0,
    };
  },
  created() {
    mitt.on("openPluginVideo", (data) => {
      // this.treeCheckChange(obj);
      this.dialog.show = true
      this.init(data.res);
      this.cameraIndexCode = data.devNo
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.handleWindEvent();
    });
  },
  methods: {
    //窗口事件
    handleWindEvent() {
      // 监听resize事件，使插件窗口尺寸跟随DIV窗口变化
      if (this.autoResize) {
        window.addEventListener('resize', () => {
          if (this.oWebControl) {
            this.iWidth =
              1334 *
              (this.$store.state.windowScale == 0
                ? 1
                : this.$store.state.windowScale);
            this.iHeight = this.iWidth * 0.5625;

            this.oWebControl.JS_Resize(this.iWidth, this.iHeight);
            // this.setWndCover();
          }
        });
      }
    },

    //初始化插件
    init(obj) {
      var that = this;

      const videoConfig = obj

      let argument = {
        ...this.options,
        ...this.argument,
      };
      argument.appkey = this.$Base64.decode(videoConfig.appKey);
      argument.secret = this.$Base64.decode(
        this.$Base64.decode(videoConfig.secret)
      );
      argument.ip = videoConfig.ip;
      argument.port = parseInt(videoConfig.port);

      if (this.playMode > -1 && this.playMode < 2) {
        argument.playMode = this.playMode;
      }
      // eslint-disable-next-line no-undef
      // this.iWidth =
      //   1334 *
      //   (this.$store.state.windowScale == 0
      //     ? 1
      //     : this.$store.state.windowScale);

      this.iHeight = this.iWidth * 0.5625;

      console.log(argument);
      this.oWebControl = initPlugin(
        "playerPreview",
        // this.iWidth,
        // this.iHeight,
        this.iWidth,
        this.iHeight,
        argument,
        this.cbIntegrationCallBack(),
        () => {
          that.isInitComolete = true;

          setTimeout(() => {
              that.startPreview(that.cameraIndexCode, argument);
            this.setResize(this.iWidth, this.iHeight);
          }, 500);
        },
        () => {
          if (this.downloadDialog) {
            this.oWebControl = null;
            this.dialogVisible = true;
          }
        }
      );
    },
    //设置窗口尺寸
    setResize(width, height) {
      this.oWebControl.JS_Resize(width, height);
      // this.setWndCover();
    },

    //推送消息回调
    cbIntegrationCallBack(oData) {
      if (oData?.responseMsg?.type) {
        console.log(oData,'odata');
        let data = oData.responseMsg;

        // console.log(oData);
        switch (data.type) {
          //窗口号改变
          case 1:
            if (this.curWindIndex !== data.msg.wndId) {
              this.curWindIndex = data.msg.wndId;
              // this.$emit("windowChange", this.curWindIndex);
            }
            break;
          //布局改变
          case 6:
            // this.$emit("layoutChange", data.msg);
            break;
        }
      }
    },

    /** 预览
     * @param {*} cameraIndexCode :获取输入的监控点编号值，必填
     * @param {*} argument:api参数,
     * argument属性cameraIndexCode优先级高于函数参数cameraIndexCode
     */
    startPreview(cameraIndexCode, argument = {}) {
      if (this.oWebControl) {
        console.log('video1',cameraIndexCode,argument);
        let params = {
          cameraIndexCode: cameraIndexCode, //监控点编号
          streamMode: 0, //主子码流标识
          transMode: 1, //传输协议
          gpuMode: 1, //是否开启GPU硬解
          wndId: -1,
        };
        this.oWebControl
          .JS_RequestInterface({
            funcName: "startPreview",
            argument: JSON.stringify({
              ...params,
              ...argument,
            }),
          })
          .then(() => {
            console.log("startPreview");
          });
      }
    },

    //停止所有预览
    stopAllPreview() {
      this.oWebControl.JS_RequestInterface({
        funcName: "stopAllPreview",
      });
    },

    // 监控被选中或取消
    treeCheckChange(obj) {
      this.$emit("setCheckedKeysPl", keys); // 点击响应
    },

    // 监控预览播放
    previewPlay: function (index) {
      const dict = this.previewArray[index];

      // param.code = dict.code;
      setTimeout(() => {
        this.startPreview(dict.code);
      }, 500);
    },

    //下载安装包
    handleDownloadExe() {
      this.dialogVisible = false;
      if (this.downloadUrl) {
        this.downloadFile(this.downloadUrl);
      } else {
        // console.log("下载地址为空");
      }
      this.$emit("clickDownload");
    },

    //下载文件
    downloadFile(url, fileName = "") {
      const a = document.createElement("a");
      a.style.display = "none";
      if (fileName) {
        a.setAttribute("download", fileName);
      }
      a.setAttribute("href", url);
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(a.href);
      document.body.removeChild(a);
    },
    close(){
      if (this.oWebControl != null) {
        this.oWebControl.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
        this.oWebControl.JS_Disconnect().then(
          () => {
            // 断开与插件服务连接成功
      this.dialog.show = false
          },
          () => {
            // 断开与插件服务连接失败
          }
        );
      }
      this.dialogVisible = false;
      this.oWebControl = null;
    }
  },
  watch: {
    // "dialog.show"(newV,oldV){
    //   if (!newV) {
    //     this.stopAllPreview()
    //     this.oWebControl.JS_Resize(0, 0);
    //     this.oWebControl.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
    //     this.oWebControl.JS_Disconnect().then(
    //       () => {
    //         // 断开与插件服务连接成功
    //       },
    //       () => {
    //         // 断开与插件服务连接失败
    //       }
    //     );
    //     this.dialogVisible = false;
    //     this.oWebControl = null;
    //   }
    // }
  },
};
</script>

<style scoped lang="less">
.monitor-view-container {
  width: 100%;
}
div /deep/ .el-dialog__body{
  height: 540px!important;
}

.video-view {
  width: 958px;
  height: 540px;

  // border: 1px solid seagreen;
  display: flex;
  position: relative;

  z-index: 99;
  overflow: hidden;
}
</style>
