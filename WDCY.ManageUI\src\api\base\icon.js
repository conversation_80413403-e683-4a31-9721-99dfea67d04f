import request from '@/utils/request'

export const listIcon = (data) =>
	request({
		url: '/icoInfo/pageList',
		method: 'get',
		params: data
	})
export const searchListIcon = (data) =>
	request({
		url: '/icoInfo/list',
		method: 'get',
		params: data
	})
export const getIcon = (id) =>
	request({
		url: '/icoInfo/'+id,
		method: 'get',
	})
export const addIcon = (data) =>
	request({
		url: '/icoInfo',
		method: 'post',
		data: data
	})
export const editIcon = (data) =>
	request({
		url: '/icoInfo',
		method: 'put',
		data: data
	})
export const deleteIcon = (id) =>
	request({
		url: '/icoInfo',
		method: 'delete',
		params: {
			id: id
		}
	})
