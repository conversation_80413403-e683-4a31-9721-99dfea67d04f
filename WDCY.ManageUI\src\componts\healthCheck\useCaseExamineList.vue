<template>
  <health-examination-useCase-edit
    @search="search"
    :statusList="statusList"
  ></health-examination-useCase-edit>
  <el-dialog
    draggable
    top="13vh"
    width="70%"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-row :gutter="20">
      <el-col :span="24">
        <el-table :data="examineList" border style="width: 100%">
          <el-table-column
            prop="funcName"
            show-overflow-tooltip
            width="120"
            align="center"
            label="接口名称"
          />
          <el-table-column
            prop="apiPath"
            show-overflow-tooltip
            width="280"
            align="left"
            label="接口路径"
          />
          <el-table-column
            prop="funcCategory"
            width="130"
            align="center"
            label="功能分类"
          >
            <template #default="scope">
              <el-tag
                :type="getDictCss(categoryList, scope.row.funcCategory)"
                >{{ formatDict(categoryList, scope.row.funcCategory) }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="funcVer"
            show-overflow-tooltip
            width="120"
            align="center"
            label="接口版本"
          />
          <el-table-column
            prop="hasRight"
            show-overflow-tooltip
            width="88"
            align="center"
            label="权限控制"
          >
            <template #default="scope">
              {{ scope.row.hasRight ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column prop="status" align="center" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="getDictCss(statusList, scope.row.status)">{{
                formatDict(statusList, scope.row.status)
              }}</el-tag>
              <!-- <el-switch :active-value="1" :inactive-value="0" v-model="scope.row.status" @change="editStatus(scope.row)" /> -->
            </template>
          </el-table-column>
          <el-table-column
            prop="checkTime"
            align="center"
            width="170"
            label="检查时间"
          />
          <el-table-column
            prop="note"
            show-overflow-tooltip
            align="center"
            label="备注"
          />

          <el-table-column align="center" width="100" label="操作">
            <template #default="scope">
              <el-button type="text" size="default" @click="view(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </el-dialog>
  <el-dialog
    draggable
    top="3vh"
    width="40%"
    v-model="viewDialog.show"
    :title="viewDialog.title"
  >
    <el-row>
      <el-col :span="24">
        <el-form-item label="扩展参数" prop="expandParams">
          <JsonEditorVue
            language="cn"
            class="editor"
            :modelValue="jsonVal"
            @update:modelValue="changeJson"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script>
import { listDictByNameEn } from "@/api/admin/dict";
import JsonEditorVue from "json-editor-vue3";
import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict";
import {
  listTestCase,
  getTestCase,
  addTestCase,
  editTestCase,
  deleteTestCase,
} from "@/api/healthCheck/useCaseScenario";
import healthExaminationUseCaseEdit from "@/componts/healthCheck/healthExaminationUseCaseEdit.vue";
export default {
  props: ["statusList"],
  components: { healthExaminationUseCaseEdit, JsonEditorVue },
  data() {
    return {
		jsonVal: {},
      searchModel: {
        name: "",
      },
	  
      examineList: [],
      categoryList: [],
      sysFuncId: "",
      imgServer: import.meta.env.VITE_BASE_API,
      dialog: {
        show: false,
        title: "",
      },
      viewDialog: {
        show: false,
        title: "",
      },
      expandParams: {},
    };
  },
  methods: {
	changeJson(json) {
		this.jsonVal = json;
	},
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    formatThirdSystem(list, value) {
      let result = "";
      list.forEach((item) => {
        if (item.id == value) {
          result = item.sysName;
        }
      });
      return result;
    },
    view(row) {
      this.viewDialog.show = true;
      this.viewDialog.title = row.funcName;
	  this.jsonVal = JSON.parse(row.expandParams);
    },
    search() {
      this.searchModel.sysFuncId = this.sysFuncId;
      listTestCase(this.searchModel).then((res) => {
        this.examineList = res.data.result.list;
        this.total = res.data.result.total;
      });
    },
    formatStatus(row, column, cellValue, index) {
      let result = "";
      for (let item of this.statusList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
        }
      }
      return result;
    },
    async init() {
      let resCategory = await listDictByNameEn("func_category");
      this.categoryList = resCategory.data.result;
    },
  },
  created() {
    this.init();
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openExamineEdit", (data) => {
        this.examineList = data;
        this.dialog.show = true;
        this.dialog.title = "检查结果";
      });
    });
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
.editor {
  width: 805px;
  height: 350px;
}
</style>
