<template>
  <governance-responsibility-edit
    @searchTraceResponsibility="searchTraceResponsibility"
    @searchTraceResponsibility1="searchTraceResponsibility1"
    key="1"
  ></governance-responsibility-edit>
  <div>
    <el-dialog
      draggable
      top="3vh"
      width="1000px"
      v-loading="loading"
      v-model="dialog.show"
      :title="dialog.title"
    >
      <el-form
        :rules="rules1"
        ref="form"
        :model="warnEventSourceModel"
        label-width="120px"
        style="background: #f1f4ff"
      >
        <el-row
          :gutter="20"
          style="margin: 0px; display: flex; justify-content: space-between"
        >
          <el-col :span="11.5">
            <el-date-picker
              v-model="searchModel.governanceStartTime"
              type="date"
              placeholder="选择开始日期"
              value-format="YYYY-MM-DD"
              :size="size"
              style="margin-right: 10px"
            />
            <el-date-picker
              style="margin-right: 10px"
              v-model="searchModel.governanceEndTime"
              type="date"
              placeholder="选择结束日期"
              value-format="YYYY-MM-DD"
              :size="size"
            />
            <el-input
              style="width: 170px"
              v-model="searchModel.keyword"
              @keydown.enter="search"
              placeholder="相关描述"
              clearable
            />
          </el-col>
          <div style="flex: 1"></div>
          <el-col :span="3">
            <el-button
              style="margin-left: 10px; width: 100%"
              type="primary"
              @click="search"
              >搜 索</el-button
            >
          </el-col>
          <el-col :span="3">
            <el-button
              style="width: 100%"
              type="primary"
              @click="add"
              :disabled="governanceEventStatus"
              >新 增</el-button
            >
          </el-col>
        </el-row>
        <el-row style="height: 500px; margin-top: 10px">
          <div style="margin: 0 20px">
            <span
              style="
                height: 15px;
                width: 5px;
                background: #4b96ff;
                display: inline-block;
                margin-right: 5px;
              "
            ></span
            >记录
          </div>
          <el-col style="margin-top: 10px; height: calc(100%); overflow: auto">
            <div
              v-for="(activity, index) in dataList"
              style="padding-left: 30px"
              :key="index"
            >
              <div
                style="
                  line-height: 32px;
                  border-left: 2px solid #e0e4ea;
                  padding: 0 15px;
                "
              >
                <div
                  style="
                    height: 10px;
                    width: 10px;
                    background: #568efe;
                    border-radius: 5px;
                    position: relative;
                    right: 21px;
                    top: 21px;
                  "
                >
                  <div
                    style="
                      height: 12px;
                      width: 12px;
                      border: 1px solid #568efe;
                      border-radius: 6px;
                      position: relative;
                      left: -2px;
                      top: -2px;
                    "
                  ></div>
                </div>
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    line-height: 32px;
                    background: #e6edff;
                    padding: 0 15px;
                  "
                >
                  <div>{{ activity.governanceTime }}</div>
                  <div>
                    <el-button
                      type="text"
                      size="default"
                      @click="edit(activity.id)"
                      :disabled="governanceEventStatus"
                      >编辑</el-button
                    >
                    <el-button
                      type="text"
                      size="default"
                      @click="remove(activity.id)"
                      :disabled="governanceEventStatus"
                      >移除</el-button
                    >
                  </div>
                </div>
                <div style="background: #f5f5f5; padding: 15px 15px 10px">
                  <div style="display: flex">
                    <div style="width: 70px">经办人员:</div>

                    <div
                      style="
                        flex: 1;
                        background: #fff;
                        border-radius: 5px;
                        padding: 0 15px;
                        display: flex;
                        align-items: center;
                      "
                    >
                      <el-tooltip
                        placement="top"
                        v-for="(
                          item, index
                        ) in activity.governanceResponsibilityVoList"
                        :key="index"
                      >
                        <template #content>
                          <div style="display: flex; flex-direction: column">
                            <span v-if="item.phone"
                              >电 话：{{ item.phone }}</span
                            >
                            <span v-if="item.dutyNote"
                              >职 责：{{ item.dutyNote }}</span
                            >
                            <span v-if="item.evaluation"
                              >评 语：{{ item.evaluation }}</span
                            >
                          </div>
                        </template>
                        <div style="position: relative;margin-right:5px;">
                          <div
                            v-if="item.evaluation"
                            style="
                              width: 5px;
                              height: 5px;
                              background: red;
                              position: absolute;
                              border-radius: 5px;
                              top: 5px;
                              right: 5px;
                            "
                          ></div>
                          <el-button
                            @click="editEvaluationFunc(item)"
                            v-if="hasPerm('base:governanceEvent:comment')"
                            >{{ item.name }}</el-button
                          >
                          <el-button v-else>{{ item.name }}</el-button>
                        </div>
                      </el-tooltip>
                      <!-- <span v-for="item in activity.governanceResponsibilityVoList">{{item.name}} {{item.phone}}{{item.evaluation}}</span> -->
                    </div>
                  </div>
                </div>
                <div style="background: #f5f5f5; padding: 0 15px 10px">
                  <div style="display: flex">
                    <div style="width: 70px">相关描述:</div>
                    <div
                      style="
                        flex: 1;
                        background: #fff;
                        border-radius: 5px;
                        padding: 0 15px;
                      "
                    >
                      {{ activity.note }}
                    </div>
                  </div>
                </div>
                <div style="display: flex; max-height: 300px">
                  <div
                    style="
                      background: #f5f5f5;
                      padding: 0 15px 10px;
                      width: 60%;
                    "
                  >
                    <div
                      style="display: flex"
                      v-if="JSON.parse(activity.extraData).photoList.length"
                    >
                      <div style="width: 70px">相关图片:</div>
                      <!-- <div style="flex:1;background:#fff;border-radius:5px;padding:0 15px">{{JSON.parse(activity.extraData).photoList.url}}</div> -->
                      <div
                        style="
                          display: flex;
                          flex-wrap: wrap;
                          overflow: auto;
                          width: 100%;
                          max-height: 300px;
                          padding-left: 15px;
                        "
                      >
                        <div
                          class="img-box"
                          v-for="(item, index) in JSON.parse(activity.extraData)
                            .photoList"
                          :key="index"
                          style="width: 32%"
                        >
                          <el-image
                            :preview-src-list="
                              formatPreviewList(
                                JSON.parse(activity.extraData).photoList
                              )
                            "
                            :initial-index="index"
                            :src="imgServer + item.url"
                            style="height: 120px"
                          ></el-image>
                          <div
                            style="
                              position: relative;
                              bottom: 13px;
                              white-space: nowrap;
                              overflow: hidden;
                              text-overflow: ellipsis;
                            "
                          >
                            {{ item.name }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style="width: 40%; max-height: 300px">
                    <div
                      style="
                        background: #f5f5f5;
                        padding-right: 15px;
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                      "
                    >
                      <div
                        style="padding-bottom: 5px"
                        v-if="JSON.parse(activity.extraData).videoList.length"
                      >
                        <div style="width: 70px">相关视频:</div>
                        <div
                          style="
                            flex: 1;
                            background: #fff;
                            border-radius: 5px;
                            max-height: 118px;
                            min-height: 50px;
                            overflow: auto;
                            line-height: 28px;
                            padding-left: 10px;
                          "
                        >
                          <div
                            v-for="(item, index) in JSON.parse(
                              activity.extraData
                            ).videoList"
                            @click="openVideo(item)"
                            style="width: 75%; cursor: pointer"
                            :key="index"
                          >
                            <el-icon
                              size="18"
                              style="position: relative; top: 4px"
                              ><VideoCamera /></el-icon
                            >{{ item.name }}
                          </div>
                        </div>
                      </div>
                      <div
                        style="padding-bottom: 5px"
                        v-if="JSON.parse(activity.extraData).otherList.length"
                      >
                        <div style="width: 70px">相关文件:</div>
                        <div
                          style="
                            flex: 1;
                            background: #fff;
                            border-radius: 5px;
                            max-height: 118px;
                            min-height: 50px;
                            overflow: auto;
                            line-height: 28px;
                            padding-left: 10px;
                          "
                        >
                          <div
                            v-for="(item, index) in JSON.parse(
                              activity.extraData
                            ).otherList"
                            @click="openFile(item)"
                            style="width: 75%; cursor: pointer"
                            :key="index"
                          >
                            <el-icon
                              size="18"
                              style="position: relative; top: 4px"
                              ><Document /></el-icon
                            >{{ item.name }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <el-row justify="center">
        <el-button style="width: 100px" type="primary" @click="close"
          >关 闭</el-button
        >
      </el-row>
    </el-dialog>
  </div>
  <el-dialog
    draggable
    top="5vh"
    width="840px"
    v-loading="loading"
    v-model="secondDialog.show"
    :title="secondDialog.title"
  >
    <el-form
      ref="formEl"
      :rules="rules2"
      :model="recordsModel"
      label-width="80px"
      style="padding-right: 25px; height: 530px; overflow: auto"
    >
      <el-form-item label="经办人员" prop="governanceResponsibilityVoList">
        <el-col :span="24">
          <el-table
            stripe
            :data="recordsModel.governanceResponsibilityVoList"
            border
            style="width: 100%"
          >
            <el-table-column
              prop="name"
              show-overflow-tooltip
              align="center"
              label="姓名"
            />
            <el-table-column
              prop="certificateType"
              align="center"
              :formatter="formatCertificateType"
              label="证件类型"
            />
            <el-table-column
              prop="idCard"
              show-overflow-tooltip
              align="center"
              label="证件号"
            />
            <el-table-column
              prop="phone"
              show-overflow-tooltip
              align="center"
              label="电话"
            />
            <el-table-column
              prop="dutyNote"
              show-overflow-tooltip
              align="center"
              label="职责描述"
            />
            <!-- <el-table-column
              prop="evaluation"
              show-overflow-tooltip
              align="center"
              label="评语"
            /> -->
            <el-table-column align="center" label="操作">
              <template #default="scope">
                <el-button
                  type="text"
                  size="default"
                  @click="toEditResponsibility(scope.row, scope.$index)"
                  v-if="hasPerm('base:governanceEvent:edit')"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  size="default"
                  @click="removeResponsibility(scope.row)"
                  v-if="hasPerm('base:governanceEvent:delete')"
                  >移除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col
          v-if="hasPerm('base:governanceEvent:add')"
          :span="24"
          style="display: flex; justify-content: center; align-items: center"
        >
          <el-button
            style="font-size: 25px; width: 100%; border-radius: 0"
            size="default"
            @click="addResponsibility"
          >
            <el-icon :size="20">
              <plus></plus>
            </el-icon>
          </el-button>
        </el-col>
      </el-form-item>

      <el-form-item label="治理时间" prop="governanceTime">
        <el-date-picker
          v-model="recordsModel.governanceTime"
          type="datetime"
          placeholder="选择治理时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          :size="size"
          style="margin-right: 10px"
        />
      </el-form-item>
      <el-form-item label="治理描述" prop="note">
        <el-input
          v-model="recordsModel.note"
          maxlength="200"
          placeholder="请填写治理描述"
          show-word-limit
          type="textarea"
        />
      </el-form-item>
      <el-form-item label="相关图片">
        <el-scrollbar style="height: 198px" class="pic--video">
          <el-upload
            multiple
            accept="image/jpeg,image/jpg,image/png"
            v-model:file-list="fileList"
            action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :on-remove="deletePic"
            :http-request="loadingImg"
            :before-upload="beforeUploadImg"
          >
            <el-icon style="display: flex; flex-wrap: nowrap"><Plus /></el-icon>
          </el-upload>
          <div style="display: flex; position: relative; top: -10px">
            <div
              v-for="(item, idx) in fileList"
              :key="idx"
              style="
                width: 146px;
                display: flex;
                flex-shrink: 0;
                margin-right: 10px;
              "
            >
              <el-input
                v-model="item.name"
                style="width: 100%"
                placeholder="请输入文件名"
              ></el-input>
            </div>
          </div>
        </el-scrollbar>
        <el-dialog v-model="dialogVisible" class="picView">
          <img
            w-full
            style="width: 100%"
            :src="dialogImageUrl"
            alt="Preview Image"
          />
        </el-dialog>
      </el-form-item>

      <el-form-item label="相关视频">
        <el-scrollbar style="height: 158px" class="pic--video">
          <el-upload
            multiple
            accept= 'video/*'
            v-model:file-list="fileList1"
            :action="imgServer + '/file/upload'"
            list-type="picture-card"
            :on-preview="handleVideoCardPreview"
            :on-remove="deleteVideo"
            :http-request="loadingVideo"
            :before-upload="beforeUploadVideo"
            :on-success="getVideoCover"
          >
            <el-icon style="display: flex; flex-wrap: nowrap"><Plus /></el-icon>
          </el-upload>
          <div style="display: flex; position: relative; top: -10px">
            <div
              v-for="(item, idx) in fileList1"
              :key="idx"
              style="
                width: 146px;
                display: flex;
                flex-shrink: 0;
                margin-right: 10px;
              "
            >
              <!-- <video
          style="width: 100px; height: 100px"
          :src="item.url"
        ></video> -->
              <el-input
                v-model="item.name"
                style="width: 100%"
                placeholder="请输入文件名"
              ></el-input>
            </div>
          </div>
        </el-scrollbar>
        <el-dialog v-model="dialogVisibleVideo" class="picView videoView">
          <video
            :src="videoUrl"
            style="width: 100%; max-height: 520px"
            controls
          >
            <!-- <source v-if="videoUrl" :src="videoUrl" type="video/mp4">
                您的浏览器不支持视频标签。 -->
          </video>
        </el-dialog>
      </el-form-item>
      <el-form-item label="相关文件">
        <el-scrollbar style="height: 100px">
          <el-upload
            multiple
            accept=".pdf, .doc, .docx, .xls, .xlsx"
            v-model:file-list="fileList2"
            :action="imgServer + '/file/upload'"
            :on-preview="handleOtherCardPreview"
            :on-remove="deleteOther"
            :http-request="loadingOther"
            :before-upload="beforeUploadOther"
          >
            <el-button size="nornaml" type="primary">点击上传</el-button>
          </el-upload>
        </el-scrollbar>
      </el-form-item>
    </el-form>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
  <el-dialog
    draggable
    class="picView"
    top="5vh"
    width="1000px"
    v-loading="loading"
    v-model="videoShow"
    title="查看视频"
  >
    <div class="view_contoiner">
      <video
        :src="videoUrl"
        style="max-width: 100%; height: 100%"
        controls
      ></video>
    </div>
  </el-dialog>

  <el-dialog draggable width="30%" v-model="evaluationShow" title="领导点评">
    <el-row style="padding: 0">
      <el-col :span="24">
        <el-input
          v-model="evaluationModel.evaluation"
          rows="5"
          maxlength="200"
          placeholder="请简单描述职责"
          show-word-limit
          type="textarea"
        />
      </el-col>
    </el-row>
    <el-row style="padding: 0" justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="submit"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
  <el-dialog
    draggable
    class="ohterView"
    top="1vh"
    width="640px"
    v-loading="loading"
    v-model="viewOhter.show"
    :title="viewOhter.title"
  >
    <div class="view_ohter" style="max-height: 300px; overflow-y: auto">
      <ul>
        <li v-for="item in viewList" :key="item" @click="openFile(item)">
          <el-icon><Document /></el-icon> {{ item.name }}
        </li>
      </ul>
      <!-- <el-upload
          :src="imgServer+viewUrl"
          multiple
          v-model:file-list="viewList"
          :action="imgServer + '/file/upload'"
          :on-preview="handleOtherCardPreview"
          :on-remove="deleteOther"
          :http-request="loadingOther"
          :before-upload="beforeUploadOther"
          list-type="text" 
          :limit="10" 
          class="file-list">
        </el-upload> -->
    </div>
  </el-dialog>
</template>
<script>
import {
  governanceLogList,
  governanceLogAdd,
  governanceLogEdit,
  governanceLog,
  governanceLogDelete,
} from "@/api/governance/governanceLog";
import mitt from "@/utils/mitt";
import { fileUpload, fileRemove } from "@/api/admin/file";
import { getDictCss, formatDict } from "@/utils/dict";
import { editEvaluation } from "@/api/governance/governanceResponsibility";
import governanceResponsibilityEdit from "@/componts/governance/governanceResponsibilityEdit1.vue";
import moment from "moment";
export default {
  props: [
    "statusList",
    "dataList",
    "typeNodeList",
    "total",
    "deviceType",
    "sexList",
  ],
  components: {
    governanceResponsibilityEdit,
  },
  data() {
    return {
      imgServer: import.meta.env.VITE_BASE_API,
      loading: false,
      warnEventSourceModel: { warnEventDetectRules: [] },
      dataList: [],
      communityId: localStorage.getItem("communityId"),
      dialog: {},
      secondDialog: {},
      videoShow: false,
      evaluationShow: false,
      viewOhter: { show: false, title: "查看" },
      searchModel: {},
      evaluationModel: {},
      recordsModel: {
        governanceResponsibilityVoList: [],
      },
      fileList: [],
      fileList1: [],
      fileList2: [],
      tableList: [],
      governanceEventStatus: null, //状态控制编辑删除按钮 可选状态
      dialogVisible: false,
      dialogVisibleVideo: false,
      governanceEventId: "",
      videoUrl: "",
      viewUrl: "",
      uploadImgType: [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".JPG",
        ".JPEG",
        ".PNG",
        ".GIF",
      ],
      uploadOtherType: [".pdf", ".docx", ".xlsx", ".xls"],
      uploadVideoType: [".mp4"],
      rules2: {
        note: [
          {
            required: true,
            message: "请输入治理描述",
            trigger: "blur",
          },
        ],
        governanceTime: [
          {
            required: true,
            message: "请输入治理时间",
            trigger: "blur",
          },
        ],
      },
      viewStatus: true,
      selectedIndex: 1,
    };
  },
  methods: {
    formatPreviewList(data) {
      const list = [];
      if (data.length) {
        for (const key in data) {
          list.push(this.imgServer + "" + data[key].url);
        }
      }
      return list;
    },

    formatName(row) {
      console.log(row.governanceResponsibilityVoList);
      var list = "";
      for (const item of row.governanceResponsibilityVoList) {
        console.log(item.name);
        list += item.name + "、";
      }
      console.log(list);
      return list;
    },
    // 编辑
    toEditResponsibility(row, index) {
      const data = {
        row,
        governanceEventId: this.governanceEventId,
        index,
        type: "trace",
      };
      mitt.emit("openGovernanceResponsibilityEdit1", data);
    },
    // 添加
    addResponsibility() {
      const params = {
        governanceEventId: this.governanceEventId,
        type: "trace",
      };
      console.log(123);
      mitt.emit("openGovernanceResponsibilityAdd1", params);
    },
    removeResponsibility(row) {
      this.$confirm("删除经办人员, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.recordsModel.delGovernanceResponsibilityList.push(row);
          this.recordsModel.governanceResponsibilityVoList.splice(
            this.recordsModel.governanceResponsibilityVoList.findIndex(
              (item) => item === row
            ),
            1
          );
        })
        .catch(() => {});
    },
    // 刷新列表
    searchTraceResponsibility(data) {
      console.log(data);
      if (this.recordsModel.governanceResponsibilityVoList) {
        var checkItem = this.recordsModel.governanceResponsibilityVoList.find(
          (item) => {
            console.log(item.idCard, data.idCard);
            return item.idCard == data.idCard;
          }
        );
      } else {
        this.recordsModel.governanceResponsibilityVoList = [];
        checkItem = false;
      }
      if (checkItem) {
        this.$message.error("该治理人员已添加");
      } else {
        this.recordsModel.governanceResponsibilityVoList.push(data);
      }
    },
    searchTraceResponsibility1(data) {
      console.log(data.data);
      // this.recordsModel.governanceResponsibilityVoList[data.tableIndex] =
      //   data.data;
        let checkItem = this.recordsModel.governanceResponsibilityVoList.find(item =>{
        console.log(item ,data.data);
        if (item.idCard == data.data.idCard && item.dutyNote == data.data.dutyNote) {
          return item
        }
      })
      if (checkItem) {
        this.$message.error("该治理人员已添加")
      } else {
      this.recordsModel.governanceResponsibilityVoList[data.tableIndex] =
        data.data;
        // this.recordsModel.governanceResponsibilityVoList.push(data);
      }
    },
// 修改评语
    editEvaluationFunc(row) {
      this.evaluationShow = true;
      this.evaluationModel.evaluation = row.evaluation;
      this.evaluationModel.id = row.id;
    },
    viewOneUrl(item, index) {
      this.selectedIndex = index;
      this.currentIndex = index; // 设置当前视频索引
      this.viewUrl = item.url; // 设置视频URL
    },
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    close() {
      this.dialog.show = false;
    },
    //查看图片列表格式化
    handlePictureCardPreview(uploadFile) {
      console.log(uploadFile);
      this.dialogImageUrl = uploadFile.url;
      this.dialogVisible = true;
    },
    handleVideoCardPreview(uploadFile) {
      this.videoUrl = [];
      this.videoUrl = uploadFile.videoUrl;
      console.log(uploadFile);
      this.dialogVisibleVideo = true;
    },
    // 打开文件
    openFile(row) {
      window.open(this.imgServer + row.url);
    },
    openVideo(row) {
      this.videoShow = true;
      this.videoUrl = this.imgServer + row.url;
    },
    handleOtherCardPreview(uploadFile) {
      window.open(uploadFile.url);
    },

    // 删除
    deletePic(uploadFile, uploadFiles) {
      fileRemove({ fileUrl: uploadFile.url.replace(this.imgServer, "") });
      this.recordsModel.photoList = uploadFiles;
    },
    deleteVideo(uploadFile, uploadFiles) {
      if (uploadFile.videoUrl.includes("base64")) {
        return;
      }
      fileRemove({ fileUrl: uploadFile.videoUrl.replace(this.imgServer, "") });
      for (let i = 0; i < this.recordsModel.videoList.length; i++) {
        if (uploadFile.videoUrl.includes(this.recordsModel.videoList[i].url)) {
          this.recordsModel.videoList.splice(i, 1);
        }
      }
    },
    deleteOther(uploadFile, uploadFiles) {
      fileRemove({ fileUrl: uploadFile.url.replace(this.imgServer, "") });
      this.recordsModel.otherList = uploadFiles;
    },
    loadingImg(files) {
      let form = new FormData();
      form.append("needCompress", false);
      form.append("file", files.file);
      form.append("modulesName", "base");
      form.append("functionName", "governance");
      form.append("communityId", localStorage.getItem("communityId"));
      fileUpload(form).then((res) => {
        console.log(res.data.result);
        console.log(this.recordsModel.photoList);
        this.recordsModel.photoList.push(res.data.result);
        if (res.data.code == 0) {
        }
      });
    },
    // 上传图片
    loadingOther(files) {
      console.log(files);
      let form = new FormData();
      form.append("file", files.file);
      form.append(
        "governanceLog",
        "governanceEvent/" + localStorage.getItem("communityId")
      );
      form.append("modulesName", "base");
      form.append("functionName", "governance");
      fileUpload(form).then((res) => {
        console.log(res.data.result);
        console.log(this.recordsModel.otherList);
        this.recordsModel.otherList.push(res.data.result);
        if (res.data.code == 0) {
        }
      });
    },
    // 上传前的钩子
    beforeUploadImg(file) {
      const type = file.name.substring(file.name.lastIndexOf(".")); // 获取文件后缀，取文件格式
      const isLt10M = file.size / 1024 / 1024 < 25;
      if (!this.uploadImgType.includes(type)) {
        this.$message({
          type: "error",
          message: "只支持jpg,jpeg,png,gif,JPG,JPEG,PNG,GIF文件格式！",
        });
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: "上传文件大小不能超过 25MB!",
          type: "warning",
        });
        return false;
      }
    },
    beforeUploadVideo(file) {
      const type = file.name.substring(file.name.lastIndexOf(".")); // 获取文件后缀，取文件格式
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!this.uploadVideoType.includes(type)) {
        this.$message({ type: "error", message: "只支持mp4文件格式！" });
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: "上传文件大小不能超过 100MB!",
          type: "warning",
        });
        return false;
      }
    },
    beforeUploadOther(file) {
      const type = file.name.substring(file.name.lastIndexOf(".")); // 获取文件后缀，取文件格式
      const isLt10M = file.size / 1024 / 1024 < 25;
      if (!this.uploadOtherType.includes(type)) {
        this.$message({
          type: "error",
          message: "只支持pdf,docx,xlsx,xls文件格式！",
        });
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: "上传文件大小不能超过 25MB!",
          type: "warning",
        });
        return false;
      }
    },
    // 上传视频
    loadingVideo(files) {
      let form = new FormData();
      form.append("file", files.file);
      form.append("modulesName", "base");
      form.append("functionName", "governance");
      form.append("communityId", localStorage.getItem("communityId"));
      fileUpload(form).then((res) => {
        if (res.data.code != 0) {
          this.$message({
            type: "error",
            message: "视频上传失败",
          });
          return false;
        }
        this.recordsModel.videoList.push(res.data.result);
      });
      console.log(1, files, "files@@@@@@@@@@@@@@@@@@@@");
      files.onSuccess((res) => {
        console.log(res, "123");
      });
      this.setFileList(this.fileList1);
    },
    getVideoCover(file) {
      const video = document.createElement("video"); // 也可以自己创建video
      video.src = file.url; // url地址 url跟 视频流是一样的

      var canvas = document.createElement("canvas"); // 获取 canvas 对象
      const ctx = canvas.getContext("2d"); // 绘制2d
      video.crossOrigin = "anonymous"; // 解决跨域问题，也就是提示污染资源无法转换视频
      video.currentTime = 1; // 第一帧

      video.oncanplay = () => {
        canvas.width = video.clientWidth ? video.clientWidth : 320; // 获取视频宽度
        canvas.height = video.clientHeight ? video.clientHeight : 320; //获取视频高度
        // 利用canvas对象方法绘图
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        // 转换成base64形式
        let videoFirstimgsrc = canvas.toDataURL("image/png"); // 截取后的视频封面
        let videoUrl = file.url;
        file.url = videoFirstimgsrc; // file的url储存封面图片
        file.videoUrl = videoUrl; // file的videoUrl储存视频

        video.remove();
        canvas.remove();
      };
      return file;
    },
    setFileList(_fileList) {
      for (let obj of _fileList) {
        //视频附件，获取第一帧画面作为 封面展示
        this.getVideoCover(obj);
      }
      this.fileList1 = _fileList; //fileList 为 Element file-list 参数值
    },
    search() {
      governanceLogList(this.searchModel).then((res) => {
        console.log(res.data.result.list);
        this.dataList = res.data.result;
      });
    },

    submit() {
      this.evaluationModel.evaluation = this.evaluationModel.evaluation.trim()
      editEvaluation(this.evaluationModel).then((res) => {
        this.$message.success(res.data.msg);
        this.evaluationShow = false;
        this.search();
      });
    },
    add() {
      this.fileList = [];
      this.fileList1 = [];
      this.fileList2 = [];
      this.recordsModel = {
        governanceEventId: this.governanceEventId,
        videoList: [],
        photoList: [],
        otherList: [],
      };
      this.secondDialog.show = true;
      this.secondDialog.title = "新增记录";
    },
    // viewOneUrl(row){
    //   this.viewUrl = this.imgServer + row.url
    // },
    edit(id, text) {
      console.log(id);
      this.secondDialog.title = "编辑记录";
      this.secondDialog.show = true;
      governanceLog(id).then((res) => {
        this.recordsModel = res.data.result;
        this.recordsModel.photoList = JSON.parse(
          this.recordsModel.extraData
        ).photoList;
        this.recordsModel.videoList = JSON.parse(
          this.recordsModel.extraData
        ).videoList;
        this.recordsModel.otherList = JSON.parse(
          this.recordsModel.extraData
        ).otherList;
        // 图片
        if (!this.recordsModel.photoList) {
          this.recordsModel.photoList = [];
        } else {
          this.fileList = [];
          for (const item of this.recordsModel.photoList) {
            if (item.url.includes(this.imgServer)) {
              this.fileList.push({ name: item.name, url: item.url });
            } else {
              this.fileList.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
            }
          }
        }
        console.log(this.fileList);
        //视频
        if (!this.recordsModel.videoList) {
          this.recordsModel.videoList = [];
        } else {
          this.fileList1 = [];
          for (const item of this.recordsModel.videoList) {
            if (item.url.includes(this.imgServer)) {
              this.fileList1.push({ name: item.name, url: item.url });
            } else {
              this.fileList1.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
            }
          }
        }
        console.log(this.fileList1);
        this.setFileList(this.fileList1);
        // 文件
        if (!this.recordsModel.otherList) {
          this.recordsModel.otherList = [];
        } else {
          this.fileList2 = [];
          for (const item of this.recordsModel.otherList) {
            if (item.url.includes(this.imgServer)) {
              this.fileList2.push({ name: item.name, url: item.url });
            } else {
              this.fileList2.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
            }
          }
        }
      });
    },
    remove(id) {
      this.$confirm("删除治理记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          governanceLogDelete(id).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
          });
        })
        .catch(() => {});
    },
    onSubmit() {
      console.log(123);
      this.$refs["form"].validate((valid) => {
        console.log(valid);
        if (valid) {
          const list = [];
          for (const key in this.fileList) {
            console.log(this.fileList[key], key, "submit");
            if (this.fileList[key].url.includes(this.imgServer)) {
              list.push({
                name: this.fileList[key].name.split(".")[0],
                url: this.recordsModel.photoList[key].url.replace(
                  this.imgServer,
                  ""
                ),
              });
            } else {
              list.push({
                name: this.fileList[key].name.split(".")[0],
                url: this.recordsModel.photoList[key].url,
              });
            }
          }

          const list1 = [];
          for (const key in this.fileList1) {
            if (this.fileList1[key].url.includes(this.imgServer)) {
              list1.push({
                name: this.fileList1[key].name.split(".")[0],
                url: this.recordsModel.videoList[key].url.replace(
                  this.imgServer,
                  ""
                ),
              });
            } else {
              list1.push({
                name: this.fileList1[key].name.split(".")[0],
                url: this.recordsModel.videoList[key].url,
              });
            }
          }

          const list2 = [];
          for (const key in this.fileList2) {
            if (
              this.fileList2[key].url &&
              this.fileList2[key].url.includes(this.imgServer)
            ) {
              list2.push({
                name: this.fileList2[key].name.split(".")[0],
                url: this.recordsModel.otherList[key].url.replace(
                  this.imgServer,
                  ""
                ),
              });
            } else {
              list2.push({
                name: this.fileList2[key].name,
                url: this.recordsModel.otherList[key].url,
              });
            }
          }
          console.log(list2);
          this.recordsModel.photoList = list;
          this.recordsModel.videoList = list1;
          this.recordsModel.otherList = list2;
          if (this.secondDialog.title == "新增记录") {
            console.log("add");
            governanceLogAdd(this.recordsModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.secondDialog.show = false;
              this.search();
            });
          } else {
            console.log("edit");
            governanceLogEdit(this.recordsModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.secondDialog.show = false;
              this.search();
            });
          }
        }
      });
    },
  },
  watch: {
    "secondDialog.show"(newValue) {
      if (!newValue) {
        this.recordsModel = {
          governanceEventId: this.governanceEventId,
        };
        this.fileList = [];
        this.fileList1 = [];
      }
    },
    dialogVisibleVideo(newValue) {
      if (!newValue) {
        this.videoUrl = "";
      }
    },
  },
  watch:{
    "recordsModel.note"(newVal,oldVal){
      if (newVal && newVal.length > 200) {
        this.recordsModel.note = String(newVal).slice(0,200);
      }
    }
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("governanceLogEdit", (params) => {
        this.governanceEventId = params.id;
        this.governanceEventStatus = params.status;
        this.searchModel = {
          governanceEventId: params.id,
        };
        this.dataList = [];
        governanceLogList(this.searchModel).then((res) => {
          this.dataList = res.data.result;
          console.log(this.dataList);
        });
        this.dialog.show = true;
        this.dialog.title = "治理记录";
      });
    });
  },
};
</script>
<style scoped lang="less">
// div /deep/ .el-upload-list{
//   display: flex;
//   width:100%;
//   flex-wrap: nowrap;
//   >li,div{
//     display: flex;
//     flex-shrink: 0;
//   }
// }
.image-container {
  max-width: 50%; /* 设置容器的最大宽度 */
  max-height: 50%; /* 设置容器的最大高度 */
  overflow: hidden; /* 隐藏超出部分 */
}
div /deep/.picView .el-dialog__header {
  background-color: #fff;
  box-shadow: none;
}
div /deep/ .el-dialog {
  background-color: #f1f5ff;
}
div /deep/ .picView .el-dialog__close {
  color: #ccc;
}
.view_contoiner {
  height: 570px;
  width: 100%;
  display: flex;
  justify-content: center;

  > .view_right {
    flex: 1;
    height: 100%;
    // background-color: black;
    margin-left: 5px;
    border: 1px solid #ccc;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 100%; /* 设置容器的最大宽度 */
    max-height: 100%; /* 设置容器的最大高度 */
    overflow: hidden; /* 隐藏超出部分 */
  }
}
.activedOne {
  background: #4b96ff;
}
// .view_ohter{
ul {
  list-style: none;
  li:hover {
    color: #4b96ff;
    cursor: pointer;
  }
}
// }
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

/deep/ .el-timeline-item__timestamp {
  width: 50%;
  line-height: 16px;
}
/deep/ .el-timeline-item__content {
  display: flex;
  background-color: rgba(243, 243, 243, 1);
  align-items: center;
  justify-content: center;
}
/deep/ .el-timeline-item__node {
  border: 2px solid rgba(86, 142, 254, 1);
}
.el-row {
  background-color: #fff;
  border-radius: 5px;
  padding: 15px 0px;
}

.img-box:not(:nth-child(3n)) {
  margin-right: calc(6% / 3);
}

.pic--video div /deep/ .el-upload-list {
  display: flex;
  width: 100%;
  flex-wrap: nowrap;
  > li,
  div {
    display: flex;
    flex-shrink: 0;
  }
}
</style>
<!-- <style>
.el-popper {
  max-width: 300px !important;
}
</style> -->
