<template>
	<el-dialog width="50%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<person-belong-edit :liveTypeList="liveTypeList" :statusList="statusList" :personTypeList="personTypeList" @search="search"></person-belong-edit>
		<el-form :rules="rules" :model="personModel" label-width="80px">
			<el-row>
				<el-col :span="8">
					<el-form-item label="姓名" prop="name">
						<el-input readonly v-model="personModel.name" placeholder="姓名"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="年龄" prop="age">
						<el-input readonly v-model="personModel.age" placeholder="年龄"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="性别" prop="sex">
						<el-select disabled style="width: 100%;" v-model="personModel.sex" placeholder="性别">
							<el-option v-for="item in sexList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="联系电话" prop="phone">
						<el-input readonly v-model="personModel.phone" placeholder="联系电话"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="住址" prop="address">
						<el-input readonly v-model="personModel.address" placeholder="住址"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="籍贯" prop="nativePlace">
						<el-input readonly v-model="personModel.nativePlace" placeholder="籍贯"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row :gutter="20">
			<el-col :span="24">
				<el-table stripe :data="myroomList" border style="width: 100%">
					<el-table-column prop="communityName" align="center" label="小区"/>
					<el-table-column prop="buildingNumber" align="center" label="楼栋" />
					<el-table-column prop="unitNumber" align="center" label="单元" />
					<el-table-column prop="roomNumber" align="center" label="房间编号" />
					<el-table-column prop="type" align="center" :formatter="formatRoomType" label="户型" />
					<el-table-column prop="personType" align="center" :formatter="formatPersonType" label="住户类型" />
					<el-table-column prop="liveType" align="center" :formatter="formatLiveType" label="居住类型" width="109" />
					<el-table-column prop="status" align="center" :formatter="formatStatus" label="状态" />
					<el-table-column align="center" label="操作">
						<template #default="scope">
							<el-button type="text" size="default" @click="toEdit(scope.row.id)">编辑</el-button>
							<el-button type="text" size="default" @click="remove(scope.row.id)" v-if="hasPerm('base:person:belong:delete')">移除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
			<el-col v-if="hasPerm('base:person:belong:add')" :span="24" style="display: flex;justify-content: center;align-items: center;">
				<el-button style="font-size: 25px;width: 100%;border-radius: 0;" size="default" @click="add" >
					<el-icon :size="20">
					  <plus></plus>
					</el-icon>
				</el-button>
			</el-col>
		</el-row>
	</el-dialog>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
</script>
<script>
import { deletePersonBelong,getPersonBelong } from "@/api/base/personBelong"
import { queryChildrenNodeBuilding } from "@/api/base/building"
import { listDictByNameEn } from "@/api/admin/dict"
import personBelongEdit from "@/componts/base/personBelongEdit.vue"
import { listPersonBelong } from "@/api/base/personBelong"
import mitt from "@/utils/mitt";

export default {
	props:['sexList'],
	components:{ personBelongEdit },
	data() {
		return {
			loading: false,
			dialog:{},
			personModel:{},
			personTypeList:[],
			statusList:[],
			roomTypeList:[],
			liveTypeList:[],
			myroomList:[]
		}
	},
	methods: {
		search() {
			listPersonBelong({personId:this.personModel.id,communityId:localStorage.getItem("communityId")})
			.then(res =>{
				this.myroomList = res.data.result
			})
			this.$emit('search')
		},
		remove(id){
			this.$confirm('移除住户, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deletePersonBelong(id)
					.then(res =>{
						this.$message.success(res.data.msg)
						this.search()
					})
			}).catch(()=>{})
		},
		toEdit(id){
			getPersonBelong(id)
			.then(res =>{
				let communityId = localStorage.getItem("communityId")
				queryChildrenNodeBuilding({communityId:communityId})
				.then(node_res =>{
					mitt.emit('openPersonBelongEdit',{
						buildingList:node_res.data.result,
						personId:this.personModel.id,
						belong:res.data.result,
						communityId:communityId
					})
				})
			})
		
		},
		add(){
			let communityId = localStorage.getItem("communityId")
			queryChildrenNodeBuilding({communityId:communityId})
			.then(res =>{
				mitt.emit('openPersonBelongAdd',{
					buildingList:res.data.result,
					personId:this.personModel.id,
					communityId:communityId
				})
			})
		},
		formatStatus(row, column, cellValue, index){
			let result = ''
			for(let item of this.statusList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		formatRoomType(row, column, cellValue, index){
			let result = ''
			for(let item of this.roomTypeList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		formatPersonType(row, column, cellValue, index){
			let result = ''
			for(let item of this.personTypeList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		formatLiveType(row, column, cellValue, index){
			let result = ''
			for(let item of this.liveTypeList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		async init(){
			try{
				let person_res = await listDictByNameEn('person_type')
				let live_res = await listDictByNameEn('live_type')
				let room_res = await listDictByNameEn('room_type')
				let status_res = await listDictByNameEn('person_status')
				this.statusList = status_res.data.result
				this.personTypeList = person_res.data.result
				this.roomTypeList = room_res.data.result
				this.liveTypeList = live_res.data.result
			}catch(err){
			}
		}
	},
	mounted(){
		this.$nextTick(function() {
			mitt.on('openPersonBelongDetail', (data) => {
				this.init()
				this.personModel = data.person
				this.myroomList = data.data
				this.dialog.show = true
				this.dialog.title = "房产信息"
			})
		})
	},
	created() {
		mitt.off('openPersonBelongAdd')
		mitt.off('openPersonBelongEdit')
	}
}
</script>

<style scoped="scoped">
	.el-dialog__body{
		height: 500px !important;
	}
</style>

