import { createApp, ref } from 'vue'
import App from './App.vue'
const app = createApp(App)

import router from './router/router.js'
import store from './store'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './assets/iconfonts/iconfont.js'
import './assets/iconfonts2/iconfont.js'
import 'element-plus/dist/index.css'
import './assets/css/index.css'
import '@/assets/js/h5player/h5player.min.js'
import directive from './directive' // directive
import 'video.js/dist/video-js.css'; // video.js样式
// 注册指令
import plugins from './plugins' // plugins
// 字典标签组件
import DictTag from '@/componts/dictTag/index.vue'
import JsonEditor from "jsoneditor";
import locale from 'element-plus/es/locale/lang/zh-cn'
import { useDict, selectDictLabel } from '@/utils/dict'
import { parseTime, resetForm, addDateRange, hasPermission, randomString, encrypt, decrypt, formatDate, getTwoTime, encryptData, saveDict } from '@/utils/myUtils'
import { Base64 } from 'js-base64';
// import { hasPermission } from './utils/myUtils'

//引入cesium基础库
// import "mars3d-cesium/Build/Cesium/Widgets/widgets.css";
// import * as Cesium from "mars3d-cesium";
//导入mars3d主库
// import "mars3d/mars3d.css";  //v3.8.6及之前版本使用 import "mars3d/dist/mars3d.css";
// import * as mars3d from "mars3d";
//导入mars3d插件（按需使用，需要先npm install）
// import "mars3d-space";


// 全局方法挂载
app.config.globalProperties.$Base64 = Base64
app.config.globalProperties.useDict = useDict
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.formatDate = formatDate
app.config.globalProperties.getTwoTime = getTwoTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.hasPerm = hasPermission
app.config.globalProperties.encryptData = encryptData
app.config.globalProperties.saveDict = saveDict
app.config.globalProperties.randomStr = randomString
app.config.globalProperties.encrypt = encrypt
app.config.globalProperties.decrypt = decrypt
// app.config.globalProperties.download = download

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
// 全局组件挂载
app.component('DictTag', DictTag)

// app.use(vuescroll)
app.use(router)
app.use(store)
app.use(plugins)
app.use(ElementPlus,{ locale })
directive(app)

app.mount('#app')
