<template>
  <el-row :gutter="24">
    <el-col :span="24">
      <div class="title_des">
        <div v-for="item in totalList" :key="item">
          <div class="icon">
            <i class="iconfont" :class="item.icon"></i>
            <i class="iconfont icon-tuoguan"></i>
          </div>
          <div class="title">
            <div style="font-size: 20px;font-weight:700; color: #333;">{{ item.total }}</div>
            <div style="font-size: 14px;line-height: 18px; color: #333;">{{item.title}}</div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <el-row :gutter="24">
    <el-col :span="16">
      <el-select style="width:100%" filterable v-model="communityIds" multiple placeholder="请选择小区名称">
        <el-option v-for="item in communityList" :key="item.id" :label="item.communityName" :value="item.id">
          <!-- <el-checkbox :key="item.id" v-model="item.communityName">{{ item.communityName }}</el-checkbox> -->
        </el-option>
      </el-select>
    </el-col>
    <el-col :span="6">
      <el-date-picker value-format="YYYY-MM-DD HH:mm:ss" v-model="startToEndTime" type="daterange"
        start-placeholder="开始时间" end-placeholder="到期时间" />
    </el-col>
    <el-col :span="2">
      <el-button type="primary" style="width: 100px;height: 30px;" @click="search">筛 选</el-button>
    </el-col>
  </el-row>
  <div :gutter="20">
    <div style="display: flex; justify-content: space-between; margin-bottom: 10px">
      <div style="height: 390px; width: 49.6%; background-color: #fff; padding: 20px;">
        <div style="height: 20px; display: flex; justify-content: space-between">
          <h3 style="margin: 0px">总人流量:{{ personTotal }}</h3>
        </div>
        <div style="width: 100%; height: 100%; box-sizing: border-box; margin-top: 10px;" id="renliu" ></div>
      </div>
      <div style="height: 390px; width: 49.6%; background-color: #fff; padding: 20px;">
        <div style="height: 20px; display: flex; justify-content: space-between">
          <h3 style="margin: 0px">总车流量:{{ vehicleTotal }}</h3>
        </div>
        <div style="width: 100%; height: 100%; box-sizing: border-box; margin-top: 10px;" id="cheliu"></div>
      </div>
    </div>
    <div style="display: flex; justify-content: space-between; margin-bottom: 10px">
      <div style="height: 390px; width: 49.6%; background-color: #fff; padding: 20px;">
        <div style="height: 20px; display: flex; justify-content: space-between">
          <h3 style="margin: 0px">总设施事件:{{ deviceEventTotal }}</h3>
        </div>
        <div style="width: 100%; height: 100%; box-sizing: border-box; margin-top: 10px;" id="shebei"></div>
      </div>
      <div style="height: 390px; width: 49.6%; background-color: #fff; padding: 20px;">
        <div style="height: 20px; display: flex; justify-content: space-between">
          <h3 style="margin: 0px">总预警事件:{{ warnTotal }}</h3>
        </div>
        <div style=" width: 100%; height: 100%; box-sizing: border-box; margin-top: 10px;" id="yujing"></div>
      </div>
    </div>
  </div>
</template>

<script>

import { getCommunityTotal, getPersonTotal, getVehicleTotal, getDeviceInfoTotal, getRoomTotal, getUserGroupTotal, getVisitorsTotal, getPersonSituation, getVehicleSituation, getWarnTotalSituation, getWarningSituation, getDeviceEventSituation, getWxTotal } from "@/api/dataScreening/overviewChart.js";
import { weChatList } from "@/api/weChat/weChatUser"
import { accountCommunity } from "@/api/base/community";
import "@/assets/iconfonts2/iconfont.css";
import * as echarts from "echarts";
export default {
  data() {
    return {
		totalList: [],
		vehicleTotal: "",
		personTotal: "",
		deviceInfoTotal: "",
    warnTotal: "",
    deviceEventTotal: {},
		communityList: [{ id: 0, communityName: "加载中..." }],
		communityId: "",
		searchModel:{
			startTime:"2022-12-01",
			endTime:"2022-12-31",
		},
		communityIds: [],
		startToEndTime: []
	};
  },
  mounted() {
    this.init();  
	
  },
  methods: {
	search(){
		if (this.communityIds.length == 0 ) {
			this.$message.error("请选择小区")
			return
		}
    this.searchModel.communityIds = this.communityIds
    // -7 为当前日期往前推7天
    this.searchDate(-7)
    if (this.startToEndTime && this.startToEndTime.length !== 0) {
      this.searchModel.startTime = this.startToEndTime[0].slice(0,10)
      this.searchModel.endTime = this.startToEndTime[1].slice(0,10)
    }

	  /** 请求图表数据 */
	  getPersonSituation(this.searchModel).then( res =>{
				this.personTotal = res.data.result.total
				if (res) {
          this.handlerPerson(res)
        }
	  })
	  getVehicleSituation(this.searchModel).then( res =>{
				this.vehicleTotal = res.data.result.total
				if (res) {
          this.handlerCar(res)
        }
	  })
	  getWarnTotalSituation(this.searchModel).then( res =>{
				this.warnTotal = res.data.result.total
				if (res) {
          this.handlerDevice(res)
        }
	  })
	  getDeviceEventSituation(this.searchModel).then( res => {
        this.deviceEventTotal = res.data.result.total
        if (res) {
          this.handlerFacility(res)
        }
	  })
	},
    async init() {
		await accountCommunity().then(res => {
			this.communityList = res.data.result
		})
    if (this.communityList.length < 7) {
      for (let index = 0; index < this.communityList.length; index++) {
        this.communityIds.push(this.communityList[index].id)
      }
    } else{
      for (let index = 0; index < 7; index++) {
        this.communityIds.push(this.communityList[index].id)
      }
    }
    
		this.searchModel.communityIds = this.communityIds


      await getCommunityTotal().then((res) => {
        this.totalList.push({total:res.data.result,title:"小区总数", icon:"icon-fangzi"})
	  });
	  await getPersonTotal().then((res) => {
        this.totalList.push({total:res.data.result,title:"总人数", icon:"icon-renshu"})
		
	  });
	  await getVehicleTotal().then((res) => {
        this.totalList.push({total:res.data.result,title:"车辆总数", icon:"icon-cheliang-"})
	  });
	  await getDeviceInfoTotal().then((res) => {
        this.totalList.push({total:res.data.result,title:"设施总数", icon:"icon-shebeiguanli1"})
	  });
	  await getRoomTotal().then((res) => {
        this.totalList.push({total:res.data.result,title:"房屋总数", icon:"icon-fangzi"})
	  });
	  await getUserGroupTotal({nodeType:4}).then((res) => {
        this.totalList.push({total:res.data.result,title:"镇总数", icon:"icon-suoshuxiangzhen"})
	  });
	  await getUserGroupTotal({nodeType:5}).then((res) => {
        this.totalList.push({total:res.data.result,title:"社区总数", icon:"icon-menu_sqgg"})
	  });
	  await getVisitorsTotal().then((res) => {
        this.totalList.push({total:res.data.result,title:"访客总数", icon:"icon-wode"})
	  });
    await getWxTotal().then(res => {
        this.totalList.push({total:res.data.result, title:"小程序用户数", icon:"icon-luyin"})
		})

    this.search()
    },

	handlerPerson(data) {
		let list = this.extractNV(data);
		this.insterXianEchat(list, "renliu", "总人流量");
	},
	handlerCar(data) {
		let list = this.extractNV(data);
		this.insterXianEchat(list, "cheliu", "总车流量");
    },
	handlerDevice(data) {
    let list = this.extractNV(data);
    this.insterXianEchat(list, "yujing", "总设施事件");
  },
  handlerFacility(data) {
    let list = this.extractNV(data);
    this.insterXianEchat(list, "shebei", "总设施事件");
  },

    // handlerEv(data) {
    //   let list = this.extractNV(data);
    //   this.insterXianEchat(list, "yujing", "事件统计");
    // },

	/** 格式化数据 */
extractNV(data) {
  if (data.data.result.total == 0) {
    return [[],[],[]]
  }
	let dateStrList = [];
	let communityList = [];
	let seriesList = []
	for (let item of data.data.result.dateStrList) {
		dateStrList.push(item)
	}
	
  this.communityIds.map( res => {
    this.communityList.some( vres => {
      if (vres.id == res) {
        communityList.push(vres.communityName)
      }
    })
  })
  //按照请求时间的length 设置值全为0的数组
  let dataNull = []
  for (let i = 0; i < dateStrList.length; i++) {
    dataNull.push(0)
  }
  // 初始化echarts图表数据
  for(var i=0;i<communityList.length;i++){
    seriesList.push({
      name:communityList[i],
      data:dataNull,
      type:"line",
      smooth: true
    })
  }
  for(var i=0;i<seriesList.length;i++){
    for (let item in data.data.result.list) {
      if(item==seriesList[i].name){
        seriesList[i].data=data.data.result.list[item]
      }
    }
	}
  //      [数据列表、时间列表、小区列表]
	return [seriesList,dateStrList,communityList];
},

//格式化时间，从今天开始 + (day) 天
searchDate(day){
  let d1 = new Date();
  let d2 = new Date(d1);
  d2.setDate( d1.getDate() + day);
  let date = []
  //添加到数组并按时间戳排序
  date.push(new Date(d1.getFullYear() + '-' + (d1.getMonth() + 1) + '-' + d1.getDate()).valueOf())
  date.push(new Date(d2.getFullYear() + '-' + (d2.getMonth() + 1) + '-' + d2.getDate()).valueOf())
  date.sort((a,b) => a-b)
  this.searchModel.startTime = new Date(date[0]).toLocaleDateString().replace(/\//g, "-");
  this.searchModel.endTime = new Date(date[1]).toLocaleDateString().replace(/\//g, "-");
},
    
    
    insterXianEchat(list, docm, title) {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById(docm));
      // 绘制图表
	//   let option = map.getOption() // 获取option数据
	//   myChart.setOption(option, true) // 重新渲染
      myChart.setOption({
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: list[2],
          scroll:true,
          left: true,
          lineStyle: {
            width: 10,
          },
        },
        // title: {
        // 	text: title
        // },
        xAxis: {
          type: "category",
          data: list[1],
        },
        yAxis: {
          type: "value",
        },
        series: list[0],
        grid: {
          containLabel: true,
        },
      },true);
    },
  },
};
</script>



<style scoped lang="less">
.el-row {
  margin-bottom: 10px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
  margin-left: 0px !important;
  margin-right: 0px !important;
}
.el-col {
  /* width: 100%; */
}
.title_des {
  font-size: 15px;
  display: flex;
  /* width: 100%; */
}
.title_des > div {
  width: 185px;
  height: 126px;
  background: rgb(244, 249, 255);
  opacity: 1;
  border-radius: 16px;
  margin-right: 10px;
  display: flex;
}
::v-deep .el-table__row--striped {
  background-color: pink !important;
}
.el-card {
  height: 390px;
  width: 49.6%;
}
.icon {
  width: 56px;
  height: 56px;
  /* line-height: 56px; */
  text-align: center;
  background: rgb(225, 238, 255);
  opacity: 1;
  border-radius: 28px;
  margin: 38px 10px 32px 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  >i {
    margin-right: 0px;
    font-size: 28px;

  }
  >i:nth-child(1){
    color:#4784ff;
    position: relative;
    bottom:-6px
  }
}
.title {
  margin-top: 41px;
}
.icon-tuoguan{
	color: rgb(85, 211, 232);
}
</style>