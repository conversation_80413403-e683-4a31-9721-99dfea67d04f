import request from '@/utils/request'

export const listGovernance = (data) =>
	request({
		url: '/governanceEvent/page',
		method: 'get',
		params: data
	})

export const getGovernance = (id) =>
	request({
		url: '/governanceEvent/'+id,
		method: 'get'
	})	
export const governanceEventNotRequireMask = (id) =>
	request({
		url: '/governanceEvent/notRequireMask/'+id,
		method: 'get'
	})	

export const addGovernance = (data) =>
	request({
		url: '/governanceEvent',
		method: 'post',
		data: data
	})

export const editGovernance = (data) =>
	request({
		url: '/governanceEvent',
		method: 'put',
		data: data
	})	

export const deleteGovernance = (id) =>
	request({
		url: '/governanceEvent/'+id,
		method: 'delete'
	})	

export const batchDeleteGovernance = (data) =>
	request({
		url: '/governanceEvent/batch',
		method: 'delete',
		data: data
	})		

export const editStatus = (data) =>
	request({
		url: '/governanceEvent/status',
		method: 'put',
		data: data
	})		