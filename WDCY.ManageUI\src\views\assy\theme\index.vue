<template>
  <div class="app-container">

    <el-row :gutter="20">
      <el-col :span="18" >
        <el-input style="width: 138px;margin-right: 10px;" v-model="queryParams.themeTitle" placeholder="请输入标题关键字" clearable @keyup.enter.native="handleQuery" />

        <el-select
          v-model="queryParams.themeType"
          clearable
          placeholder="选择主题类型"
          style="width: 130px;margin-right: 10px;"
        >
          <el-option
            v-for="item in themeTypeList"
            :key="item.nameEn"
            :label="item.nameCn"
            :value="item.nameEn"
          />
        </el-select>
        
        <el-select
          v-model="queryParams.status"
          clearable
          placeholder="选择状态"
          style="width: 130px;margin-right: 10px;"
        >
          <el-option
            v-for="item in statusList"
            :key="item.nameEn"
            :label="item.nameCn"
            :value="item.nameEn"
          />
        </el-select>

        <el-date-picker
          v-model="daterangeCreateTime"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 200px;margin-right: 10px;"
        />

        <el-button type="primary" @click="handleQuery">搜 索</el-button>
      </el-col>
      <el-col :span="6" >
        <el-button style="float: right;" type="primary" @click="handleAdd" v-if="hasPerm('assy:theme:add')">添 加</el-button>
        <el-button style="float: right; margin-right: 10px;" type="primary" :disabled="single" @click="handleUpdate" v-if="hasPerm('assy:theme:add')">修 改</el-button>
        <el-button style="float: right; " type="primary" :disabled="multiple" @click="handleDelete" v-if="hasPerm('assy:theme:remove')">删 除</el-button>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-table v-loading="loading" :data="themeList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            prop="videoCover"
            width="100"
            align="center"
            label="图片"
          >
            <template #default="scope">
              <el-image
                preview-teleported
                style="
                  width: 60px;
                  height: 36px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
                :src="getVC(scope.row)"
                :preview-src-list="[getVC(scope.row)]"
              >
                <template #error>
                  <span style="display: flex; justify-content: center">暂无图片</span>
                </template>
              </el-image>
            </template>
          </el-table-column>

          <el-table-column label="主题ID" align="center" prop="id" />
          <el-table-column label="主题类型" align="center" prop="themeType">
            <template #default="scope">
              <el-tag size="default" :type="tagType(themeTypeList, scope.row.themeType)">{{ tagName(themeTypeList,
                scope.row.themeType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="标题" align="center" prop="themeTitle" show-overflow-tooltip />
          <el-table-column label="主题说明" align="center" prop="themeNote" show-overflow-tooltip />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <el-tag size="default" :type="tagType(statusList, scope.row.status)">{{ tagName(statusList,
                scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="额外数据" align="center" prop="extraData" show-overflow-tooltip />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center" prop="createUser" />
          <el-table-column label="修改人" align="center" prop="updateUser" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="168">
            <template #default="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['assy:theme:edit']">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['assy:theme:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div style="float: right; margin-top: 20px;">
          <el-pagination background :total="Number(total)" v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" @change="getList"
            layout="total, sizes, prev, pager, next, jumper" />
        </div>
      </el-col>
    </el-row>

    <!-- 添加或修改主题对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="themeTitle">
          <el-input v-model="form.themeTitle" placeholder="请输入标题" maxlength="50" show-word-limit/>
        </el-form-item>

        <el-form-item label="主题类型" prop="themeType">
          <el-select
            v-model="form.themeType"
            clearable
            placeholder="选择主题类型"
            style="width: 130px;margin-right: 10px;"
          >
            <el-option
              v-for="item in themeTypeList"
              :key="item.nameEn"
              :label="item.nameCn"
              :value="item.nameEn"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="说明" prop="themeNote">
          <el-input v-model="form.themeNote" type="textarea" placeholder="请输入主题说明" maxlength="500" show-word-limit/>
        </el-form-item>
        <el-form-item label="拓展参数" prop="extraData">
          <!-- <el-input v-model="form.extraData" type="textarea" placeholder="拓展参数" /> -->
          <JsonEditorVue
            class="editor"
            language="cn"
            v-model="form.extraData"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            inline-prompt
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>

        <el-form-item label="背景">
          <ImageUpload v-model="imageUrl" modName="assy" funcName="theme" isCommunity="false"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import JsonEditorVue from "json-editor-vue3";
import ImageUpload from "@/componts/fileUpload/imgUpload.vue";
import { pagingTheme, getTheme, delTheme, addTheme, updateTheme } from "@/api/assy/theme";
import { listDictByNameEn } from "@/api/admin/dict";
import { getDictCss, formatDict } from "@/utils/dict";

export default {
  name: "Theme",
  components: {
    JsonEditorVue, ImageUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 主题表格数据
      themeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 修改人时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        themeType: null,
        themeTitle: null,
        themeNote: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        themeType: [
          { required: true, message: "主题类型不能为空", trigger: "change" }
        ],
        themeTitle: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        themeNote: [
          { required: false, message: "主题说明不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      statusList: [],
      themeTypeList: [],
      imageUrl: "",
      imgServer: import.meta.env.VITE_BASE_API
    };
  },
  created() {
    this.asyncInit();
    this.getList();
  },
  methods: {
    async asyncInit() {
      try {
        let respone = await listDictByNameEn('common_status');
        console.info("listDictByNameEn", respone, respone.data);
        this.statusList = respone.data.result;
        respone = await listDictByNameEn('assy_theme_type');
        this.themeTypeList = respone.data.result;
      } catch (err) {
      }
    },
    tagType(dicList, cellValue) {
      return getDictCss(dicList, cellValue)
    },
    tagName(dicList, cellValue) {
      return formatDict(dicList, cellValue)
    },
    // 格式化图片
    getVC(row) {
      if (row.extraData) {
        let obj = JSON.parse(row.extraData);
        if (obj.bgUrl) {
          return this.imgServer + obj.bgUrl;
        } else {
          return false;
        }
      }
    },
    /** 查询主题列表 */
    getList() {
      this.loading = true;
      this.queryParams.map = null;
      //console.info("daterange", this.daterangeCreateTime);
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.map = {};
        this.queryParams.map["beginTime"] = this.daterangeCreateTime[0];
        this.queryParams.map["endTime"] = this.daterangeCreateTime[1];
      }
      pagingTheme(this.queryParams).then(response => {
        let data = response.data;
        if (data.code == 0) {
          this.themeList = data.result.list;
          this.total = data.result.total;
        } else {
          this.$modal.msgError(data.msg);
        }
        this.loading = false;
      }).catch(res => {        
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        themeType: null,
        themeTitle: null,
        themeNote: '',
        status: 1,
        extraData: {
          "path": "theme/normal",
          "themeColor": "normal"
        },
        // createTime: null,
        // updateTime: null,
        // createUser: null,
        // updateUser: null
      };
      this.imageUrl = "";
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加主题";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTheme(id).then(response => {
        let data = response.data;
        if (data.code == 0) {
          this.form = data.result;
          if (this.form.extraData) {
            this.form.extraData = JSON.parse(this.form.extraData);
            this.imageUrl = this.form.extraData.bgUrl;
          }
          this.open = true;
          this.title = "修改主题";
        } else {
          this.$modal.msgSuccess(data.msg);
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let formData = JSON.parse(JSON.stringify(this.form));
          if (this.form.extraData) {
            this.form.extraData.bgUrl = this.imageUrl;
            formData.extraData = JSON.stringify(this.form.extraData);
          }

          if (formData.id != null) {
            updateTheme(formData).then(response => {
              let data = response.data;
              if (data.code == 0) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
              } else {
                this.$modal.msgSuccess(data.msg);
              }
              this.getList();
            });
          } else {
            addTheme(formData).then(response => {
              let data = response.data;
              if (data.code == 0) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
              } else {
                this.$modal.msgSuccess(data.msg);
              }
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除主题编号为"' + ids + '"的数据项？').then(function () {
        return delTheme(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('assy/theme/export', {
        ...this.queryParams
      }, `theme_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}

div /deep/ .diaLogClass {
  height: 780px;
  width: 1124px;
  background-color: rgb(241, 245, 255);
  overflow-x: auto;
  position: relative;
  border-radius: 16px;
}

div /deep/ .el-dialog__body {
  padding: 0 34px;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.editor {
  width: 805px;
}
.dialog-footer{
  text-align: right;
}
</style>