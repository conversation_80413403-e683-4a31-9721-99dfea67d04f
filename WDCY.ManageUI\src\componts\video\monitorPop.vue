<template>
  <el-dialog
    v-model="dialog.show"
    :before-close="close"
    width="1000px"
    title="视频播放"
  >
    <div class="monitor-pop-container" v-if="dialog.show">
      <div class="onerow">
        <div
          :class="0 == playMode ? 'preview-btn' : 'preview-btn2'"
          @click="changeMode(0)"
        >
          实时监控
        </div>
        <div
          :class="0 == playMode ? 'playback-btn' : 'playback-btn2'"
          @click="changeMode(1)"
        >
          监控回放
        </div>

        <div class="date-picker-content" v-if="playMode==1">
          <el-date-picker
            v-model="playback_date"
            type="date"
            placeholder="选择日期"
            size="small"
            @visible-change="visibleChange"
          />
        </div>
      </div>

      <div class="video-view">
        <monitor
          v-if="getConfigSuccess"
          ref="monitor"
          :width="videoWidth"
          :height="videoHeight"
          :isShowCuttingPartWindow="isShowCuttingPartWindow"
          :communityId="communityId"
          :playMode="playMode"
          :videoMode="videoMode"
          :devId="devId"
          :devNo="devNo"
          :backStartTime="backStartTime"
          :backEndTime="backEndTime"
          :videoConfig="videoConfig"
        >
        </monitor>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import mitt from "@/utils/mitt";
import monitor from "@/componts/monitor/index.vue";
import dateUtil from "@/utils/dateUtil.js";
import {
  queryVideoConfig
} from "@/api/device/device";
export default {
  components: { monitor },


  watch: {
  
    playback_date: {
      handler: function (val, oldVal) {
        console.log("playback_date", val);
        this.selectPlaybackDate(val);
      },
      // 深度观察监听
      deep: true,
    },
  },

  data() {
    return {
      dialog: { show: false },

      getConfigSuccess: false, //初始化monitor
      isShowCuttingPartWindow: false,
      communityId: String(localStorage.getItem("communityId")),
      playMode: 0,
      videoMode: "PLUGIN",
      devId: "",
      devNo: "",
      backStartTime: "",
      backEndTime: "",
      playback_date: "",
   
      videoConfig: null, //视频播放参数
      videoWidth: 960,
      videoHeight: 550,
    };
  },
  created() {
    mitt.on("openMonitorPop", (data) => {
      this.dialog.show = true;
      this.getVideoConfig(data);
    });
  },

  mounted() {},
  unmounted() {},

  methods: {
    close() {
      this.dialog.show = false;
      this.playMode = 1;
      this.previewMode = "HLS";
      this.playbackMode = "HLS";
      this.devId = "";
      this.devNo = "";
      this.backStartTime = "";
      this.backEndTime = "";
      this.playback_date = "";
      this.tableDataDevice = [];
      this.videoConfig = null;
      this.getConfigSuccess = false;
      this.communityId = "";

    },

    getVideoConfig(monitorItem) {
        debugger
      this.getConfigSuccess = false;

      queryVideoConfig().then((res) => {
        debugger
        this.videoConfig = res.data.result;
        debugger
        this.videoMode = this.videoConfig.previewMode;

        this.devId = monitorItem.id;
        this.devNo = monitorItem.devNo;
        setTimeout(() => {
          this.getConfigSuccess = true; //初始化monitor
        }, 100);
      });
    },

    //切换模式
    changeMode(mode) {
      var that = this;
      if (that.playMode == mode) return;

      that.getConfigSuccess = false;
      //   模式，0：预览，1：回放

      setTimeout(() => {
        that.playMode = mode;

        that.getConfigSuccess = true;
      }, 100);
    },

    visibleChange(e) {
      console.log("visibleChange", e);
      if (e) {
        this.startTimeFocus();
      } else {
        this.startTimeBlur();
      }
    },

    startTimeFocus() {
      if (!this.isShowCuttingPartWindow) {
        this.isShowCuttingPartWindow = true;
      }
    },
    startTimeBlur() {
      if (this.isShowCuttingPartWindow) {
        this.isShowCuttingPartWindow = false;
      }
    },

    // 选择录像具体日期
    selectPlaybackDate(e) {
      // console.log(e);

      var playback_date = dateUtil.formatDate(e);

      this.backStartTime = playback_date + " 00:00:00";
      this.backEndTime = playback_date + " 23:59:59";
    },
  },
};
</script>

<style>

.el-message-box {
  position: absolute;
  left: 50%; /* 将子元素的左边缘偏移父元素宽度的50% */
  right: 50%; /* 将子元素的右边缘偏移父元素宽度的50% */
  top: 30px;
  transform: translateX(-50%); /* 使用transform进行自我偏移，实现居中 */
}
</style>


<style scoped lang="less">
.monitor-pop-container {
  width: 1000px;
  height: 600px;

  .onerow {
    margin-left: -15px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .preview-btn {
    width: 108px;
    height: 46px;
    background-image: url("@/assets/img/monitor-switch-image.png");
    background-size: cover;
    text-align: center;
    font-size: 18px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #35ffd2;
    line-height: 48px;
    margin-left: 10px;
  }

  .preview-btn2 {
    width: 97px;
    height: 35px;
    background-image: url("@/assets/img/playback-switch-image.png");
    background-size: cover;
    text-align: center;
    font-size: 18px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #dadada;
    line-height: 35px;
    margin-left: 15px;
  }

  .date-picker-content {
    width: auto;
    height: 35px;
    line-height: 35px;
    margin-left: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .playback-btn {
    width: 97px;
    height: 35px;
    background-image: url("@/assets/img/playback-switch-image.png");
    background-size: cover;
    text-align: center;
    font-size: 18px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #dadada;
    line-height: 35px;
    margin-left: 5px;
  }

  .playback-btn2 {
    width: 108px;
    height: 46px;
    background-image: url("@/assets/img/monitor-switch-image.png");
    background-size: cover;
    text-align: center;
    font-size: 18px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #35ffd2;
    line-height: 48px;
    margin-left: 5px;
  }

  .preview-cameraname {
    height: 26px;
    text-align: center;
    font-size: 18px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #35ffd2;
    margin-left: 200px;
  }

  .plugin-tips {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 30px;
    font-size: 16px;
    .download-icon {
      width: 30px;
      height: 30px;
    }
  }

  .video-view {
    display: flex;
    position: relative;

    width: 1000px;
    height: 600px;

    z-index: 99;
    overflow: hidden;
  }
}
</style>