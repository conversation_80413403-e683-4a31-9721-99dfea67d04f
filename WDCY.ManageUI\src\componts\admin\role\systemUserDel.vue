<template>
    <el-dialog draggable
		top="3vh"
		width="70%"
		v-loading="loading"
		v-model="dialog.show"
		:title="dialog.title"
	>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-cascader :show-all-levels="false" style="width: 100%;" :props="{checkStrictly:true}" :options="userGroupList" @change="handleChange" clearable placeholder="选择组"/>
		</el-col>
		<el-col :span="4">
			<el-input v-model="searchModel.keyWord" @keydown.enter="search" placeholder="用户名|昵称" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="8">
			<el-button style="float: right;" type="primary" @click="deleted">删除</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table ref="multipleTableRef" stripe row-key="id" @selection-change="handleSelectionChange" :data="userList" border height="500px" style="width: 100%">
                <el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
				<el-table-column prop="avatar" align="center" label="头像" width="100">
					<template #default="scope">
						 <el-image preview-teleported fit="contain" style="width: 50px; height: 50px" :src="imgServer+scope.row.avatar" :preview-src-list="[imgServer+scope.row.avatar]" :z-index="3000">
							<template #error>
								<el-icon style="width: 100%; height: 100%;"><Avatar  style="width: 100%; height: 100%;"/></el-icon>
							</template>
						</el-image>
					</template>
				</el-table-column>
				<el-table-column prop="status" align="center" label="用户组" width="140">
					<template #default="scope">
						{{deptLabelFormat(userGroupList,scope.row.groupId)}}
					</template>
				</el-table-column>
				<el-table-column prop="userName" align="center" label="用户名" />
				<el-table-column prop="nickName" align="center" label="昵称"/>
				<el-table-column prop="sex" align="center" label="性别" :formatter="formatSex" width="88" />


				<el-table-column prop="status" align="center" label="状态" width="88">
					<template #default="scope">
						<el-tag :type="scope.row.status == 0 ? 'success' : 'danger'">{{ formatStatus(scope.row,null,scope.row.status) }}</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="updateTime" show-overflow-tooltip align="center" label="更新时间" />
				<el-table-column prop="authEndTime" show-overflow-tooltip align="center" label="授权过期" />
				<el-table-column prop="mobile" align="center" label="手机" />
				<!-- <el-table-column prop="email" align="center" label="邮箱" /> -->
				<!-- <el-table-column align="center" width="160" label="操作">
					<template #default="scope">
						<el-button style="margin-right: 10px;" type="text" size="default" @click="deleted(scope.row.id)">删除</el-button>
					</template>
				</el-table-column> -->
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</el-dialog>
</template>

<script>
import { pagingUser, systemUserRemove } from "@/api/admin/user"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import { listTestCase, getTestCase, addTestCase, editTestCase, deleteTestCase } from "@/api/healthCheck/useCaseScenario"
import useCaseScenarioCheckEdit from "@/componts/healthCheck/useCaseScenarioCheckEdit.vue"
export default {
	props: ['statusList','userGroupList'],
	components:{ useCaseScenarioCheckEdit },
	data() {
		return {
			searchModel: {
				name:""
			},
			ids: [],
			userList: [],
			checkSceneId: "",
			checkSceneName: "",
			imgServer: import.meta.env.VITE_BASE_API,
			total:0,
			pageSize: 10,
            dialog: {
				show:false,
				title:''
			},
            roleId:null
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		deptLabelFormat(arr,deptId){
			for(let item of arr){
				if (item.value == deptId) {
					return item.label
				}
				if (item.children && item.children.length > 0) {
					var foundNode = this.deptLabelFormat(item.children,deptId)
					if (foundNode) {
						return foundNode
					}
				}
			}
		},
		handleChange(e){
			if(e == null){
				this.searchModel.groupId = null
				return
			}
			this.searchModel.groupId = e[e.length-1]
		},
		handleSelectionChange(val) {
            console.log(val);
			let list = []
			for (let item of val) {
				list.push(item.id)
			}
			this.ids = list
		},
		search() {
            this.searchModel.roleId = this.roleId
			pagingUser(this.searchModel).then( res => {
				this.userList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		deleted(id){
			this.$confirm('删除信息, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					systemUserRemove({userIds:this.ids,roleId:this.roleId})
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		formatStatus(row, column, cellValue, index){
			let result = ''
			for(let item of this.statusList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
		async init(){
			try{
				this.searchModel.pageSize = 10
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	},
    mounted(){
        this.$nextTick(function(){
            mitt.on("openSystemUserDelList",(data)=>{
                if(this.$refs.multipleTableRef) this.$refs.multipleTableRef.clearSelection()
                this.roleId = data.id
				this.pageSize = 10
				this.userList = data.result.list
				this.total = data.result.total
                this.dialog.show = true
                this.dialog.title = "系统用户"
            })

        })
    }
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>