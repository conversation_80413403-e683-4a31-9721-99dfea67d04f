<template>
	<third-sys-conf-edit @search="search" :userList="userList" :icoList="icoList" :allowPushList="allowPushList"
		:thirdSystemList="thirdSystemList"></third-sys-conf-edit>
	<el-dialog draggable width="70%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<el-row>
			<el-col :span="8" style="display:flex">
				<el-input style="margin-right:10px" v-model="searchModel.sysName" placeholder="系统名称" clearable />
			</el-col>
			<el-col :span="4">
				<el-button type="primary" @click="search">搜 索</el-button>
			</el-col>
			<el-col :span="4" :push="8">
				<el-button style="float: right;" type="primary" @click="add">添 加</el-button>
			</el-col>
		</el-row>

		<el-table :data="confList" border style="width: 98%; margin: 0 10px;">
			<el-table-column label="图标" align="center" prop="icoUrl" width="100">
				<template #default="scope">
					<el-image style="width:70px;height:70px" :src="imgServer + formatImg(scope.row.icoInfoId)"
						fit="contain"></el-image>
				</template>
			</el-table-column>
			<el-table-column prop="thirdSystemId" align="center" label="三方系统ID" />
			<el-table-column prop="sysName" align="center" label="自定名称" />
			<!-- <el-table-column prop="userId" width="180" align="center" label="用户ID" /> -->
			<el-table-column prop="userId" align="center" label="归属账号">
				<template #default="scope">
					{{ formatUser(userList, scope.row.userId) }}
				</template>
			</el-table-column>
			<el-table-column prop="sort" sortable align="center" label="排序" width="88" />
			<el-table-column prop="allowPush" align="center" label="允许推送" width="95">
				<template #default="scope">
					<el-tag :type="getDictCss(allowPushList, scope.row.allowPush)">{{ formatDict(allowPushList,
						scope.row.allowPush) }}</el-tag>
				</template>
			</el-table-column>
			<el-table-column align="center" width="100" label="操作">
				<template #default="scope">
					<el-button type="text" size="default" @click="edit(scope.row.id)">编辑</el-button>
					<el-button type="text" size="default" @click="deleted(scope.row.id)">删除</el-button>
				</template>
			</el-table-column>
		</el-table>
		<el-row>
			<el-col :span="24"> </el-col>
			<!--<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
				<el-pagination background v-model:page-size="pageSize" :page-sizes="[5, 10, 20, 50, 100]"
					layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange"
					@size-change="handleSizeChange" :total="Number(total)"></el-pagination>
			</el-col> -->
		</el-row>
	</el-dialog>
</template>

<script>
// import { confList,confListDelete} from "@/api/situation/situation"
import { listDictByNameEn } from "@/api/admin/dict"
import { listUser } from "@/api/admin/user"
import { listIcon } from "@/api/base/icon"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import { listThirdSystem, getThirdSysConf, deleteThirdSysConf, listThirdSysConf, listThirdSysAccount } from "@/api/admin/thirdSystem"
import thirdSysConfEdit from "@/componts/admin/thirdSysConfEdit.vue"

export default {
	props: ['thirdSystemList'],
	components: { thirdSysConfEdit },
	data() {
		return {
			searchModel: {
				sysName: ""
			},
			statusList: [],
			confList: [],
			icoList: [],
			userList: [],
			allowPushList: [],
			imgServer: import.meta.env.VITE_BASE_API,
			total: 0,
			pageSize: 5,
			dialog: {
				show: false,
				title: ''
			}
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		},
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatThirdSystem(list, value) {
			let result = ""
			list.forEach(item => {
				if (item.id == value) {
					result = item.sysName
				}
			})
			return result
		},
		formatUser(list, value) {
			let result = ''
			list.forEach(item => {
				if (item.id == value) {
					result = item.nickName
				}
			})
			return result
		},
		formatImg(icoId) {
			let result = "";
			this.icoList.forEach(element => {
				if (element.id == icoId) {
					result = element.icoUrl
				}
			})
			return result;
		},
		search() {
			// let newConfList = []
			// this.confList.forEach(element => {
			// 	if (element.sysName.search(this.searchModel.sysName) != -1) {
			// 		newConfList.push(element)
			// 	}
			// });
			// if (this.searchModel.sysName == '') {
			// 	this.newConfList = this.confList
			// }else{
			// 	this.newConfList = newConfList
			// }

			if (this.searchModel.sysName) {
				let confList = []
				this.confList.forEach(element => {
					if (element.sysName.search(this.searchModel.sysName) != -1) {
						confList.push(element)
					}
				});
				this.confList = confList
			} else {
				this.$parent.conf(this.confId)
			}
		},
		add() {
			mitt.emit('openThirdSysConfAdd', this.confId)
			this.dialog.show = false
		},
		edit(id) {
			getThirdSysConf(id).then(res => {
				mitt.emit('openThirdSysConfEdit', res.data.result)
			})
			this.dialog.show = false

		},
		deleted(id) {
			this.$confirm('删除信息, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteThirdSysConf(id)
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num) {
			this.searchModel.pageSize = num
			this.search()
		},
		formatStatus(row, column, cellValue, index) {
			let result = ''
			for (let item of this.statusList) {
				if (item.nameEn == cellValue) {
					result = item.nameCn
				}
			}
			return result
		},
		async init() {
			console.log(this.$route);
			try {
				this.searchModel.pageSize = 5
				let deviceStatus = await listDictByNameEn('flow_type')
				this.statusList = deviceStatus.data.result
				let allowPushList = await listDictByNameEn('allow_push')
				this.allowPushList = allowPushList.data.result
				let icoList = await listIcon({ pageSize: 9999, icoCategory: "ico_third_sys" })
				this.icoList = icoList.data.result.list
			} catch (err) {
			}
		}
	},
	created() {
		this.init()
	},
	mounted() {
		this.$nextTick(function () {
			listUser({ status:0 }).then(res => {
				this.userList = res.data.result
			})
			mitt.on("conf", (res) => {
				this.confList = res.list
				this.confId = res.confId

				this.dialog.show = true
				this.dialog.title = "第三方系统配置"
			})

		})
	}
}
</script>

<style scoped>
.el-row {
	/* margin-bottom: 20px; */
	background-color: #fff;
	padding: 20px 10px;
	border-radius: 5px;
}
</style>
