import {
	createRouter,
	createWebHistory
} from 'vue-router'

const params = new URLSearchParams(window.location.search);
const code = params.get('code')
const modules = import.meta.glob("../views/**/**.vue")

const router = createRouter({
	history: createWebHistory(),
	routes: [{
			path: '/login',
			component: () => import('../views/login/index.vue')
		},
		{
			path: '/NDA',
			component: () => import('../views/login/NDA.vue')
		},
		{
			path: '/index',
			redirect: '/',
		},
		{
			path: '/',
			redirect:'/home',
			params: {type1: 1},
			children: [...customerRouter()],
			component: () => import('../views/home/<USER>')
		},
		{
			path: '/notFound',
			component: () => import('../views/error/NotFound.vue')
		},
		{
			path: '/:path(.*)',
			redirect: '/notFound'
		},
	],
})


router.beforeEach((to, from, next) => {
	if (to.path == "/NDA") {
		return next()
	}
	
	if (to.path == "/" || to.path == "/index" || to.path.trim() == "" || to.path == "/home") {
		
		if (!localStorage.getItem("token")) {
			sessionStorage.removeItem("wdcy_socketToken")
			return next("/login")
		}
	}
	if (to.path == '/' && localStorage.getItem("token")) {
		next("/home")
	}
	if (code) {
		return next()
	}
	if (to.path == '/login' && localStorage.getItem("token")) {
		next("/home")
	}
	if (to.path != "/login" && !localStorage.getItem("token")){
			sessionStorage.removeItem("wdcy_socketToken")
			return next("/login")
	}
	
	return next()
})

//加载子路由
function customerRouter() {
	let list = []
	if (localStorage.getItem("router")) {
		let routerAry = JSON.parse(localStorage.getItem("router"))
		for (let item of routerAry) {
			const component = modules['../views/' + item.componentPath + '.vue']
			if (component) {
// console.log(item)
				list.push({
					path: item.path,
					name: item.menuName,
					icon: item.icon,
					meta: item.menuName,
					component: modules['../views/' + item.componentPath + '.vue']
				})
			}else {
				list.push({
					path: item.path,
					name: item.name,
					icon: item.icon,
					component: () => import('@/views/error/NotFound.vue')
				})
			}
		}
	}
	list.push({
		path: 'home',
		name: '首页',
		component: modules['../views/home/<USER>']
	})
	list.push({
		hidden: true,
		path: '/jobLog',
		component: () => import('@/views/monitor/job/log.vue'),
		name: '调度日志',
		meta: { title: '调度日志', activeMenu: '/jobTask' }
	})
	list.push({
		path: '/jvmMonitor',
		component: () => import('@/views/monitor/jvmMonitor.vue'),
		name: 'java虚拟运行',
	})
	list.push({
		path: 'lssData',
		name: 'lssData',
		component: modules['../views/base/lssDataList.vue']
	})
	return list
}

export default router;
