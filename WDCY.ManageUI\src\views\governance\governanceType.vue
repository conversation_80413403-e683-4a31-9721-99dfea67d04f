<template>
	<governance-type-edit @search="search" :statusList="statusList"></governance-type-edit>
	<el-row :gutter="20">
		<el-col :span="3" style="display:flex;width:500px">
			<el-input v-model="searchModel.keyWord" @keydown.enter="search" placeholder="治理类型名称" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="13">
			<el-button style="float: right;" type="primary" @click="add" v-if="hasPerm('base:governanceType:add')">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="governanceTypeList" border style="width: 100%">
				<el-table-column prop="nameCn" align="center" label="名称"/>
				<el-table-column prop="nameEn" align="center" label="编码" />
				<el-table-column prop="cssClass" align="center" label="样式" >
					<template #default="scope">
						<span :style="'padding:1px 2px;color:white;background-color:' + scope.row.cssClass">{{ scope.row.cssClass }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="remark" align="center" label="备注" show-overflow-tooltip />
				<el-table-column prop="sort" align="center" label="排序" />
				<el-table-column align="center" width="200" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row)" v-if="hasPerm('base:governanceType:edit')">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('base:governanceType:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<!-- <el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col> -->
	</el-row>
</template>
<script>
import { listDictByNameEn,deleteAdministerTypeDict,listAdministerTypeList } from "@/api/governance/governanceType"
import mitt from "@/utils/mitt"
import governanceTypeEdit from "@/componts/governance/governanceTypeEdit.vue"
export default {
	components:{ governanceTypeEdit },
	data() {
		return {
			searchModel: {},
			governanceTypeList: [],
			statusList:[],
			total:0
		}
	},
	methods: {
		search() {
			var data = {"nameEn":"governance_type","searchModel": this.searchModel}
			listAdministerTypeList(data)
			.then(res => {
				this.governanceTypeList = res.data.result
			})
		},
		edit(row){
			mitt.emit('openAdministerTypeEdit',JSON.parse(JSON.stringify(row)))
		},
		add(){
			mitt.emit('openAdministerTypeAdd')
		},
		deleted(id){
			this.$confirm('删除标签, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteAdministerTypeDict(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		async init(){
			mitt.off('openAdministerTypeEdit')
			mitt.off('openAdministerTypeAdd')
			try{
				var data = {"nameEn":"governance_type"}
				let res = await listAdministerTypeList(data)
				this.governanceTypeList = res.data.result
				let status_res = await listDictByNameEn('common_status')
        		this.statusList = status_res.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
