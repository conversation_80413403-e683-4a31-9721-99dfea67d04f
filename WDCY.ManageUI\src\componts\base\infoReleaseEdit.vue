<template>
	<el-dialog draggable width="50%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" :model="infoReleaseModel" label-width="50px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="标题" prop="title">
						<el-input v-model="infoReleaseModel.title" placeholder="标题"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="类型" prop="type">
						  <el-select style="width: 100%;" v-model="infoReleaseModel.type" class="m-2" placeholder="类型">
						    <el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						  </el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<div id="editor">
					
				</div>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import Editor from "wangeditor"
import { addInfoRelease,editInfoRelease } from "@/api/base/infoRelease"
import mitt from "@/utils/mitt";
export default {
	props:['typeList'],
	data() {
		return {
			loading: false,
			infoReleaseModel: {},
			dialog:{},
			rules: {
				a: [{
					required: true,
					message: '请输入用户名',
					trigger: 'blur',  
				}]
			}
		}
	},
	methods: {
		onSubmit(){
			this.infoReleaseModel.content = editor.txt.html()
			if(this.infoReleaseModel.id == 0){
				addInfoRelease(this.infoReleaseModel)
				.then(res =>{
					this.$message.success(res.data.msg)
					this.$emit("search")
					this.dialog.show = false
				})
			}else{
				editInfoRelease(this.infoReleaseModel)
				.then(res =>{
					this.$message.success(res.data.msg)
					this.$emit("search")
					this.dialog.show = false
				})
			}
		}
	},
	mounted(){
		this.$nextTick(function() {
			mitt.on('openInfoReleaseEdit', (infoRelease) => {
				this.infoReleaseModel = infoRelease
				setTimeout(function(){
					this.editor = new Editor("#editor")
					editor.config.pasteFilterStyle = false
					editor.create()
					editor.txt.html(infoRelease.content)
				},50)
				this.dialog.show = true
				this.dialog.title = "修改信息"
			})
			mitt.on('openInfoReleaseAdd', () => {
				this.infoReleaseModel = {
					id:0
				}
				setTimeout(function(){
					this.editor = new Editor("#editor")
					editor.config.pasteFilterStyle = false
					editor.create()
				},50)
				this.dialog.show = true
				this.dialog.title = "发布信息"
			})
		})
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
	}
	#{
		max-width: 400px;
	}
</style>
