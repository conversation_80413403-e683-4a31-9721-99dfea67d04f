<template>
  <canvas
    style="position: absolute; left: -9999px"
    ref="canvas"
    id="canvas"
    width="640"
    height="480"
  ></canvas>

  <!-- v-loading="true" element-loading-text="读取中..." element-loading-background="#e6e94452"  -->
  <el-dialog
    draggable
    width="50%"
    v-loading="loading"
    v-model="dialog.show"
    destroy-on-close
    :title="dialog.title"
  >
    <section v-if="loadingAnimas" @click="closeLoading">
      <div class="dots">
        <span style="--i: 1"></span>
        <span style="--i: 2"></span>
        <span style="--i: 3"></span>
        <span style="--i: 4"></span>
        <span style="--i: 5"></span>
        <span style="--i: 6"></span>
        <span style="--i: 7"></span>
        <span style="--i: 8"></span>
        <span style="--i: 9"></span>
        <span style="--i: 10"></span>
        <span style="--i: 11"></span>
        <span style="--i: 12"></span>
        <span style="--i: 13"></span>
        <span style="--i: 14"></span>
        <span style="--i: 15"></span>
        <text
          style="
            position: absolute;
            left: calc(50% - 66px);
            top: calc(50% + 58px);
            color: yellow;
            font-size: larger;
          "
          >正在等待读卡器识别...</text
        >
      </div>
    </section>
    <el-form :rules="rules" ref="form" :model="personModel" label-width="80px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基础信息" name="first">
          <el-row>
            <el-col :span="8">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model="personModel.name"
                  placeholder="姓名"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="生日" prop="birthday">
                <!-- <el-input v-model="personModel.age" placeholder="生日"></el-input> -->
                <el-date-picker
                  :disabled="personModel.certificateType == 'IDCARD'"
                  value-format="YYYY-MM-DD"
                  v-model="personModel.birthday"
                  type="date"
                  placeholder="请选择生日"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="性别" prop="sex">
                <el-select
                  style="width: 100%"
                  v-model="personModel.sex"
                  placeholder="性别"
                >
                  <el-option
                    v-for="item in sexList"
                    :key="item.nameEn"
                    :label="item.nameCn"
                    :value="parseInt(item.nameEn)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="联系电话" prop="phone">
                <el-input
                  v-model="personModel.phone"
                  placeholder="联系电话"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="住址" prop="address">
                <el-input
                  v-model="personModel.address"
                  placeholder="住址"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="籍贯" prop="nativePlace">
                <el-input
                  v-model="personModel.nativePlace"
                  placeholder="籍贯"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="证件类型" prop="certificateType">
                <el-select
                  clearable
                  style="width: 100%"
                  v-model="personModel.certificateType"
                  placeholder="选择证件类型"
                >
                  <el-option
                    v-for="item in certificateTypeTagList"
                    :key="item.nameEn"
                    :label="item.nameCn"
                    :value="item.nameEn"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件号" prop="idCard">
                <el-input
                  v-model="personModel.idCard"
                  placeholder="证件号"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="标签" prop="tags">
                <el-select
                  clearable
                  style="width: 100%"
                  v-model="personModel.tags"
                  collapse-tags
                  multiple
                  placeholder="选择标签"
                >
                  <el-option
                    v-for="item in tagList"
                    :key="item.nameEn"
                    :label="item.nameCn"
                    :value="item.nameEn"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="开放式ID" prop="openId">
                <el-input
                  readonly
                  disabled
                  v-model="personModel.openId"
                  placeholder="人员开放式ID"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="卡号" prop="cardNumber">
                <el-input
                  v-if="disabled"
                  disabled
                  v-model="personModel.cardNumber"
                  placeholder="门禁卡号"
                ></el-input>
                <el-input
                  v-else
                  @focus="cardNumberFocus(2)"
                  @blur="cardBlur"
                  v-model="personModel.cardNumber"
                  placeholder="门禁卡号"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="状态" prop="sex">
                <el-select
                  disabled
                  style="width: 100%"
                  v-model="personModel.status"
                  placeholder="状态"
                >
                  <el-option
                    v-for="item in statusList"
                    :key="item.nameEn"
                    :label="item.nameCn"
                    :value="parseInt(item.nameEn)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="note">
                <el-input
                  v-model="personModel.note"
                  maxlength="200"
                  placeholder="请简单说明人员情况"
                  show-word-limit
                  type="textarea"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label-width="0">
                <el-card class="box-card">
                  <template #header>
                    <div style="display: flex; justify-content: space-between">
                      <span>照片</span>
                      <div>
                        <el-button
                          @click="uploadImg"
                          class="button"
                          type="text"
                        >
                          <input
                            style="
                              position: fixed;
                              left: -9999px;
                              display: none;
                            "
                            type="file"
                            accept="image/*"
                            id="imgReader"
                            @change="loadingImg"
                            :value="upload_input"
                          />裁剪上传
                          <!-- <el-button @click="uploadImg">上传</el-button> -->
                        </el-button>
                        <el-button class="button" type="text">
                          <el-upload
                            :show-file-list="false"
                            :http-request="loadingImg2"
                            :accept="acceptRule"
                            >原图上传</el-upload
                          >
                        </el-button>
                        <el-button
                          @click="callCamera"
                          class="button"
                          type="text"
                          >采集</el-button
                        >
                        <!--canvas截取流-->
                        <!--图片展示-->
                        <!--确认-->
                        <el-button
                          class="button"
                          type="text"
                          @click="deletePhoto"
                          >删除</el-button
                        >
                      </div>
                    </div>
                  </template>
                  <el-image
                    id="canvas"
                    fit="contain"
                    style="height: 200px; width: 200px"
                    :preview-src-list="[imgServer + personModel.photo]"
                    :src="imgServer + personModel.photo"
                  >
                    <template
                      v-if="
                        personModel.photo == undefined ||
                        personModel.photo == null ||
                        personModel.photo == ''
                      "
                      #error
                    >
                      <div
                        style="
                          line-height: 200px;
                          text-align: center;
                          background-color: #eee;
                          color: #999;
                        "
                      >
                        暂无数据
                      </div>
                    </template>
                  </el-image>
                </el-card>
              </el-form-item>
            </el-col>
          </el-row>

          <el-dialog
            draggable
            width="50%"
            v-loading="loading"
            v-model="videoDialog.show"
            destroy-on-close
            :title="videoDialog.title"
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              "
            >
              <video ref="video" width="640" height="480" autoplay></video>
              <el-button
                size="default"
                style="width: 120px; margin-top: 15px"
                type="primary"
                @click="photograph"
                >确认</el-button
              >
            </div>
          </el-dialog>

          <el-dialog draggable v-model="viewPic" title="裁剪头像">
            <div style="display: flex">
              <img id="cropImg" style="width: 300px; height: 300px" />
            </div>
            <div>
              <div class="previewText">裁剪预览</div>
              <div style="display: flex">
                <div style="height: 100px; width: 100px; position: relative">
                  <div class="previewBox"></div>
                </div>
                <div
                  style="
                    height: 100px;
                    width: 100px;
                    position: relative;
                    border-radius: 50%;
                    overflow: hidden;
                  "
                >
                  <div class="previewBoxRound"></div>
                </div>
              </div>
              <div style="display: flex; margin-top: 10px">
                <el-button type="primary" @click="GetData">确认</el-button>
              </div>
            </div>
          </el-dialog>
        </el-tab-pane>
        <!-- <person-photo :personModel="personModel"></person-photo> -->
      </el-tabs>
    </el-form>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        :loading="loadingSubmit"
        >提 交</el-button
      >
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="readCard"
        >读取身份证</el-button
      >
    </el-row>
  </el-dialog>

  <el-dialog
    draggable
    width="25%"
    v-model="cardNumDialog.show"
    :title="cardNumDialog.title"
  >
    <el-form ref="formcard" :model="personModel" label-width="80px">
      <el-form-item label="旧卡号">
        <el-input
          readonly
          v-model="updateCard.cardNumber"
          placeholder="旧卡号"
        ></el-input>
      </el-form-item>
      <el-form-item label="新卡号">
        <el-input
          @focus="cardNumberFocus(1)"
          @blur="cardBlur"
          v-model="newCardNumber"
          placeholder="新卡号"
        ></el-input>
      </el-form-item>
      <div style="padding-left: 30px">
        <el-checkbox v-model="hexadecimal" label="十六进制" size="large" />
        <el-checkbox v-model="oneKeyCopy" label="一键复制" size="large" />
      </div>

      <el-row justify="center">
        <el-button
          type="primary"
          style="width: 100px; height: 30px; margin-top: 20px"
          @click="onCardSubmit"
          >提 交</el-button
        >
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
import { editPerson, addPerson, editCard } from "@/api/base/person";
import personPhoto from "@/componts/personPhoto/personPhoto.vue";
import mitt from "@/utils/mitt";
import { fileUpload } from "@/api/admin/file";
import "cropperjs/dist/cropper.css";
import Cropper from "cropperjs";
import CROPPER from "../../utils/myUtils.js";
import { truncate } from "lodash";
import { pushUser } from "@/api/admin/auth";
export default {
  components: { personPhoto },
  props: ["statusList", "tagList", "certificateTypeTagList", "sexList"],
  data() {
    return {
      newCardNumber: "",
      loadingSubmit: false,
      updateCard: {},
      disabled: true,
      viewPic: false,
      loading: false,
      personModel: {},
      activeName: "first",
      upload_input: "",
      imgServer: import.meta.env.VITE_BASE_API,
      dialog: {},
      cardNumDialog: {},
      videoDialog: {},
      rules: {
        name: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            required: true,
            message: "请输入身份信息",
            trigger: "blur",
          },
        ],
        phone: [
          {
            required: true,
            message: "请输入联系方式",
            trigger: "blur",
          },
        ],
        certificateType: [
          {
            required: true,
            message: "请选择证件类型",
            trigger: "change",
          },
        ],
        // birthday: [
        //   {
        //     required: true,
        //     message: "请选择生日",
        //     trigger: "blur"
        //   }
        // ]
      },
      readCardObj: "",
      hexadecimal: false,
      oneKeyCopy: false,
      // CROPPER: CROPPER,
      loadingAnimas: false,
      timer: null,
      acceptRule: "image/jpeg,image/jpg,image/png",
    };
  },
  methods: {
    //用户操作
    pushUserAction(actionName) {
      if (actionName != "home") {
        let senObj = {
          oper: actionName,
          receiveClient: "all",
          tags: ["manager"],
        };
        pushUser(senObj).then((res) => {
          if (res !== null && res.code === 0) {
          } else {
          }
        });
      }
    },
    callCamera() {
      // H5调用电脑摄像头API
      this.videoDialog.show = true;
      this.videoDialog.title = "拍摄头像";
      navigator.mediaDevices
        .getUserMedia({
          video: true,
        })
        .then((success) => {
          // 摄像头开启成功
          console.log(this.$refs);
          console.log(this.$refs["video"]);
          this.$refs["video"].srcObject = success;
          // 实时拍照效果
          this.$refs["video"].play();
        })
        .catch((error) => {
          console.error("摄像头开启失败，请检查摄像头是否可用！");
          this.$message.error("摄像头开启失败，请检查摄像头是否可用！");
        });
    },
    //拍照

    photograph() {
      this.videoDialog.show = false;
      let ctx = this.$refs["canvas"].getContext("2d");
      // 把当前视频帧内容渲染到canvas上
      ctx.drawImage(this.$refs["video"], 0, 0, 640, 480);
      // 转base64格式、图片格式转换、图片质量压缩
      let imgBase64 = this.$refs["canvas"].toDataURL("image/jpeg", 1.0);

      document.querySelector("#canvas").src = imgBase64;

      const image = document.getElementById("canvas");
      image.toBlob(
        (blob) => {
          //然后调用浏览器原生的toBlob方法将canvas数据转换成blob数据

          //之后就可以愉快的将blob数据发送至后端啦，可根据自己情况进行发送，我这里用的是axios
          const form = new FormData();
          // 第三个参数为文件名，可选填.
          form.append("file", blob, "example.jpg");
          form.append("modulesName", "base");
          form.append("functionName", "person");
          form.append("communityId", localStorage.getItem("communityId"));
          fileUpload(form).then((res) => {
            this.personModel.photo = res.data.result.url;
            if (res.data.code == 0) {
              this.$message.success("上传成功");
              this.closeCamera();
            }
          });
        },
        "image/jpeg",
        0.95
      );
    },
    //关闭摄像头
    closeCamera() {
      console.log("进入", "··················");
      if (!this.$refs["video"].srcObject) {
        this.dialogCamera = false;
        return;
      }
      let stream = this.$refs["video"].srcObject;
      let tracks = stream.getTracks();
      tracks.forEach((track) => {
        track.stop();
      });
      this.$refs["video"].srcObject = null;
    },
    loadingImg(eve) {
      this.upload_input = eve.target.files[0];
      this.viewPic = truncate;
      //读取上传文件
      let reader = new FileReader();
      if (event.target.files[0]) {
        //readAsDataURL方法可以将File对象转化为data:URL格式的字符串（base64编码）
        reader.readAsDataURL(eve.target.files[0]);
        reader.onload = (e) => {
          let dataURL = reader.result;
          //将img的src改为刚上传的文件的转换格式
          console.log(document.querySelector("#cropImg"));
          document.querySelector("#cropImg").src = dataURL;

          const image = document.getElementById("cropImg");
          //创建cropper实例-----------------------------------------
          let CROPPER = new Cropper(image, {
            // aspectRatio: 16 / 16,
            initialAspectRatio: 2 / 3,
            viewMode: 1,
            autoCropArea: 0.95,
            minCanvasWidth: 100,
            minCanvasHeight: 100,
            // minContainerWidth:500,
            // minContainerHeight:500,
            dragMode: "move",
            preview: [
              document.querySelector(".previewBox"),
              // document.querySelector(".previewBoxRound"),
            ],
          });
          this.CROPPER = CROPPER;
        };
      }
      this.upload_input = "";
    },

    // 原图上传
    loadingImg2(files) {
      let form = new FormData();
      form.append("file", files.file);
      form.append("modulesName", "base");
      form.append("functionName", "person");
      form.append("communityId", localStorage.getItem("communityId"));
      fileUpload(form).then((res) => {
        this.personModel.photo = res.data.result.url;
        if (res.data.code == 0) {
          this.$message.success("上传成功");
        }
      });
    },

    // 激活裁剪上传
    uploadImg() {
      document.querySelector("#imgReader").click();
      if (this.CROPPER) {
        this.CROPPER.destroy();
      }
    },
    GetData() {
      this.viewPic = false;
      //getCroppedCanvas方法可以将裁剪区域的数据转换成canvas数据
      this.CROPPER.getCroppedCanvas({
        maxWidth: 480,
        maxHeight: 480,
        fillColor: "#fff",
        imageSmoothingEnabled: true,
        imageSmoothingQuality: "high",
      }).toBlob(
        (blob) => {
          //然后调用浏览器原生的toBlob方法将canvas数据转换成blob数据

          //之后就可以愉快的将blob数据发送至后端啦，可根据自己情况进行发送，我这里用的是axios
          const form = new FormData();
          // 第三个参数为文件名，可选填.
          form.append("file", blob, "example.jpg");
          form.append("modulesName", "base");
          form.append("functionName", "person");
          form.append("communityId", localStorage.getItem("communityId"));
          fileUpload(form).then((res) => {
            this.personModel.photo = res.data.result.url;
            if (res.data.code == 0) {
              this.$message.success("上传成功");
            }
          });
        },
        "image/jpeg",
        0.95
      );
    },
    deletePhoto() {
      this.personModel.photo = null;
    },
    // 读卡功能
    readCard() {
      this.loadingAnimas = true;
      navigator.clipboard.writeText(
        '{"ReadMode":"PERSONIDCARD","DeviceType":"iDR210"}'
      );
      this.timer = setInterval(async () => {
        try {
          const text = await navigator.clipboard.readText();
          const readCardObj = JSON.parse(text);
          console.log(readCardObj);
          if (!readCardObj.IDCode) {
            console.log(1);
            return;
          } else {
            console.log(2);
            this.loadingAnimas = false;
            this.personModel.name = readCardObj.Name;
            this.personModel.idCard = readCardObj.IDCode;
            navigator.clipboard.writeText("");
            this.$message.success("读取成功");
          }
        } catch (error) {
          console.log(error);
        }
        this.loadingAnimas = false;
        clearTimeout(this.timer);
      }, 1000);
    },

    //  关闭身份证加载动画
    closeLoading() {
      this.loadingAnimas = false;
      clearTimeout(this.timer);
    },

    // newCardNumber 为1 则读取新卡，为2时读取身份证
    cardNumberFocus(newCardNumber) {
      if (newCardNumber == 1 && this.oneKeyCopy != true) {
        return;
      }
      // 剪切板写入
      navigator.clipboard.writeText(
        '{"ReadMode":"ICSN","DeviceType":"iDR210"}'
      );
      this.timer = setInterval(async () => {
        try {
          const text = await navigator.clipboard.readText();
          const readCardObj = JSON.parse(text);
          if (!readCardObj.IDCode) {
            return;
          } else {
            if (newCardNumber == 1) {
              this.newCardNumber = readCardObj.IDCode;
            }
            if (newCardNumber == 2) {
              this.personModel.cardNumber = readCardObj.IDCode;
            }
            navigator.clipboard.writeText("");
          }
        } catch (error) {}
      }, 500);
    },
    // 获取焦点,读取门禁卡
    cardBlur() {
      clearInterval(this.timer);
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.loadingSubmit = true;
          if (this.personModel.birthday) {
            this.personModel.birthday = this.personModel.birthday.substr(0, 10);
          }
          if (this.personModel.tags) {
            this.personModel.tags = JSON.stringify(this.personModel.tags);
          }
          if (this.personModel.id == 0) {
            this.personModel.communityId = localStorage.getItem("communityId");
            addPerson(this.personModel)
              .then((res) => {
                this.$message.success(res.data.msg);
                this.$emit("search");
                this.dialog.show = false;
                this.loadingSubmit = false;
              })
              .catch((error) => {
                // 如果 Promise 被拒绝（rejected），在这里处理错误
                this.loadingSubmit = false;
              });
          } else {
            editPerson(this.personModel)
              .then((res) => {
                this.$message.success(res.data.msg);
                this.$emit("search");
                this.dialog.show = false;
                this.loadingSubmit = false;
              })
              .catch((error) => {
                // 如果 Promise 被拒绝（rejected），在这里处理错误
                this.loadingSubmit = false;
              });
          }
          if (this.personModel.tags) {
            this.personModel.tags = JSON.parse(this.personModel.tags);
          }
        }
      });
    },
    // 换卡提交
    onCardSubmit() {
      this.updateCard.newCardNumber = this.newCardNumber;
      this.newCardNumber = "";
      this.$refs["formcard"].validate((valid) => {
        if (valid) {
          editCard(this.updateCard).then((res) => {
            this.$message.success(res.data.msg);
            this.$emit("search");
            this.cardNumDialog.show = false;
          });
        }
      });
    },
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openPersonEdit", (data) => {
        if (data.person.tags) {
          data.person.tags = JSON.parse(data.person.tags);
        }
        this.pushUserAction("人员编辑");
        this.personModel = data.person;
        this.dialog.show = true;
        this.dialog.title = "修改信息";
        this.disabled = true;
      });
      mitt.on("openPersonAdd", () => {
        this.personModel = {
          id: 0,
          communityId: localStorage.getItem("communityId"),
        };
        this.pushUserAction("人员新增");
        // 进入添加页面 默认值
        this.personModel.sex = Number(this.sexList[0].nameEn);
        this.personModel.status = 1;
        this.personModel.certificateType = "IDCARD";
        this.dialog.show = true;
        this.dialog.title = "添加人员";
        this.disabled = false;
      });
      mitt.on("openCardEdit", (data) => {
        this.updateCard.cardNumber = data.cardNumber;
        // this.newCardNumber = data.cardNumber;
        this.updateCard.personId = data.id;
        this.cardNumDialog.show = true;
        this.cardNumDialog.title = "修改卡号";
      });
    });
  },
  watch: {
    "dialog.show"(newVal, oldVal) {
      if (!newVal) {
        this.pushUserAction("人员管理");
      } else {
        this.activeName = "first";
      }
    },
  },
};
</script>
<style scoped>
section {
  z-index: 99999;
  position: absolute;
  display: flex;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  align-items: center;
  justify-content: center;
  background: #00000082;
}

section .dots span {
  position: absolute;
  height: 10px;
  width: 10px;
  background-color: rgb(7, 241, 132);
  border-radius: 50%;
  transform: rotate(calc(var(--i) * (360deg / 15))) translateY(35px);
  animation: animate 1.5s linear infinite;
  animation-delay: calc(var(--i) * 0.1s);
  opacity: 0;
}

@keyframes animate {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.upload {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed var(--el-color-primary);
  flex-direction: column;
  border-radius: 5px;
}

.upload_1 {
  border: 0;
}

.box-card {
  width: 100%;
}

.previewBox {
  background-color: #eee;
  box-shadow: 0 0 5px #adadad;
  width: 100px;
  height: 100px;
  position: absolute;
  left: 50%;
  /* top: 50%; */
  margin-left: -50px;
  margin-top: -50px;
  margin-right: 10px;
  margin-top: 10px;
  overflow: hidden;
}

#cropImg {
  height: 300px;
  width: 300px;
  display: block;
  overflow: hidden;
  box-shadow: 0 0 5px #adadad;
}

.previewText {
  margin-top: 10px;
}
.el-button .custom-loading .circular {
  margin-right: 6px;
  width: 18px;
  height: 18px;
  animation: loading-rotate 2s linear infinite;
}
.el-button .custom-loading .circular .path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: var(--el-button-text-color);
  stroke-linecap: round;
}
</style>
