var e,t=new Uint8Array(16);function s(){if(!e&&!(e="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return e(t)}var n=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function o(e){return"string"==typeof e&&n.test(e)}for(var i=[],r=0;r<256;++r)i.push((r+256).toString(16).substr(1));function a(e,t,n){var r=(e=e||{}).random||(e.rng||s)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var a=0;a<16;++a)t[n+a]=r[a];return t}return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=(i[e[t+0]]+i[e[t+1]]+i[e[t+2]]+i[e[t+3]]+"-"+i[e[t+4]]+i[e[t+5]]+"-"+i[e[t+6]]+i[e[t+7]]+"-"+i[e[t+8]]+i[e[t+9]]+"-"+i[e[t+10]]+i[e[t+11]]+i[e[t+12]]+i[e[t+13]]+i[e[t+14]]+i[e[t+15]]).toLowerCase();if(!o(s))throw TypeError("Stringified UUID is invalid");return s}(r)}const l="function"==typeof atob,c="function"==typeof btoa,d="function"==typeof Buffer,u="function"==typeof TextDecoder?new TextDecoder:void 0,h="function"==typeof TextEncoder?new TextEncoder:void 0,b=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),p=(e=>{let t={};return b.forEach(((e,s)=>t[e]=s)),t})(),f=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,m=String.fromCharCode.bind(String),g="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):(e,t=(e=>e))=>new Uint8Array(Array.prototype.slice.call(e,0).map(t)),_=e=>e.replace(/=/g,"").replace(/[+\/]/g,(e=>"+"==e?"-":"_")),w=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),C=e=>{let t,s,n,o,i="";const r=e.length%3;for(let r=0;r<e.length;){if((s=e.charCodeAt(r++))>255||(n=e.charCodeAt(r++))>255||(o=e.charCodeAt(r++))>255)throw new TypeError("invalid character found");t=s<<16|n<<8|o,i+=b[t>>18&63]+b[t>>12&63]+b[t>>6&63]+b[63&t]}return r?i.slice(0,r-3)+"===".substring(r):i},S=c?e=>btoa(e):d?e=>Buffer.from(e,"binary").toString("base64"):C,R=d?e=>Buffer.from(e).toString("base64"):e=>{let t=[];for(let s=0,n=e.length;s<n;s+=4096)t.push(m.apply(null,e.subarray(s,s+4096)));return S(t.join(""))},y=(e,t=!1)=>t?_(R(e)):R(e),k=e=>{if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?m(192|t>>>6)+m(128|63&t):m(224|t>>>12&15)+m(128|t>>>6&63)+m(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return m(240|t>>>18&7)+m(128|t>>>12&63)+m(128|t>>>6&63)+m(128|63&t)},v=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,q=e=>e.replace(v,k),I=d?e=>Buffer.from(e,"utf8").toString("base64"):h?e=>R(h.encode(e)):e=>S(q(e)),P=(e,t=!1)=>t?_(I(e)):I(e),x=e=>P(e,!0),E=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,z=e=>{switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return m(55296+(t>>>10))+m(56320+(1023&t));case 3:return m((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return m((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},U=e=>e.replace(E,z),D=e=>{if(e=e.replace(/\s+/g,""),!f.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,s,n,o="";for(let i=0;i<e.length;)t=p[e.charAt(i++)]<<18|p[e.charAt(i++)]<<12|(s=p[e.charAt(i++)])<<6|(n=p[e.charAt(i++)]),o+=64===s?m(t>>16&255):64===n?m(t>>16&255,t>>8&255):m(t>>16&255,t>>8&255,255&t);return o},A=l?e=>atob(w(e)):d?e=>Buffer.from(e,"base64").toString("binary"):D,T=d?e=>g(Buffer.from(e,"base64")):e=>g(A(e),(e=>e.charCodeAt(0))),W=e=>T(F(e)),O=d?e=>Buffer.from(e,"base64").toString("utf8"):u?e=>u.decode(T(e)):e=>U(A(e)),F=e=>w(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),M=e=>O(F(e)),J=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),L=function(){const e=(e,t)=>Object.defineProperty(String.prototype,e,J(t));e("fromBase64",(function(){return M(this)})),e("toBase64",(function(e){return P(this,e)})),e("toBase64URI",(function(){return P(this,!0)})),e("toBase64URL",(function(){return P(this,!0)})),e("toUint8Array",(function(){return W(this)}))},B=function(){const e=(e,t)=>Object.defineProperty(Uint8Array.prototype,e,J(t));e("toBase64",(function(e){return y(this,e)})),e("toBase64URI",(function(){return y(this,!0)})),e("toBase64URL",(function(){return y(this,!0)}))},Z={version:"3.7.2",VERSION:"3.7.2",atob:A,atobPolyfill:D,btoa:S,btoaPolyfill:C,fromBase64:M,toBase64:P,encode:P,encodeURI:x,encodeURL:x,utob:q,btou:U,decode:M,isValid:e=>{if("string"!=typeof e)return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fromUint8Array:y,toUint8Array:W,extendString:L,extendUint8Array:B,extendBuiltins:()=>{L(),B()}};const N=new class{constructor(){this.oBase64=Z}browser(){const e=navigator.userAgent.toLowerCase(),t=/(edge)[/]([\w.]+)/.exec(e)||/(chrome)[/]([\w.]+)/.exec(e)||/(safari)[/]([\w.]+)/.exec(e)||/(opera)(?:.*version)?[/]([\w.]+)/.exec(e)||/(trident.*rv:)([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+))?/.exec(e)||["unknow","0"];t.length>0&&t[1].indexOf("trident")>-1&&(t[1]="msie");const s={};return s[t[1]]=!0,s.version=t[2],s}getCreateWndMode(){const e=navigator.userAgent,t=navigator.platform,s="Win64"===t||"Win32"===t||"Windows"===t,n=this.browser();let o=!0;return window.top!==window?o=!1:s?(e.indexOf("Windows NT 10.0")>-1&&n.mozilla&&(o=!1),n.edge&&(o=!1)):o=!1,o}getWndPostion(e,t,s,n){let o=0,i=0;const r=e.ownerDocument.defaultView,a=e.getBoundingClientRect(),l=window.getComputedStyle(e),c={top:a.top+parseInt(l["border-top-width"].slice(0,-2),10),left:a.left+parseInt(l["border-left-width"].slice(0,-2),10)},d=this.getDevicePixelRatio();if(t)if(this.browser().msie){const e=r.outerWidth-r.innerWidth-(r.screenLeft-r.screenX);o=c.left+(r.screenLeft-r.screenX)-e,i=c.top+(r.screenTop-r.screenY)}else{let e=0,t=0;const s=this.browser().chrome?r.outerWidth/d:r.outerWidth,n=this.browser().chrome?r.outerHeight/d:r.outerHeight,a=Math.round((s-r.innerWidth)/2);this.isWindows()&&this.browser().chrome&&(-8===a||r.screen.height-r.outerHeight==0?-8===a&&(e=8,t=8):8===a?e=-5:0===a&&(t=8)),this.browser().mozilla&&(7===a||6===a?e=-6:8===a&&(e=-8)),o=c.left+a+e,i=c.top+(n-r.innerHeight-a)+t}else{const e=window.top;let t=0,r=0,a=0,l=0;try{t=e.outerWidth-e.innerWidth,r=e.outerHeight-e.innerHeight,a=e.screenLeft-e.screenX,l=e.screenTop-e.screenY}catch(e){t=s.outerWidth-s.innerWidth,r=s.outerHeight-s.innerHeight,a=s.screenLeft-s.screenX,l=s.screenTop-s.screenY}if(this.browser().msie){let e=t-a;e=0,o=c.left+a-e,i=c.top+l}else{const e=t/2;o=c.left+e,i=c.top+(r-e),this.isWindows()&&this.browser().chrome&&0===e&&(o+=8,i+=8)}o+=n.left,i+=n.top}return this.isWindows()&&(this.browser().chrome||this.browser().safari)&&(o=c.left,i=c.top,o+=n.left,i+=n.top),this.browser().msie&&"10.0"===this.browser().version&&(o+=r.pageXOffset,i+=r.pageYOffset),o=Math.round(o*d),i=Math.round(i*d),{left:o,top:i}}detectPort(e,t,s){if(e===t)return void s.success(e);const n=`HikCentralWebControlPort:${e}-${t}`,o=this;let i=0,r=!1,a=null;sessionStorage&&(a=sessionStorage.getItem(n),null!==a&&(a=parseInt(a,10)));const l=[];for(let s=e;s<=t;s++)s!==a&&l.push(s);null!==a&&l.unshift(a);const c=[],d=function(){i>0&&clearTimeout(i)},u=function(){for(let e=0,t=c.length;e<t;e++)delete c[e]};let h=0;const b=function(e){sessionStorage&&sessionStorage.setItem(n,e),!r&&s.success&&(d(),u(),s.success(e))},p=(new Date).getTime();for(let e=0,t=l.length;e<t;e++)setTimeout((()=>{c.push(o.createImageHttp(l[e],{timeStamp:p+e,success(e){b(e)},error(){h++,l.length===h&&!r&&s.error&&(d(),u(),s.error())}}))}),100);i=setTimeout((()=>{r=!0,s.error&&(u(),s.error())}),6e4)}createImageHttp(e,t){const s=new Image;return s.crossOrigin="anonymous",s.onload=function(){t.success&&t.success(e)},s.onerror=function(){t.error&&t.error()},s.onabort=function(){t.abort&&t.abort()},s.src=`http://127.0.0.1:${e}/imghttp/local?update=${t.timeStamp}`,s}utf8to16(e){let t="",s=0;const n=e.length;let o,i,r;for(;s<n;)switch(o=e.charCodeAt(s++),o>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:t+=e.charAt(s-1);break;case 12:case 13:i=e.charCodeAt(s++),t+=String.fromCharCode((31&o)<<6|63&i);break;case 14:i=e.charCodeAt(s++),r=e.charCodeAt(s++),t+=String.fromCharCode((15&o)<<12|(63&i)<<6|(63&r)<<0)}return t}createEventScript(e,t,s){const n=document.createElement("script");n.htmlFor=e,n.event=t,n.innerHTML=s,document.getElementById(e).appendChild(n)}isMacOS(){return"MacIntel"===navigator.platform}isWindows(){return navigator.platform.indexOf("Win")>-1}getDevicePixelRatio(){let e=1;return this.isMacOS()||(e=window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI),e}Base64(){return this.oBase64||{}}},$="　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　";function H(e){window.top.document.title=window.top.document.title+$+e}function V(e){window.top.document.title=window.top.document.title.replace($+e,"")}function G(e,t){(t||H)(e)}function j(e,t){(t||V)(e)}const K=class{constructor(e){this.oOptions=Object.assign({iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null},e),this.oWebSocket=null,this.szUUID="",this.szVersion="",this.oRequestList={},this.bNormalClose=!1,this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}init(){const e=this,t=function(){e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose),e.bNormalClose=!1};e.oWebSocket=new WebSocket(`ws://127.0.0.1:${e.oOptions.iPort}`),e.oWebSocket.onerror=function(){},e.oWebSocket.onopen=function(){const t={sequence:a(),cmd:"system.connect"},s=JSON.stringify(t);e.oWebSocket.send(s)},e.oWebSocket.onmessage=function(t){const s=t.data,n=JSON.parse(s),o=n.sequence;void 0===o&&void 0===n.cmd?(e.szUUID=n.uuid,e.szVersion=n.version,e.oOptions.cbConnectSuccess&&e.oOptions.cbConnectSuccess()):void 0!==n.cmd?e.parseCmd(n):void 0!==e.oRequestList[o]&&(0===n.errorModule&&0===n.errorCode?e.oRequestList[o].resolve(n):e.oRequestList[o].reject(n),delete e.oRequestList[o])},e.oWebSocket.onclose=function(){e.oWebSocket=null,N.browser().mozilla?setTimeout((()=>{t()}),100):t()}}setWindowControlCallback(e){this.oWindowControlCallback=e}setSadpCallback(e){this.oSadpCallback=e}setSliceCallback(e){this.oSliceCallback=e}setSerialCallback(e){this.oSerialCallback=e}setUIControlCallback(e){this.oUIControlCallback=e}setUpgradeCallback(e){this.oUpgradeCallback=e}getServiceVersion(){return this.szVersion}getRequestUUID(){return this.szUUID}disconnect(){this.bNormalClose=!0,this.oWebSocket&&WebSocket.OPEN===this.oWebSocket.readyState&&(this.oWebSocket.close(),delete this.oWebSocket)}sendRequest(e){const t=this;return new Promise(((s,n)=>{const o=a();e.sequence=o,t.oRequestList[o]={resolve:s,reject:n},e.uuid=t.szUUID,e.timestamp=`${(new Date).getTime()}`;const i=JSON.stringify(e);t.oWebSocket&&WebSocket.OPEN===t.oWebSocket.readyState?t.oWebSocket.send(i):n()}))}parseCmd(e){const t=e.cmd.split("."),s=t[1].replace(/^[a-z]{1}/g,(e=>e.toUpperCase()));"window"===t[0]||"play"===t[0]?this.oWindowControlCallback[`cb${s}`]&&this.oWindowControlCallback[`cb${s}`](e):"sadp"===t[0]?this.oSadpCallback[`cb${s}`]&&this.oSadpCallback[`cb${s}`](e):"serial"===t[0]?this.oSerialCallback[`cb${s}`]&&this.oSerialCallback[`cb${s}`](e):"slice"===t[0]?this.oSliceCallback[`cb${s}`]&&this.oSliceCallback[`cb${s}`](e):"ui"===t[0]?this.oUIControlCallback[`cb${s}`]&&this.oUIControlCallback[`cb${s}`](e):"upgrade"===t[0]&&this.oUpgradeCallback[`cb${s}`]&&this.oUpgradeCallback[`cb${s}`](e)}};
/*! pako 2.0.4 https://github.com/nodeca/pako @license (MIT AND Zlib) */function X(e){let t=e.length;for(;--t>=0;)e[t]=0}const Y=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),Q=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),ee=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),te=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),se=new Array(576);X(se);const ne=new Array(60);X(ne);const oe=new Array(512);X(oe);const ie=new Array(256);X(ie);const re=new Array(29);X(re);const ae=new Array(30);function le(e,t,s,n,o){this.static_tree=e,this.extra_bits=t,this.extra_base=s,this.elems=n,this.max_length=o,this.has_stree=e&&e.length}let ce,de,ue;function he(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}X(ae);const be=e=>e<256?oe[e]:oe[256+(e>>>7)],pe=(e,t)=>{e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255},fe=(e,t,s)=>{e.bi_valid>16-s?(e.bi_buf|=t<<e.bi_valid&65535,pe(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=s-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=s)},me=(e,t,s)=>{fe(e,s[2*t],s[2*t+1])},ge=(e,t)=>{let s=0;do{s|=1&e,e>>>=1,s<<=1}while(--t>0);return s>>>1},_e=(e,t,s)=>{const n=new Array(16);let o,i,r=0;for(o=1;o<=15;o++)n[o]=r=r+s[o-1]<<1;for(i=0;i<=t;i++){let t=e[2*i+1];0!==t&&(e[2*i]=ge(n[t]++,t))}},we=e=>{let t;for(t=0;t<286;t++)e.dyn_ltree[2*t]=0;for(t=0;t<30;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0},Ce=e=>{e.bi_valid>8?pe(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0},Se=(e,t,s,n)=>{const o=2*t,i=2*s;return e[o]<e[i]||e[o]===e[i]&&n[t]<=n[s]},Re=(e,t,s)=>{const n=e.heap[s];let o=s<<1;for(;o<=e.heap_len&&(o<e.heap_len&&Se(t,e.heap[o+1],e.heap[o],e.depth)&&o++,!Se(t,n,e.heap[o],e.depth));)e.heap[s]=e.heap[o],s=o,o<<=1;e.heap[s]=n},ye=(e,t,s)=>{let n,o,i,r,a=0;if(0!==e.last_lit)do{n=e.pending_buf[e.d_buf+2*a]<<8|e.pending_buf[e.d_buf+2*a+1],o=e.pending_buf[e.l_buf+a],a++,0===n?me(e,o,t):(i=ie[o],me(e,i+256+1,t),r=Y[i],0!==r&&(o-=re[i],fe(e,o,r)),n--,i=be(n),me(e,i,s),r=Q[i],0!==r&&(n-=ae[i],fe(e,n,r)))}while(a<e.last_lit);me(e,256,t)},ke=(e,t)=>{const s=t.dyn_tree,n=t.stat_desc.static_tree,o=t.stat_desc.has_stree,i=t.stat_desc.elems;let r,a,l,c=-1;for(e.heap_len=0,e.heap_max=573,r=0;r<i;r++)0!==s[2*r]?(e.heap[++e.heap_len]=c=r,e.depth[r]=0):s[2*r+1]=0;for(;e.heap_len<2;)l=e.heap[++e.heap_len]=c<2?++c:0,s[2*l]=1,e.depth[l]=0,e.opt_len--,o&&(e.static_len-=n[2*l+1]);for(t.max_code=c,r=e.heap_len>>1;r>=1;r--)Re(e,s,r);l=i;do{r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],Re(e,s,1),a=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=a,s[2*l]=s[2*r]+s[2*a],e.depth[l]=(e.depth[r]>=e.depth[a]?e.depth[r]:e.depth[a])+1,s[2*r+1]=s[2*a+1]=l,e.heap[1]=l++,Re(e,s,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],((e,t)=>{const s=t.dyn_tree,n=t.max_code,o=t.stat_desc.static_tree,i=t.stat_desc.has_stree,r=t.stat_desc.extra_bits,a=t.stat_desc.extra_base,l=t.stat_desc.max_length;let c,d,u,h,b,p,f=0;for(h=0;h<=15;h++)e.bl_count[h]=0;for(s[2*e.heap[e.heap_max]+1]=0,c=e.heap_max+1;c<573;c++)d=e.heap[c],h=s[2*s[2*d+1]+1]+1,h>l&&(h=l,f++),s[2*d+1]=h,d>n||(e.bl_count[h]++,b=0,d>=a&&(b=r[d-a]),p=s[2*d],e.opt_len+=p*(h+b),i&&(e.static_len+=p*(o[2*d+1]+b)));if(0!==f){do{for(h=l-1;0===e.bl_count[h];)h--;e.bl_count[h]--,e.bl_count[h+1]+=2,e.bl_count[l]--,f-=2}while(f>0);for(h=l;0!==h;h--)for(d=e.bl_count[h];0!==d;)u=e.heap[--c],u>n||(s[2*u+1]!==h&&(e.opt_len+=(h-s[2*u+1])*s[2*u],s[2*u+1]=h),d--)}})(e,t),_e(s,c,e.bl_count)},ve=(e,t,s)=>{let n,o,i=-1,r=t[1],a=0,l=7,c=4;for(0===r&&(l=138,c=3),t[2*(s+1)+1]=65535,n=0;n<=s;n++)o=r,r=t[2*(n+1)+1],++a<l&&o===r||(a<c?e.bl_tree[2*o]+=a:0!==o?(o!==i&&e.bl_tree[2*o]++,e.bl_tree[32]++):a<=10?e.bl_tree[34]++:e.bl_tree[36]++,a=0,i=o,0===r?(l=138,c=3):o===r?(l=6,c=3):(l=7,c=4))},qe=(e,t,s)=>{let n,o,i=-1,r=t[1],a=0,l=7,c=4;for(0===r&&(l=138,c=3),n=0;n<=s;n++)if(o=r,r=t[2*(n+1)+1],!(++a<l&&o===r)){if(a<c)do{me(e,o,e.bl_tree)}while(0!=--a);else 0!==o?(o!==i&&(me(e,o,e.bl_tree),a--),me(e,16,e.bl_tree),fe(e,a-3,2)):a<=10?(me(e,17,e.bl_tree),fe(e,a-3,3)):(me(e,18,e.bl_tree),fe(e,a-11,7));a=0,i=o,0===r?(l=138,c=3):o===r?(l=6,c=3):(l=7,c=4)}};let Ie=!1;const Pe=(e,t,s,n)=>{fe(e,0+(n?1:0),3),((e,t,s,n)=>{Ce(e),n&&(pe(e,s),pe(e,~s)),e.pending_buf.set(e.window.subarray(t,t+s),e.pending),e.pending+=s})(e,t,s,!0)};var xe=(e,t,s,n)=>{let o,i,r=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=(e=>{let t,s=4093624447;for(t=0;t<=31;t++,s>>>=1)if(1&s&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<256;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0})(e)),ke(e,e.l_desc),ke(e,e.d_desc),r=(e=>{let t;for(ve(e,e.dyn_ltree,e.l_desc.max_code),ve(e,e.dyn_dtree,e.d_desc.max_code),ke(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*te[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t})(e),o=e.opt_len+3+7>>>3,i=e.static_len+3+7>>>3,i<=o&&(o=i)):o=i=s+5,s+4<=o&&-1!==t?Pe(e,t,s,n):4===e.strategy||i===o?(fe(e,2+(n?1:0),3),ye(e,se,ne)):(fe(e,4+(n?1:0),3),((e,t,s,n)=>{let o;for(fe(e,t-257,5),fe(e,s-1,5),fe(e,n-4,4),o=0;o<n;o++)fe(e,e.bl_tree[2*te[o]+1],3);qe(e,e.dyn_ltree,t-1),qe(e,e.dyn_dtree,s-1)})(e,e.l_desc.max_code+1,e.d_desc.max_code+1,r+1),ye(e,e.dyn_ltree,e.dyn_dtree)),we(e),n&&Ce(e)},Ee={_tr_init:e=>{Ie||((()=>{let e,t,s,n,o;const i=new Array(16);for(s=0,n=0;n<28;n++)for(re[n]=s,e=0;e<1<<Y[n];e++)ie[s++]=n;for(ie[s-1]=n,o=0,n=0;n<16;n++)for(ae[n]=o,e=0;e<1<<Q[n];e++)oe[o++]=n;for(o>>=7;n<30;n++)for(ae[n]=o<<7,e=0;e<1<<Q[n]-7;e++)oe[256+o++]=n;for(t=0;t<=15;t++)i[t]=0;for(e=0;e<=143;)se[2*e+1]=8,e++,i[8]++;for(;e<=255;)se[2*e+1]=9,e++,i[9]++;for(;e<=279;)se[2*e+1]=7,e++,i[7]++;for(;e<=287;)se[2*e+1]=8,e++,i[8]++;for(_e(se,287,i),e=0;e<30;e++)ne[2*e+1]=5,ne[2*e]=ge(e,5);ce=new le(se,Y,257,286,15),de=new le(ne,Q,0,30,15),ue=new le(new Array(0),ee,0,19,7)})(),Ie=!0),e.l_desc=new he(e.dyn_ltree,ce),e.d_desc=new he(e.dyn_dtree,de),e.bl_desc=new he(e.bl_tree,ue),e.bi_buf=0,e.bi_valid=0,we(e)},_tr_stored_block:Pe,_tr_flush_block:xe,_tr_tally:(e,t,s)=>(e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&s,e.last_lit++,0===t?e.dyn_ltree[2*s]++:(e.matches++,t--,e.dyn_ltree[2*(ie[s]+256+1)]++,e.dyn_dtree[2*be(t)]++),e.last_lit===e.lit_bufsize-1),_tr_align:e=>{fe(e,2,3),me(e,256,se),(e=>{16===e.bi_valid?(pe(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)})(e)}};var ze=(e,t,s,n)=>{let o=65535&e|0,i=e>>>16&65535|0,r=0;for(;0!==s;){r=s>2e3?2e3:s,s-=r;do{o=o+t[n++]|0,i=i+o|0}while(--r);o%=65521,i%=65521}return o|i<<16|0};const Ue=new Uint32Array((()=>{let e,t=[];for(var s=0;s<256;s++){e=s;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[s]=e}return t})());var De=(e,t,s,n)=>{const o=Ue,i=n+s;e^=-1;for(let s=n;s<i;s++)e=e>>>8^o[255&(e^t[s])];return-1^e},Ae={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},Te={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};const{_tr_init:We,_tr_stored_block:Oe,_tr_flush_block:Fe,_tr_tally:Me,_tr_align:Je}=Ee,{Z_NO_FLUSH:Le,Z_PARTIAL_FLUSH:Be,Z_FULL_FLUSH:Ze,Z_FINISH:Ne,Z_BLOCK:$e,Z_OK:He,Z_STREAM_END:Ve,Z_STREAM_ERROR:Ge,Z_DATA_ERROR:je,Z_BUF_ERROR:Ke,Z_DEFAULT_COMPRESSION:Xe,Z_FILTERED:Ye,Z_HUFFMAN_ONLY:Qe,Z_RLE:et,Z_FIXED:tt,Z_DEFAULT_STRATEGY:st,Z_UNKNOWN:nt,Z_DEFLATED:ot}=Te,it=(e,t)=>(e.msg=Ae[t],t),rt=e=>(e<<1)-(e>4?9:0),at=e=>{let t=e.length;for(;--t>=0;)e[t]=0};let lt=(e,t,s)=>(t<<e.hash_shift^s)&e.hash_mask;const ct=e=>{const t=e.state;let s=t.pending;s>e.avail_out&&(s=e.avail_out),0!==s&&(e.output.set(t.pending_buf.subarray(t.pending_out,t.pending_out+s),e.next_out),e.next_out+=s,t.pending_out+=s,e.total_out+=s,e.avail_out-=s,t.pending-=s,0===t.pending&&(t.pending_out=0))},dt=(e,t)=>{Fe(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,ct(e.strm)},ut=(e,t)=>{e.pending_buf[e.pending++]=t},ht=(e,t)=>{e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t},bt=(e,t,s,n)=>{let o=e.avail_in;return o>n&&(o=n),0===o?0:(e.avail_in-=o,t.set(e.input.subarray(e.next_in,e.next_in+o),s),1===e.state.wrap?e.adler=ze(e.adler,t,o,s):2===e.state.wrap&&(e.adler=De(e.adler,t,o,s)),e.next_in+=o,e.total_in+=o,o)},pt=(e,t)=>{let s,n,o=e.max_chain_length,i=e.strstart,r=e.prev_length,a=e.nice_match;const l=e.strstart>e.w_size-262?e.strstart-(e.w_size-262):0,c=e.window,d=e.w_mask,u=e.prev,h=e.strstart+258;let b=c[i+r-1],p=c[i+r];e.prev_length>=e.good_match&&(o>>=2),a>e.lookahead&&(a=e.lookahead);do{if(s=t,c[s+r]===p&&c[s+r-1]===b&&c[s]===c[i]&&c[++s]===c[i+1]){i+=2,s++;do{}while(c[++i]===c[++s]&&c[++i]===c[++s]&&c[++i]===c[++s]&&c[++i]===c[++s]&&c[++i]===c[++s]&&c[++i]===c[++s]&&c[++i]===c[++s]&&c[++i]===c[++s]&&i<h);if(n=258-(h-i),i=h-258,n>r){if(e.match_start=t,r=n,n>=a)break;b=c[i+r-1],p=c[i+r]}}}while((t=u[t&d])>l&&0!=--o);return r<=e.lookahead?r:e.lookahead},ft=e=>{const t=e.w_size;let s,n,o,i,r;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=t+(t-262)){e.window.set(e.window.subarray(t,t+t),0),e.match_start-=t,e.strstart-=t,e.block_start-=t,n=e.hash_size,s=n;do{o=e.head[--s],e.head[s]=o>=t?o-t:0}while(--n);n=t,s=n;do{o=e.prev[--s],e.prev[s]=o>=t?o-t:0}while(--n);i+=t}if(0===e.strm.avail_in)break;if(n=bt(e.strm,e.window,e.strstart+e.lookahead,i),e.lookahead+=n,e.lookahead+e.insert>=3)for(r=e.strstart-e.insert,e.ins_h=e.window[r],e.ins_h=lt(e,e.ins_h,e.window[r+1]);e.insert&&(e.ins_h=lt(e,e.ins_h,e.window[r+3-1]),e.prev[r&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=r,r++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<262&&0!==e.strm.avail_in)},mt=(e,t)=>{let s,n;for(;;){if(e.lookahead<262){if(ft(e),e.lookahead<262&&t===Le)return 1;if(0===e.lookahead)break}if(s=0,e.lookahead>=3&&(e.ins_h=lt(e,e.ins_h,e.window[e.strstart+3-1]),s=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==s&&e.strstart-s<=e.w_size-262&&(e.match_length=pt(e,s)),e.match_length>=3)if(n=Me(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do{e.strstart++,e.ins_h=lt(e,e.ins_h,e.window[e.strstart+3-1]),s=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=lt(e,e.ins_h,e.window[e.strstart+1]);else n=Me(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(dt(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,t===Ne?(dt(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(dt(e,!1),0===e.strm.avail_out)?1:2},gt=(e,t)=>{let s,n,o;for(;;){if(e.lookahead<262){if(ft(e),e.lookahead<262&&t===Le)return 1;if(0===e.lookahead)break}if(s=0,e.lookahead>=3&&(e.ins_h=lt(e,e.ins_h,e.window[e.strstart+3-1]),s=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==s&&e.prev_length<e.max_lazy_match&&e.strstart-s<=e.w_size-262&&(e.match_length=pt(e,s),e.match_length<=5&&(e.strategy===Ye||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){o=e.strstart+e.lookahead-3,n=Me(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=o&&(e.ins_h=lt(e,e.ins_h,e.window[e.strstart+3-1]),s=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,n&&(dt(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if(n=Me(e,0,e.window[e.strstart-1]),n&&dt(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=Me(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,t===Ne?(dt(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(dt(e,!1),0===e.strm.avail_out)?1:2};function _t(e,t,s,n,o){this.good_length=e,this.max_lazy=t,this.nice_length=s,this.max_chain=n,this.func=o}const wt=[new _t(0,0,0,0,((e,t)=>{let s=65535;for(s>e.pending_buf_size-5&&(s=e.pending_buf_size-5);;){if(e.lookahead<=1){if(ft(e),0===e.lookahead&&t===Le)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;const n=e.block_start+s;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,dt(e,!1),0===e.strm.avail_out))return 1;if(e.strstart-e.block_start>=e.w_size-262&&(dt(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===Ne?(dt(e,!0),0===e.strm.avail_out?3:4):(e.strstart>e.block_start&&(dt(e,!1),e.strm.avail_out),1)})),new _t(4,4,8,4,mt),new _t(4,5,16,8,mt),new _t(4,6,32,32,mt),new _t(4,4,16,16,gt),new _t(8,16,32,32,gt),new _t(8,16,128,128,gt),new _t(8,32,128,256,gt),new _t(32,128,258,1024,gt),new _t(32,258,258,4096,gt)];function Ct(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=ot,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),at(this.dyn_ltree),at(this.dyn_dtree),at(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),at(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),at(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}const St=e=>{if(!e||!e.state)return it(e,Ge);e.total_in=e.total_out=0,e.data_type=nt;const t=e.state;return t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?42:113,e.adler=2===t.wrap?0:1,t.last_flush=Le,We(t),He},Rt=e=>{const t=St(e);var s;return t===He&&((s=e.state).window_size=2*s.w_size,at(s.head),s.max_lazy_match=wt[s.level].max_lazy,s.good_match=wt[s.level].good_length,s.nice_match=wt[s.level].nice_length,s.max_chain_length=wt[s.level].max_chain,s.strstart=0,s.block_start=0,s.lookahead=0,s.insert=0,s.match_length=s.prev_length=2,s.match_available=0,s.ins_h=0),t},yt=(e,t,s,n,o,i)=>{if(!e)return Ge;let r=1;if(t===Xe&&(t=6),n<0?(r=0,n=-n):n>15&&(r=2,n-=16),o<1||o>9||s!==ot||n<8||n>15||t<0||t>9||i<0||i>tt)return it(e,Ge);8===n&&(n=9);const a=new Ct;return e.state=a,a.strm=e,a.wrap=r,a.gzhead=null,a.w_bits=n,a.w_size=1<<a.w_bits,a.w_mask=a.w_size-1,a.hash_bits=o+7,a.hash_size=1<<a.hash_bits,a.hash_mask=a.hash_size-1,a.hash_shift=~~((a.hash_bits+3-1)/3),a.window=new Uint8Array(2*a.w_size),a.head=new Uint16Array(a.hash_size),a.prev=new Uint16Array(a.w_size),a.lit_bufsize=1<<o+6,a.pending_buf_size=4*a.lit_bufsize,a.pending_buf=new Uint8Array(a.pending_buf_size),a.d_buf=1*a.lit_bufsize,a.l_buf=3*a.lit_bufsize,a.level=t,a.strategy=i,a.method=s,Rt(e)};var kt={deflateInit:(e,t)=>yt(e,t,ot,15,8,st),deflateInit2:yt,deflateReset:Rt,deflateResetKeep:St,deflateSetHeader:(e,t)=>e&&e.state?2!==e.state.wrap?Ge:(e.state.gzhead=t,He):Ge,deflate:(e,t)=>{let s,n;if(!e||!e.state||t>$e||t<0)return e?it(e,Ge):Ge;const o=e.state;if(!e.output||!e.input&&0!==e.avail_in||666===o.status&&t!==Ne)return it(e,0===e.avail_out?Ke:Ge);o.strm=e;const i=o.last_flush;if(o.last_flush=t,42===o.status)if(2===o.wrap)e.adler=0,ut(o,31),ut(o,139),ut(o,8),o.gzhead?(ut(o,(o.gzhead.text?1:0)+(o.gzhead.hcrc?2:0)+(o.gzhead.extra?4:0)+(o.gzhead.name?8:0)+(o.gzhead.comment?16:0)),ut(o,255&o.gzhead.time),ut(o,o.gzhead.time>>8&255),ut(o,o.gzhead.time>>16&255),ut(o,o.gzhead.time>>24&255),ut(o,9===o.level?2:o.strategy>=Qe||o.level<2?4:0),ut(o,255&o.gzhead.os),o.gzhead.extra&&o.gzhead.extra.length&&(ut(o,255&o.gzhead.extra.length),ut(o,o.gzhead.extra.length>>8&255)),o.gzhead.hcrc&&(e.adler=De(e.adler,o.pending_buf,o.pending,0)),o.gzindex=0,o.status=69):(ut(o,0),ut(o,0),ut(o,0),ut(o,0),ut(o,0),ut(o,9===o.level?2:o.strategy>=Qe||o.level<2?4:0),ut(o,3),o.status=113);else{let t=ot+(o.w_bits-8<<4)<<8,s=-1;s=o.strategy>=Qe||o.level<2?0:o.level<6?1:6===o.level?2:3,t|=s<<6,0!==o.strstart&&(t|=32),t+=31-t%31,o.status=113,ht(o,t),0!==o.strstart&&(ht(o,e.adler>>>16),ht(o,65535&e.adler)),e.adler=1}if(69===o.status)if(o.gzhead.extra){for(s=o.pending;o.gzindex<(65535&o.gzhead.extra.length)&&(o.pending!==o.pending_buf_size||(o.gzhead.hcrc&&o.pending>s&&(e.adler=De(e.adler,o.pending_buf,o.pending-s,s)),ct(e),s=o.pending,o.pending!==o.pending_buf_size));)ut(o,255&o.gzhead.extra[o.gzindex]),o.gzindex++;o.gzhead.hcrc&&o.pending>s&&(e.adler=De(e.adler,o.pending_buf,o.pending-s,s)),o.gzindex===o.gzhead.extra.length&&(o.gzindex=0,o.status=73)}else o.status=73;if(73===o.status)if(o.gzhead.name){s=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>s&&(e.adler=De(e.adler,o.pending_buf,o.pending-s,s)),ct(e),s=o.pending,o.pending===o.pending_buf_size)){n=1;break}n=o.gzindex<o.gzhead.name.length?255&o.gzhead.name.charCodeAt(o.gzindex++):0,ut(o,n)}while(0!==n);o.gzhead.hcrc&&o.pending>s&&(e.adler=De(e.adler,o.pending_buf,o.pending-s,s)),0===n&&(o.gzindex=0,o.status=91)}else o.status=91;if(91===o.status)if(o.gzhead.comment){s=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>s&&(e.adler=De(e.adler,o.pending_buf,o.pending-s,s)),ct(e),s=o.pending,o.pending===o.pending_buf_size)){n=1;break}n=o.gzindex<o.gzhead.comment.length?255&o.gzhead.comment.charCodeAt(o.gzindex++):0,ut(o,n)}while(0!==n);o.gzhead.hcrc&&o.pending>s&&(e.adler=De(e.adler,o.pending_buf,o.pending-s,s)),0===n&&(o.status=103)}else o.status=103;if(103===o.status&&(o.gzhead.hcrc?(o.pending+2>o.pending_buf_size&&ct(e),o.pending+2<=o.pending_buf_size&&(ut(o,255&e.adler),ut(o,e.adler>>8&255),e.adler=0,o.status=113)):o.status=113),0!==o.pending){if(ct(e),0===e.avail_out)return o.last_flush=-1,He}else if(0===e.avail_in&&rt(t)<=rt(i)&&t!==Ne)return it(e,Ke);if(666===o.status&&0!==e.avail_in)return it(e,Ke);if(0!==e.avail_in||0!==o.lookahead||t!==Le&&666!==o.status){let s=o.strategy===Qe?((e,t)=>{let s;for(;;){if(0===e.lookahead&&(ft(e),0===e.lookahead)){if(t===Le)return 1;break}if(e.match_length=0,s=Me(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,s&&(dt(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===Ne?(dt(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(dt(e,!1),0===e.strm.avail_out)?1:2})(o,t):o.strategy===et?((e,t)=>{let s,n,o,i;const r=e.window;for(;;){if(e.lookahead<=258){if(ft(e),e.lookahead<=258&&t===Le)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(o=e.strstart-1,n=r[o],n===r[++o]&&n===r[++o]&&n===r[++o])){i=e.strstart+258;do{}while(n===r[++o]&&n===r[++o]&&n===r[++o]&&n===r[++o]&&n===r[++o]&&n===r[++o]&&n===r[++o]&&n===r[++o]&&o<i);e.match_length=258-(i-o),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(s=Me(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(s=Me(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),s&&(dt(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===Ne?(dt(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(dt(e,!1),0===e.strm.avail_out)?1:2})(o,t):wt[o.level].func(o,t);if(3!==s&&4!==s||(o.status=666),1===s||3===s)return 0===e.avail_out&&(o.last_flush=-1),He;if(2===s&&(t===Be?Je(o):t!==$e&&(Oe(o,0,0,!1),t===Ze&&(at(o.head),0===o.lookahead&&(o.strstart=0,o.block_start=0,o.insert=0))),ct(e),0===e.avail_out))return o.last_flush=-1,He}return t!==Ne?He:o.wrap<=0?Ve:(2===o.wrap?(ut(o,255&e.adler),ut(o,e.adler>>8&255),ut(o,e.adler>>16&255),ut(o,e.adler>>24&255),ut(o,255&e.total_in),ut(o,e.total_in>>8&255),ut(o,e.total_in>>16&255),ut(o,e.total_in>>24&255)):(ht(o,e.adler>>>16),ht(o,65535&e.adler)),ct(e),o.wrap>0&&(o.wrap=-o.wrap),0!==o.pending?He:Ve)},deflateEnd:e=>{if(!e||!e.state)return Ge;const t=e.state.status;return 42!==t&&69!==t&&73!==t&&91!==t&&103!==t&&113!==t&&666!==t?it(e,Ge):(e.state=null,113===t?it(e,je):He)},deflateSetDictionary:(e,t)=>{let s=t.length;if(!e||!e.state)return Ge;const n=e.state,o=n.wrap;if(2===o||1===o&&42!==n.status||n.lookahead)return Ge;if(1===o&&(e.adler=ze(e.adler,t,s,0)),n.wrap=0,s>=n.w_size){0===o&&(at(n.head),n.strstart=0,n.block_start=0,n.insert=0);let e=new Uint8Array(n.w_size);e.set(t.subarray(s-n.w_size,s),0),t=e,s=n.w_size}const i=e.avail_in,r=e.next_in,a=e.input;for(e.avail_in=s,e.next_in=0,e.input=t,ft(n);n.lookahead>=3;){let e=n.strstart,t=n.lookahead-2;do{n.ins_h=lt(n,n.ins_h,n.window[e+3-1]),n.prev[e&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=e,e++}while(--t);n.strstart=e,n.lookahead=2,ft(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=2,n.match_available=0,e.next_in=r,e.input=a,e.avail_in=i,n.wrap=o,He},deflateInfo:"pako deflate (from Nodeca project)"};const vt=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var qt=function(e){const t=Array.prototype.slice.call(arguments,1);for(;t.length;){const s=t.shift();if(s){if("object"!=typeof s)throw new TypeError(s+"must be non-object");for(const t in s)vt(s,t)&&(e[t]=s[t])}}return e},It=e=>{let t=0;for(let s=0,n=e.length;s<n;s++)t+=e[s].length;const s=new Uint8Array(t);for(let t=0,n=0,o=e.length;t<o;t++){let o=e[t];s.set(o,n),n+=o.length}return s};let Pt=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){Pt=!1}const xt=new Uint8Array(256);for(let e=0;e<256;e++)xt[e]=e>=252?6:e>=248?5:e>=240?4:e>=224?3:e>=192?2:1;xt[254]=xt[254]=1;var Et=e=>{if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(e);let t,s,n,o,i,r=e.length,a=0;for(o=0;o<r;o++)s=e.charCodeAt(o),55296==(64512&s)&&o+1<r&&(n=e.charCodeAt(o+1),56320==(64512&n)&&(s=65536+(s-55296<<10)+(n-56320),o++)),a+=s<128?1:s<2048?2:s<65536?3:4;for(t=new Uint8Array(a),i=0,o=0;i<a;o++)s=e.charCodeAt(o),55296==(64512&s)&&o+1<r&&(n=e.charCodeAt(o+1),56320==(64512&n)&&(s=65536+(s-55296<<10)+(n-56320),o++)),s<128?t[i++]=s:s<2048?(t[i++]=192|s>>>6,t[i++]=128|63&s):s<65536?(t[i++]=224|s>>>12,t[i++]=128|s>>>6&63,t[i++]=128|63&s):(t[i++]=240|s>>>18,t[i++]=128|s>>>12&63,t[i++]=128|s>>>6&63,t[i++]=128|63&s);return t},zt=(e,t)=>{const s=t||e.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(e.subarray(0,t));let n,o;const i=new Array(2*s);for(o=0,n=0;n<s;){let t=e[n++];if(t<128){i[o++]=t;continue}let r=xt[t];if(r>4)i[o++]=65533,n+=r-1;else{for(t&=2===r?31:3===r?15:7;r>1&&n<s;)t=t<<6|63&e[n++],r--;r>1?i[o++]=65533:t<65536?i[o++]=t:(t-=65536,i[o++]=55296|t>>10&1023,i[o++]=56320|1023&t)}}return((e,t)=>{if(t<65534&&e.subarray&&Pt)return String.fromCharCode.apply(null,e.length===t?e:e.subarray(0,t));let s="";for(let n=0;n<t;n++)s+=String.fromCharCode(e[n]);return s})(i,o)},Ut=(e,t)=>{(t=t||e.length)>e.length&&(t=e.length);let s=t-1;for(;s>=0&&128==(192&e[s]);)s--;return s<0||0===s?t:s+xt[e[s]]>t?s:t};var Dt=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0};const At=Object.prototype.toString,{Z_NO_FLUSH:Tt,Z_SYNC_FLUSH:Wt,Z_FULL_FLUSH:Ot,Z_FINISH:Ft,Z_OK:Mt,Z_STREAM_END:Jt,Z_DEFAULT_COMPRESSION:Lt,Z_DEFAULT_STRATEGY:Bt,Z_DEFLATED:Zt}=Te;function Nt(e){this.options=qt({level:Lt,method:Zt,chunkSize:16384,windowBits:15,memLevel:8,strategy:Bt},e||{});let t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Dt,this.strm.avail_out=0;let s=kt.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(s!==Mt)throw new Error(Ae[s]);if(t.header&&kt.deflateSetHeader(this.strm,t.header),t.dictionary){let e;if(e="string"==typeof t.dictionary?Et(t.dictionary):"[object ArrayBuffer]"===At.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,s=kt.deflateSetDictionary(this.strm,e),s!==Mt)throw new Error(Ae[s]);this._dict_set=!0}}function $t(e,t){const s=new Nt(t);if(s.push(e,!0),s.err)throw s.msg||Ae[s.err];return s.result}Nt.prototype.push=function(e,t){const s=this.strm,n=this.options.chunkSize;let o,i;if(this.ended)return!1;for(i=t===~~t?t:!0===t?Ft:Tt,"string"==typeof e?s.input=Et(e):"[object ArrayBuffer]"===At.call(e)?s.input=new Uint8Array(e):s.input=e,s.next_in=0,s.avail_in=s.input.length;;)if(0===s.avail_out&&(s.output=new Uint8Array(n),s.next_out=0,s.avail_out=n),(i===Wt||i===Ot)&&s.avail_out<=6)this.onData(s.output.subarray(0,s.next_out)),s.avail_out=0;else{if(o=kt.deflate(s,i),o===Jt)return s.next_out>0&&this.onData(s.output.subarray(0,s.next_out)),o=kt.deflateEnd(this.strm),this.onEnd(o),this.ended=!0,o===Mt;if(0!==s.avail_out){if(i>0&&s.next_out>0)this.onData(s.output.subarray(0,s.next_out)),s.avail_out=0;else if(0===s.avail_in)break}else this.onData(s.output)}return!0},Nt.prototype.onData=function(e){this.chunks.push(e)},Nt.prototype.onEnd=function(e){e===Mt&&(this.result=It(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var Ht={Deflate:Nt,deflate:$t,deflateRaw:function(e,t){return(t=t||{}).raw=!0,$t(e,t)},gzip:function(e,t){return(t=t||{}).gzip=!0,$t(e,t)},constants:Te};var Vt=function(e,t){let s,n,o,i,r,a,l,c,d,u,h,b,p,f,m,g,_,w,C,S,R,y,k,v;const q=e.state;s=e.next_in,k=e.input,n=s+(e.avail_in-5),o=e.next_out,v=e.output,i=o-(t-e.avail_out),r=o+(e.avail_out-257),a=q.dmax,l=q.wsize,c=q.whave,d=q.wnext,u=q.window,h=q.hold,b=q.bits,p=q.lencode,f=q.distcode,m=(1<<q.lenbits)-1,g=(1<<q.distbits)-1;e:do{b<15&&(h+=k[s++]<<b,b+=8,h+=k[s++]<<b,b+=8),_=p[h&m];t:for(;;){if(w=_>>>24,h>>>=w,b-=w,w=_>>>16&255,0===w)v[o++]=65535&_;else{if(!(16&w)){if(0==(64&w)){_=p[(65535&_)+(h&(1<<w)-1)];continue t}if(32&w){q.mode=12;break e}e.msg="invalid literal/length code",q.mode=30;break e}C=65535&_,w&=15,w&&(b<w&&(h+=k[s++]<<b,b+=8),C+=h&(1<<w)-1,h>>>=w,b-=w),b<15&&(h+=k[s++]<<b,b+=8,h+=k[s++]<<b,b+=8),_=f[h&g];s:for(;;){if(w=_>>>24,h>>>=w,b-=w,w=_>>>16&255,!(16&w)){if(0==(64&w)){_=f[(65535&_)+(h&(1<<w)-1)];continue s}e.msg="invalid distance code",q.mode=30;break e}if(S=65535&_,w&=15,b<w&&(h+=k[s++]<<b,b+=8,b<w&&(h+=k[s++]<<b,b+=8)),S+=h&(1<<w)-1,S>a){e.msg="invalid distance too far back",q.mode=30;break e}if(h>>>=w,b-=w,w=o-i,S>w){if(w=S-w,w>c&&q.sane){e.msg="invalid distance too far back",q.mode=30;break e}if(R=0,y=u,0===d){if(R+=l-w,w<C){C-=w;do{v[o++]=u[R++]}while(--w);R=o-S,y=v}}else if(d<w){if(R+=l+d-w,w-=d,w<C){C-=w;do{v[o++]=u[R++]}while(--w);if(R=0,d<C){w=d,C-=w;do{v[o++]=u[R++]}while(--w);R=o-S,y=v}}}else if(R+=d-w,w<C){C-=w;do{v[o++]=u[R++]}while(--w);R=o-S,y=v}for(;C>2;)v[o++]=y[R++],v[o++]=y[R++],v[o++]=y[R++],C-=3;C&&(v[o++]=y[R++],C>1&&(v[o++]=y[R++]))}else{R=o-S;do{v[o++]=v[R++],v[o++]=v[R++],v[o++]=v[R++],C-=3}while(C>2);C&&(v[o++]=v[R++],C>1&&(v[o++]=v[R++]))}break}}break}}while(s<n&&o<r);C=b>>3,s-=C,b-=C<<3,h&=(1<<b)-1,e.next_in=s,e.next_out=o,e.avail_in=s<n?n-s+5:5-(s-n),e.avail_out=o<r?r-o+257:257-(o-r),q.hold=h,q.bits=b};const Gt=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),jt=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),Kt=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),Xt=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]);var Yt=(e,t,s,n,o,i,r,a)=>{const l=a.bits;let c,d,u,h,b,p,f=0,m=0,g=0,_=0,w=0,C=0,S=0,R=0,y=0,k=0,v=null,q=0;const I=new Uint16Array(16),P=new Uint16Array(16);let x,E,z,U=null,D=0;for(f=0;f<=15;f++)I[f]=0;for(m=0;m<n;m++)I[t[s+m]]++;for(w=l,_=15;_>=1&&0===I[_];_--);if(w>_&&(w=_),0===_)return o[i++]=20971520,o[i++]=20971520,a.bits=1,0;for(g=1;g<_&&0===I[g];g++);for(w<g&&(w=g),R=1,f=1;f<=15;f++)if(R<<=1,R-=I[f],R<0)return-1;if(R>0&&(0===e||1!==_))return-1;for(P[1]=0,f=1;f<15;f++)P[f+1]=P[f]+I[f];for(m=0;m<n;m++)0!==t[s+m]&&(r[P[t[s+m]]++]=m);if(0===e?(v=U=r,p=19):1===e?(v=Gt,q-=257,U=jt,D-=257,p=256):(v=Kt,U=Xt,p=-1),k=0,m=0,f=g,b=i,C=w,S=0,u=-1,y=1<<w,h=y-1,1===e&&y>852||2===e&&y>592)return 1;for(;;){x=f-S,r[m]<p?(E=0,z=r[m]):r[m]>p?(E=U[D+r[m]],z=v[q+r[m]]):(E=96,z=0),c=1<<f-S,d=1<<C,g=d;do{d-=c,o[b+(k>>S)+d]=x<<24|E<<16|z|0}while(0!==d);for(c=1<<f-1;k&c;)c>>=1;if(0!==c?(k&=c-1,k+=c):k=0,m++,0==--I[f]){if(f===_)break;f=t[s+r[m]]}if(f>w&&(k&h)!==u){for(0===S&&(S=w),b+=g,C=f-S,R=1<<C;C+S<_&&(R-=I[C+S],!(R<=0));)C++,R<<=1;if(y+=1<<C,1===e&&y>852||2===e&&y>592)return 1;u=k&h,o[u]=w<<24|C<<16|b-i|0}}return 0!==k&&(o[b+k]=f-S<<24|64<<16|0),a.bits=w,0};const{Z_FINISH:Qt,Z_BLOCK:es,Z_TREES:ts,Z_OK:ss,Z_STREAM_END:ns,Z_NEED_DICT:os,Z_STREAM_ERROR:is,Z_DATA_ERROR:rs,Z_MEM_ERROR:as,Z_BUF_ERROR:ls,Z_DEFLATED:cs}=Te,ds=e=>(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24);function us(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}const hs=e=>{if(!e||!e.state)return is;const t=e.state;return e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=1,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Int32Array(852),t.distcode=t.distdyn=new Int32Array(592),t.sane=1,t.back=-1,ss},bs=e=>{if(!e||!e.state)return is;const t=e.state;return t.wsize=0,t.whave=0,t.wnext=0,hs(e)},ps=(e,t)=>{let s;if(!e||!e.state)return is;const n=e.state;return t<0?(s=0,t=-t):(s=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?is:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=s,n.wbits=t,bs(e))},fs=(e,t)=>{if(!e)return is;const s=new us;e.state=s,s.window=null;const n=ps(e,t);return n!==ss&&(e.state=null),n};let ms,gs,_s=!0;const ws=e=>{if(_s){ms=new Int32Array(512),gs=new Int32Array(32);let t=0;for(;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(Yt(1,e.lens,0,288,ms,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;Yt(2,e.lens,0,32,gs,0,e.work,{bits:5}),_s=!1}e.lencode=ms,e.lenbits=9,e.distcode=gs,e.distbits=5},Cs=(e,t,s,n)=>{let o;const i=e.state;return null===i.window&&(i.wsize=1<<i.wbits,i.wnext=0,i.whave=0,i.window=new Uint8Array(i.wsize)),n>=i.wsize?(i.window.set(t.subarray(s-i.wsize,s),0),i.wnext=0,i.whave=i.wsize):(o=i.wsize-i.wnext,o>n&&(o=n),i.window.set(t.subarray(s-n,s-n+o),i.wnext),(n-=o)?(i.window.set(t.subarray(s-n,s),0),i.wnext=n,i.whave=i.wsize):(i.wnext+=o,i.wnext===i.wsize&&(i.wnext=0),i.whave<i.wsize&&(i.whave+=o))),0};var Ss={inflateReset:bs,inflateReset2:ps,inflateResetKeep:hs,inflateInit:e=>fs(e,15),inflateInit2:fs,inflate:(e,t)=>{let s,n,o,i,r,a,l,c,d,u,h,b,p,f,m,g,_,w,C,S,R,y,k=0;const v=new Uint8Array(4);let q,I;const P=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return is;s=e.state,12===s.mode&&(s.mode=13),r=e.next_out,o=e.output,l=e.avail_out,i=e.next_in,n=e.input,a=e.avail_in,c=s.hold,d=s.bits,u=a,h=l,y=ss;e:for(;;)switch(s.mode){case 1:if(0===s.wrap){s.mode=13;break}for(;d<16;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if(2&s.wrap&&35615===c){s.check=0,v[0]=255&c,v[1]=c>>>8&255,s.check=De(s.check,v,2,0),c=0,d=0,s.mode=2;break}if(s.flags=0,s.head&&(s.head.done=!1),!(1&s.wrap)||(((255&c)<<8)+(c>>8))%31){e.msg="incorrect header check",s.mode=30;break}if((15&c)!==cs){e.msg="unknown compression method",s.mode=30;break}if(c>>>=4,d-=4,R=8+(15&c),0===s.wbits)s.wbits=R;else if(R>s.wbits){e.msg="invalid window size",s.mode=30;break}s.dmax=1<<s.wbits,e.adler=s.check=1,s.mode=512&c?10:12,c=0,d=0;break;case 2:for(;d<16;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if(s.flags=c,(255&s.flags)!==cs){e.msg="unknown compression method",s.mode=30;break}if(57344&s.flags){e.msg="unknown header flags set",s.mode=30;break}s.head&&(s.head.text=c>>8&1),512&s.flags&&(v[0]=255&c,v[1]=c>>>8&255,s.check=De(s.check,v,2,0)),c=0,d=0,s.mode=3;case 3:for(;d<32;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}s.head&&(s.head.time=c),512&s.flags&&(v[0]=255&c,v[1]=c>>>8&255,v[2]=c>>>16&255,v[3]=c>>>24&255,s.check=De(s.check,v,4,0)),c=0,d=0,s.mode=4;case 4:for(;d<16;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}s.head&&(s.head.xflags=255&c,s.head.os=c>>8),512&s.flags&&(v[0]=255&c,v[1]=c>>>8&255,s.check=De(s.check,v,2,0)),c=0,d=0,s.mode=5;case 5:if(1024&s.flags){for(;d<16;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}s.length=c,s.head&&(s.head.extra_len=c),512&s.flags&&(v[0]=255&c,v[1]=c>>>8&255,s.check=De(s.check,v,2,0)),c=0,d=0}else s.head&&(s.head.extra=null);s.mode=6;case 6:if(1024&s.flags&&(b=s.length,b>a&&(b=a),b&&(s.head&&(R=s.head.extra_len-s.length,s.head.extra||(s.head.extra=new Uint8Array(s.head.extra_len)),s.head.extra.set(n.subarray(i,i+b),R)),512&s.flags&&(s.check=De(s.check,n,b,i)),a-=b,i+=b,s.length-=b),s.length))break e;s.length=0,s.mode=7;case 7:if(2048&s.flags){if(0===a)break e;b=0;do{R=n[i+b++],s.head&&R&&s.length<65536&&(s.head.name+=String.fromCharCode(R))}while(R&&b<a);if(512&s.flags&&(s.check=De(s.check,n,b,i)),a-=b,i+=b,R)break e}else s.head&&(s.head.name=null);s.length=0,s.mode=8;case 8:if(4096&s.flags){if(0===a)break e;b=0;do{R=n[i+b++],s.head&&R&&s.length<65536&&(s.head.comment+=String.fromCharCode(R))}while(R&&b<a);if(512&s.flags&&(s.check=De(s.check,n,b,i)),a-=b,i+=b,R)break e}else s.head&&(s.head.comment=null);s.mode=9;case 9:if(512&s.flags){for(;d<16;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if(c!==(65535&s.check)){e.msg="header crc mismatch",s.mode=30;break}c=0,d=0}s.head&&(s.head.hcrc=s.flags>>9&1,s.head.done=!0),e.adler=s.check=0,s.mode=12;break;case 10:for(;d<32;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}e.adler=s.check=ds(c),c=0,d=0,s.mode=11;case 11:if(0===s.havedict)return e.next_out=r,e.avail_out=l,e.next_in=i,e.avail_in=a,s.hold=c,s.bits=d,os;e.adler=s.check=1,s.mode=12;case 12:if(t===es||t===ts)break e;case 13:if(s.last){c>>>=7&d,d-=7&d,s.mode=27;break}for(;d<3;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}switch(s.last=1&c,c>>>=1,d-=1,3&c){case 0:s.mode=14;break;case 1:if(ws(s),s.mode=20,t===ts){c>>>=2,d-=2;break e}break;case 2:s.mode=17;break;case 3:e.msg="invalid block type",s.mode=30}c>>>=2,d-=2;break;case 14:for(c>>>=7&d,d-=7&d;d<32;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if((65535&c)!=(c>>>16^65535)){e.msg="invalid stored block lengths",s.mode=30;break}if(s.length=65535&c,c=0,d=0,s.mode=15,t===ts)break e;case 15:s.mode=16;case 16:if(b=s.length,b){if(b>a&&(b=a),b>l&&(b=l),0===b)break e;o.set(n.subarray(i,i+b),r),a-=b,i+=b,l-=b,r+=b,s.length-=b;break}s.mode=12;break;case 17:for(;d<14;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if(s.nlen=257+(31&c),c>>>=5,d-=5,s.ndist=1+(31&c),c>>>=5,d-=5,s.ncode=4+(15&c),c>>>=4,d-=4,s.nlen>286||s.ndist>30){e.msg="too many length or distance symbols",s.mode=30;break}s.have=0,s.mode=18;case 18:for(;s.have<s.ncode;){for(;d<3;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}s.lens[P[s.have++]]=7&c,c>>>=3,d-=3}for(;s.have<19;)s.lens[P[s.have++]]=0;if(s.lencode=s.lendyn,s.lenbits=7,q={bits:s.lenbits},y=Yt(0,s.lens,0,19,s.lencode,0,s.work,q),s.lenbits=q.bits,y){e.msg="invalid code lengths set",s.mode=30;break}s.have=0,s.mode=19;case 19:for(;s.have<s.nlen+s.ndist;){for(;k=s.lencode[c&(1<<s.lenbits)-1],m=k>>>24,g=k>>>16&255,_=65535&k,!(m<=d);){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if(_<16)c>>>=m,d-=m,s.lens[s.have++]=_;else{if(16===_){for(I=m+2;d<I;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if(c>>>=m,d-=m,0===s.have){e.msg="invalid bit length repeat",s.mode=30;break}R=s.lens[s.have-1],b=3+(3&c),c>>>=2,d-=2}else if(17===_){for(I=m+3;d<I;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}c>>>=m,d-=m,R=0,b=3+(7&c),c>>>=3,d-=3}else{for(I=m+7;d<I;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}c>>>=m,d-=m,R=0,b=11+(127&c),c>>>=7,d-=7}if(s.have+b>s.nlen+s.ndist){e.msg="invalid bit length repeat",s.mode=30;break}for(;b--;)s.lens[s.have++]=R}}if(30===s.mode)break;if(0===s.lens[256]){e.msg="invalid code -- missing end-of-block",s.mode=30;break}if(s.lenbits=9,q={bits:s.lenbits},y=Yt(1,s.lens,0,s.nlen,s.lencode,0,s.work,q),s.lenbits=q.bits,y){e.msg="invalid literal/lengths set",s.mode=30;break}if(s.distbits=6,s.distcode=s.distdyn,q={bits:s.distbits},y=Yt(2,s.lens,s.nlen,s.ndist,s.distcode,0,s.work,q),s.distbits=q.bits,y){e.msg="invalid distances set",s.mode=30;break}if(s.mode=20,t===ts)break e;case 20:s.mode=21;case 21:if(a>=6&&l>=258){e.next_out=r,e.avail_out=l,e.next_in=i,e.avail_in=a,s.hold=c,s.bits=d,Vt(e,h),r=e.next_out,o=e.output,l=e.avail_out,i=e.next_in,n=e.input,a=e.avail_in,c=s.hold,d=s.bits,12===s.mode&&(s.back=-1);break}for(s.back=0;k=s.lencode[c&(1<<s.lenbits)-1],m=k>>>24,g=k>>>16&255,_=65535&k,!(m<=d);){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if(g&&0==(240&g)){for(w=m,C=g,S=_;k=s.lencode[S+((c&(1<<w+C)-1)>>w)],m=k>>>24,g=k>>>16&255,_=65535&k,!(w+m<=d);){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}c>>>=w,d-=w,s.back+=w}if(c>>>=m,d-=m,s.back+=m,s.length=_,0===g){s.mode=26;break}if(32&g){s.back=-1,s.mode=12;break}if(64&g){e.msg="invalid literal/length code",s.mode=30;break}s.extra=15&g,s.mode=22;case 22:if(s.extra){for(I=s.extra;d<I;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}s.length+=c&(1<<s.extra)-1,c>>>=s.extra,d-=s.extra,s.back+=s.extra}s.was=s.length,s.mode=23;case 23:for(;k=s.distcode[c&(1<<s.distbits)-1],m=k>>>24,g=k>>>16&255,_=65535&k,!(m<=d);){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if(0==(240&g)){for(w=m,C=g,S=_;k=s.distcode[S+((c&(1<<w+C)-1)>>w)],m=k>>>24,g=k>>>16&255,_=65535&k,!(w+m<=d);){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}c>>>=w,d-=w,s.back+=w}if(c>>>=m,d-=m,s.back+=m,64&g){e.msg="invalid distance code",s.mode=30;break}s.offset=_,s.extra=15&g,s.mode=24;case 24:if(s.extra){for(I=s.extra;d<I;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}s.offset+=c&(1<<s.extra)-1,c>>>=s.extra,d-=s.extra,s.back+=s.extra}if(s.offset>s.dmax){e.msg="invalid distance too far back",s.mode=30;break}s.mode=25;case 25:if(0===l)break e;if(b=h-l,s.offset>b){if(b=s.offset-b,b>s.whave&&s.sane){e.msg="invalid distance too far back",s.mode=30;break}b>s.wnext?(b-=s.wnext,p=s.wsize-b):p=s.wnext-b,b>s.length&&(b=s.length),f=s.window}else f=o,p=r-s.offset,b=s.length;b>l&&(b=l),l-=b,s.length-=b;do{o[r++]=f[p++]}while(--b);0===s.length&&(s.mode=21);break;case 26:if(0===l)break e;o[r++]=s.length,l--,s.mode=21;break;case 27:if(s.wrap){for(;d<32;){if(0===a)break e;a--,c|=n[i++]<<d,d+=8}if(h-=l,e.total_out+=h,s.total+=h,h&&(e.adler=s.check=s.flags?De(s.check,o,h,r-h):ze(s.check,o,h,r-h)),h=l,(s.flags?c:ds(c))!==s.check){e.msg="incorrect data check",s.mode=30;break}c=0,d=0}s.mode=28;case 28:if(s.wrap&&s.flags){for(;d<32;){if(0===a)break e;a--,c+=n[i++]<<d,d+=8}if(c!==(4294967295&s.total)){e.msg="incorrect length check",s.mode=30;break}c=0,d=0}s.mode=29;case 29:y=ns;break e;case 30:y=rs;break e;case 31:return as;default:return is}return e.next_out=r,e.avail_out=l,e.next_in=i,e.avail_in=a,s.hold=c,s.bits=d,(s.wsize||h!==e.avail_out&&s.mode<30&&(s.mode<27||t!==Qt))&&Cs(e,e.output,e.next_out,h-e.avail_out),u-=e.avail_in,h-=e.avail_out,e.total_in+=u,e.total_out+=h,s.total+=h,s.wrap&&h&&(e.adler=s.check=s.flags?De(s.check,o,h,e.next_out-h):ze(s.check,o,h,e.next_out-h)),e.data_type=s.bits+(s.last?64:0)+(12===s.mode?128:0)+(20===s.mode||15===s.mode?256:0),(0===u&&0===h||t===Qt)&&y===ss&&(y=ls),y},inflateEnd:e=>{if(!e||!e.state)return is;let t=e.state;return t.window&&(t.window=null),e.state=null,ss},inflateGetHeader:(e,t)=>{if(!e||!e.state)return is;const s=e.state;return 0==(2&s.wrap)?is:(s.head=t,t.done=!1,ss)},inflateSetDictionary:(e,t)=>{const s=t.length;let n,o,i;return e&&e.state?(n=e.state,0!==n.wrap&&11!==n.mode?is:11===n.mode&&(o=1,o=ze(o,t,s,0),o!==n.check)?rs:(i=Cs(e,t,s,s),i?(n.mode=31,as):(n.havedict=1,ss))):is},inflateInfo:"pako inflate (from Nodeca project)"};var Rs=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1};const ys=Object.prototype.toString,{Z_NO_FLUSH:ks,Z_FINISH:vs,Z_OK:qs,Z_STREAM_END:Is,Z_NEED_DICT:Ps,Z_STREAM_ERROR:xs,Z_DATA_ERROR:Es,Z_MEM_ERROR:zs}=Te;function Us(e){this.options=qt({chunkSize:65536,windowBits:15,to:""},e||{});const t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Dt,this.strm.avail_out=0;let s=Ss.inflateInit2(this.strm,t.windowBits);if(s!==qs)throw new Error(Ae[s]);if(this.header=new Rs,Ss.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=Et(t.dictionary):"[object ArrayBuffer]"===ys.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(s=Ss.inflateSetDictionary(this.strm,t.dictionary),s!==qs)))throw new Error(Ae[s])}Us.prototype.push=function(e,t){const s=this.strm,n=this.options.chunkSize,o=this.options.dictionary;let i,r,a;if(this.ended)return!1;for(r=t===~~t?t:!0===t?vs:ks,"[object ArrayBuffer]"===ys.call(e)?s.input=new Uint8Array(e):s.input=e,s.next_in=0,s.avail_in=s.input.length;;){for(0===s.avail_out&&(s.output=new Uint8Array(n),s.next_out=0,s.avail_out=n),i=Ss.inflate(s,r),i===Ps&&o&&(i=Ss.inflateSetDictionary(s,o),i===qs?i=Ss.inflate(s,r):i===Es&&(i=Ps));s.avail_in>0&&i===Is&&s.state.wrap>0&&0!==e[s.next_in];)Ss.inflateReset(s),i=Ss.inflate(s,r);switch(i){case xs:case Es:case Ps:case zs:return this.onEnd(i),this.ended=!0,!1}if(a=s.avail_out,s.next_out&&(0===s.avail_out||i===Is))if("string"===this.options.to){let e=Ut(s.output,s.next_out),t=s.next_out-e,o=zt(s.output,e);s.next_out=t,s.avail_out=n-t,t&&s.output.set(s.output.subarray(e,e+t),0),this.onData(o)}else this.onData(s.output.length===s.next_out?s.output:s.output.subarray(0,s.next_out));if(i!==qs||0!==a){if(i===Is)return i=Ss.inflateEnd(this.strm),this.onEnd(i),this.ended=!0,!0;if(0===s.avail_in)break}}return!0},Us.prototype.onData=function(e){this.chunks.push(e)},Us.prototype.onEnd=function(e){e===qs&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=It(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};const{Deflate:Ds,deflate:As,deflateRaw:Ts,gzip:Ws}=Ht;var Os=As;const Fs=class{constructor(e){this.oOptions=Object.assign({iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null},e),this.szHost="http://127.0.0.1",this.szUUID="",this.szVersion="",this.bNormalClose=!1,this.bConnected=!1,this.bInitConnect=!0,this.iGetErrorCount=0,this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}init(){const e=this,t=a(),s={sequence:t,cmd:"system.connect"},n=JSON.stringify(s);e.sendImageHttp(`${e.szHost}:${e.oOptions.iPort}/imghttp/local`,n,t,{success(t){const s=JSON.parse(t);e.szUUID=s.uuid,e.szVersion=s.version,e.bConnected=!0,e.bInitConnect=!1,setTimeout((()=>{e.imageHttpPolling()}),100),e.oOptions.cbConnectSuccess&&e.oOptions.cbConnectSuccess()},error(){}})}sendImageHttp(e,t,s,n){const o=this;n=Object.assign({success:null,error:null,abort:null},n);let i=Os(t);""!==(new Uint8Array).toString()&&(N.isMacOS()||N.browser().msie)&&(i=Array.prototype.slice.call(i));const r=encodeURIComponent(btoa(i)),a=this.splitStr(r),l=[];let c="";for(let e=0,t=a.length;e<t;e++)c=e===t-1?`update=${(new Date).getTime()}&isLast=true&data=${a[e]}&sequence=${s}`:`update=${(new Date).getTime()}&isLast=false&data=${a[e]}&sequence=${s}`,l.push(c);if(l.length>0){const t=function(){o.imageHttp(`${e}?${l[0]}`,{success(e){l.shift(),l.length>0?(o.bInitConnect||o.bConnected)&&t():n.success&&n.success(e)},error(){n.error&&n.error()},abort(){n.abort&&n.abort()}})};t()}}splitStr(e){const t=this.getByteLen(e),s=[],n=1500;for(let o=0,i=Math.ceil(t/n);o<i;o++)s[o]=e.slice(n*o,n*(o+1));return s}getByteLen(e){let t=0,s="";for(let n=0,o=e.length;n<o;n++)s=e.charAt(n),/[^\x00-\xff]/.test(s)?t+=2:t+=1;return t}imageHttp(e,t){t=Object.assign({success:null,error:null,abort:null},t);const s=new Image;s.onload=function(){if(t.success){const e=document.createElement("canvas"),n=e.getContext("2d"),o=s.width,i=s.height;e.width=o,e.height=i;try{n.drawImage(s,0,0);const e=n.getImageData(0,0,o,i).data;let r="",a=-1;for(let t=i-1;t>=0;t--)for(let s=0;s<4*o&&(a=t*o*4+s,0!==e[a]);s++)255!==e[a]&&(r+=String.fromCharCode(e[a]));t.success(N.utf8to16(r))}catch(e){t.error&&t.error()}}},s.onerror=function(){t.error&&t.error()},s.onabort=function(){t.abort&&t.abort()},s.crossOrigin="anonymous",s.src=e}setWindowControlCallback(e){this.oWindowControlCallback=e}setSadpCallback(e){this.oSadpCallback=e}setSliceCallback(e){this.oSliceCallback=e}setSerialCallback(e){this.oSerialCallback=e}setUIControlCallback(e){this.oUIControlCallback=e}setUpgradeCallback(e){this.oUpgradeCallback=e}getServiceVersion(){return this.szVersion}getRequestUUID(){return this.szUUID}disconnect(){const e=this,t=a(),s={sequence:t,uuid:e.szUUID,cmd:"system.disconnect"},n=JSON.stringify(s);e.bConnected&&e.sendImageHttp(`${e.szHost}:${e.oOptions.iPort}/imghttp/local`,n,t,{success(){e.bNormalClose=!0,e.bConnected=!1,e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose)},error(){e.bConnected=!1}})}imageHttpPolling(){const e=this,t=a(),s={sequence:t,uuid:e.szUUID,cmd:"system.get"},n=JSON.stringify(s);e.bConnected&&e.sendImageHttp(`${e.szHost}:${e.oOptions.iPort}/imghttp/local`,n,t,{success(t){if(e.iGetErrorCount=0,"timeout"===t)setTimeout((()=>{e.imageHttpPolling()}),100);else if("invalid"===t)e.bConnected=!1,e.oOptions.cbConnectError&&e.oOptions.cbConnectError();else if("closed"===t)console.log("connected is disconnected");else{const s=JSON.parse(t);void 0!==s.cmd?e.parseCmd(s):console.log(`[jsWebControl]imgHttpPolling push message error:${t}`),setTimeout((()=>{e.imageHttpPolling()}),100)}},error(){5===e.iGetErrorCount?(console.log("[jsWebControl]imageHttpPolling get polling finished"),e.bNormalClose=!1,e.bConnected=!1,e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose)):setTimeout((()=>{console.log("[jsWebControl]imgHttpPolling get polling failed"),e.iGetErrorCount++,e.imageHttpPolling()}),100)}})}sendRequest(e){const t=this;return new Promise(((s,n)=>{const o=e.cmd.split(".");let i="";o.length>1?i="laputa"===o[0]?"laputa":"local":n();const r=a();e.sequence=r,e.uuid=t.szUUID,e.timestamp=`${(new Date).getTime()}`;const l=JSON.stringify(e);t.bConnected?t.sendImageHttp(`${t.szHost}:${t.oOptions.iPort}/imghttp/${i}`,l,r,{success(e){const t=JSON.parse(e);0===t.errorModule&&0===t.errorCode?s(t):n(t)},error(){n()}}):n()}))}parseCmd(e){const t=e.cmd.split("."),s=t[1].replace(/^[a-z]{1}/g,(e=>e.toUpperCase()));"window"===t[0]||"play"===t[0]?this.oWindowControlCallback[`cb${s}`]&&this.oWindowControlCallback[`cb${s}`](e):"sadp"===t[0]?this.oSadpCallback[`cb${s}`]&&this.oSadpCallback[`cb${s}`](e):"serial"===t[0]?this.oSerialCallback[`cb${s}`]&&this.oSerialCallback[`cb${s}`](e):"slice"===t[0]?this.oSliceCallback[`cb${s}`]&&this.oSliceCallback[`cb${s}`](e):"ui"===t[0]?this.oUIControlCallback[`cb${s}`]&&this.oUIControlCallback[`cb${s}`](e):"upgrade"===t[0]&&this.oUpgradeCallback[`cb${s}`]&&this.oUpgradeCallback[`cb${s}`](e)}},Ms=class{constructor(e){this.oOptions=Object.assign({szPluginContainer:"",iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,szClassId:""},e),this.oPlugin=null,this.szPluginId="",this.szUUID="",this.szVersion="",this.oRequestList={},this.bNormalClose=!1,this.aMessage=[],this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}init(){const e=this;e.initPlugin(),e.oPlugin.object&&e.oPlugin.createSocket(`ws://127.0.0.1:${e.oOptions.iPort}`)}initPlugin(){const e=this;this.szPluginId=`webActiveX_${(new Date).getTime()}`;const t=`<object id='${this.szPluginId}' classid='clsid:${e.oOptions.szClassId}' codebase='' standby='Waiting...' width='100%' height='100%' align='center' ></object>`;let{szPluginContainer:s}=e.oOptions;if(""===s){s=`${this.szPluginId}_div`;const e=document.createElement("div");e.id=s,document.body.parentNode.appendChild(e)}document.getElementById(s).innerHTML=t,e.oPlugin=document.getElementById(this.szPluginId),window.onConnectMessage=function(t,s){s?(e.aMessage.push(t),e.onConnectMessage(e.aMessage.join("")),e.aMessage.length=0):e.aMessage.push(t)},window.onConnectClose=function(){e.onConnectClose()},window.onConnectError=function(){e.onConnectError()},window.onConnectCloseException=function(){e.onConnectCloseException()},window.onConnectOpen=function(){e.onConnectOpen()},N.createEventScript(this.szPluginId,"onConnectMessage(szData, bLast)","onConnectMessage(szData, bLast);"),N.createEventScript(this.szPluginId,"onConnectClose()","onConnectClose();"),N.createEventScript(this.szPluginId,"onConnectError()","onConnectError();"),N.createEventScript(this.szPluginId,"onConnectCloseException()","onConnectCloseException();"),N.createEventScript(this.szPluginId,"onConnectOpen()","onConnectOpen();")}onConnectMessage(e){const t=this;if(e){const s=JSON.parse(e),n=s.sequence;void 0===n&&void 0===s.cmd?(t.szUUID=s.uuid,t.szVersion=s.version,t.oOptions.cbConnectSuccess&&t.oOptions.cbConnectSuccess()):void 0!==s.cmd?t.parseCmd(s):void 0!==t.oRequestList[n]&&(0===s.errorModule&&0===s.errorCode?t.oRequestList[n].resolve(s):t.oRequestList[n].reject(s),delete t.oRequestList[n])}}onConnectClose(){if(this.oPlugin=null,""!==this.szPluginId){const e=document.getElementById(this.szPluginId);e.parentNode.removeChild(e);const t=document.getElementById(`${this.szPluginId}_div`);null!==t&&t.parentNode.removeChild(t)}this.oOptions.cbConnectClose&&this.oOptions.cbConnectClose(this.bNormalClose)}onConnectCloseException(){const e=this;setTimeout((()=>{e.oPlugin.object&&e.oPlugin.closeSocket()}),1e3)}onConnectOpen(){const e={sequence:a(),cmd:"system.connect"},t=JSON.stringify(e);this.oPlugin.object&&this.oPlugin.sendRequest(t)}onConnectError(){}setWindowControlCallback(e){this.oWindowControlCallback=e}setSadpCallback(e){this.oSadpCallback=e}setSliceCallback(e){this.oSliceCallback=e}setSerialCallback(e){this.oSerialCallback=e}setUIControlCallback(e){this.oUIControlCallback=e}setUpgradeCallback(e){this.oUpgradeCallback=e}getServiceVersion(){return this.szVersion}getRequestUUID(){return this.szUUID}disconnect(){this.bNormalClose=!0,this.oPlugin&&this.oPlugin.object&&this.oPlugin.closeSocket()}sendRequest(e){const t=this;return"window.hideWnd"===e.cmd?t.oPlugin&&t.oPlugin.object&&(t.oPlugin.style.visibility="hidden"):"window.showWnd"===e.cmd&&t.oPlugin&&t.oPlugin.object&&(t.oPlugin.style.visibility="visible"),new Promise(((s,n)=>{const o=a();e.sequence=o,t.oRequestList[o]={resolve:s,reject:n},e.uuid=t.szUUID,e.timestamp=`${(new Date).getTime()}`;const i=JSON.stringify(e);t.oPlugin&&t.oPlugin.object?t.oPlugin.sendRequest(i):n()}))}parseCmd(e){const t=e.cmd.split("."),s=t[1].replace(/^[a-z]{1}/g,(e=>e.toUpperCase()));"window"===t[0]||"play"===t[0]?this.oWindowControlCallback[`cb${s}`]&&this.oWindowControlCallback[`cb${s}`](e):"sadp"===t[0]?this.oSadpCallback[`cb${s}`]&&this.oSadpCallback[`cb${s}`](e):"serial"===t[0]?this.oSerialCallback[`cb${s}`]&&this.oSerialCallback[`cb${s}`](e):"slice"===t[0]?this.oSliceCallback[`cb${s}`]&&this.oSliceCallback[`cb${s}`](e):"ui"===t[0]?this.oUIControlCallback[`cb${s}`]&&this.oUIControlCallback[`cb${s}`](e):"upgrade"===t[0]&&this.oUpgradeCallback[`cb${s}`]&&this.oUpgradeCallback[`cb${s}`](e)}},Js=class{constructor(e){this.oOptions=Object.assign({szPluginContainer:"",cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,iServicePortStart:-1,iServicePortEnd:-1,szClassId:""},e),this.iPort=-1,this.oRequest=null,this.bInit=!1,this.oCallbacks={},this.init()}init(){const e=this;N.detectPort(e.oOptions.iServicePortStart,e.oOptions.iServicePortEnd,{success(t){if(e.iPort=t,N.browser().msie)"11.0"===N.browser().version?"https:"===window.location.protocol?e.oRequest=new Fs({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}):e.oRequest=new K({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}):e.oRequest=new Ms({szPluginContainer:e.oOptions.szPluginContainer,iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose,szClassId:e.oOptions.szClassId});else if("https:"===window.location.protocol)if(N.browser().chrome)try{e.oRequest=new K({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose})}catch(t){e.oRequest=new Fs({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose})}else e.oRequest=new Fs({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose});else"WebSocket"in window&&(e.oRequest=new K({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}));e.bInit=!0;for(const t in e.oCallbacks)e.oRequest[t](e.oCallbacks[t])},error(){e.oOptions.cbConnectError&&e.oOptions.cbConnectError()}})}setWindowControlCallback(e){this.bInit?this.oRequest.setWindowControlCallback(e):this.oCallbacks.setWindowControlCallback=e}setSadpCallback(e){this.bInit?this.oRequest.setSadpCallback(e):this.oCallbacks.setSadpCallback=e}setSliceCallback(e){this.bInit?this.oRequest.setSliceCallback(e):this.oCallbacks.setSliceCallback=e}setSerialCallback(e){this.bInit?this.oRequest.setSerialCallback(e):this.oCallbacks.setSerialCallback=e}setUIControlCallback(e){this.bInit?this.oRequest.setUIControlCallback(e):this.oCallbacks.setUIControlCallback=e}setUpgradeCallback(e){this.bInit?this.oRequest.setUpgradeCallback(e):this.oCallbacks.setUpgradeCallback=e}getServiceVersion(){return this.oRequest.getServiceVersion()}getRequestUUID(){return this.oRequest.getRequestUUID()}startService(e,t){const s={cmd:"system.startService",type:e};return void 0!==t&&(s.options=t),this.oRequest.sendRequest(s)}stopService(e){const t=this;return new Promise(((s,n)=>{null!==t.oRequest?t.oRequest.sendRequest({cmd:"system.stopService",type:e}).then((e=>{s(e)}),(e=>{n(e)})):n()}))}disconnect(){const e=this;return new Promise(((t,s)=>{null!==e.oRequest?(e.oRequest.disconnect(),t("cbConnectClose callback is really success")):s()}))}openDirectory(e){return this.oRequest.sendRequest({cmd:"system.openDirectory",path:e})}openFile(e,t,s){return this.oRequest.sendRequest({cmd:"system.openFile",path:e,relative:t,version:s})}selectDirectory(e,t){const s=this;return new Promise(((n,o)=>{null!==s.oRequest?s.oRequest.sendRequest({cmd:"system.selectDirectory",caption:void 0!==e&&""!==e?N.Base64().encode(e):"",dir:void 0!==t&&""!==t?N.Base64().encode(t):""}).then((e=>{""!==e.path&&(e.path=N.Base64().decode(e.path)),n(e)}),(e=>{o(e)})):o()}))}selectFile(e,t,s){const n=this;return new Promise(((o,i)=>{null!==n.oRequest?n.oRequest.sendRequest({cmd:"system.selectFile",caption:""!==e?N.Base64().encode(e):"",dir:""!==t?N.Base64().encode(t):"",filter:s}).then((e=>{""!==e.path&&(e.path=N.Base64().decode(e.path)),o(e)}),(e=>{i(e)})):i()}))}getLocalConfig(e){return this.oRequest.sendRequest({cmd:"system.getLocalConfig",default:e})}setLocalConfig(e){return e.cmd="system.setLocalConfig",this.oRequest.sendRequest(e)}createWnd(e,t,s,n,o,i,r,a=""){return this.oRequest.sendRequest({cmd:"window.createWnd",rect:{left:e,top:t,width:s,height:n},className:o,embed:i,activeXParentWnd:r,HWND:a})}showWnd(){return this.oRequest.sendRequest({cmd:"window.showWnd"})}hideWnd(){return this.oRequest.sendRequest({cmd:"window.hideWnd"})}destroyWnd(){const e=this;return new Promise(((t,s)=>{null!==e.oRequest?e.oRequest.sendRequest({cmd:"window.destroyWnd"}).then((e=>{t(e)}),(e=>{s(e)})):s()}))}setWndGeometry(e,t,s,n){return this.oRequest.sendRequest({cmd:"window.setWndGeometry",rect:{left:e,top:t,width:s,height:n}})}setWndCover(e,t){const s=this;return new Promise(((n,o)=>{null!==s.oRequest?s.oRequest.sendRequest({cmd:"window.setWndCover",position:e,size:t}).then((e=>{n(e)}),(e=>{o(e)})):o()}))}cuttingPartWindow(e,t,s,n,o){const i=this;return new Promise(((r,a)=>{null!==i.oRequest?i.oRequest.sendRequest({cmd:"window.cuttingPartWindow",rect:{left:e,top:t,width:s,height:n},round:o}).then((e=>{r(e)}),(e=>{a(e)})):a()}))}repairPartWindow(e,t,s,n,o){const i=this;return new Promise(((r,a)=>{null!==i.oRequest?i.oRequest.sendRequest({cmd:"window.repairPartWindow",rect:{left:e,top:t,width:s,height:n},round:o}).then((e=>{r(e)}),(e=>{a(e)})):a()}))}setWndZOrder(e){return this.oRequest.sendRequest({cmd:"window.setWndZOrder",flag:e})}changePlayMode(e){return this.oRequest.sendRequest({cmd:"window.changePlayMode",type:e})}setLanguageType(e){return this.oRequest.sendRequest({cmd:"window.setLanguageType",type:e})}initLoginInfo(e){return this.oRequest.sendRequest({cmd:"window.initLoginInfo",vsmAddress:e.vsmAddress,vsmPort:e.vsmPort,sessionID:e.sessionID,loginModel:e.loginModel,userType:e.userType,networkType:e.networkType})}setTranslateFile(e){return this.oRequest.sendRequest({cmd:"window.setTranslateFile",url:e})}switchToSimple(e){return this.oRequest.sendRequest({cmd:"window.switchToSimple",simple:e})}setVsmToken(e){return this.oRequest.sendRequest({cmd:"play.setVsmToken",token:e})}startPlay(e,t,s,n,o,i,r,a,l){const c={cmd:"play.startPlay",url:e,username:t,password:s,siteID:n,areaName:N.Base64().encode(o),cameraName:N.Base64().encode(i),permission:r,wndIndex:a};return void 0!==l&&(c.options=l,void 0!==c.options.siteName&&(c.options.siteName=N.Base64().encode(c.options.siteName))),this.oRequest.sendRequest(c)}setPreview3DPosition(e){return this.oRequest.sendRequest({cmd:"play.setPreview3DPosition",open:e})}stopTotal(){const e=this;return new Promise(((t,s)=>{null!==e.oRequest?e.oRequest.sendRequest({cmd:"play.stopTotal"}).then((e=>{t(e)}),(e=>{s(e)})):s()}))}setDragMode(e){return this.oRequest.sendRequest({cmd:"play.setDragMode",drag:e})}showErrorInfoInFullScreen(e){return this.oRequest.sendRequest({cmd:"play.showErrorInfoInFullScreen",error:N.Base64().encode(e)})}setNumberOfWindows(e){return this.oRequest.sendRequest({cmd:"play.setNumberOfWindows",number:e})}initCardReader(e){return this.oRequest.sendRequest({cmd:"serial.ACSInitCardReader",param:e})}unInitCardReader(){return this.oRequest.sendRequest({cmd:"serial.ACSUnInitCardReader"})}startAutoMode(){return this.oRequest.sendRequest({cmd:"serial.ACSStartAutoMode"})}stopAutoMode(){return this.oRequest.sendRequest({cmd:"serial.ACSStopAutoMode"})}initFingerprint(e){return this.oRequest.sendRequest({cmd:"serial.ACSInitFingerprint",param:e})}unInitFingerprint(){return this.oRequest.sendRequest({cmd:"serial.ACSUnInitFingerprint"})}startCollectFingerprint(){return this.oRequest.sendRequest({cmd:"serial.ACSStartCollectFingerprint"})}stopCollectFingerprint(){return this.oRequest.sendRequest({cmd:"serial.ACSStopCollectFingerprint"})}isCollectingFingerprint(){return this.oRequest.sendRequest({cmd:"serial.ACSIsCollectingFingerprint"})}initVideocapture(e){return e.majorTitle=N.Base64().encode(e.majorTitle),e.tip=N.Base64().encode(e.tip),e.captureBtnTxt=N.Base64().encode(e.captureBtnTxt),e.USBRemovedTip=N.Base64().encode(e.USBRemovedTip),this.oRequest.sendRequest({cmd:"serial.ACSStartCollectImage",param:e})}unInitVideocapture(){return this.oRequest.sendRequest({cmd:"serial.ACSStopCollectImage"})}registerDeviceType(e){return this.oRequest.sendRequest({cmd:"sadp.registDeviceType",deviceType:e})}activeOnlineDevice(e,t){return this.oRequest.sendRequest({cmd:"sadp.activeDevice",serialNumber:e,password:t})}refreshDeviceList(){return this.oRequest.sendRequest({cmd:"sadp.refreshDeviceList"})}modifyDeviceNetParam(e,t,s,n,o,i,r){return this.oRequest.sendRequest({cmd:"sadp.modifyDeviceParam",macAddress:e,password:t,ipv4Address:s,ipv4Gateway:n,ipv4SubnetMask:o,port:i,httpPort:r})}exportKeyFile(e){return this.oRequest.sendRequest({cmd:"sadp.exportKeyFile",serialNumber:e})}importKeyFile(){return this.oRequest.sendRequest({cmd:"sadp.importKeyFile"})}resetPassword(e,t,s,n){return this.oRequest.sendRequest({cmd:"sadp.resetPassword",serialNumber:e,password:t,importFileData:s,szCode:n})}uploadPicture(e){return this.oRequest.sendRequest({cmd:"slice.uploadPicture",path:N.Base64().encode(e)})}showSelectMenu(e,t,s,n,o){return this.oRequest.sendRequest({cmd:"ui.showSelectMenu",items:o,rect:{left:e,top:t,width:s,height:n}})}hideSelectMenu(){return this.oRequest.sendRequest({cmd:"ui.hideSelectMenu"})}destroySelectMenu(){const e=this;return new Promise(((t,s)=>{null!==e.oRequest?e.oRequest.sendRequest({cmd:"ui.destroySelectMenu"}).then((e=>{t(e)}),(e=>{s(e)})):s()}))}deviceConfig(e){return this.oRequest.sendRequest({cmd:"laputa.encodingDevice",param:e})}cloudStorageConfig(e){return this.oRequest.sendRequest({cmd:"laputa.cloudStorage",param:e})}ezvizRemoteConfig(e){return this.oRequest.sendRequest({cmd:"laputa.ezvizRemote",param:e})}showAlarmInfoInFullScreen(e,t,s){return this.oRequest.sendRequest({cmd:"window.showAlarmInfoInFullScreen",alarmTitle:e,alarmMessage:t,alarmId:s})}updateParentWnd(){return this.oRequest.sendRequest({cmd:"window.updateParentWnd"})}restoreWnd(){return this.oRequest.sendRequest({cmd:"window.restoreWnd"})}setImmediatePlaybackTime(e){return this.oRequest.sendRequest({cmd:"play.setImmediatePlaybackTime",specifyTime:e})}setDrawStatus(e){return this.oRequest.sendRequest({cmd:"draw.setDrawStatus",enable:e})}clearRegion(){return this.oRequest.sendRequest({cmd:"draw.clearRegion"})}setDrawShapeInfo(e,t){return this.oRequest.sendRequest({cmd:"draw.setDrawShapeInfo",drawType:e,drawInfo:t})}setGridInfo(e){return this.oRequest.sendRequest({cmd:"draw.setGridInfo",gridInfo:e})}getGridInfo(){return this.oRequest.sendRequest({cmd:"draw.getGridInfo"})}setPolygonInfo(e){return this.oRequest.sendRequest({cmd:"draw.setPolygonInfo",polygonInfo:e})}getPolygonInfo(){return this.oRequest.sendRequest({cmd:"draw.getPolygonInfo"})}setLineInfo(e){return this.oRequest.sendRequest({cmd:"draw.setLineInfo",lineInfo:e})}getLineInfo(){return this.oRequest.sendRequest({cmd:"draw.getLineInfo"})}setRectInfo(e){return this.oRequest.sendRequest({cmd:"draw.setRectInfo",rectInfo:e})}getRectInfo(){return this.oRequest.sendRequest({cmd:"draw.getRectInfo"})}clearShapeByType(e){return this.oRequest.sendRequest({cmd:"draw.clearShapeByType",type:e})}sensitiveEncrypt(e,t,s){const n={cmd:"laputa.sensitiveEncrypt",encryptType:e,encryptField:t};return void 0!==s&&(n.options=s),this.oRequest.sendRequest(n)}sendRequest(e){return this.oRequest.sendRequest(e)}requestInterface(e){const t={cmd:"window.requestInterface"};return t.requestParams=e,this.oRequest.sendRequest(t)}stopPlay(e){return void 0===e&&(e=-1),this.oRequest.sendRequest({cmd:"play.stopPlay",wndIndex:e})}showRemoteConfig(e){const t=this;return e.cmd="config.showRemoteConfig",new Promise(((s,n)=>{null!==t.oRequest?t.oRequest.sendRequest(e).then((e=>{s(e)}),(e=>{n(e)})):n()}))}video2Picture(){const e={cmd:"window.video2Picture"};return this.oRequest.sendRequest(e)}picture2Video(){const e={cmd:"window.picture2Video"};return this.oRequest.sendRequest(e)}ptzControl(e){return this.oRequest.sendRequest({cmd:"laputa.ptzControl",param:e})}simMouseClickEvent(e,t){return this.oRequest.sendRequest({cmd:"window.simMouseClickEvent",pointX:e,pointY:t})}us_SetMaxJobCount(e){return this.oRequest.sendRequest({cmd:"upgrade.setMaxJobCount",xml:e})}us_GetMaxJobCount(){return this.oRequest.sendRequest({cmd:"upgrade.getMaxJobCount"})}us_AddSchedule(e){return this.oRequest.sendRequest({cmd:"upgrade.addSchedule",xml:N.Base64().encode(e)})}us_DelSchedule(e){return this.oRequest.sendRequest({cmd:"upgrade.delSchedule",scheduleId:e})}us_GetScheduleList(e){const t=this;return new Promise(((s,n)=>{null!==t.oRequest?t.oRequest.sendRequest({cmd:"upgrade.getScheduleList",xml:e}).then((e=>{""!==e.xml&&(e.xml=N.Base64().decode(e.xml)),s(e)}),(e=>{n(e)})):n()}))}us_GetSchedule(e,t){const s=this;return new Promise(((n,o)=>{null!==s.oRequest?s.oRequest.sendRequest({cmd:"upgrade.getSchedule",xml:t,scheduleId:e}).then((e=>{""!==e.xml&&(e.xml=N.Base64().decode(e.xml)),n(e)}),(e=>{o(e)})):o()}))}us_UpgradeAction(e,t){return this.oRequest.sendRequest({cmd:"upgrade.upgradeAction",xml:t,scheduleId:e})}us_CheckUpgradeableDevice(e){return this.oRequest.sendRequest({cmd:"upgrade.checkUpgradeableDevice",param:e})}us_CheckUpgradeableDeviceList(e){return this.oRequest.sendRequest({cmd:"upgrade.checkUpgradeableDeviceList",param:e})}us_IsRunningAsyCheckUpgradeable(){return this.oRequest.sendRequest({cmd:"upgrade.isRunningAsyCheckUpgradeable"})}us_StopAsyCheckUpgradeable(){return this.oRequest.sendRequest({cmd:"upgrade.stopAsyCheckUpgradeable"})}getFishEyePTZPreset(e){return this.oRequest.sendRequest({cmd:"play.getFishEyePTZPreset",wndIndex:e})}setFishEyePTZPreset(e,t,s){return this.oRequest.sendRequest({cmd:"play.setFishEyePTZPreset",wndIndex:e,command:t,presetInfo:s})}controlFishEyePTZ(e,t,s,n){return this.oRequest.sendRequest({cmd:"play.controlFishEyePTZ",wndIndex:e,command:t,stop:s,speed:n})}controlFishEyeParol(e,t,s){return this.oRequest.sendRequest({cmd:"play.controlFishEyeParol",wndIndex:e,command:t,cruisePointList:s})}setFirstDayOfWeek(e){return this.oRequest.sendRequest({cmd:"window.setFirstDayOfWeek",firstDay:e})}setEhomePlayInfo(e,t,s,n,o,i){return this.oRequest.sendRequest({cmd:"play.setEhomePlayInfo",guid:e,protocal:t,session:s,token:n,ip:o,port:i})}startPlayPatch(e){if(e.length>0)for(let t=0,s=e.length;t<s;t++)e[t].areaName=N.Base64().encode(e[t].areaName),e[t].cameraName=N.Base64().encode(e[t].cameraName);return this.oRequest.sendRequest({cmd:"play.startPlayPatch",params:e})}grabOpen(){const e=this;return new Promise(((t,s)=>{null!==e.oRequest?e.oRequest.sendRequest({cmd:"window.grabOpen"}).then((e=>{t(e)}),(e=>{s(e)})):s()}))}setWndAutoPanState(e,t){return this.oRequest.sendRequest({cmd:"play.setWndAutoPanState",wndIndex:e,open:t})}enablePrivileges(){return this.oRequest.sendRequest({cmd:"system.enablePrivileges"})}},Ls=class{constructor(e){const t=this;this.oOptions=Object.assign({szPluginContainer:"",cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,iServicePortStart:16960,iServicePortEnd:16969,szClassId:"55A7329E-FAAD-439a-87BC-75BAB3332E7C"},e),this.bFreeze=!1,this.bFocus=!0,this.bEmbed=N.getCreateWndMode(),this.szWndId="",this.iCreateWndTimer=-1,this.iUpdateParentWndTimer=-1,this.bDevTool=!1,this.iVCTimeStart=-1,this.iVCTimeEnd=-1,this.oWndCover={left:0,top:0,right:0,bottom:0},this.oDocOffset={left:0,top:0},this.szTitle="",this.oWindowAttr={outerWidth:0,innerWidth:0,outerHeight:0,innerHeight:0,screenTop:0,screenLeft:0,screenX:0,screenY:0},this.iFixedResizeTimer=-1,this.fVisibilityChange=function(){if(N.isMacOS())document.hidden?t.fHideWnd():t.fShowWnd();else if(document.hidden)t.iVCTimeStart=(new Date).getTime(),t.fHideWnd();else{t.iVCTimeEnd=(new Date).getTime();const e=N.browser();if(e.chrome||e.mozilla)if(t.iUpdateParentWndTimer>0&&(clearTimeout(t.iUpdateParentWndTimer),t.iUpdateParentWndTimer=-1),t.iVCTimeEnd-t.iVCTimeStart<100){const e=t.oRequest.getRequestUUID();G(e,t.oOptions.cbSetDocTitle),t.iUpdateParentWndTimer=setTimeout((()=>{t.oRequest.updateParentWnd().then((()=>{j(e,t.oOptions.cbUnsetDocTitle),t.bFreeze||t.bDevTool||t.fShowWnd()}),(()=>{j(e,t.oOptions.cbUnsetDocTitle)}))}),100)}else t.bFreeze||t.bDevTool||t.fShowWnd();else t.bFreeze||t.bDevTool||t.fShowWnd()}},this.fHideWnd=function(){t.oRequest.hideWnd().then((()=>{}),(()=>{}))},this.fShowWnd=function(){t.oRequest.showWnd().then((()=>{}),(()=>{}))},this.fFocus=function(){t.bFocus=!0,setTimeout((()=>{t.removeGrabImage(),document.hidden||t.bFreeze||t.bDevTool||t.fShowWnd()}),200)},this.fBlur=function(){t.bFocus=!1},this.removeGrabImage=function(){if(!N.isMacOS()){let e=null;if(""!==t.szWndId&&(e=document.getElementById(t.szWndId),e)){const t=e.querySelectorAll('[data-name="wc-grab-open-image"]');Array.prototype.slice.call(t).forEach((e=>{e.parentNode.removeChild(e)}))}}},this.oRequest=new Js({szPluginContainer:this.oOptions.szPluginContainer,cbConnectSuccess:this.oOptions.cbConnectSuccess,cbConnectError:this.oOptions.cbConnectError,cbConnectClose(e){t.iCreateWndTimer>0&&(clearTimeout(t.iCreateWndTimer),t.iCreateWndTimer=-1),t.removeGrabImage(),t.oOptions.cbConnectClose&&t.oOptions.cbConnectClose(e)},iServicePortStart:this.oOptions.iServicePortStart,iServicePortEnd:this.oOptions.iServicePortEnd,szClassId:this.oOptions.szClassId})}JS_SetWindowControlCallback(e){const t=this,s={cbSelectWnd(t){e.cbSelectWnd&&e.cbSelectWnd(parseInt(t.wndIndex,10),t.cameraID,t.siteID,t.opendFisheye)},cbTogglePTZ(t){e.cbTogglePTZ&&e.cbTogglePTZ(t.cameraID,t.siteID)},cbUpdateCameraIcon(t){e.cbUpdateCameraIcon&&e.cbUpdateCameraIcon(t.cameraID,parseInt(t.playing,10),t.siteID)},cbGetLastError(t){e.cbGetLastError&&e.cbGetLastError(t.error,parseInt(t.type,10))},cbTalkUrlEmpty(t){e.cbTalkUrlEmpty&&e.cbTalkUrlEmpty(t.cameraID)},cbGotoPlayback(t){e.cbGotoPlayback&&e.cbGotoPlayback(t.cameraID,t.siteID)},cbShowDisplayInfo(t){e.cbShowDisplayInfo&&e.cbShowDisplayInfo(parseInt(t.videoWidth,10),parseInt(t.videoHeight,10),parseInt(t.frameRate,10))},cbPreviewWnd3DPostion(t){e.cbPreviewWnd3DPostion&&e.cbPreviewWnd3DPostion(parseInt(t.startX,10),parseInt(t.startY,10),parseInt(t.endX,10),parseInt(t.endY,10))},cbStopPlayAll(){e.cbStopPlayAll&&e.cbStopPlayAll()},cbWheelEvent(t){e.cbWheelEvent&&e.cbWheelEvent(parseInt(t.delta,10))},cbAlarmDetail(t){e.cbAlarmDetail&&e.cbAlarmDetail(t.alarmId)},cbQuitedFullScreen(){setTimeout((()=>{t.fShowWnd()}),100)},cbManuallyClose(t){e.cbManuallyClose&&e.cbManuallyClose(t.cameraID,t.siteID,parseInt(t.wndIndex,10))},cbIntegrationCallBack(t){e.cbIntegrationCallBack&&e.cbIntegrationCallBack(t)},cbChangeStorage(t){e.cbChangeStorage&&e.cbChangeStorage(parseInt(t.storageType,10),t.cameraID,t.siteID)},cbFisheyeExpandChanged(t){e.cbFisheyeExpandChanged&&e.cbFisheyeExpandChanged(t.cameraID,t.siteID,parseInt(t.wndIndex,10),t.open)},cbGetEhomePlayInfo(t){e.cbGetEhomePlayInfo&&e.cbGetEhomePlayInfo(t.siteID,t.guid)},cbWndPtzControl(t){e.cbWndPtzControl&&e.cbWndPtzControl(parseInt(t.wndIndex,10),t.cameraID,t.command,t.speed,t.stop)},cbMessageCallBack(s){s=s.data;const n=t.oRequest.getRequestUUID();"menuOpen"===s.type?""!==t.szWndId&&(document.getElementById(t.szWndId).innerHTML=`<img data-name='wc-grab-open-image' src='data:image/png;base64,${s.message.image}' width='100%' height='100%' />`):"changeTitle"===s.type?-1===document.title.indexOf(n)&&(t.szTitle=document.title,G(n,t.oOptions.cbSetDocTitle),setTimeout((()=>{"updateParentWnd"===s.message?t.oRequest.updateParentWnd():"restoreWnd"===s.message&&t.oRequest.restoreWnd()}),300)):"changeTitleDone"===s.type?""!==t.szTitle&&j(n,t.oOptions.cbUnsetDocTitle):"splitChange"===s.type?e.cbSplitChange&&e.cbSplitChange(s.message.splitType):"showMaximized"===s.type&&e.cbShowMaximized&&e.cbShowMaximized(s.message.showMax)}};this.oRequest.setWindowControlCallback(s)}JS_SetSadpCallback(e){const t={cbDeviceFind:null};Object.assign(t,e),this.oRequest.setSadpCallback(t)}JS_SetSliceCallback(e){const t={cbImageSliced(t){e.cbImageSliced&&(""!==t.picName&&(t.picName=N.Base64().decode(t.picName)),e.cbImageSliced(t))}};this.oRequest.setSliceCallback(t)}JS_SetSerialCallback(e){const t={cbCardFind(t){e.cbCardFind&&e.cbCardFind(t)},cbFingerFind(t){e.cbFingerFind&&e.cbFingerFind(t.fingerPrint,t.fingerQuality)},cbImageFind(t){e.cbImageFind&&e.cbImageFind(t.image)},cbImageErrorFind(t){e.cbImageErrorFind&&e.cbImageErrorFind(t.errorModule,t.errorCode)},cbImageWndVisibleFind(t){e.cbImageWndVisibleFind&&e.cbImageWndVisibleFind(t.visible)}};this.oRequest.setSerialCallback(t)}JS_SetUIControlCallback(e){const t={cbClickMenuItem(t){e.cbClickMenuItem&&e.cbClickMenuItem(t.itemIndex)},cbMenuMouseIn(){e.cbMenuMouseIn&&e.cbMenuMouseIn()},cbMenuMouseOut(){e.cbMenuMouseOut&&e.cbMenuMouseOut()}};this.oRequest.setUIControlCallback(t)}JS_SetUpgradeCallback(e){const t={cbCheckUpgrade(t){e.cbCheckUpgrade&&e.cbCheckUpgrade(t)}};this.oRequest.setUpgradeCallback(t)}JS_CheckVersion(e){let t=this.oRequest.getServiceVersion(),s=[],n=[];""!==t&&(t=t.replace(/,[\s]*/g,"."),s=t.split(".")),""!==e&&(n=(e=e.replace(/,[\s]*/g,".")).split("."));let o=!1;if(n.length===s.length)for(let e=0,t=s.length;e<t;e++)if(parseInt(n[e],10)!==parseInt(s[e],10)){if(parseInt(n[e],10)>parseInt(s[e],10)){o=!0;break}break}return o}JS_StartService(e,t){return this.oRequest.startService(e,t)}JS_StopService(e){return this.oRequest.stopService(e)}JS_Disconnect(){return this.oRequest.disconnect()}JS_OpenDirectory(e){return this.oRequest.openDirectory(e)}JS_OpenFile(e,t,s){return this.oRequest.openFile(e,t,s)}JS_SelectDirectory(e,t){return this.oRequest.selectDirectory(e,t)}JS_SelectFile(e,t,s){return this.oRequest.selectFile(e,t,s)}JS_GetLocalConfig(e){return this.oRequest.getLocalConfig(e)}JS_SetLocalConfig(e){return this.oRequest.setLocalConfig(e)}JS_SetDocOffset(e){return e&&(this.oDocOffset=e),!0}JS_SetWindowAttr(e){return e&&(this.oWindowAttr=e),!0}JS_CreateWnd(e,t,s,n){const o=this;this.szWndId=e,void 0!==(n=n||{}).bEmbed&&(this.bEmbed=n.bEmbed);let i=!0;return void 0!==n.bActiveXParentWnd&&(i=n.bActiveXParentWnd),new Promise(((r,a)=>{const l=document.getElementById(e);if(l){let e="";N.browser().msie?e="IEFrame":N.browser().chrome?e="Chrome":N.browser().safari&&(e=window.top.document.title),n.cbSetDocTitle&&(o.oOptions.cbSetDocTitle=n.cbSetDocTitle),n.cbUnsetDocTitle&&(o.oOptions.cbUnsetDocTitle=n.cbUnsetDocTitle);const c=o.oRequest.getRequestUUID();G(c,n.cbSetDocTitle),o.iCreateWndTimer=setTimeout((()=>{if(!o.bDevTool){const d=N.getDevicePixelRatio(),u=N.getWndPostion(l,o.bEmbed,o.oWindowAttr,o.oDocOffset);t=Math.round(t*d),s=Math.round(s*d),o.oRequest.createWnd(u.left,u.top,t,s,e,o.bEmbed,i,n.HWND||"").then((()=>{j(c,n.cbUnsetDocTitle),r()}),(e=>{j(c,n.cbUnsetDocTitle),5001===e.errorCode?(document.hidden||o.bFreeze||!o.bFocus||o.fShowWnd(),r()):a(e)}))}}),300),document.addEventListener("visibilitychange",o.fVisibilityChange,!1),window.addEventListener("focus",o.fFocus),window.addEventListener("blur",o.fBlur)}else a()}))}JS_ShowWnd(){this.bFreeze=!1,document.hidden||this.bDevTool||this.fShowWnd()}JS_HideWnd(){this.bFreeze=!0,this.fHideWnd()}JS_DestroyWnd(){return document.removeEventListener("visibilitychange",this.fVisibilityChange,!1),window.removeEventListener("focus",this.fFocus),window.removeEventListener("blur",this.fBlur),this.oRequest.destroyWnd()}JS_Resize(e,t,s){let n=null;const o=e,i=t;if(""!==this.szWndId&&(n=document.getElementById(this.szWndId)),n){const r=N.getWndPostion(n,this.bEmbed,this.oWindowAttr,this.oDocOffset),a=N.getDevicePixelRatio();(!N.browser().msie||N.browser().msie&&"11.0"===N.browser().version)&&(this.oWndCover.left>0&&(r.left+=Math.round(this.oWndCover.left*a),e-=this.oWndCover.left),this.oWndCover.top>0&&(r.top+=Math.round(this.oWndCover.top*a),t-=this.oWndCover.top),this.oWndCover.right>0&&(e-=this.oWndCover.right),this.oWndCover.bottom>0&&(t-=this.oWndCover.bottom)),e=Math.round(e*a),t=Math.round(t*a),this.oRequest.setWndGeometry(r.left,r.top,e,t),(N.browser().msie&&"11.0"===N.browser().version||!N.isWindows())&&(s&&s.bFixed?this.iFixedResizeTimer=-1:(this.iFixedResizeTimer>-1&&(clearTimeout(this.iFixedResizeTimer),this.iFixedResizeTimer=-1),this.iFixedResizeTimer=setTimeout((()=>{this.JS_Resize(o,i,{bFixed:!0})}),300)))}}JS_SetWndCover(e,t){const s=N.getDevicePixelRatio();return(!N.browser().msie||N.browser().msie&&"11.0"===N.browser().version)&&("left"===e?this.oWndCover.left=t:"top"===e?this.oWndCover.top=t:"right"===e?this.oWndCover.right=t:"bottom"===e&&(this.oWndCover.bottom=t)),t=Math.round(t*s),this.oRequest.setWndCover(e,t)}JS_CuttingPartWindow(e,t,s,n,o){const i=N.getDevicePixelRatio();return e=Math.round(e*i),t=Math.round(t*i),s=Math.round(s*i),n=Math.round(n*i),o=Math.round(o*i),this.oRequest.cuttingPartWindow(e,t,s,n,o)}JS_RepairPartWindow(e,t,s,n,o){const i=N.getDevicePixelRatio();return e=Math.round(e*i),t=Math.round(t*i),s=Math.round(s*i),n=Math.round(n*i),o=Math.round(o*i),this.oRequest.repairPartWindow(e,t,s,n,o)}JS_ChangePlayMode(e){return this.oRequest.changePlayMode(e)}JS_SetLanguageType(e){return this.oRequest.setLanguageType(e)}JS_InitLoginInfo(e){return this.oRequest.initLoginInfo(e)}JS_SetTranslateFile(e){return this.oRequest.setTranslateFile(e)}JS_SwitchToSimple(e){return this.oRequest.switchToSimple(e)}JS_SetVsmToken(e){return this.oRequest.setVsmToken(e)}JS_Play(e,t,s,n,o,i,r,a,l){return this.oRequest.startPlay(e,t,s,n,o,i,r,a,l)}JS_Enable3DZoom(e){return this.oRequest.setPreview3DPosition(e)}JS_StopTotal(){return this.oRequest.stopTotal()}JS_SetDragMode(e){return this.oRequest.setDragMode(e)}JS_ShowErrorInfoInFullScreen(e){return this.oRequest.showErrorInfoInFullScreen(e)}JS_SetNumberOfWindows(e){return this.oRequest.setNumberOfWindows(e)}JS_InitCardReader(e){return this.oRequest.initCardReader(e)}JS_UnInitCardReader(){return this.oRequest.unInitCardReader()}JS_StartAutoMode(){return this.oRequest.startAutoMode()}JS_StopAutoMode(){return this.oRequest.stopAutoMode()}JS_InitFingerprint(e){return this.oRequest.initFingerprint(e)}JS_UnInitFingerprint(){return this.oRequest.unInitFingerprint()}JS_StartCollectFingerprint(){return this.oRequest.startCollectFingerprint()}JS_StopCollectFingerprint(){return this.oRequest.stopCollectFingerprint()}JS_IsCollectingFingerprint(){return this.oRequest.isCollectingFingerprint()}JS_InitVideocapture(e){return this.oRequest.initVideocapture(e)}JS_UnInitVideocapture(){return this.oRequest.unInitVideocapture()}JS_RegisterDeviceType(e){return this.oRequest.registerDeviceType(e)}JS_ActiveOnlineDevice(e,t){return this.oRequest.activeOnlineDevice(e,t)}JS_RefreshDeviceList(){return this.oRequest.refreshDeviceList()}JS_ModifyDeviceNetParam(e,t,s,n,o,i,r){return this.oRequest.modifyDeviceNetParam(e,t,s,n,o,i,r)}JS_ExportKeyFile(e){return this.oRequest.exportKeyFile(e)}JS_ImportKeyFile(){return this.oRequest.importKeyFile()}JS_ResetPassword(e,t,s,n){return this.oRequest.resetPassword(e,t,s,n)}JS_UploadPicture(e){return this.oRequest.uploadPicture(e)}JS_ShowSelectMenu(e,t,s,n,o){const i=document.getElementById(e);if(i){const e=N.getWndPostion(i,!1,this.oWindowAttr,this.oDocOffset);"center"===o?e.left-=Math.round((t-i.offsetWidth)/2):"right"===o&&(e.left-=Math.round(t-i.offsetWidth));const r=N.getDevicePixelRatio();t=Math.round(t*r),s=Math.round(s*r);const a=1*window.getComputedStyle(i).height.slice(0,-2),l=Math.round(a*r);this.oRequest.showSelectMenu(e.left,e.top+l,t,s,n)}}JS_HideSelectMenu(){this.oRequest.hideSelectMenu()}JS_DestroySelectMenu(){return this.oRequest.destroySelectMenu()}JS_DeviceConfig(e){return this.oRequest.deviceConfig(e)}JS_CloudStorageConfig(e){return this.oRequest.cloudStorageConfig(e)}JS_EzvizRemoteConfig(e){return this.oRequest.ezvizRemoteConfig(e)}JS_ShowAlarmInfoInFullScreen(e,t,s){return this.oRequest.showAlarmInfoInFullScreen(e,t,s)}JS_SetImmediatePlaybackTime(e){return this.oRequest.setImmediatePlaybackTime(e)}JS_SetDrawStatus(e){return this.oRequest.setDrawStatus(e)}JS_ClearRegion(){return this.oRequest.clearRegion()}JS_SetDrawShapeInfo(e,t){return this.oRequest.setDrawShapeInfo(e,t)}JS_SetGridInfo(e){return this.oRequest.setGridInfo(e)}JS_GetGridInfo(){return this.oRequest.getGridInfo()}JS_SetPolygonInfo(e){return this.oRequest.setPolygonInfo(e)}JS_GetPolygonInfo(){return this.oRequest.getPolygonInfo()}JS_SetLineInfo(e){return this.oRequest.setLineInfo(e)}JS_GetLineInfo(){return this.oRequest.getLineInfo()}JS_SetRectInfo(e){return this.oRequest.setRectInfo(e)}JS_GetRectInfo(){return this.oRequest.getRectInfo()}JS_ClearShapeByType(e){return this.oRequest.clearShapeByType(e)}JS_SensitiveEncrypt(e,t,s){return this.oRequest.sensitiveEncrypt(e,t,s)}JS_SendRequest(e){return this.oRequest.sendRequest(e)}JS_RequestInterface(e){return this.oRequest.requestInterface(e)}JS_StopPlay(e){return this.oRequest.stopPlay(e)}JS_ShowRemoteConfig(e){return this.oRequest.showRemoteConfig(e)}static JS_WakeUp(e){const t=document.createElement("iframe");t.style.display="none",t.src=e,document.body.appendChild(t),setTimeout((()=>{document.body.removeChild(t)}),3e3)}JS_Video2Picture(){return this.oRequest.video2Picture()}JS_Picture2Video(){return this.oRequest.picture2Video()}JS_PtzControl(e){return this.oRequest.ptzControl(e)}JS_SimMouseClickEvent(e,t){return this.oRequest.simMouseClickEvent(e,t)}JS_US_SetMaxJobCount(e){return this.oRequest.us_SetMaxJobCount(e)}JS_US_GetMaxJobCount(){return this.oRequest.us_GetMaxJobCount()}JS_US_AddSchedule(e){return this.oRequest.us_AddSchedule(e)}JS_US_DelSchedule(e){return this.oRequest.us_DelSchedule(e)}JS_US_GetScheduleList(e){return this.oRequest.us_GetScheduleList(e)}JS_US_GetSchedule(e,t){return this.oRequest.us_GetSchedule(e,t)}JS_US_UpgradeAction(e,t){return this.oRequest.us_UpgradeAction(e,t)}JS_US_CheckUpgradeableDevice(e){return this.oRequest.us_CheckUpgradeableDevice(e)}JS_US_CheckUpgradeableDeviceList(e){return this.oRequest.us_CheckUpgradeableDeviceList(e)}JS_US_IsRunningAsyCheckUpgradeable(){return this.oRequest.us_IsRunningAsyCheckUpgradeable()}JS_US_StopAsyCheckUpgradeable(){return this.oRequest.us_StopAsyCheckUpgradeable()}JS_GetFishEyePTZPreset(e){return this.oRequest.getFishEyePTZPreset(e)}JS_SetFishEyePTZPreset(e,t,s){return this.oRequest.setFishEyePTZPreset(e,t,s)}JS_ControlFishEyePTZ(e,t,s,n){return this.oRequest.controlFishEyePTZ(e,t,s,n)}JS_ControlFishEyeParol(e,t,s){return this.oRequest.controlFishEyeParol(e,t,s)}JS_SetFirstDayOfWeek(e){return this.oRequest.setFirstDayOfWeek(e)}JS_SetEhomePlayInfo(e,t,s,n,o,i){return this.oRequest.setEhomePlayInfo(e,t,s,n,o,i)}JS_PlayPatch(e){return this.oRequest.startPlayPatch(e)}JS_SetWndAutoPanState(e,t){return this.oRequest.setWndAutoPanState(e,t)}JS_EnablePrivileges(){return this.oRequest.enablePrivileges()}};Ls.version="1.2.5";export{Ls as WebControl};
