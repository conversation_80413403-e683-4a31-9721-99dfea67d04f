<template>
    <!-- <suggestion-edit @search="search"></suggestion-edit> -->
    <el-row :gutter="20">
        <el-col :span="6" style="display:flex">
            <el-input style="margin-right:10px;width:50%" @keydown.enter="search(searchModel.keyWord)" v-model="searchModel.keyWord"
                placeholder="请输入服务名称" clearable />
            <el-button type="primary" @click="search(searchModel.keyWord)">搜 索</el-button>
            <el-button type="primary" @click="refresh">刷 新</el-button>
            <el-button type="primary" @click="memoryContrast">内存对比</el-button>
        </el-col>
        <!-- <el-col :span="4" :push="14">
				<el-button style="float: right;" :disabled="ids.length == 0" type="primary" @click="deleted">删 除</el-button>
			</el-col> -->
    </el-row>
    <el-row :gutter="20">
        <el-col :span="24">
            <el-table row-key="id" stripe height="calc(100vh - 265px)" :data="serverList" border style="width: 100%;"
                @selection-change="handleSelectionChange">
                <el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
                <el-table-column width="70px" type="index" align="center" :index="indexMethod" label="序号" />
                <el-table-column prop="serviceName" align="center" label="服务名" />
                <el-table-column width="200px" prop="host" align="center" label="IP" />
                <el-table-column prop="hostName" align="center" label="主机名" />
                <el-table-column width="100px" prop="port" align="center" label="端口号" />
                <el-table-column width="100px" prop="status" align="center" label="状态">
                    <template #default="scope">
                        <el-tag v-if="scope.row.status == 'UP'" type="success">{{ scope.row.status }}</el-tag>
                        <el-tag v-else type="danger">{{ scope.row.status }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="registerTime" align="center" sortable label="注册时间" />
                <el-table-column prop="renewalTime" align="center" sortable label="续约时间" />
                <el-table-column align="center" label="操作">
                    <template #default="scope">
                        <el-button type="text" size="default" @click="configuration(scope.row.serviceName)">配 置</el-button>
                        <el-button type="text" size="default" @click="log(scope.row)">日 志</el-button>
                        <el-dropdown style="margin-left:8px">
                            <el-button type="text" size="default">监 控</el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>
                                        <el-button type="text" size="default"
                                            @click="jumpToServer(scope.row.host)">服务监控</el-button>
                                        <el-button type="text" size="default"
                                            @click="jumpToCache(scope.row.host)">缓存监控</el-button>
                                        <el-button type="text" size="default"
                                            @click="jumpToJava(scope.row)">java虚拟运行</el-button>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>
        </el-col>

    </el-row>
    <el-dialog width="80%" top="3vh" v-loading="loading" v-model="dialog.show" :title="dialog.title">
        <div class="dialog-content">
            <el-scrollbar class="scroll">
                <div class="log-list" v-for="item in logList" :key="item" @click="check(item.fileName)">
                    <div>{{item.fileName}}</div>   <div style="width:82px">({{ formatFileSize(item.fileLength) }})</div>
                </div>
            </el-scrollbar>
            <div>
                文件创建时间：{{ createTime }}
                <el-button @click="logWrap" type="primary" style="float:right;margin-bottom:5px">自 动 换 行</el-button>
                <el-input
                    :wrap="wrap"
                    class="input-ta"
                    v-model="logDetail"
                    show-word-limit
                    :rows="32"
                    type="textarea"
                    placeholder="点击按钮查看日志"
                />
                <!-- <p class="LogViewer">{{ logDetail }}</p> -->
            </div>
        </div>
        
    </el-dialog>
</template>
<script>
import { Base64 } from 'js-base64';
import { encode, decode } from 'js-base64';
import { ElLoading } from 'element-plus'
import { getServerList, getLogList, getLogDetail } from "@/api/monitor/serverList"
export default {
    data() {
        return {
            searchModel: {},
            serverList: [],
            ids: [],
            logList: [],
            loading: false,
            dialog: {
                show: false,
                title: '日志列表'
            },
            logDetail: '',
            ipPort: {},
            fullLoading: null,
            bytes: 0,
            wrap: "on",
            createTime: ''
        }
    },
    methods: {
        start(res) {
            console.log("启动", res);
        },
        suspend(res) {
            console.log("暂停", res);
        },
        configuration(res) {
            console.log("配置", res);
            // this.$router.push({
            //     // 跳转到的页面路径
            //     path: '/test/'+res,
            //     // 传递的参数集合
            // })
        },
        log(data) {
            this.dialog.show = true;
            this.dialog.title = `日志列表（${data.serviceName}）`;
            console.info("row:", data);
            getLogList({ ip: data.host, port: data.port }).then(res => {
                this.logList = res.data.result
                this.ipPort = { ip: data.host, port: data.port }
            });
        },
        logWrap() {
            if (this.wrap == 'off') {
                this.wrap = "on"
            } else {
                this.wrap = "off"
            }
        },
        back() {
            this.logDetail = ''
            getLogList(this.ipPort).then(res => {
                this.logList = res.data.result
            })
        },

        formatFileSize(bytes) {
            const units = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            let size = bytes;
            let unitIndex = 0;

            while (size >= 1024 && unitIndex < units.length - 1) {
                size /= 1024;
                unitIndex++;
            }

            // 保留两位小数，四舍五入
            size = Math.round(size * 100) / 100;

            return `${size} ${units[unitIndex]}`;
        },
        async check(fileName) {
            this.fullLoading = ElLoading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.7)', text: '正在加载，请不要刷新！！！' });
            this.ipPort.fileName = fileName
            await getLogDetail(this.ipPort).then(res => {
                this.fullLoading.close()
                var file = res.data.result.file
                this.createTime = res.data.result.createTime
                this.logDetail = file
                // this.logDetail = decodeURIComponent(atob(file))
            })
        },
        jumpToServer(res) {
            console.log("服务监控", res);
            this.$router.push({
                // 跳转到的页面路径
                path: '/server',
                // 传递的参数集合
                query: {
                    ip: res
                }
            })
        },
        jumpToCache(res) {
            console.log("缓存监控", res);
            this.$router.push({
                // 跳转到的页面路径
                path: '/cache',
                // 传递的参数集合
                query: {
                    ip: res
                }
            })
        },
        jumpToJava(row) {
            this.$router.push({
                // 跳转到的页面路径
                path: '/jvmMonitor',
                // 传递的参数集合
                query: {
                    port: row.port,
                    ip: row.host
                }
            })
        },
        memoryContrast() {
            alert('开发中...')
        },
        indexMethod(index) {
            return index + 1
        },
        /** 查询服务列表 */
        getList() {
            getServerList().then(res => {
                this.serverList = res.data.result;
                // this.loading.close();
            });
        },
        /*** 搜索 */
        search(keyWord) {
            if (keyWord) {
                console.log(keyWord);
                this.serverList = this.serverList.filter(item => item.serviceName.includes(keyWord))
            } else {
                this.getList()
            }
        },
        refresh() {
            this.getList()
            this.searchModel.keyWord = ""
        }
    },
    created() {
        this.getList()
    },
    watch: {
        "dialog.show"(newVal, oldVal) {
            if (!newVal) {
                this.logDetail = ''
                getLogList(this.ipPort).then(res => {
                    this.logList = res.data.result
                })
            }
        }
    }

}
</script>

<style scoped lang="scss">

.el-row {
    margin-bottom: 20px;
    background-color: #fff;
    padding: 20px 10px;
    border-radius: 5px;
}

.title_des {
    font-size: 15px;
    display: flex;
    align-items: flex-end;
}
.log-list:nth-child(2n-1){
    background-color: rgba(248, 248, 248);
}
.log-list{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 10px;
    height: 35px;
    line-height: 35px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
}
.log-list:hover{
    background-color: rgb(227, 233, 241);
}

.scroll{
    height: vh(850) !important;
    // height:vh(850);
    width: 22% !important;
    margin-right: 15px;
}
.dialog-content{
    height: vh(850);
    width: vw(1480);
    display: flex; 
    flex-direction: row;
    >div{
        height: vh(850);
        width:78%;
    }
}
</style>