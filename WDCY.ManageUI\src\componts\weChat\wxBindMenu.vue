<template>
    <el-dialog draggable width="25%" top="5vh" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
        <el-form ref="form" :model="wxBindRoleModel" :rules="rules" label-width="60px">
          <el-row>
            <el-col>
              <el-form-item label="用户组" prop="groupId">
                <el-cascader :show-all-levels="false" style="width: 100%;" v-model="wxBindRoleModel.groupId" :props="{ checkStrictly: true }" :options="userGroupList" @change="handleChange" clearable placeholder="选择组"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="角色" prop="roleId">
                <el-select style="width: 100%;" v-model="wxBindRoleModel.roleId" placeholder="选择角色" clearable>
                  <el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row justify="center">
          <el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
        </el-row>
      </el-dialog>
  </template>
  
  <script>
  import mitt from "@/utils/mitt"
  // import { listRole } from "@/api/admin/role"
  import { listUserGroup } from "@/api/admin/userGroup"
  import { wxBindMenu } from "@/api/weChat/weChatUser"
  export default {
    props: ["roleList", "userGroupList"],
    data() {
      return {
        loading: false,
        bindMenuList: [],
			  // roleList:[],
        dialog: {},
        mask: false,
        wxBindRoleModel: {},
        imgServer: import.meta.env.VITE_BASE_API,
      }
    },
    methods: {
      handleChange(e){
        if(e == null){
          this.wxBindRoleModel.groupId = null
          return
        }
        this.wxBindRoleModel.groupId = e[e.length-1]
      },
      onSubmit() {
        this.$refs["form"].validate(valid => {
          if (valid) {
              wxBindMenu(this.wxBindRoleModel)
                .then(res => {
                  console.log(res);
                  this.$message.success(res.data.msg)
                  this.$emit("search")
                  this.dialog.show = false
                })
          }
        });
      },
      async init(){
        // let role_res = await listRole({pageSize:99999})
				// this.roleList = role_res.data.result.list

        // let resGroup = await listUserGroup({totalize:99999})
				// var groupStr = JSON.stringify(resGroup.data.result.list).replaceAll('groupName','label').replaceAll('id','value')
				// this.userGroupList = JSON.parse(groupStr)
      }
    },
    created(){
        this.init()
    },
    mounted() {
      this.$nextTick(function () {
        mitt.on("openWxBind", (data) => {
          console.log(data);
          this.groupId=data.groupId
          this.wxBindRoleModel = {
            id: data.id,
            groupId: data.groupId,
            roleId: data.roleId
          }
          this.dialog.show = true
          this.dialog.title = "绑定角色、组"
        })
      })
    },
  }
  </script>
  <style scoped>
  .avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  
  .upload {
    border: 1px dashed #ddd;
    border-radius: 6px;
  }
  
  div /deep/.avatar-uploader .el-upload {
    /* border: 1px dashed #ddd; */
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }
  
  .avatar-uploader:hover,
  .el-upload:hover {
    border-color: #409eff;
  }
  
  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
  }
  </style>