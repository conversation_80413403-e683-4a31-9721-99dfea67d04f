<template>
  <water-mask></water-mask>
  <el-container style="height: 100vh; background-color: #f1f4f5">
    <!-- 左侧区域 -->
    <el-aside
      width="200px"
      style="height: 100vh; overflow-x: hidden; background-color: #314157"
    >
      <el-scrollbar height="100vh">
        <!-- logo区域 -->
        <el-row
          style="
            height: 5vh;
            background-color: #314157;
            display: flex;
            flex-direction: column;
          "
        >
          <el-col :span="24" @click="routeToHome" class="logoText">
            <el-image :src="logo" style="height: 30px; width: 30px"></el-image
            >&nbsp;&nbsp;{{ systemShortName
            }}<sub style="font-size: 10px; padding: 10px 0px 0px 10px"
              >v{{ appInfo.version }}</sub
            >
          </el-col>
        </el-row>
        <!-- 左侧菜单  default-active="2" :default-openeds="['0']"-->
        <el-menu
          unique-opened
          background-color="#314157"
          :router="true"
          :default-openeds="['0']"
          :default-active="nowRoute"
          style="height: 95vh; border: 0"
          text-color="#fff"
        >
          <template v-for="(item, index) in secondMenuList" :key="index">
            <!-- 二级菜单，带图标 -->
            <el-sub-menu
              :key="index"
              :index="index + ''"
              v-if="item.children.length > 0 && !item.hidden"
              v-click="index"
            >
              <template #title>
                <svg class="icona" aria-hidden="true">
                  <use :xlink:href="item.icon"></use>
                </svg>
                <span>{{ item.menuName }}</span>
              </template>
              <!-- 三级菜单 -->
              <el-menu-item
                v-for="route in item.children"
                :key="route.id"
                :index="route.path"
              >
                {{ route.menuName }}
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item v-else-if="!item.hidden" :index="item.path">
              <svg class="icona" aria-hidden="true">
                <use :xlink:href="item.icon"></use>
              </svg>
              {{ item.menuName }}
            </el-menu-item>
          </template>
        </el-menu>
      </el-scrollbar>
    </el-aside>
    <!-- 右侧区域 -->
    <el-container>
      <!-- 顶部区域 -->
      <el-header>
        <!-- :default-active="activeIndex" @select="handleSelect" -->
        <!-- 顶部总菜单 -->
        <div style="justify-content: space-between">
          <el-menu
            mode="vertical"
            class="el-menu-demo"
            active-text-color="#4b96ff"
            style="display: flex; border-right: 0px; background-color: #fff"
          >
            <!-- 一级菜单，带图标 -->
            <el-menu-item
              v-for="(item, index) in rootMenuList"
              :key="item.path"
              :index="item.path"
              v-click="index"
              @click="onClickMenu"
              class="topNav"
            >
              <template #title v-if="item.children.length > 0 && !item.hidden">
                <!-- <svg class="icona" aria-hidden="true">
                    <use :xlink:href="item.icon"></use>
                  </svg> -->
                <i :class="'iconfont ' + item.icon.substr(1)"></i>
                <span>{{ item.menuName }}</span>
              </template>
            </el-menu-item>
          </el-menu>

          <div style="display: flex; align-items: center; height: 60px">
            <!-- 小区切换 -->
            <!-- <el-button @click="onlinePerson">在线人数</el-button> -->
            <el-button
              style="width: 60px; height: 30px; margin-right: 15px"
              @click="copyCommunity"
              v-if="hasPerm('base:community:copy')"
              >复 制</el-button
            >
            <el-select
              style="width: 214px"
              filterable
              v-show="selectShow"
              @change="communityChange"
              v-model="communityId"
              placeholder="选择小区"
            >
              <el-option
                v-for="item in communityList"
                :key="item.id"
                :label="item.communityName"
                :value="item.id"
              ></el-option>
            </el-select>
            <!-- 欢迎词 -->
            <span>&nbsp;&nbsp;&nbsp;</span>
            <span v-show="nickName" style="font-size: 15px"
              >欢迎您,{{ groupName }}&nbsp;{{ nickName }}!</span
            >
            <span>&nbsp;&nbsp;&nbsp;</span>
            <!-- 头像及操作菜单 -->
            <el-dropdown>
              <el-avatar :size="50" :src="avatar">
                <el-icon :size="30"><user-filled></user-filled></el-icon>
              </el-avatar>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <el-button type="text" size="default" @click="userInfo"
                      >个人信息</el-button
                    >
                  </el-dropdown-item>
                  <el-dropdown-item v-if="hasPerm('manageui:online')">
                    <el-button type="text" size="default" @click="onlinePerson"
                      >在线人数</el-button
                    >
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button
                      type="text"
                      size="default"
                      @click="updatePassword"
                      >修改密码</el-button
                    >
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" size="default" @click="toLoginOut"
                      >退出登录</el-button
                    >
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        <!-- <div style="padding:10px">
          <el-breadcrumb :separator-icon="ArrowRight">
            <template v-for="(item, index) in lists">
              <el-breadcrumb-item
                v-if="item.path"
                :key="index"
                :to="item.path"
              >{{ item.name }}</el-breadcrumb-item>
            </template>
          </el-breadcrumb>
        </div> -->
      </el-header>
      <!-- 内容区 -->
      <el-main>
        <component :is="userPersonInfo"></component>
        <component :is="modifyPassword"></component>
        <transition name="el-fade-in">
          <router-view v-if="show"></router-view>
        </transition>
      </el-main>

      <div v-if="dialog" class="dialog">
        <el-dialog top="10vh" v-model="dialog" title="通知公告">
          <el-carousel height="600px" :autoplay="false" trigger="click">
            <el-carousel-item
              style="overflow-y: auto"
              v-for="item in noticeList"
              :key="item"
              ><el-scrollbar height="590px">
                <p
                  class="small justify-center"
                  text="2xl"
                  v-html="item.noticeContent"
                ></p>
              </el-scrollbar>
            </el-carousel-item>
          </el-carousel>
          <el-row justify="center">
            <el-button
              type="primary"
              style="width: 100px; height: 30px; margin-top: 20px"
              @click="read"
              >已 读
            </el-button>
          </el-row>
        </el-dialog>
      </div>
      <div v-if="onlinedialog">
        <el-dialog v-model="onlinedialog" width="47%" title="在线人数">
          <el-table :data="onlineList" border>
            <el-table-column
              prop="nickName"
              align="center"
              label="用户"
              width="82"
            />
            <el-table-column
              prop="groupName"
              align="center"
              label="所在组"
              width="100"
            />
            <el-table-column
              prop="oper"
              align="center"
              label="当前操作"
              width="130"
            />
            <el-table-column
              prop="mobile"
              align="center"
              label="联系方式"
              width="120"
            />
            <el-table-column
              prop="client"
              align="center"
              label="客户端"
              width="110"
            />
            <el-table-column
              prop="onlineTime"
              align="center"
              label="登陆时间"
              width="180"
            />
            <el-table-column
              prop="onlineTimes"
              align="center"
              label="在线时长"
            />
          </el-table>
        </el-dialog>
      </div>
    </el-container>
  </el-container>
</template>

<script setup>
// import axios from "axios";
import store from "../../store/index.js";
import { UserFilled } from "@element-plus/icons-vue";
import { loginOut, pushUser } from "@/api/admin/auth";
import { getUser, currentUser } from "@/api/admin/user";
// import { listRole } from "@/api/admin/role";
// import { listDictByNameEn } from "@/api/admin/dict";
import modifyPassword from "@/componts/admin/user/modifyPassword.vue";
import logo from "@/assets/icon/community_logo.png";
import userPersonInfo from "@/componts/admin/user/userInfo.vue";
import { accountCommunity } from "@/api/base/community";
import {
  ref,
  reactive,
  nextTick,
  onMounted,
  watch,
  watchEffect,
  onUnmounted,
} from "vue";
import { ElMessage } from "element-plus";
import mitt from "@/utils/mitt";
import { useRouter } from "vue-router";
import { defineComponent, getCurrentInstance, toRaw } from "vue";
import router from "../../router/router";
import "@/assets/iconfonts2/iconfont.css";
// import "@/assets/iconfonts/iconfont.css";
import appInfo from "../../../package.json";
import { ArrowRight } from "@element-plus/icons-vue";
import { getNotice, readed } from "@/api/admin/inform";
import waterMask from "@/componts/waterMark/waterMark.vue";

import socket from "@/utils/socket.js";

const dialog = ref(false);
const onlinedialog = ref(false);
const onlineList = ref([]);
const noticeList = ref([]);

const communityList = ref([{ id: 0, communityName: "加载中..." }]);
const communityId = ref(0);
const groupId = ref(0);
const nowRoute = ref("");
const show = ref(false);
const avatar = ref(null);
const nickName = ref(false);
const groupName = ref(false);
const selectShow = ref(false);
const rootMenuList = ref([]);
const secondMenuList = ref([]);
const lists = ref([]);
const systemName = ref("");
const systemShortName = ref("");
const timer = ref([]);

const vClick = {
  mounted: (el, binding) => {
    if (localStorage.getItem("menuIndex") == 0) {
      el.click();
      // console.info('vClick',el.innerText ,binding);
    }
    if (el.innerText == localStorage.getItem("menuIndex")) {
      el.click();
    }
  },
};
const { proxy } = getCurrentInstance();

onUnmounted(() => {
  if (timer.value) {
    //
    clearInterval(timer.value);
    timer.value = null;
  }
});

onMounted(() => {
  systemName.value = localStorage.getItem("systemName");
  systemShortName.value = localStorage.getItem("systemShortName")
    ? localStorage.getItem("systemShortName")
    : "数字小区";
  document.title = systemName.value;

  //水印蒙版
  mitt.emit("isShowWaterMarker", true);
  let userInfoObj = JSON.parse(localStorage.getItem("userInfo"));
  socket.connect(userInfoObj);

  // 第一个路由为首页
  lists.value.unshift({ path: "/home", name: "首页", meta: { title: "首页" } });
  let router = useRouter();
  nowRoute.value = toRaw(router).currentRoute.value.fullPath.substr(1);

  getNotice().then((res) => {
    if (res.data.result.length > 0) {
      dialog.value = true;
    }
    noticeList.value = res.data.result;
  });

  // const randomString = proxy.randomStr(8)

  // const encrypt = proxy.encrypt(randomString,localStorage.getItem("userInfo"))

  // const decrypt = proxy.decrypt(encrypt)

  mitt.on("initUserInfo", () => {
    localStorage.removeItem("userInfo");
    currentUser().then((res) => {
      // console.log("刷新");
      localStorage.setItem("userInfo", JSON.stringify(res.data.result));
      // console.log(proxy.encrypt(JSON.stringify(res.data.result)));
      init();
    });
  });

  mitt.on("online-status-failed", () => {
    ElMessage.error("查看在线人数授权失败");
  });

  mitt.on("online-status-list", (data) => {
    setTimeout(() => {
      onlineList.value = [];

      // const list = JSON.parse(data.dataContent);
      // for (const item of list) {
      //   let newItem = JSON.parse(item);
      //   let onlineTime = JSON.parse(item).onlineTime;
      //   newItem.onlineTimes = proxy.getTwoTime(onlineTime);
      //   onlineList.value.push(newItem);
      // }
      // onlinedialog.value = true;

      var dataContent = JSON.parse(data.dataContent);
      dataContent.forEach((element) => {
        element = JSON.parse(element);
        var hasIn = false;
        for (let index = 0; index < onlineList.value.length; index++) {
          var userName = onlineList.value[index].userName;
          var client = onlineList.value[index].client;
          if (userName == element.userName && client == element.client) {
            onlineList.value[index].oper = element.oper;
            hasIn = true;
          }
        }
        if (!hasIn) {
          onlineList.value.unshift(element);
        }
      });

      onlinedialog.value = true;
      setTimerShow();
    }, 500);
  });
  if (_router.currentRoute.value.matched[1].path == "/home") {
    pushUserAction("首页");
  }
  //触发第一个菜单
  // routeToHome();
});

function getTwoTimeDistence(time) {
  if (time < 0) return "--";
  //时间戳转化为天时分秒
  // 总秒数
  var second = Math.floor(time / 1000);
  // 天数
  var day = Math.floor(second / 3600 / 24);
  // 小时
  var hr = Math.floor((second / 3600) % 24);
  // 分钟
  var min = Math.floor((second / 60) % 60);
  // 秒
  // var sec = Math.floor(second % 60);
  // return (day?day + "天":'') + (hr?hr+ "小时":'') + ( min?min + "分钟":'') + sec + "秒";

  if (min < 0) return "--";
  else
    return (
      (day ? day + "天" : "") +
      (hr ? hr + "小时" : "") +
      (min ? min + "分钟" : "")
    );
}

async function setTimerShow() {
  if (!timer.value)
    timer.value = setInterval(() => {
      setTimerShow();
    }, 1000 * 20);

  if (onlineList.value.length > 0) {
    var tempList = onlineList.value;
    var now = new Date().getTime();

    for (let index = 0; index < tempList.length; index++) {
      var markerTime = new Date(tempList[index].onlineTime).getTime();

      tempList[index].onlineTimes = getTwoTimeDistence(now - markerTime);
    }
    onlineList.value = null;

    tempList.sort(function (a, b) {
      return a.client > b.client ? 1 : -1;
    });
    tempList.sort((a, b) => {
      if (b.client !== a.client) {
        return b.client - a.client;
      } else {
        return b.onlineTime.localeCompare(a.onlineTime);
      }
    });
    onlineList.value = tempList;
  }
}

const _router = useRouter();
watch(
  () => _router.currentRoute.value,
  (newValue) => {
    let matched = [];
    if (lists.value.length == 0) {
      lists.value = [newValue.matched[1]];
    } else {
      // console.log(newValue.matched);
      lists.value.push(newValue.matched[1]);
    }
    // console.log(lists);
    if (lists.value.length && lists.value[0].name !== "首页") {
      lists.value.unshift({
        path: "/home",
        name: "首页",
        meta: { title: "首页" },
      });
    }

    // lists.value = matched;
    // lists.value.

    let newArr = [];
    let obj = {};
    for (var i = 0; i < lists.value.length; i++) {
      if (!obj[lists.value[i].path]) {
        newArr.push(lists.value[i]);
        obj[lists.value[i].path] = true;
      }
    }
    let routerAry = JSON.parse(localStorage.getItem("router"));

    if (!routerAry) return;
    routerAry.forEach((el) => {
      for (let i = 0; i < newArr.length; i++) {
        if ("/" + el.path == newArr[i].path) {
          newArr[i].name = el.menuName;
        }
      }
    });
    lists.value = newArr;
    // console.log(newValue);
    // console.log(newValue.matched[1].name);
    pushUserAction(newValue.matched[1].name);
    // console.log(newArr);
  },
  { deep: true, immediate: true }
);
watch(
  // 监听在线人数是否关闭,关闭断开连接
  () => onlinedialog.value,
  (newValue) => {
    if (!newValue) {
      unsubscribeOnLine();
    }
  }
);

init();

async function init() {
  mitt.off("openUserEdit");
  mitt.off("openUserPwdModify");
  mitt.off("refreshUserList");
  //获取小区列表，并取第一个为默认值，同时设置地图中心点
  accountCommunity()
    .then((res) => {
      if (res.data.result.length > 0) {
        communityList.value = res.data.result;
        let _communityId = localStorage.getItem("communityId");
        let defaultData = communityList.value.find(
          (m) => m.id + "" == _communityId
        );
        if (!defaultData) {
          defaultData = res.data.result[0];
        }

        communityId.value = defaultData.id;
        groupId.value = defaultData.groupId;
        localStorage.setItem("groupId", groupId.value);
        localStorage.setItem("communityId", communityId.value);

        if (defaultData.lng != null && defaultData.lat != null) {
          localStorage.setItem(
            "lnglat",
            JSON.stringify([defaultData.lng, defaultData.lat])
          );
        }
      } else {
        communityList.value = [{ id: 0, communityName: "无数据" }];
      }
      show.value = true;
    })
    .catch((err) => {
      console.error("get community list fail", err);
      communityList.value = [{ id: 0, communityName: "加载失败" }];
      ElMessage.error("获取小区失败，稍后自动刷新");
      setTimeout(function () {
        window.location.reload();
      }, 5000);
    });

  //取一级菜单
  let list = [];
  if (localStorage.getItem("router")) {
    let routerAry = JSON.parse(localStorage.getItem("router"));

    for (let item of routerAry) {
      if (item.parentId == 0) {
        list.push({
          path: item.path,
          menuName: item.menuName,
          sort: item.sort,
          children: getChildren(item, routerAry),
          icon: item.icon,
          type: item.type,
        });
        console.log(list);
      }
    }
  }
  rootMenuList.value = list.sort((a, b) => a.sort - b.sort);

  //设置当前用户信息
  // console.log(rootMenuList);
  let user = JSON.parse(localStorage.getItem("userInfo"));
  if (user.nodeType != 10) {
    selectShow.value = true;
  }
  groupName.value = user.groupName;
  nickName.value = user.nickName;
  avatar.value = import.meta.env.VITE_BASE_API + user.avatar;

  let storeData = [];
  const authorities = user.permissions.split(",");
  for (const key in authorities) {
    storeData.push(authorities[key]);
  }
  store.commit("SET_PERMISSIONS", storeData);
}

// 点击菜单操作
function onClickMenu(item) {
  // console.log('onClickMenu', item)
  let currentMenu = rootMenuList.value.find((m) => m.path == item.index);
  if (currentMenu) {
    secondMenuList.value = currentMenu.children;
  }
  localStorage.setItem("menuIndex", currentMenu.menuName);
  // console.log('onClickMenu.secondMenuList', currentMenu, secondMenuList.value)
}

//取消订阅
function unsubscribeOnLine() {
  socket.unsubscribe([
    {
      topic: "online-status",
    },
  ]);
}
//订阅
function subscribeOnLine() {
  socket.subscribe([
    {
      topic: "online-status",
    },
  ]);
}
// 查看在线人数
function onlinePerson() {
  subscribeOnLine();
}

//通知公告 已读按钮
function read() {
  let searchModel = [];
  for (let i = 0; i < noticeList.value.length; i++) {
    console.log(noticeList.value[i]);
    searchModel.push(noticeList.value[i].id);
  }
  readed(searchModel).then((res) => {
    // ElMessage.success({
    // 	message: res.data.msg
    // })
  });
  dialog.value = false;
}

function search() {
  mitt.emit("refreshUserList");
}

// 个人信息弹床
function userInfo() {
  let user = JSON.parse(localStorage.getItem("userInfo"));
  mitt.emit("openUserInfo", user);
}

function updatePassword() {
  mitt.emit("openUserPwdModify");
}

// 退出登录
function toLoginOut() {
  loginOut()
    .then((res) => {
      localStorage.clear();
      window.location.reload();
    })
    .catch((err) => {
      localStorage.clear();
      window.location.reload();
      console.log("清除令牌失败");
    });
}

function getChildren(route, routerAry) {
  let list = [];
  if (routerAry == null || routerAry == undefined) {
    return list;
  }
  for (let item of routerAry) {
    if (route.id == item.parentId) {
      list.push({
        path: item.path,
        menuName: item.menuName,
        sort: item.sort,
        children: getChildren(item, routerAry),
        icon: item.icon,
        type: item.type,
      });
    }
  }
  return list.sort((a, b) => a.sort - b.sort);
}

// 复制小区id
function copyCommunity() {
  navigator.clipboard.writeText(localStorage.getItem("communityId"));
  ElMessage.success({
    message: "复制成功",
  });
}

/**
 * 用户操作，发送socket
 */
function pushUserAction(actionName) {
  if (actionName != "home") {
    let senObj = {
      oper: actionName,
      receiveClient: "all",
      tags: ["manager"],
    };
    pushUser(senObj).then((res) => {
      if (res !== null && res.code === 0) {
      } else {
      }
    });
  }
}

/**
 * 切换小区，同时更新当前地图中心点
 * @param {*} id 当前小区ID
 */
function communityChange(id) {
  // console.log(id, communityList.value.find(m => m.id + '' == communityId.value).groupId);
  groupId.value = communityList.value.find(
    (m) => m.id + "" == communityId.value
  ).groupId;
  localStorage.setItem("groupId", groupId.value);
  localStorage.setItem("communityId", id);
  show.value = false;
  for (let item of communityList.value) {
    if (item.id == id) {
      if (item.lng != null && item.lat != null) {
        localStorage.setItem("lnglat", JSON.stringify([item.lng, item.lat]));
      }
    }
  }
  nextTick(() => {
    show.value = true;
  });
}

function routeToHome() {
  router.push("/home");
}
</script>

<style scoped lang="less">
* {
  box-sizing: border-box;
}

.el-header {
  width: 100%;
  background-color: #fff;
  box-shadow: 0 3px 5px #959595;
  color: var(--el-text-color-primary);
  display: flex;
  flex-direction: column;
  height: 60px !important;
  > div:nth-child(1) {
    // background-color: #fff;
    // box-shadow: 0 3px 5px #959595;
    height: 60px !important;
    color: var(--el-text-color-primary);
    line-height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.logoText {
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
}

.el-menu {
  background-color: #314157;
}

.icona {
  width: 1.5em;
  height: 1.5em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  margin-right: 5px;
}

.el-menu-item:hover {
  color: var(--el-color-primary #4b96ff);
  background-color: var(--el-color-primary-light-5);
}

.topNav {
  margin-right: 10px;
  padding: 0px 5px !important;
}

.topNav:hover {
  /* color: var(--el-color-primary: #000); */
  background-color: #fff;
}

.iconfont {
  font-size: 21px;
  fill: currentColor;
  overflow: hidden;
  margin-right: 5px;
}

@keyframes shakeX {
  from,
  to {
    transform: translate3d(0, 0, 0);
  }

  50%,
  90% {
    transform: translate3d(-10px, 0, 0);
  }

  30%,
  70% {
    transform: translate3d(10px, 0, 0);
  }
}

.el-menu-demo .is-active {
  border-bottom: 2px solid #5097ff;
  animation: shakeX 0.5s;
}
/deep/.el-breadcrumb__item:last-child .el-breadcrumb__inner {
  font-weight: 700 !important;
}
/deep/.el-breadcrumb__inner:hover {
  cursor: pointer !important;
}

.dialog {
  ::v-deep .el-dialog__header {
    background-color: #fff !important;
    box-shadow: none;
    .el-dialog__title {
      color: black;
    }
    .el-dialog__close {
      color: #ccc;
    }
  }
  ::v-deep .el-dialog__body {
    padding: 0 20px 20px;
  }
}
</style>
