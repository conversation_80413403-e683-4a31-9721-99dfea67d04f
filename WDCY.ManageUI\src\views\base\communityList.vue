<template>
  <community-edit
    :groupList="groupList"
    :statusList="statusList"
    @search="search"
  ></community-edit>

  <subscribe-edit></subscribe-edit>
  <open-id-edit @search="search"></open-id-edit>

  <mars-map @point="point"></mars-map>
  <geofencing @point="point"></geofencing>

  <el-row :gutter="20">
    <el-col :span="4">
      <el-cascader
        :show-all-levels="false"
        style="width: 100%"
        :props="{ checkStrictly: true }"
        :options="userGroupList"
        @change="handleChange"
        clearable
        placeholder="选择组"
      />
    </el-col>
    <el-col :span="4">
      <el-input
        v-model="searchModel.communityName"
        @keydown.enter="search"
        placeholder="小区名称"
        clearable
      />
    </el-col>
    <el-col :span="2">
      <el-select
        style="width: 100%"
        v-model="searchModel.status"
        placeholder="状态"
        clearable
      >
        <el-option
          v-for="item in statusList"
          :key="item.nameEn"
          :label="item.nameCn"
          :value="parseInt(item.nameEn)"
        ></el-option>
      </el-select>
    </el-col>
    <el-col :span="4">
      <el-button type="primary" @click="search">搜 索</el-button>
    </el-col>
    <el-col :span="4" :push="6" v-if="hasPerm('base:community:add')">
      <el-button style="float: right" type="primary" @click="add"
        >添 加</el-button
      >
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-table
        stripe
        :data="communityList"
        border
        height="calc(100vh - 300px)"
        style="width: 100%"
      >
        <el-table-column
          prop="communityName"
          align="center"
          label="小区名称"
          show-overflow-tooltip
        />
        <el-table-column
          prop="ancestorNames"
          align="center"
          label="社区"
          width="320"
        />
        <el-table-column prop="status" align="center" label="状态" width="100">
          <template #default="scope">
            <!-- <el-tag :type="getDictCss(statusList, scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag> -->
            <el-switch
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.status"
              @change="editStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="enabled3d"
          align="center"
          label="启用3D"
          width="100"
          v-if="hasPerm('base:community:edit:enabled3d')"
        >
          <template #default="scope">
            <el-switch
              v-model="scope.row.enabled3d"
              @change="enabled3dChange(scope.row.id, scope.row.enabled3d)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="address" align="center" label="位置">
          <template #default="scope">
            <el-col
              :span="24"
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <el-tooltip :content="scope.row.address">
                <span
                  style="
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  "
                  >{{ scope.row.address }}</span
                >
              </el-tooltip>
              <el-button
                v-show="isShow(scope.row)"
                type="text"
                style="font-size: 30px; padding: 0"
                @click="selectPoint(scope.row)"
              >
                <el-icon :size="30">
                  <location-filled></location-filled>
                </el-icon>
              </el-button>
            </el-col>
          </template>
        </el-table-column>
        <el-table-column
          prop="updateTime"
          align="center"
          label="更新时间"
          width="168"
        />
        <el-table-column prop="note" align="center" label="备注">
          <template #default="scope">
            <el-tooltip
              :content="'<pre>' + scope.row.note + '</pre>'"
              raw-content
            >
              <span
                style="
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
                >{{ scope.row.note }}</span
              >
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="360">
          <template #default="scope">
            <el-button
              type="text"
              size="default"
              @click="lookSubscribe(scope.row.id)"
              v-if="hasPerm('base:community:subscribe:detial')"
              >订阅
            </el-button>
            <el-button
              type="text"
              size="default"
              @click="preset(scope.row.id)"
              v-if="hasPerm('base:community:add:presetData')"
              >预置
            </el-button>
            <el-button
              type="text"
              size="default"
              @click="openId(scope.row)"
              v-if="hasPerm('base:person:update-openid:edit')"
              >OpenId
            </el-button>
            <el-button
              type="text"
              size="default"
              @click="selectArea(scope.row)"
              v-if="hasPerm('base:community:edit')"
              >围栏
            </el-button>
            <el-button
              type="text"
              size="default"
              @click="map3d(scope.row)"
              v-if="hasPerm('base:community:queryByRoutePoints')"
              >巡游</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="edit(scope.row.id)"
              v-if="hasPerm('base:community:edit')"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="deleted(scope.row.id)"
              v-if="hasPerm('base:community:delete')"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination
        background
        v-model:page-size="searchModel.pageSize"
        :page-sizes="[12, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :total="Number(total)"
      ></el-pagination>
    </el-col>
  </el-row>
</template>

<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>

<script>
import {
  listCommunity,
  deleteCommunity,
  getCommunity,
  editCommunity,
  presetCommunityData,
  editEnabled3dCommunity,
  eventSubscription,
  queryByRoutePoints,
  updateByPolyCoords
} from "@/api/base/community";
import { listUserGroup } from "@/api/admin/userGroup";
import { listDictByNameEn } from "@/api/admin/dict";
import { currentUserGroup } from "@/api/admin/userGroup";

import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict";
import { openGeofencingMap } from "@/utils/myUtils";
import communityEdit from "@/componts/base/communityEdit.vue";
import subscribeEdit from "@/componts/base/subscribeEdit.vue";
import openIdEdit from "@/componts/base/openIdEdit.vue";
import marsMap from "@/componts/map/marsMap.vue";
import geofencing from "@/componts/map/geofencing.vue";

export default {
  components: {
    communityEdit,
    subscribeEdit,
    openIdEdit,
    marsMap,
    geofencing
  },
  data() {
    return {
      searchModel: { pageSize: 12 },
      communityList: [],
      statusList: [],
      groupList: [],
      userGroupList: [],
      communityModel: {},
      total: 0,
    };
  },

  mounted() {
    mitt.on("updateGeoPolyCoords", (data) => {
      if (data) {
        console.log("提交保存地理围栏信息", data);
        updateByPolyCoords(data).then((res) => {
          if (res.data.code == 0) {
            this.$message.success(res.data.msg);
          } else {
            this.$message.error(res.data.msg);
          }
        });
      }
    });
  },
  methods: {
    handleChange(e) {
      if (e == null) {
        this.searchModel.groupId = null;
        return;
      }
      this.searchModel.groupId = e[e.length - 1];
    },
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    // 是否3d按钮
    enabled3dChange(id, value) {
      editEnabled3dCommunity({ id: id, enabled3d: value }).then((res) => {
        this.search();
        this.$message.success(res.data.msg);
      });
    },
    // 更改状态
    async editStatus(data) {
      await getCommunity(data.id).then((res) => {
        this.communityModel = res.data.result;
        this.communityModel.id = data.id;
        this.communityModel.status = data.status;
      });
      editCommunity(this.communityModel);
    },
    search() {
      listCommunity(this.searchModel).then((res) => {
        this.communityList = res.data.result.list;
        this.total = res.data.result.total;
      });
    },
    // 订阅详情
    lookSubscribe(id) {
      let data = { id: id };
      eventSubscription(data).then((res) => {
        mitt.emit("openSubscribeEdit", res.data.result);
      });
    },
    // 预置功能
    preset(id) {
      this.$confirm("即将创建或补充本小区预置数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          presetCommunityData(id).then((res) => {
            if (res.data.code == 0)
              this.$message.success("本小区预置数据创建成功");
            else this.$message.error(res.data.msg);
          });
        })
        .catch(() => {});
    },
    // 更改openid
    openId(row) {
      const data = {
        id: row.id,
        openId: row.openId,
        type: "community",
      };
      mitt.emit("openOpenIdEdit", data);
    },
    // 地理围栏编辑
    selectArea(row) {
      getCommunity(row.id)
        .then((res) => {
          var result = res.data.result;
          if (res.data.code == 0 && result) {
            let position = {
              lng: row.lng,
              lat: row.lat
            };
            openGeofencingMap(mitt, {
              id: row.id,
              title: result.communityName,
              position: row.lng && row.lat ? position : null,
              polyCoords: row.polyCoords,
              enabled3d: result.enabled3d,
              mapConfig: result.expandParams
            });
          }
        })
        .catch((err) => { });
    },
    // 巡游路线
    map3d(row) {
      console.log(row);
      var communityId = row.id || 0;
      queryByRoutePoints(communityId)
        .then((res) => {
          var result = res.data.result;

          var map = result.map ? JSON.parse(result.map) : null;
          var mode = map ? map.mode : null;

          var routePoints =
            result.routePoints == "" ? null : JSON.parse(result.routePoints);
          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          if (result.enabled3d && mode == "mars3d") {
            position = map.sdgis.position;

            modeUrl = map.sdgis.tdtile;

            try {
              rotationSet = map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (result.lng && result.lat) {
            center = {
              lng: result.lng,
              lat: result.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }

          var data = {
            enabled3d: result.enabled3d,
            edit: true,
            point: [row.lng, row.lat, 0],
            position: position,
            routePoints: routePoints,
            routePointsStatus: true,
            comunityData: result,
            lineStatus: true,
            contextMenuStatus: true,
            center: center,
            modeUrl: modeUrl,
            title: "巡游路线",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);

          mitt.emit("openMarsMap", data);
        })
        .catch((err) => {});
    },
    edit(id) {
      getCommunity(id).then((res) => {
        mitt.emit("openCommunityEdit", res.data.result);
      });
    },
    isShow(row) {
      if (
        row.lng != undefined &&
        row.lng != null &&
        row.lng != "" &&
        row.lat != undefined &&
        row.lat != null &&
        row.lat != ""
      ) {
        return true;
      }
      return false;
    },
    point(e) {
      mitt.emit("setPointValue", e);
    },
    setAddress(e) {
      mitt.emit("setAddress", e);
    },
    selectPoint(row) {
      console.log("selectPoint", row);
      //var communityId = localStorage.getItem("communityId");
      getCommunity(row.id)
        .then((res) => {
          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map ? config.map.mode : null;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          var polyCoords = [];
          if (result.polyCoords) {
            polyCoords = JSON.parse(result.polyCoords);
          }

          if (result.enabled3d && mode == "mars3d") {
            position = config.map.sdgis.position;

            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (result.lng && result.lat) {
            center = {
              lng: result.lng,
              lat: result.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }

          var data = {
            enabled3d: result.enabled3d,
            edit: false,
            point: [center.lng, center.lat, 0],
            position: position,
            polyCoords: polyCoords,
            center: center,
            modeUrl: modeUrl,
            title: "查看点位",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);
          mitt.emit("openMarsMap", data);
        })
        .catch((err) => {});
    },
    add() {
      mitt.emit("openCommunityAdd");
    },
    deleted(id) {
      this.$confirm("删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteCommunity(id).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
          });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    handleSizeChange(num) {
      this.searchModel.pageSize = num;
      this.search();
    },
    async init() {
      mitt.off("openCommunityEdit");
      mitt.off("openCommunityAdd");
      mitt.off("openMarsMap");
      mitt.off("setPointValue");
      mitt.off("setAddress");
      try {
        let res = await listCommunity(this.searchModel);
        this.communityList = res.data.result.list;
        this.total = res.data.result.total;

        let status_res = await listDictByNameEn("common_status");
        this.statusList = status_res.data.result;

        let resGroup = await listUserGroup({ totalize: 99999 });
        var groupStr = JSON.stringify(resGroup.data.result.list)
          .replaceAll("groupName", "label")
          .replaceAll("id", "value");
        this.userGroupList = JSON.parse(groupStr);

        let group_res = await currentUserGroup();
        var groupStr = JSON.stringify(group_res.data.result)
          .replaceAll("groupName", "label")
          .replaceAll("id", "value");
        this.groupList = JSON.parse(groupStr);
      } catch (err) {
        this.$message.error(err);
      }
    },
  },
  created() {
    this.init();
  },
};
</script>


<style scoped>

.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}

div /deep/ .cell {
  display: flex;
  justify-content: center;
}
</style>
