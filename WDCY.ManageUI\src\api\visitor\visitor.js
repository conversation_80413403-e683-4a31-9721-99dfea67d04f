import request from '@/utils/request'

export const visitorsList = (data) =>
	request({
		url: '/visitors',
		method: 'get',
		params: data
	})
export const visitorsRecordList = (data) =>
	request({
		url: '/visit-record',
		method: 'get',
		params: data
	})
	export const getVisitorsDetail = (id) =>
	request({
		url: '/visit-record/'+id,
		method: 'get'
	})
export const visitorstDelete = (id) =>
	request({
		url: '/visitors',
		method: 'delete',
		params: {
			id: id
		}
	})