import request from '@/utils/request'

export const warnRecordList = (data) =>
	request({
		url: '/warnInfo',
		method: 'get',
		params: data
	})
export const getEventSourceName = (data) =>
	request({
		url: '/warnEventSourceInfo/queryEventSourceName',
		method: 'get',
		params: data
	})
export const warnRecordDelete = (id) =>
	request({
		url: '/warnInfo',
		method: 'delete',
		params: {
			id: id
		}
	})
	//确认
  export const warnInfoConfirm = (data) =>
	request({
		url: '/warnInfo/confirm',
		method: 'put',
		data: data
	})
	//批量确认
  export const warnInfo = (data) =>
	request({
		url: '/warnInfo',
		method: 'put',
		data: data
	})
  export const warnInfoDelete = (data) =>
	request({
		url: '/warnInfo/deleteByIds',
		method: 'delete',
		data: data
	})
  export const warnEventTypeList = (data) =>
	request({
		url: '/warnEventType',
		method: 'get',
		params: data
	})

  export const getWarnEventType = (id) =>
	request({
		url: '/warnEventType/'+id,
		method: 'get',
	})
export const warnEventTypeListAdd = (data) =>
	request({
		url: '/warnEventType',
		method: 'post',
		data: data
	})
export const warnEventTypeListEdit = (data) =>
	request({
		url: '/warnEventType',
		method: 'put',
		data: data
	})
export const warnEventTypeListDelete = (id) =>
	request({
		url: '/warnEventType',
		method: 'delete',
		params: {
			id: id
		}
	})
	export const warnEventSourceInfoList = (data) =>
	request({
		url: '/warnEventSourceInfo',
		method: 'get',
		params: data
	})
	export const warnEventSourceInfoCheckTypeList = () =>
	request({
		url: '/warnEventSourceInfo/queryEventCheckTypes',
		method: 'get'
	})

	export const warnEventSourceInfoDelete = (id) =>
	request({
		url: '/warnEventSourceInfo',
		method: 'delete',
		params: {
			id: id
		}
	})

	export const getWarnEventSourceInfo = (id) =>
	request({
		url: '/warnEventSourceInfo/'+id,
		method: 'get',
	})

	export const warnEventSourceInfoAdd = (data) =>
	request({
		url: '/warnEventSourceInfo',
		method: 'post',
		data: data
	})
export const warnEventSourceInfoEdit = (data) =>
	request({
		url: '/warnEventSourceInfo',
		method: 'put',
		data: data
	})

	export const deviceTypeList = (data) =>
	request({
		url: '/deviceType/queryAll',
		method: 'get',
		params:data
	})

	export const eventTypeList = (data) =>
	request({
		url: '/warnEventType/queryAll',
		method: 'get',
		params: data
	})

	export const getWarnEventSourceInfoType = (data) =>
	request({
		url: '/warnEventSourceInfo/queryByType',
		method: 'get',
		params : data
	})

	export const copyEctype = (data) =>
	request({
		url: '/warnEventSourceInfo/copy',
		method: 'get',
		params : data
	})

	export const actionInfoAllList = (devTypeId) =>
	request({
		url: '/warnDeviceAction',
		method: 'get',
		params: {
			devTypeId: devTypeId
		}
	})

	export const warnDeviceActionList = (data) =>
	request({
		url: '/warnDeviceAction/queryByDevTypeId',
		method: 'get',
		params: data
	})
	
	export const warnDeviceActionDelete = (id) =>
	request({
		url: '/warnDeviceAction',
		method: 'delete',
		params: {
			id: id
		}
	})

	export const getWarnDeviceAction = (id) =>
	request({
		url: '/warnDeviceAction/'+id,
		method: 'get',
	})

	export const warnDeviceActionAdd = (data) =>
	request({
		url: '/warnDeviceAction',
		method: 'post',
		data: data
	})
export const warnDeviceActionEdit = (data) =>
	request({
		url: '/warnDeviceAction',
		method: 'put',
		data: data
	})
	export const eventList = (data) =>
	request({
		url:'/warnAnalysis',
		method: 'get',
		params: data
	})
	export const eventListDelete = (id) =>
	request({
		url:'/warnAnalysis',
		method: 'delete',
		params: {
			id: id
		}
	})