;(function (_0x3e116a, _0x4ef499) {
    var _0x1e71e6 = { _0x29b0c2: 0x1a3, _0x3d99be: 0x1a7, _0x1b5b2a: 0x1c0 },
      _0x8cac8b = _0x262e,
      _0x274cd7 = _0x3e116a()
    while (!![]) {
      try {
        var _0x13153f =
          parseInt(_0x8cac8b(0x1a9)) / 0x1 +
          -parseInt(_0x8cac8b(0x1be)) / 0x2 +
          (parseInt(_0x8cac8b(_0x1e71e6._0x29b0c2)) / 0x3) * (parseInt(_0x8cac8b(0x1c3)) / 0x4) +
          -parseInt(_0x8cac8b(_0x1e71e6._0x3d99be)) / 0x5 +
          -parseInt(_0x8cac8b(_0x1e71e6._0x1b5b2a)) / 0x6 +
          -parseInt(_0x8cac8b(0x1b6)) / 0x7 +
          parseInt(_0x8cac8b(0x1b9)) / 0x8
        if (_0x13153f === _0x4ef499) break
        else _0x274cd7["push"](_0x274cd7["shift"]())
      } catch (_0x2ef361) {
        _0x274cd7["push"](_0x274cd7["shift"]())
      }
    }
  })(_0xdc07, 0x3155b)
  function _0xdc07() {
    var _0x2aace9 = [
      "Interpolation",
      "123893XIUigO",
      "interpolation",
      "Linear",
      "4913448jObyJX",
      "repeat",
      "exports",
      "call",
      "delay",
      "692558pmNmIc",
      "splice",
      "2061558Cfzgyk",
      "cos",
      "sqrt",
      "38116DiUqoc",
      "length",
      "performance",
      "path",
      "undefined",
      "onStop",
      "remove",
      "start",
      "string",
      "update",
      "end",
      "Utils",
      "now",
      "Dynamic\x20requires\x20are\x20not\x20currently\x20supported\x20by\x20@rollup/plugin-commonjs",
      "None",
      "push",
      "Factorial",
      "onComplete",
      "Bounce",
      "120vIbjlt",
      "sin",
      "Out",
      "repeatDelay",
      "1942505dGYwhJ",
      "yoyo",
      "302806WNbPsD",
      "pow",
      "Easing",
      "getTime",
      "charAt",
      "Bernstein",
      "onUpdate",
      "stopChainedTweens",
      "concat",
      "floor",
      "CatmullRom",
      "stop"
    ]
    _0xdc07 = function () {
      return _0x2aace9
    }
    return _0xdc07()
  }
  function createCommonjsModule(_0x3a35a7, _0x31f0d0, _0x4cd2f1) {
    var _0x5a754c = { _0xc0562d: 0x1c6 }
    return (
      (_0x4cd2f1 = {
        path: _0x31f0d0,
        exports: {},
        require: function (_0x86e7e7, _0x518c2d) {
          var _0x56da69 = _0x262e
          return commonjsRequire(_0x86e7e7, _0x518c2d === undefined || _0x518c2d === null ? _0x4cd2f1[_0x56da69(_0x5a754c._0xc0562d)] : _0x518c2d)
        }
      }),
      _0x3a35a7(_0x4cd2f1, _0x4cd2f1["exports"]),
      _0x4cd2f1["exports"]
    )
  }
  function commonjsRequire() {
    var _0xb530de = _0x262e
    throw new Error(_0xb530de(0x1d0))
  }
  var Tween = createCommonjsModule(function (_0x56dea7, _0x5d9df9) {
    var _0x4231c3 = { _0x5e470e: 0x1c5, _0x3eb1d5: 0x1cf },
      _0x1cc5c4 = { _0x5a887e: 0x1bb },
      _0x3426f9 = { _0x4b71e7: 0x1b5, _0x3d17c9: 0x1ce, _0x335a8f: 0x1d3 },
      _0x4ca60f = { _0x114f3c: 0x1b2, _0xe9260d: 0x1b5, _0x52d243: 0x1ce },
      _0x2fb404 = { _0x2a3aa8: 0x1b5, _0x4e4b3b: 0x1ce },
      _0x5ba7de = { _0x43b565: 0x1b2, _0x4dad94: 0x1b5, _0x1971be: 0x1b8 },
      _0x10cd08 = { _0x454c9e: 0x1a2, _0x1a1afd: 0x1ab, _0x4fef71: 0x1a2 },
      _0x3fd216 = { _0x5d0a27: 0x1a2, _0x2046f1: 0x1a5 },
      _0x464fbc = { _0x30d136: 0x1a4, _0x4ddef2: 0x1aa },
      _0x115477 = { _0x43807d: 0x1aa },
      _0x31fd51 = { _0x1de0ac: 0x1c2 },
      _0x58aa1c = { _0x56fb6c: 0x1c2 },
      _0x490b7a = { _0x686cac: 0x1a4 },
      _0x12fd9f = {
        _0x38ce67: 0x1ab,
        _0xb00912: 0x1d1,
        _0x17f35a: 0x1b8,
        _0x348c9c: 0x1b4,
        _0x468ade: 0x1b0,
        _0x1efa5e: 0x1bd,
        _0x2df296: 0x1ba,
        _0x35f958: 0x1a6,
        _0x254c25: 0x1b7,
        _0x44b9ac: 0x1a1,
        _0x57115f: 0x1c8
      },
      _0x4de9e9 = { _0x1b9d17: 0x1ad, _0x56902d: 0x1bc, _0x3086f1: 0x1c4 },
      _0x311978 = { _0x487492: 0x1bc },
      _0x460f44 = _0x262e,
      _0x331257 =
        _0x331257 ||
        (function () {
          var _0x2fe8b5 = { _0x81af3e: 0x1cf },
            _0x6bf6d2 = { _0x3b85cc: 0x1bf },
            _0x4a3430 = { _0x1f22e4: 0x1d2 },
            _0x417435 = []
          return {
            getAll: function () {
              return _0x417435
            },
            removeAll: function () {
              _0x417435 = []
            },
            add: function (_0x599bae) {
              var _0x4cd3b0 = _0x262e
              _0x417435[_0x4cd3b0(_0x4a3430._0x1f22e4)](_0x599bae)
            },
            remove: function (_0x3a298d) {
              var _0xe61407 = _0x262e,
                _0xac7f24 = _0x417435["indexOf"](_0x3a298d)
              _0xac7f24 !== -0x1 && _0x417435[_0xe61407(_0x6bf6d2._0x3b85cc)](_0xac7f24, 0x1)
            },
            update: function (_0x53e84c, _0x2cf2ec) {
              var _0x513c88 = _0x262e
              if (_0x417435[_0x513c88(0x1c4)] === 0x0) return ![]
              var _0x41f05a = 0x0
              _0x53e84c = _0x53e84c !== undefined ? _0x53e84c : _0x331257[_0x513c88(_0x2fe8b5._0x81af3e)]()
              while (_0x41f05a < _0x417435["length"]) {
                _0x417435[_0x41f05a]["update"](_0x53e84c) || _0x2cf2ec ? _0x41f05a++ : _0x417435[_0x513c88(0x1bf)](_0x41f05a, 0x1)
              }
              return !![]
            }
          }
        })()
    if (typeof window === "undefined" && typeof process !== _0x460f44(0x1c7))
      _0x331257[_0x460f44(0x1cf)] = function () {
        var _0x2f7600 = process["hrtime"]()
        return _0x2f7600[0x0] * 0x3e8 + _0x2f7600[0x1] / 0xf4240
      }
    else {
      if (typeof window !== _0x460f44(0x1c7) && window[_0x460f44(0x1c5)] !== undefined && window["performance"][_0x460f44(0x1cf)] !== undefined)
        _0x331257["now"] = window[_0x460f44(0x1c5)][_0x460f44(0x1cf)]["bind"](window[_0x460f44(_0x4231c3._0x5e470e)])
      else
        Date[_0x460f44(_0x4231c3._0x3eb1d5)] !== undefined
          ? (_0x331257[_0x460f44(_0x4231c3._0x3eb1d5)] = Date[_0x460f44(0x1cf)])
          : (_0x331257[_0x460f44(0x1cf)] = function () {
              var _0xa81ce8 = _0x460f44
              return new Date()[_0xa81ce8(0x1ac)]()
            })
    }
    ;(_0x331257["Tween"] = function (_0x19350b) {
      var _0x1b128d = { _0x50d46b: 0x1c4 },
        _0x225b10 = _0x460f44,
        _0xdc49c0 = _0x19350b,
        _0x229ecd = {},
        _0x82db35 = {},
        _0x36ad6b = {},
        _0x5f4658 = 0x3e8,
        _0x1a2ccc = 0x0,
        _0xdbdfdb,
        _0x2f13e9 = ![],
        _0x31ddec = ![],
        _0x33badb = 0x0,
        _0x549490 = null,
        _0xf4f92b = _0x331257[_0x225b10(_0x12fd9f._0x38ce67)][_0x225b10(0x1b8)][_0x225b10(_0x12fd9f._0xb00912)],
        _0x41d6b9 = _0x331257["Interpolation"][_0x225b10(_0x12fd9f._0x17f35a)],
        _0x20943f = [],
        _0x1a173d = null,
        _0x41478e = ![],
        _0x4ad8c2 = null,
        _0x399b11 = null,
        _0x562a5d = null
      ;(this["to"] = function (_0x5bfdc0, _0x2e1213) {
        return (_0x82db35 = _0x5bfdc0), _0x2e1213 !== undefined && (_0x5f4658 = _0x2e1213), this
      }),
        (this[_0x225b10(0x1ca)] = function (_0x368ef7) {
          var _0x5dcd2f = _0x225b10
          _0x331257["add"](this),
            (_0x31ddec = !![]),
            (_0x41478e = ![]),
            (_0x549490 = _0x368ef7 !== undefined ? _0x368ef7 : _0x331257[_0x5dcd2f(0x1cf)]()),
            (_0x549490 += _0x33badb)
          for (var _0x5350c5 in _0x82db35) {
            if (_0x82db35[_0x5350c5] instanceof Array) {
              if (_0x82db35[_0x5350c5][_0x5dcd2f(_0x1b128d._0x50d46b)] === 0x0) continue
              _0x82db35[_0x5350c5] = [_0xdc49c0[_0x5350c5]][_0x5dcd2f(0x1b1)](_0x82db35[_0x5350c5])
            }
            if (_0xdc49c0[_0x5350c5] === undefined) continue
            ;(_0x229ecd[_0x5350c5] = _0xdc49c0[_0x5350c5]),
              _0x229ecd[_0x5350c5] instanceof Array === ![] && (_0x229ecd[_0x5350c5] *= 0x1),
              (_0x36ad6b[_0x5350c5] = _0x229ecd[_0x5350c5] || 0x0)
          }
          return this
        }),
        (this[_0x225b10(_0x12fd9f._0x348c9c)] = function () {
          var _0x331d65 = _0x225b10
          if (!_0x31ddec) return this
          return (
            _0x331257[_0x331d65(0x1c9)](this),
            (_0x31ddec = ![]),
            _0x562a5d !== null && _0x562a5d[_0x331d65(_0x311978._0x487492)](_0xdc49c0, _0xdc49c0),
            this[_0x331d65(0x1b0)](),
            this
          )
        }),
        (this[_0x225b10(0x1cd)] = function () {
          var _0x2f04c8 = _0x225b10
          return this[_0x2f04c8(0x1cc)](_0x549490 + _0x5f4658), this
        }),
        (this[_0x225b10(_0x12fd9f._0x468ade)] = function () {
          var _0x2a10fb = _0x225b10
          for (var _0x27176b = 0x0, _0x531caf = _0x20943f["length"]; _0x27176b < _0x531caf; _0x27176b++) {
            _0x20943f[_0x27176b][_0x2a10fb(0x1b4)]()
          }
        }),
        (this[_0x225b10(_0x12fd9f._0x1efa5e)] = function (_0x5364a7) {
          return (_0x33badb = _0x5364a7), this
        }),
        (this[_0x225b10(_0x12fd9f._0x2df296)] = function (_0x3dacfa) {
          return (_0x1a2ccc = _0x3dacfa), this
        }),
        (this[_0x225b10(_0x12fd9f._0x35f958)] = function (_0x12a0f2) {
          return (_0xdbdfdb = _0x12a0f2), this
        }),
        (this[_0x225b10(0x1a8)] = function (_0x310ef8) {
          return (_0x2f13e9 = _0x310ef8), this
        }),
        (this["easing"] = function (_0x4c83f4) {
          return (_0xf4f92b = _0x4c83f4), this
        }),
        (this[_0x225b10(_0x12fd9f._0x254c25)] = function (_0x3e9b9d) {
          return (_0x41d6b9 = _0x3e9b9d), this
        }),
        (this["chain"] = function () {
          return (_0x20943f = arguments), this
        }),
        (this["onStart"] = function (_0x40f025) {
          return (_0x1a173d = _0x40f025), this
        }),
        (this[_0x225b10(0x1af)] = function (_0x29973b) {
          return (_0x4ad8c2 = _0x29973b), this
        }),
        (this[_0x225b10(_0x12fd9f._0x44b9ac)] = function (_0x398466) {
          return (_0x399b11 = _0x398466), this
        }),
        (this[_0x225b10(_0x12fd9f._0x57115f)] = function (_0x2810e7) {
          return (_0x562a5d = _0x2810e7), this
        }),
        (this["update"] = function (_0x45e89e) {
          var _0x68de08 = _0x225b10,
            _0x46dddb,
            _0x4d50c0,
            _0x62e8ac
          if (_0x45e89e < _0x549490) return !![]
          _0x41478e === ![] && (_0x1a173d !== null && _0x1a173d[_0x68de08(0x1bc)](_0xdc49c0, _0xdc49c0), (_0x41478e = !![]))
          ;(_0x4d50c0 = (_0x45e89e - _0x549490) / _0x5f4658), (_0x4d50c0 = _0x4d50c0 > 0x1 ? 0x1 : _0x4d50c0), (_0x62e8ac = _0xf4f92b(_0x4d50c0))
          for (_0x46dddb in _0x82db35) {
            if (_0x229ecd[_0x46dddb] === undefined) continue
            var _0x5b9d54 = _0x229ecd[_0x46dddb] || 0x0,
              _0x4d6e71 = _0x82db35[_0x46dddb]
            _0x4d6e71 instanceof Array
              ? (_0xdc49c0[_0x46dddb] = _0x41d6b9(_0x4d6e71, _0x62e8ac))
              : (typeof _0x4d6e71 === "string" &&
                  (_0x4d6e71["charAt"](0x0) === "+" || _0x4d6e71[_0x68de08(_0x4de9e9._0x1b9d17)](0x0) === "-"
                    ? (_0x4d6e71 = _0x5b9d54 + parseFloat(_0x4d6e71))
                    : (_0x4d6e71 = parseFloat(_0x4d6e71))),
                typeof _0x4d6e71 === "number" && (_0xdc49c0[_0x46dddb] = _0x5b9d54 + (_0x4d6e71 - _0x5b9d54) * _0x62e8ac))
          }
          _0x4ad8c2 !== null && _0x4ad8c2[_0x68de08(_0x4de9e9._0x56902d)](_0xdc49c0, _0x62e8ac)
          if (_0x4d50c0 === 0x1) {
            if (_0x1a2ccc > 0x0) {
              isFinite(_0x1a2ccc) && _0x1a2ccc--
              for (_0x46dddb in _0x36ad6b) {
                typeof _0x82db35[_0x46dddb] === _0x68de08(0x1cb) && (_0x36ad6b[_0x46dddb] = _0x36ad6b[_0x46dddb] + parseFloat(_0x82db35[_0x46dddb]))
                if (_0x2f13e9) {
                  var _0x414670 = _0x36ad6b[_0x46dddb]
                  ;(_0x36ad6b[_0x46dddb] = _0x82db35[_0x46dddb]), (_0x82db35[_0x46dddb] = _0x414670)
                }
                _0x229ecd[_0x46dddb] = _0x36ad6b[_0x46dddb]
              }
              return _0xdbdfdb !== undefined ? (_0x549490 = _0x45e89e + _0xdbdfdb) : (_0x549490 = _0x45e89e + _0x33badb), !![]
            } else {
              _0x399b11 !== null && _0x399b11[_0x68de08(0x1bc)](_0xdc49c0, _0xdc49c0)
              for (var _0x9ee75b = 0x0, _0x211225 = _0x20943f[_0x68de08(_0x4de9e9._0x3086f1)]; _0x9ee75b < _0x211225; _0x9ee75b++) {
                _0x20943f[_0x9ee75b]["start"](_0x549490 + _0x5f4658)
              }
              return ![]
            }
          }
          return !![]
        })
    }),
      (_0x331257[_0x460f44(0x1ab)] = {
        Linear: {
          None: function (_0x335d8d) {
            return _0x335d8d
          }
        },
        Quadratic: {
          In: function (_0x14305d) {
            return _0x14305d * _0x14305d
          },
          Out: function (_0x2331fc) {
            return _0x2331fc * (0x2 - _0x2331fc)
          },
          InOut: function (_0x366024) {
            if ((_0x366024 *= 0x2) < 0x1) return 0.5 * _0x366024 * _0x366024
            return -0.5 * (--_0x366024 * (_0x366024 - 0x2) - 0x1)
          }
        },
        Cubic: {
          In: function (_0x25f54) {
            return _0x25f54 * _0x25f54 * _0x25f54
          },
          Out: function (_0x3280dd) {
            return --_0x3280dd * _0x3280dd * _0x3280dd + 0x1
          },
          InOut: function (_0x4ef1c1) {
            if ((_0x4ef1c1 *= 0x2) < 0x1) return 0.5 * _0x4ef1c1 * _0x4ef1c1 * _0x4ef1c1
            return 0.5 * ((_0x4ef1c1 -= 0x2) * _0x4ef1c1 * _0x4ef1c1 + 0x2)
          }
        },
        Quartic: {
          In: function (_0x2841e4) {
            return _0x2841e4 * _0x2841e4 * _0x2841e4 * _0x2841e4
          },
          Out: function (_0x3cf302) {
            return 0x1 - --_0x3cf302 * _0x3cf302 * _0x3cf302 * _0x3cf302
          },
          InOut: function (_0x35c464) {
            if ((_0x35c464 *= 0x2) < 0x1) return 0.5 * _0x35c464 * _0x35c464 * _0x35c464 * _0x35c464
            return -0.5 * ((_0x35c464 -= 0x2) * _0x35c464 * _0x35c464 * _0x35c464 - 0x2)
          }
        },
        Quintic: {
          In: function (_0x56c0ea) {
            return _0x56c0ea * _0x56c0ea * _0x56c0ea * _0x56c0ea * _0x56c0ea
          },
          Out: function (_0x151329) {
            return --_0x151329 * _0x151329 * _0x151329 * _0x151329 * _0x151329 + 0x1
          },
          InOut: function (_0x12fe43) {
            if ((_0x12fe43 *= 0x2) < 0x1) return 0.5 * _0x12fe43 * _0x12fe43 * _0x12fe43 * _0x12fe43 * _0x12fe43
            return 0.5 * ((_0x12fe43 -= 0x2) * _0x12fe43 * _0x12fe43 * _0x12fe43 * _0x12fe43 + 0x2)
          }
        },
        Sinusoidal: {
          In: function (_0x2c1565) {
            var _0x5a7ac3 = _0x460f44
            return 0x1 - Math[_0x5a7ac3(0x1c1)]((_0x2c1565 * Math["PI"]) / 0x2)
          },
          Out: function (_0x49f861) {
            var _0x5b5195 = _0x460f44
            return Math[_0x5b5195(_0x490b7a._0x686cac)]((_0x49f861 * Math["PI"]) / 0x2)
          },
          InOut: function (_0x4cc9ac) {
            var _0x2ded69 = _0x460f44
            return 0.5 * (0x1 - Math[_0x2ded69(0x1c1)](Math["PI"] * _0x4cc9ac))
          }
        },
        Exponential: {
          In: function (_0x5e05d2) {
            var _0x1dc44a = _0x460f44
            return _0x5e05d2 === 0x0 ? 0x0 : Math[_0x1dc44a(0x1aa)](0x400, _0x5e05d2 - 0x1)
          },
          Out: function (_0x14e9a6) {
            var _0x396e82 = _0x460f44
            return _0x14e9a6 === 0x1 ? 0x1 : 0x1 - Math[_0x396e82(0x1aa)](0x2, -0xa * _0x14e9a6)
          },
          InOut: function (_0x57f95a) {
            var _0x51edbf = _0x460f44
            if (_0x57f95a === 0x0) return 0x0
            if (_0x57f95a === 0x1) return 0x1
            if ((_0x57f95a *= 0x2) < 0x1) return 0.5 * Math[_0x51edbf(0x1aa)](0x400, _0x57f95a - 0x1)
            return 0.5 * (-Math[_0x51edbf(0x1aa)](0x2, -0xa * (_0x57f95a - 0x1)) + 0x2)
          }
        },
        Circular: {
          In: function (_0x18b080) {
            var _0x64de4f = _0x460f44
            return 0x1 - Math[_0x64de4f(0x1c2)](0x1 - _0x18b080 * _0x18b080)
          },
          Out: function (_0x2133ca) {
            var _0x46c2c7 = _0x460f44
            return Math[_0x46c2c7(_0x58aa1c._0x56fb6c)](0x1 - --_0x2133ca * _0x2133ca)
          },
          InOut: function (_0x2688e1) {
            var _0x20062c = _0x460f44
            if ((_0x2688e1 *= 0x2) < 0x1) return -0.5 * (Math[_0x20062c(0x1c2)](0x1 - _0x2688e1 * _0x2688e1) - 0x1)
            return 0.5 * (Math[_0x20062c(_0x31fd51._0x1de0ac)](0x1 - (_0x2688e1 -= 0x2) * _0x2688e1) + 0x1)
          }
        },
        Elastic: {
          In: function (_0x4c41dd) {
            var _0x4657f0 = _0x460f44
            if (_0x4c41dd === 0x0) return 0x0
            if (_0x4c41dd === 0x1) return 0x1
            return -Math[_0x4657f0(_0x115477._0x43807d)](0x2, 0xa * (_0x4c41dd - 0x1)) * Math[_0x4657f0(0x1a4)]((_0x4c41dd - 1.1) * 0x5 * Math["PI"])
          },
          Out: function (_0x6096c) {
            var _0x33acb2 = _0x460f44
            if (_0x6096c === 0x0) return 0x0
            if (_0x6096c === 0x1) return 0x1
            return Math[_0x33acb2(0x1aa)](0x2, -0xa * _0x6096c) * Math["sin"]((_0x6096c - 0.1) * 0x5 * Math["PI"]) + 0x1
          },
          InOut: function (_0x529713) {
            var _0x29307e = _0x460f44
            if (_0x529713 === 0x0) return 0x0
            if (_0x529713 === 0x1) return 0x1
            _0x529713 *= 0x2
            if (_0x529713 < 0x1)
              return -0.5 * Math["pow"](0x2, 0xa * (_0x529713 - 0x1)) * Math[_0x29307e(_0x464fbc._0x30d136)]((_0x529713 - 1.1) * 0x5 * Math["PI"])
            return (
              0.5 *
                Math[_0x29307e(_0x464fbc._0x4ddef2)](0x2, -0xa * (_0x529713 - 0x1)) *
                Math[_0x29307e(_0x464fbc._0x30d136)]((_0x529713 - 1.1) * 0x5 * Math["PI"]) +
              0x1
            )
          }
        },
        Back: {
          In: function (_0x53ae69) {
            var _0x5407b6 = 1.70158
            return _0x53ae69 * _0x53ae69 * ((_0x5407b6 + 0x1) * _0x53ae69 - _0x5407b6)
          },
          Out: function (_0x16aaa1) {
            var _0x16c411 = 1.70158
            return --_0x16aaa1 * _0x16aaa1 * ((_0x16c411 + 0x1) * _0x16aaa1 + _0x16c411) + 0x1
          },
          InOut: function (_0x40a292) {
            var _0x1dcc5f = 1.70158 * 1.525
            if ((_0x40a292 *= 0x2) < 0x1) return 0.5 * (_0x40a292 * _0x40a292 * ((_0x1dcc5f + 0x1) * _0x40a292 - _0x1dcc5f))
            return 0.5 * ((_0x40a292 -= 0x2) * _0x40a292 * ((_0x1dcc5f + 0x1) * _0x40a292 + _0x1dcc5f) + 0x2)
          }
        },
        Bounce: {
          In: function (_0x49b77a) {
            var _0x3ca66e = _0x460f44
            return 0x1 - _0x331257["Easing"][_0x3ca66e(_0x3fd216._0x5d0a27)][_0x3ca66e(_0x3fd216._0x2046f1)](0x1 - _0x49b77a)
          },
          Out: function (_0x549555) {
            if (_0x549555 < 0x1 / 2.75) return 7.5625 * _0x549555 * _0x549555
            else {
              if (_0x549555 < 0x2 / 2.75) return 7.5625 * (_0x549555 -= 1.5 / 2.75) * _0x549555 + 0.75
              else
                return _0x549555 < 2.5 / 2.75
                  ? 7.5625 * (_0x549555 -= 2.25 / 2.75) * _0x549555 + 0.9375
                  : 7.5625 * (_0x549555 -= 2.625 / 2.75) * _0x549555 + 0.984375
            }
          },
          InOut: function (_0x371494) {
            var _0x551ac1 = _0x460f44
            if (_0x371494 < 0.5) return _0x331257[_0x551ac1(0x1ab)][_0x551ac1(_0x10cd08._0x454c9e)]["In"](_0x371494 * 0x2) * 0.5
            return _0x331257[_0x551ac1(_0x10cd08._0x1a1afd)][_0x551ac1(_0x10cd08._0x4fef71)][_0x551ac1(0x1a5)](_0x371494 * 0x2 - 0x1) * 0.5 + 0.5
          }
        }
      }),
      (_0x331257["Interpolation"] = {
        Linear: function (_0x4869eb, _0x545217) {
          var _0x3a92f9 = _0x460f44,
            _0x502d52 = _0x4869eb["length"] - 0x1,
            _0x2c82a0 = _0x502d52 * _0x545217,
            _0x3c5a6a = Math[_0x3a92f9(_0x5ba7de._0x43b565)](_0x2c82a0),
            _0x5dca8b = _0x331257[_0x3a92f9(_0x5ba7de._0x4dad94)][_0x3a92f9(0x1ce)][_0x3a92f9(_0x5ba7de._0x1971be)]
          if (_0x545217 < 0x0) return _0x5dca8b(_0x4869eb[0x0], _0x4869eb[0x1], _0x2c82a0)
          if (_0x545217 > 0x1) return _0x5dca8b(_0x4869eb[_0x502d52], _0x4869eb[_0x502d52 - 0x1], _0x502d52 - _0x2c82a0)
          return _0x5dca8b(_0x4869eb[_0x3c5a6a], _0x4869eb[_0x3c5a6a + 0x1 > _0x502d52 ? _0x502d52 : _0x3c5a6a + 0x1], _0x2c82a0 - _0x3c5a6a)
        },
        Bezier: function (_0x374d9f, _0x1fa68c) {
          var _0x46f899 = _0x460f44,
            _0x295127 = 0x0,
            _0x33a3b3 = _0x374d9f[_0x46f899(0x1c4)] - 0x1,
            _0x4a616f = Math[_0x46f899(0x1aa)],
            _0xa8fd0a = _0x331257[_0x46f899(_0x2fb404._0x2a3aa8)][_0x46f899(_0x2fb404._0x4e4b3b)][_0x46f899(0x1ae)]
          for (var _0x13d265 = 0x0; _0x13d265 <= _0x33a3b3; _0x13d265++) {
            _0x295127 +=
              _0x4a616f(0x1 - _0x1fa68c, _0x33a3b3 - _0x13d265) *
              _0x4a616f(_0x1fa68c, _0x13d265) *
              _0x374d9f[_0x13d265] *
              _0xa8fd0a(_0x33a3b3, _0x13d265)
          }
          return _0x295127
        },
        CatmullRom: function (_0x1f4e6c, _0x42b356) {
          var _0x5c6a0b = _0x460f44,
            _0x3d24dd = _0x1f4e6c["length"] - 0x1,
            _0x4f8db2 = _0x3d24dd * _0x42b356,
            _0x40f0d1 = Math[_0x5c6a0b(_0x4ca60f._0x114f3c)](_0x4f8db2),
            _0x1c57a9 = _0x331257[_0x5c6a0b(_0x4ca60f._0xe9260d)][_0x5c6a0b(_0x4ca60f._0x52d243)][_0x5c6a0b(0x1b3)]
          if (_0x1f4e6c[0x0] === _0x1f4e6c[_0x3d24dd])
            return (
              _0x42b356 < 0x0 && (_0x40f0d1 = Math["floor"]((_0x4f8db2 = _0x3d24dd * (0x1 + _0x42b356)))),
              _0x1c57a9(
                _0x1f4e6c[(_0x40f0d1 - 0x1 + _0x3d24dd) % _0x3d24dd],
                _0x1f4e6c[_0x40f0d1],
                _0x1f4e6c[(_0x40f0d1 + 0x1) % _0x3d24dd],
                _0x1f4e6c[(_0x40f0d1 + 0x2) % _0x3d24dd],
                _0x4f8db2 - _0x40f0d1
              )
            )
          else {
            if (_0x42b356 < 0x0)
              return _0x1f4e6c[0x0] - (_0x1c57a9(_0x1f4e6c[0x0], _0x1f4e6c[0x0], _0x1f4e6c[0x1], _0x1f4e6c[0x1], -_0x4f8db2) - _0x1f4e6c[0x0])
            if (_0x42b356 > 0x1)
              return (
                _0x1f4e6c[_0x3d24dd] -
                (_0x1c57a9(_0x1f4e6c[_0x3d24dd], _0x1f4e6c[_0x3d24dd], _0x1f4e6c[_0x3d24dd - 0x1], _0x1f4e6c[_0x3d24dd - 0x1], _0x4f8db2 - _0x3d24dd) -
                  _0x1f4e6c[_0x3d24dd])
              )
            return _0x1c57a9(
              _0x1f4e6c[_0x40f0d1 ? _0x40f0d1 - 0x1 : 0x0],
              _0x1f4e6c[_0x40f0d1],
              _0x1f4e6c[_0x3d24dd < _0x40f0d1 + 0x1 ? _0x3d24dd : _0x40f0d1 + 0x1],
              _0x1f4e6c[_0x3d24dd < _0x40f0d1 + 0x2 ? _0x3d24dd : _0x40f0d1 + 0x2],
              _0x4f8db2 - _0x40f0d1
            )
          }
        },
        Utils: {
          Linear: function (_0x50a0d5, _0x54f224, _0x35c481) {
            return (_0x54f224 - _0x50a0d5) * _0x35c481 + _0x50a0d5
          },
          Bernstein: function (_0x48be9d, _0x2996f2) {
            var _0x139c10 = _0x460f44,
              _0x24444f = _0x331257[_0x139c10(_0x3426f9._0x4b71e7)][_0x139c10(_0x3426f9._0x3d17c9)][_0x139c10(_0x3426f9._0x335a8f)]
            return _0x24444f(_0x48be9d) / _0x24444f(_0x2996f2) / _0x24444f(_0x48be9d - _0x2996f2)
          },
          Factorial: (function () {
            var _0x359c65 = [0x1]
            return function (_0x58c3b8) {
              var _0xa35462 = 0x1
              if (_0x359c65[_0x58c3b8]) return _0x359c65[_0x58c3b8]
              for (var _0x2c4cd8 = _0x58c3b8; _0x2c4cd8 > 0x1; _0x2c4cd8--) {
                _0xa35462 *= _0x2c4cd8
              }
              return (_0x359c65[_0x58c3b8] = _0xa35462), _0xa35462
            }
          })(),
          CatmullRom: function (_0x1f21e2, _0x57c5cb, _0x439d0e, _0x33bfe0, _0x46f75e) {
            var _0x3c09c6 = (_0x439d0e - _0x1f21e2) * 0.5,
              _0x32072b = (_0x33bfe0 - _0x57c5cb) * 0.5,
              _0x46bc33 = _0x46f75e * _0x46f75e,
              _0x1fd429 = _0x46f75e * _0x46bc33
            return (
              (0x2 * _0x57c5cb - 0x2 * _0x439d0e + _0x3c09c6 + _0x32072b) * _0x1fd429 +
              (-0x3 * _0x57c5cb + 0x3 * _0x439d0e - 0x2 * _0x3c09c6 - _0x32072b) * _0x46bc33 +
              _0x3c09c6 * _0x46f75e +
              _0x57c5cb
            )
          }
        }
      }),
      (function (_0x3f1d24) {
        var _0x19b239 = _0x460f44
        {
          _0x56dea7[_0x19b239(_0x1cc5c4._0x5a887e)] = _0x331257
        }
      })()
  })
  function _0x262e(_0x58047a, _0x57ad80) {
    var _0xdc071d = _0xdc07()
    return (
      (_0x262e = function (_0x262e27, _0x258ae4) {
        _0x262e27 = _0x262e27 - 0x1a1
        var _0x2352ed = _0xdc071d[_0x262e27]
        return _0x2352ed
      }),
      _0x262e(_0x58047a, _0x57ad80)
    )
  }

  export { Tween }