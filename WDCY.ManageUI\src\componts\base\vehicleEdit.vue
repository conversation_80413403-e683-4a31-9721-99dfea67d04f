<template>
	<el-dialog draggable width="50%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="vehicleModel" label-width="80px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="车牌号" prop="vehicleNumber">
						<el-input v-model="vehicleModel.vehicleNumber" placeholder="车牌号"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="车型" prop="vehicleType">
						<el-select style="width: 100%;" v-model="vehicleModel.vehicleType" placeholder="车型">
							<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="车位类型" prop="parkingType">
						<el-select style="width: 100%;" v-model="vehicleModel.parkingType" placeholder="车位类型">
							<el-option v-for="item in parkingList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="状态" prop="status">
						<el-select disabled style="width: 100%;" v-model="vehicleModel.status" placeholder="状态">
							<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="所属人员" prop="personId">
						<el-select style="width: 100%;" v-model="vehicleModel.personId" filterable remote
							reserve-keyword placeholder="输入关键字搜索" :remote-method="remoteMethod" :loading="loading">
							<el-option v-for="item in personList" :key="item.id" :label="item.name" :value="item.id">
								<span style="float: left">{{ item.name }}</span>
								<span style=" float: right; color: var(--el-text-color-secondary); font-size: 13px; ">{{ item.idCard }}</span>
							</el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="车牌颜色" prop="plateColor">
						<el-select style="width: 100%;" v-model="vehicleModel.plateColor" placeholder="车牌颜色">
							<el-option v-for="item in plateColorList" :key="item.nameEn" :label="item.nameCn" :value="Number(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="车位编号" prop="parkingNumber">
						<el-input v-model="vehicleModel.parkingNumber" placeholder="车位编号"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="日期">
						<el-date-picker value-format="YYYY-MM-DD HH:mm:ss" v-model="startToEndTime" type="daterange"
							start-placeholder="开始时间" end-placeholder="到期时间" :shortcuts="shortcuts" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="开放式ID" prop="openId">
						<el-input readonly disabled v-model="vehicleModel.openId" placeholder="车辆开放式ID"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交
			</el-button>
		</el-row>
		<!-- <vehicle-number></vehicle-number> -->
	</el-dialog>
</template>

<script>
import vehicleNumber from "@/componts/base/vehicleNumber.vue"
import { editVehicle, addVehicle } from "@/api/base/vehicle"
import { queryList } from "@/api/base/person"
import { pushUser } from "@/api/admin/auth";
import mitt from "@/utils/mitt";
export default {
	props: ['statusList', 'tagList', 'parkingList', 'typeList', 'plateColorList'],
	components: { vehicleNumber },
	data() {
		return {
			loading: false,
			vehicleModel: {},
			dialog: {},
			personList: [],
			personId: "",
			startToEndTime: [],
			defaultTime: ([
				new Date(2000, 1, 1, 0, 0, 0),
				new Date(2000, 2, 1, 23, 59, 59),
			]),
			rules: {
				vehicleNumber: [{
					required: true,
					message: '请输入正确车牌号',
					trigger: 'blur',
					pattern: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DABCEFGHJK]$)|([DABCEFGHJK][A-HJ-NP-Z0-9][0-9]{4}$))/ | /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/
				}],
				personId: [{
					required: true,
					message: '请选择所属人员',
					trigger: 'change',
				}],
				parkingType: [{
					required: true,
					message: '请选择车位类型',
					trigger: 'change',
				}]
			},
			shortcuts: [{
				text: '7天',
				value: () => {
					const start = new Date()
					const end = new Date()
					end.setTime(end.getTime() + 3600 * 1000 * 24 * 7)
					return [start, end]
				},

			}, {
				text: '180天',
				value: () => {
					const start = new Date()
					const end = new Date()
					end.setTime(end.getTime() + 3600 * 1000 * 24 * 180)
					return [start, end]
				},
			},
			{
				text: '一年',
				value: () => {
					const start = new Date()
					const end = new Date()
					end.setTime(end.getTime() + 3600 * 1000 * 24 * 365)
					return [start, end]
				},
			},
			{
				text: '50年',
				value: () => {
					const start = new Date()
					const end = new Date()
					let year = 50
					let newYear = 0
					if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0) {
						newYear /= 4
					} else {
						newYear = year / 4 + 1
					}
					end.setTime(end.getTime() + 3600 * 1000 * 24 * 365 * year + newYear * 3600 * 1000 * 24)
					console.log(end.getFullYear(), end);
					return [start, end]
				},
			}]
		}
	},
	methods: {
    	//用户操作
		pushUserAction(actionName) {
			if (actionName != 'home') {
        let senObj = {
          oper: actionName,
          receiveClient: "all",
          tags: ['manager']
        }
				pushUser(senObj).then(res =>{
					if (res !== null && res.code === 0) {
					} else {
					}
				})
			}
		},
		remoteMethod(val) { // 编辑时候调用获取输入的姓，更改所属人员列表
			const communityId = localStorage.getItem("communityId")
			queryList({ name: val, communityId: communityId })
				.then(res => {
					this.personList = res.data.result
				})
		},
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				this.vehicleModel.communityId = String(localStorage.getItem("communityId"))
				if (valid) {
					if (this.vehicleModel.id == 0) {
						this.vehicleModel.validBeginTime = this.startToEndTime[0]
						this.vehicleModel.validEndTime = this.startToEndTime[1]
						addVehicle(this.vehicleModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						// this.vehicleModel.personId = this.vehicleModel.personId.personId
						this.vehicleModel.validBeginTime = this.startToEndTime[0]
						this.vehicleModel.validEndTime = this.startToEndTime[1]
						editVehicle(this.vehicleModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		}
	},
	mounted() {
		this.$nextTick(function () {
			mitt.on('openVehicleEdit', (vehicle) => {
        		this.pushUserAction("车辆编辑")
				this.vehicleModel = vehicle
				this.dialog.show = true
				this.dialog.title = "修改信息"
				this.personList = [{ id: this.vehicleModel.personId, name: this.vehicleModel.personName }]
				this.startToEndTime = [this.vehicleModel.validBeginTime, this.vehicleModel.validEndTime]
			})
			mitt.on('openVehicleAdd', () => {
				this.vehicleModel = {
					id: 0
				}
        		this.pushUserAction("车辆新增")
				this.startToEndTime = []
				this.dialog.show = true
				this.dialog.title = "添加车辆"
			})
			mitt.on("carNum", (data) => {
				console.log(data, 'yesyes');
			})
		})
	},
	watch:{
		// 监听车牌号 判断车类型
		'vehicleModel.vehicleNumber'(newValue,oldValue){
			if (newValue && newValue.length == 7) {
				this.vehicleModel.vehicleType = 0
			} else if( newValue && newValue.length == 8){
				this.vehicleModel.vehicleType = 1
			} else{
				this.vehicleModel.vehicleType = null
			}
		},
		"dialog.show"(newVal,oldVal){
			if (!newVal) {
				this.pushUserAction("车辆管理")
			}
		}
	}
}
</script>
