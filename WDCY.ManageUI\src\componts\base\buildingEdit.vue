<template>
  <el-dialog
    draggable
    width="50%"
    v-loading="loading"
    v-model="dialog.show"
    destroy-on-close
    :title="dialog.title"
  >
    <el-form
      :rules="rules"
      ref="form"
      :model="buildingModel"
      label-width="80px"
    >
      <el-row>
        <el-col :span="12">
          <!-- <el-form-item label="楼栋序号" prop="sort">
            <el-input
              v-model="buildingModel.sort"
              placeholder="楼栋序号"
            ></el-input>
          </el-form-item> -->
          <el-form-item label="楼栋编号" prop="sort">
            <el-input-number
              style="width: 100%"
              @change="changeBuildingNumber"
              v-model="buildingModel.sort"
              placeholder="楼栋编号"
              :min="1"
              :tep="0"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="楼栋名称" prop="buildingNumber">
            <el-input
              v-model="buildingModel.buildingNumber"
              placeholder="楼宇编号(A110)"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="10">
          <el-form-item label="经纬度" prop="lng">
            <el-input
              style="width: 45%"
              v-model="buildingModel.lng"
              placeholder="经度"
            ></el-input
            >&nbsp;-&nbsp;<el-input
              style="width: 45%"
              v-model="buildingModel.lat"
              placeholder="纬度"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-form-item label-width="0">
            <el-button
              type="text"
              style="font-size: 30px; padding: 0"
              @click="selectPoint"
            >
              <el-icon :size="30">
                <location-filled></location-filled>
              </el-icon>
            </el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="海拔"
            prop="alt"
            style="width: 100%; text-align: left"
          >
            <el-input-number
              style="width: 100%"
              controls-position="right"
              v-model="buildingModel.alt"
              placeholder="海拔"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="开放式ID" prop="openId">
            <el-input
              readonly
              disabled
              v-model="buildingModel.openId"
              placeholder="楼栋开放式ID"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="面积" prop="acreage">
            <el-input
              v-model="buildingModel.acreage"
              placeholder="占地面积m²"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="地理围栏" prop="polyCoords">
            <el-input
              type="textarea"
              v-model="buildingModel.polyCoords"
              placeholder="多坐标点"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="buildingModel.remark"
              placeholder="备注"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="楼栋图片">
            <el-scrollbar style="height: 198px">
              <el-upload
                multiple
                accept="image/jpeg,image/jpg,image/png"
                v-model:file-list="fileList"
                action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :on-remove="deletePic"
                :http-request="loadingImg"
                :before-upload="beforeUploadImg"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>

              <div style="display: flex; position: relative; top: -10px">
                <div
                  v-for="(item, idx) in fileList"
                  :key="idx"
                  style="
                    width: 146px;
                    display: flex;
                    flex-shrink: 0;
                    margin-right: 10px;
                  "
                >
                  <el-input
                    v-model="item.name"
                    style="width: 100%"
                    placeholder="请输入文件名"
                  ></el-input>
                </div>
              </div>
            </el-scrollbar>
            <el-dialog v-model="dialogVisible" class="picView">
              <img
                w-full
                style="width: 100%"
                :src="dialogImageUrl"
                alt="Preview Image"
              />
            </el-dialog>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>

<script>
import { editBuilding, addBuilding } from "@/api/base/building";
import mitt from "@/utils/mitt";
import { fileUpload, fileRemove } from "@/api/admin/file";
// import { openMap } from "@/utils/myUtils";
import { getCommunity } from "@/api/base/community";
export default {
  props: ["communityList"],
  data() {
    return {
      uploadImgType: [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".JPG",
        ".JPEG",
        ".PNG",
        ".GIF",
      ],
      imgServer: import.meta.env.VITE_BASE_API,
      fileList: [],
      dialogImageUrl: "",
      dialogVisible: false,
      loading: false,
      activeName: "first",
      buildingModel: {
        expandParams: [],
      },
      dialog: {},
      rules: {
        buildingNumber: [
          {
            required: true,
            message: "请输入楼栋名称",
            trigger: "blur",
          },
        ],
        communityId: [
          {
            required: true,
            message: "请选择小区",
            trigger: "blur",
          },
        ],
        sort: [
          {
            required: true,
            message: "请输入楼宇序号",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    deletePic(uploadFile, uploadFiles) {
      console.log(uploadFiles, uploadFile);
      fileRemove({ fileUrl: uploadFile.url.replace(this.imgServer, "") });
      this.buildingModel.expandParams = uploadFiles;
    },

    //上传图片前的钩子
    beforeUploadImg(file) {
      const type = file.name.substring(file.name.lastIndexOf(".")); // 获取文件后缀，取文件格式
      const isLt10M = file.size / 1024 / 1024 < 25;
      if (!this.uploadImgType.includes(type)) {
        this.$message({
          type: "error",
          message: "只支持jpg,jpeg,png,gif,JPG,JPEG,PNG,GIF文件格式！",
        });
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: "上传文件大小不能超过 25MB!",
          type: "warning",
        });
        return false;
      }
    },
    // 图片点开大图
    handlePictureCardPreview(uploadFile) {
      console.log(uploadFile);
      this.dialogImageUrl = uploadFile.url;
      this.dialogVisible = true;
    },

    // 上传图片
    loadingImg(files) {
      let form = new FormData();
      form.append("needCompress", false);
      form.append("file", files.file);
      form.append("modulesName", "base");
      form.append("functionName", "building");
      form.append("communityId", localStorage.getItem("communityId"));
      fileUpload(form).then((res) => {
        this.buildingModel.expandParams.push(res.data.result);
        if (res.data.code == 0) {
        }
      });
      console.log(this.fileList);
    },
    changeBuildingNumber(e) {
      if (e) this.buildingModel.buildingNumber = e + "栋";
      else this.buildingModel.buildingNumber = "";
    },

    // 选择点位
    selectPoint() {
      var communityId = localStorage.getItem("communityId");
      getCommunity(communityId)
        .then((res) => {
         
          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map.mode;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          if (result.enabled3d && mode == "mars3d") {
            position = config.map.sdgis.position;

            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (this.buildingModel.lng && this.buildingModel.lat) {
            center = {
              lng: this.buildingModel.lng,
              lat: this.buildingModel.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }

          var data = {
            enabled3d: result.enabled3d,
            edit: true,
            point: [
              this.buildingModel.lng,
              this.buildingModel.lat,
              this.buildingModel.alt,
            ],
            position: position,
            center: center,
            modeUrl: modeUrl,
            title: "地图选点",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);

          mitt.emit("openMarsMap", data);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        const list = [];

        // uploadFiles.forEach(item => {
        //   if (item.url.includes(this.imgServer)) {
        //     const url = item.url
        //     item.url = url.replace(this.imgServer,"")
        //     console.log(item.url);
        //   }
        // });

        // 处理文件图片
        for (const key in this.fileList) {
          if (this.fileList[key].url.includes(this.imgServer)) {
            list.push({
              name: this.fileList[key].name.split(".")[0],
              url: this.buildingModel.expandParams[key].url.replace(
                this.imgServer,
                ""
              ),
            });
          } else {
            list.push({
              name: this.fileList[key].name.split(".")[0],
              url: this.buildingModel.expandParams[key].url,
            });
          }
        }
        this.buildingModel.expandParams = JSON.stringify(list);

        this.buildingModel.communityId = localStorage.getItem("communityId");
        if (valid) {
          this.buildingModel.sort = Number(this.buildingModel.sort);
          if (this.buildingModel.id == 0) {
            addBuilding(this.buildingModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          } else {
            editBuilding(this.buildingModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          }
        }
      });
    },
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openBuildingEdit", (building) => {
        this.buildingModel = building;
        console.log(building);
        // 处理文件
        if (!this.buildingModel.expandParams) {
          this.buildingModel.expandParams = [];
          this.fileList = [];
        } else {
          this.fileList = [];
          for (const item of JSON.parse(this.buildingModel.expandParams)) {
            if (item.url.includes(this.imgServer)) {
              this.fileList.push({ name: item.name, url: item.url });
            } else {
              this.fileList.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
            }
          }
          // this.fileList =
          this.buildingModel.expandParams = JSON.parse(
            this.buildingModel.expandParams
          );
        }

        this.dialog.show = true;
        this.dialog.title = "修改信息";
      });

      mitt.on("openBuildingAdd", () => {
        this.buildingModel = {
          id: 0,
          expandParams: [],
          fileList: [],
        };
        this.dialog.show = true;
        this.dialog.title = "添加楼栋";
      });
      mitt.on("setPointValue", (e) => {
        this.buildingModel.lng = e[0];
        this.buildingModel.lat = e[1];
        if (e.length == 3) {
          this.buildingModel.alt = e[2];
        }
      });
    });
  },
};
</script>
<style scoped lang="less">
div /deep/.picView .el-dialog__header {
  background-color: #fff;
  box-shadow: none;
}
div /deep/ .picView .el-dialog__close {
  color: #ccc;
}

div /deep/ .el-upload-list {
  display: flex;
  width: 100%;
  flex-wrap: nowrap;
  > li,
  div {
    display: flex;
    flex-shrink: 0;
  }
}
</style>
