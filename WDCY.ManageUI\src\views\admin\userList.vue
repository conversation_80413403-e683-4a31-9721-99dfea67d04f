<template>
	<user-register :userGroupList="userGroupList" :roleList="roleList" @search="search"></user-register>
	<user-edit :userGroupList="userGroupList" :sexList="sexList" :statusList="statusList" :roleList="roleList" @search="search" ></user-edit>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-cascader :show-all-levels="false" style="width: 100%;" :props="{checkStrictly:true}" :options="userGroupList" @change="handleChange" clearable placeholder="选择组"/>
		</el-col>
		<el-col :span="4">
			<el-input v-model="searchModel.keyWord" @keydown.enter="search" placeholder="用户名|昵称" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="8">
			<el-button style="float: right;" type="primary" @click="register" v-if="hasPerm('sys:user:add')">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="userList" border height="calc(100vh - 300px)" style="width: 100%">
				<el-table-column prop="avatar" align="center" label="头像" width="100">
					<template #default="scope">
						 <el-image preview-teleported fit="contain" style="width: 50px; height: 50px" :src="imgServer+scope.row.avatar" :preview-src-list="[imgServer+scope.row.avatar]" :z-index="3000">
							<template #error>
								<!-- <el-icon>暂无</el-icon> -->
								<el-icon style="width: 100%; height: 100%;"><Avatar  style="width: 100%; height: 100%;"/></el-icon>
							</template>
						</el-image>
					</template>
				</el-table-column>
				<el-table-column prop="status" align="center" label="用户组" width="140">
					<template #default="scope">
						{{deptLabelFormat(userGroupList,scope.row.groupId)}}
					</template>
				</el-table-column>
				<el-table-column prop="userName" align="center" label="用户名" />
				<el-table-column prop="nickName" align="center" label="昵称"/>
				<el-table-column prop="sex" align="center" label="性别" :formatter="formatSex" width="88" />


				<el-table-column prop="status" align="center" label="状态" width="88">
					<template #default="scope">
						<el-tag :type="scope.row.status == 0 ? 'success' : 'danger'">{{ formatStatus(scope.row,null,scope.row.status) }}</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="updateTime" align="center" label="更新时间" />
				<el-table-column prop="authEndTime" align="center" label="授权过期" />
				<el-table-column prop="mobile" align="center" label="手机" />
				<!-- <el-table-column prop="email" align="center" label="邮箱" /> -->
				<el-table-column align="center" width="160" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row)">编辑</el-button>
						<el-button style="margin-right: 10px;" type="text" size="default" @click="deleted(scope.row.id)">删除</el-button>
						<el-dropdown>
							<el-button type="text" size="default">更多</el-button>
							<template #dropdown>
							  <el-dropdown-menu>
								<el-dropdown-item>
									<el-button type="text" size="default" @click="updatePassword(scope.row)">修改密码</el-button>
									<el-button type="text" size="default" @click="resetPassword(scope.row)">重置密码</el-button>
								</el-dropdown-item>
							  </el-dropdown-menu>
							</template>
						</el-dropdown>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>

<script>
import { pagingUser,deleteUser,resetPassword } from "@/api/admin/user"
import { listUserGroup } from "@/api/admin/userGroup"
import userEdit from "@/componts/admin/user/userEdit.vue"
import { listDictByNameEn } from "@/api/admin/dict"
import { listRole } from "@/api/admin/role"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import userRegister from "@/componts/admin/user/userRegister.vue"

export default {
	components:{ userRegister,userEdit },
	data() {
		return {
			searchModel: {},
			userList: [],
			roleList:[],
			statusList:[],
			sexList:[],
			userGroupList:[],
			imgServer: import.meta.env.VITE_BASE_API,
			total:0,
			pageSize:10,
			
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		deptLabelFormat(arr,deptId){
			for(let item of arr){
				// console.log(item,deptId);
				if (item.value == deptId) {
					return item.label
				}
				if (item.children && item.children.length > 0) {
					var foundNode = this.deptLabelFormat(item.children,deptId)
					if (foundNode) {
						return foundNode
					}
				}
			}
		},
		handleChange(e){
			if(e == null){
				this.searchModel.groupId = null
				return
			}
			this.searchModel.groupId = e[e.length-1]
		},
		search() {
			pagingUser(this.searchModel)
			.then(res => {
				this.userList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		updatePassword(row){
			mitt.emit('openUserPwdModifyByUserName',row.userName)
		},
		// 重置密码
		resetPassword(row){
			this.$confirm('重置密码, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				resetPassword({userName:row.userName})
					.then(res => {
						this.$message.success('密码重置为'+res.data.result)
					})
			}).catch(() => { })
		},
		edit(user){
			mitt.emit('openUserEdit',user)
		},
		deleted(id){
			this.$confirm('删除用户, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteUser(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		register() {
			mitt.emit('openUserRegister',this.searchModel)
		},

		// 格式化数据
		formatStatus(row, column, cellValue, index){
			let result = ''
			for(let item of this.statusList){
				if(item.nameEn == cellValue){
					result = item.nameCn
					break
				}
			}
			return result
		},
		formatSex(row, column, cellValue, index){
			let result = ''
			for(let item of this.sexList){
				if(item.nameEn == cellValue){
					result = item.nameCn
					break
				}
			}
			return result
		},
		async init(){
			try{

				let sex_res = await listDictByNameEn('sex')
				this.sexList = sex_res.data.result
				//console.log(this.sexList,'this.sexList');
				let status_res = await listDictByNameEn('user_status')
				this.statusList = status_res.data.result
				
				let res = await pagingUser(this.searchModel)
				this.userList = res.data.result.list
				this.total = res.data.result.total
				
				let resGroup = await listUserGroup({totalize:99999})
				var groupStr = JSON.stringify(resGroup.data.result.list).replaceAll('groupName','label').replaceAll('id','value')
				this.userGroupList = JSON.parse(groupStr)

				let role_res = await listRole({status:0})
				this.roleList = role_res.data.result
			}catch(err){
			}
		}
	},
	created() {
		mitt.off('openUserEdit')
		mitt.off('openUserRegister')
		mitt.on('refreshUserList',()=>{this.search()})
		this.init()
		this.search()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	div /deep/ .el-tree-node__content{
		height: 35px;
	}
	div /deep/ .el-tree-node__content:hover{
		color: #fff;
		background-color: var(--el-color-primary-light-5);
	}

	div /deep/ .el-tree-node__expand-icon{
		font-size: 18px;
		margin-right: 15px;
	}
</style>
