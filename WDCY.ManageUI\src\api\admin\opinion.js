import request from '@/utils/request'

export const listSuggestion = (data) =>
	request({
		url: '/suggestion/page',
		method: 'get',
		params: data
	})
export const deleteSuggestion = (data) =>
	request({
		url: '/suggestion/batch-delete',
		method: 'delete',
		data: data
	})
export const editSuggestion = (data) =>
	request({
		url: '/suggestion',
		method: 'put',
		data: data
	})
export const getSuggestion = (id) =>
	request({
		url: '/suggestion/' + id,
		method: 'get'
	})