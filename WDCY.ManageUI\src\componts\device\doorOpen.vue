<template>
  <div>
    <el-dialog
      draggable
      width="50%"
      distroy-on-close
      v-model="dialog.show"
      :title="dialog.title"
      class="data-dialog"
      :before-close="close"
    >
      <!-- 状态：{{ doorStatus }} -->
      <el-row :gutter="20">
        <el-col :span="8" class="header">
          <div>
            <div>道闸状态：</div>
            <div>{{ barrierStatus == 1 ? "开" : "关" }}</div>
          </div>
        </el-col>
        <el-col :span="8" class="header">
          <div>
            <div>道闸状态：</div>
            <div>{{ doorStatus }}</div>
          </div>
        </el-col>
        <el-col :span="8" class="header">
          <div>
            <div>操作道闸：</div>
            <el-switch
              :active-value="true"
              :inactive-value="false"
              v-model="barrierStatus"
              @change="editStatus()"
            />
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="video">
            <div class="video-title">视频监控列表</div>
            <div style="display: flex">
              <el-scrollbar class="video-list" v-if="deviceLinkList[0]">
                <div
                  v-for="item in deviceLinkList"
                  :key="item.id"
                  @click="treeCheckChange(item)"
                >
                  {{ item.linkDevName }} <span>点击播放 ▶</span>
                </div>
              </el-scrollbar>
              <div
                class="video-list"
                style="text-align: center; padding-top: 20px"
                v-else
              >
                暂无数据
              </div>
              <div style="width: 512px; height: 294px">
                <!-- <div style="width: 100%; height: 100%" id="player1"></div> -->
                <monitor
                  v-if="getConfigSuccess"
                  ref="monitor"
                  :width="videoWidth"
                  :height="videoHeight"
                  :isShowCuttingPartWindow="isShowCuttingPartWindow"
                  :communityId="communityId"
                  :playMode="playMode"
                  :videoMode="videoMode"
                  :devId="linkDevId"
                  :devNo="linkDevNo"
                  :backStartTime="backStartTime"
                  :backEndTime="backEndTime"
                  :videoConfig="videoConfig"
                >
                </monitor>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <!-- <div style="height: 100px;">
            <el-select v-model="deviceLinkSelected" style="width:250px" @change="deviceLinkSelect(deviceLinkList[0])" placeholder="设施类型" clearable>
            <el-option v-for="item in deviceLinkList" :key="item.linkDevId" :label="item.linkDevName"
              :value="item.linkDevId"></el-option>
          </el-select>
        </div> -->

      <!-- <el-row justify="center">
            <el-button type="primary" style="width: 100px; height: 30px; margin-top: 20px" @click="onSubmit">确 认</el-button>
        </el-row> -->
    </el-dialog>
  </div>
</template>

<script>
import mitt from "@/utils/mitt";
import { ref, reactive, nextTick, onMounted, onUnmounted } from "vue";
import {
  deviceLink,
  queryMonitorByDevNo,
  queryVideoConfig,
} from "@/api/device/device";
import videoEdit from "@/componts/video/videoEdit.vue";
import monitorView from "@/componts/video/monitorView.vue";
import { listDictByNameEn } from "@/api/admin/dict";
import { ElMessageBox } from "element-plus";
import { ElMessage } from "element-plus";
import { getDictCss, formatDict } from "@/utils/dict";
import { openVehicleDoor, openPeopleDoor } from "@/api/device/device";
import Hls from "hls.js";
import monitor from "@/componts/monitor/index.vue";

export default {
  components: { monitor },

  data() {
    return {
      dialog: {
        show: false,
        title: "",
      },
      dataInfo: {},
      doorStatus: "",
      hls: null,
      barrierStatus: false,
      deviceLinkList: [],
      vd: null,
      deviceLinkSelected: "",
      player: null,

      splitNum: 2,
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
      show: true,
      urls: {
        realplay: "",
        talk: "",
        playback: "",
      },
      playback: {
        startTime: "2021-07-26T00:00:00",
        endTime: "2021-07-26T23:59:59",
        valueFormat: "",
        seekStart: "2021-07-26T12:00:00",
        rate: "",
      },
      loading: [],
      muted: true,
      volume: 50,
      performanceLack: false, //浏览器性能不足回调

      getConfigSuccess: false, //初始化monitor
      isShowCuttingPartWindow: false,
      communityId: String(localStorage.getItem("communityId")),
      playMode: 0,
      videoMode: "PLUGIN",
      linkDevId: "",
      linkDevNo: "",
      backStartTime: "",
      backEndTime: "",
      videoConfig: null, //视频播放参数
      videoWidth: 512,
      videoHeight: 294,
    };
  },

  mounted() {
    mitt.on("doorOpen", (res) => {
      nextTick(() => {
        this.initPop(res);

        // deviceLink({ devId: res.id })
        //   .then((dres) => {

        //     // this.createPlayer();

        //     this.deviceLinkList = dres.data.result.filter((item) => {
        //       if (item.linkType == "视频监控" && item.linkDevStatus == 0) {
        //         return item;
        //       }
        //     });
        //     this.deviceLinkSelected = this.deviceLinkList[0].linkDevId;
        //     console.log(this.deviceLinkList[0]);
        //     this.queryVideo(this.deviceLinkList[0]);
        //     // queryMonitorByDevNo(deviceLinkList.value[0].devNo).then(vres => {
        //     //     setTimeout(function () {
        //     //         var video = vd.value
        //     //         if (Hls.isSupported()) {
        //     //             hls = new Hls()
        //     //             if (!vres.data.result) {
        //     //                 return
        //     //             }
        //     //             hls.loadSource(vres.data.result.url)
        //     //             hls.attachMedia(video)
        //     //             hls.on(Hls.Events.MANIFEST_PARSED, () => {
        //     //                 video.play()
        //     //             })
        //     //             hls = hls
        //     //         } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        //     //             video.src = vres
        //     //             video.play()
        //     //         }
        //     //     }, 500)
        //     // })
        //   })
        //   .catch((err) => {
        //     this.deviceLinkList = [];
        //     console.log(err);
        //   });

        // if (this.deviceLinkList[0]) {
        //   // queryMonitorByDevNo(deviceLinkList.value[0].linkDevId).then(res => {
        //   //     setTimeout(function() {
        //   //     var video = vd.value
        //   //     if (Hls.isSupported()) {
        //   //         hls = new Hls()
        //   //         hls.loadSource(res.videoSrc.url)
        //   //         hls.attachMedia(video)
        //   //         hls.on(Hls.Events.MANIFEST_PARSED, () => {
        //   //             video.play()
        //   //         })
        //   //         hls = hls
        //   //     } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        //   //         video.src = res.videoSrc
        //   //         video.play()
        //   //     }
        //   // }, 500)
        //   // })
        // }

        // this.dataInfo = res;
        // this.dialog.show = true;
        // // ————${res.data.devNo}
        // this.dialog.title = `${res.data.name}`;
        // console.log(res);
        // if (res.type == 1) {
        //   this.doorStatus = formatDict(res.doorStatusList, res.data.doorStatus);
        // } else if (res.type == 2) {
        //   this.doorStatus = formatDict(res.doorStatusList, res.data.doorStatus);
        // }
        // const data = {};
        // data.id = String(res.id);
      });
    });
  },
  methods: {
    initPop(res) {
      this.dataInfo = res;
      this.dialog.show = true;

      this.dialog.title = `${res.data.name}`;

      if (res.type == 1) {
        this.doorStatus = formatDict(res.doorStatusList, res.data.doorStatus);
      } else if (res.type == 2) {
        this.doorStatus = formatDict(res.doorStatusList, res.data.doorStatus);
      }

      this.getDeviceLinkList(res.id);
    },

    getDeviceLinkList(id) {
      deviceLink({ devId: id }).then((dres) => {
        this.deviceLinkList = dres.data.result.filter((item) => {
          if (item.linkType == "视频监控" && item.linkDevStatus == 0) {
            return item;
          }
        });
        this.deviceLinkSelected = this.deviceLinkList[0].linkDevId;
        console.log(this.deviceLinkList[0]);

        this.getVideoConfig(this.deviceLinkList[0]);
      });
    },

    getVideoConfig(monitorItem) {
      this.getConfigSuccess = false;

      queryVideoConfig().then((res) => {
        // appKey: "MjY4ODI3OTc=";
        // ip: "*************";
        // playbackMode: "PLUGIN";
        // playbackProtocolType: null;
        // port: "443";
        // previewMode: "PLUGIN";
        // previewProtocolType: null;
        // ptSign: "hik_video";
        // secret: "VmxSNlZqVXpkV1YwUVRGMFVEWjJjemx0VTJJPQ==";

        this.videoConfig = res.data.result;

        this.videoMode = this.videoConfig.previewMode;

        this.linkDevId = monitorItem.linkDevId;
        this.linkDevNo = monitorItem.linkDevNo;
        setTimeout(() => {
          this.getConfigSuccess = true; //初始化monitor
        }, 100);
      });
    },

    // 弹窗关闭销毁监控
    close() {
      // if (this.player) {
      //   this.stopPlay();
      //   this.stopAllPlay();
      // }

      // if (this.hls) {
      //   this.hls.destroy();
      //   this.dialog.show = false;
      // } else {
      this.getConfigSuccess = false;
      this.dialog.show = false;
      // }
    },
    //点击视频监控列表，展示每个视频
    videoPlay(params) {
      console.log(params);
      this.queryVideo(params);
      // queryMonitorByDevNo(params.devNo).then(vres => {
      //     setTimeout(function () {
      //         var video = this.vd
      //         if (Hls.isSupported()) {
      //             this.hls = new Hls()
      //             if (!vres.data.result) {
      //                 return
      //             }
      //             this.hls.loadSource(vres.data.result.url)
      //             this.hls.attachMedia(video)
      //             this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
      //                 video.play()
      //             })
      //             hls = hls
      //         } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      //             video.src = vres
      //             video.play()
      //         }
      //     }, 500)
      // })
    },
    // 操作开闸
    editStatus() {
      var _this = this;
      console.log(this.barrierStatus);
      if (this.barrierStatus) {
        this.barrierStatus = false;
        ElMessageBox.confirm("此操作将开启闸门，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          _this.barrierStatus = true;
          const data = {};
          data.id = String(this.dataInfo.id);
          if (this.dataInfo.type == 2) {
            data.type = 2;
            openVehicleDoor(data)
              .then((res) => {
                ElMessage.success({
                  message: "开闸成功!",
                });
                setTimeout(function () {
                  _this.barrierStatus = false;
                }, 5000);
              })
              .catch((err) => (_this.barrierStatus = false));
          } else if (this.dataInfo.type == 1) {
            data.type = 2;
            openPeopleDoor(data)
              .then((res) => {
                ElMessage.success({
                  message: "开闸成功!",
                });
                setTimeout(function () {
                  _this.barrierStatus = false;
                }, 5000);
              })
              .catch((err) => (_this.barrierStatus = false));
          }
        });
      }
    },
    // 初始化创建播放器
    createPlayer() {
      var that = this;
      this.player = new window.JSPlugin({
        szId: "player1",
        szBasePath: "/lib/h5player",
        // szBasePath: "../../../public/lib/h5player",
        // iMaxSplit: 16, //分屏播放
        // iCurrentSplit: 1,
        // openDebug: true,
        oStyle: {
          // borderSelect: IS_MOVE_DEVICE ? "#000" : "#FFCC00",
        },
      });

      // 事件回调绑定
      this.player.JS_SetWindowControlCallback({
        performanceLack: function () {
          //性能不足回调
          that.performanceLack = true;
          // ElMessage.error({
          // 	message: "性能不足！"
          // })
          console.log("performanceLack callback: 性能不足");
        },
      });
    },
    // treeCheckChange(obj) {
    //   this.previewPlay(obj);
    // },
    // // 监控被选中或取消
    treeCheckChange(item) {
      var that = this;
      that.getConfigSuccess = false;
      setTimeout(() => {
        that.linkDevId = item.linkDevId;
        that.linkDevNo = item.linkDevNo;
        that.linkDevName = item.linkDevName;

        that.getConfigSuccess = true;
      }, 100);
    },

    // 监控预览请求地址并播放
    previewPlay(obj) {
      // console.log("监控预览请求地址并播放");

      const dict = obj;
      if (dict.code === null) {
        return;
      }
      var videoSrc = "";
      const param = {
        code: "",
        id: "",
        urlType: "",
        communityId: "",
        playType: "previewURLs",
      };

      param.code = dict.code;
      param.communityId = dict.communityId;
      param.id = dict.id;
      param.urlType = "ws";
      param.playType = "previewURLs";
      // showLoading();
      // console.log("param: " + JSON.stringify(param));

      queryMonitorByDevNo(param)
        .then((res) => {
          if (res.data.result !== null) {
            if (res.data.result !== null) {
              videoSrc = res.data.result.url;

              if (!videoSrc) {
                videoSrc = "";
                this.$message({
                  type: "warning",
                  message: "无视频播放资源",
                });
                return;
              }
              setTimeout(() => {
                this.realplay(videoSrc, obj);
              }, 100);
            } else {
              videoSrc = "";
              this.$message({
                type: "warning",
                message: "无视频播放资源",
              });
            }
          } else {
            this.$message({
              type: "error",
              message: "获取数据失败",
            });
          }
        })
        .catch((e) => {
          this.$message({
            type: "error",
            message: "获取数据失败",
          });
        });
    },

    /* 预览&对讲 */
    realplay(urls, obj) {
      console.log(this.player);
      let { player, mode = 1, config = {} } = this,
        index = player.currentWindowIndex;

      this.player
        .JS_Play(
          urls,
          {
            playURL: urls,
            mode: mode, //解码类型：0=普通模式; 1=高级模式 默认为0,h5高级模式才能播放
            ...config,
          },
          index
        )
        .then(() => {
          var playerObj = {
            currentWindowIndex: index,
            src: urls,
            obj: obj,
          };
        });
    },
    stopAllPlay() {
      this.player.JS_StopRealPlayAll().then(
        () => {
          this.playback.rate = 0;
          console.log("stopAllPlay success");
        },
        (e) => {
          console.log(e);
        }
      );
    },
    stopPlay() {
      this.player.JS_Stop().then(
        () => {
          this.playback.rate = 0;
          console.log("stop realplay success");
        },
        (e) => {
          console.log(e);
        }
      );
    },

    queryVideo(row) {
      queryVideoConfig().then((res) => {
        console.log(res);
        // let urlType = res.data.result.previewMode=="GBT" ? res.data.result.previewProtocolType : (res.data.result.previewMode?res.data.result.previewMode:'hls')
        let urlType = "ws";
        this.urlType = urlType.toLowerCase();
        let video = {
          communityId: String(localStorage.getItem("communityId")),
          code: row.linkDevNo,
          urlType: urlType.toLowerCase(),
          playType: "previewURLs", //playbackURLs 回看
          id: row.linkDevId,
        };
        if (video.urlType == "plugin") {
          return;
        }

        this.treeCheckChange(video);
      });

      // queryVideoConfig().then((res) => {
      //   let urlType =
      //     res.data.result.previewMode == "GBT"
      //       ? res.data.result.previewProtocolType
      //       : res.data.result.previewMode
      //       ? res.data.result.previewMode
      //       : "hls";
      //   let video = {
      //     communityId: String(localStorage.getItem("communityId")),
      //     code: row.linkDevNo,
      //     urlType: urlType.toLowerCase(),
      //     playType: "previewURLs", //playbackURLs 回看
      //     id: row.id,
      //   };
      //
      //   if (video.urlType == "plugin") {
      //     const data = {
      //       res: res.data.result,
      //       devNo: row.linkDevNo,
      //     };
      //     mitt.emit("openPluginVideo", data);
      //   } else if (video.urlType == "ws") {
      //     var data = { video: video, name: row.name };
      //     mitt.emit("openWSVideo", data);
      //   } else {
      //     queryMonitorByDevNo(video).then((res) => {
      //       var data = {
      //         videoSrc: res.data.result,
      //         name: row.name,
      //         urlType: urlType.toLowerCase(),
      //       };

      //       mitt.emit("openVideoEdit", data);
      //     });
      //   }
      // });
    },
  },
};
</script>


<style scoped lang="less">
/deep/.el-dialog__body {
  background-color: #f1f5ff;
  padding: 10px !important;
}
/deep/ #player1_playCanvas0 {
  // width:512px!important;
  // height:294px!important;
}

.header {
  // padding: 5px;
  > div {
    // padding: 5px;
    background-color: #fff;
    line-height: 69px;
    font-size: 16px;
    color: #666666;
    // width: 270px;
    display: flex;
    justify-content: space-around;
    align-items: center;

    > div:nth-child(1) {
    }
  }
}

.video {
  margin-top: 10px;
  padding: 21px 25px 20px 25px;
  background-color: #fff;

  .video-title {
    width: 96px;
    height: 32px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 700;
    line-height: 21px;
    color: #000000;
    opacity: 1;
  }

  .video-list {
    flex: 1;
    margin-right: 10px;
    height: 294px;
    border: 1px solid #ccc;

    div {
      height: 52px;
      line-height: 52px;
      padding-left: 20px;
      display: flex;
      justify-content: space-between;
      span {
        margin-right: 40px;
      }
      span:hover {
        color: rgb(96, 148, 255);
      }
    }

    div:nth-child(2n-1) {
      background-color: rgba(248, 248, 248);
    }

    div:hover {
      background-color: rgb(227, 233, 241);
      cursor: pointer;
      color: rgb(96, 148, 255);
    }
  }
}
</style>