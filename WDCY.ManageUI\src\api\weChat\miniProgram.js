import request from '@/utils/request'

export const miniProgramList = (data) =>
	request({
		url: '/wxminiapp',
		method: 'get',
		params: data
	})
export const getMiniProgram = (id) =>
	request({
		url: '/wxminiapp/'+id,
		method: 'get',
	})
export const addMiniProgram = (data) =>
	request({
		url: '/wxminiapp',
		method: 'post',
		data: data
	})
export const editMiniProgram = (data) =>
	request({
		url: '/wxminiapp',
		method: 'put',
		data: data
	})
export const deleteMiniProgram = (id) =>
	request({
		url: '/wxminiapp',
		method: 'delete',
        data: {
			id: id
		}
	})

//小程序绑定菜单的 菜单列表
export const menuList = () =>
	request({
		url: '/menu/bindList',
		method: 'get',
		params: {
			menuCategory: 2
		}
	})

// 小程序绑定菜单
export const miniAppBindMenu = (data) =>
	request({
		url: '/wxminiapp/bind',
		method: 'put',
		data: data
	})