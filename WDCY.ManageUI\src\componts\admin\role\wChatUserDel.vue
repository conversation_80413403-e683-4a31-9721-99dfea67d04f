<template>
    <el-dialog draggable
		top="3vh"
		width="70%"
		v-loading="loading"
		v-model="dialog.show"
		:title="dialog.title"
	>
	<el-row :gutter="20">
		<el-col :span="16" style="display:flex">
			<el-input style="margin-right:10px" v-model="searchModel.name" placeholder="姓名" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.phone" placeholder="手机号" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.idCard" placeholder="身份证" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.address" placeholder="证件类型" clearable />
			<!-- <el-select style="width: 100%;margin-right:10px" v-model="searchModel.roleId" placeholder="选择角色" clearable>
				<el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
			</el-select> -->
			<el-cascader :show-all-levels="false" style="width: 100%;" v-model="searchModel.groupId" :props="{ checkStrictly: true }" :options="userGroupList" @change="handleChange" clearable placeholder="选择组"/>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
        <el-col :span="4">
			<el-button style="float: right;" type="primary" @click="deleted">删除</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table ref="multipleTableRef" stripe row-key="id" @selection-change="handleSelectionChange" :data="personList" border height="500px" style="width: 100%">
                <el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
                <el-table-column prop="photo" width="100" align="center" label="照片">
                    <template #default="scope">
							<el-image preview-teleported fit="contain" style="height: 60px; width:60px" :src="imgServer+scope.row.photo" :preview-src-list="[imgServer+scope.row.photo]">
								<template #error>
									<el-image preview-teleported fit="contain" style="height: 60px; width:60px" :src="errorHeadImg" :preview-src-list="[errorHeadImg]"></el-image>
								</template>
							</el-image>
					</template>
                </el-table-column>
				<el-table-column prop="name" width="90px" align="center" label="姓名" />
				<el-table-column prop="gender" width="54px" align="center" :formatter="formatSex" label="性别" />
				<el-table-column prop="phone" align="center" label="电话" />
				<el-table-column prop="idCard" align="center" label="证件号" />
				<el-table-column prop="certificateType" width="150" :formatter="formatCertificateType" align="center" label="证件类型" />
                <el-table-column prop="idType" width="70" align="center" label="公众号" >
					<template #default="scope">
						{{ scope.row.mpOpenId ?"是":"否" }}
					</template>
				</el-table-column>
				<el-table-column prop="createTime" width="165" align="center" label="创建时间" />
				<el-table-column prop="collectionTime" width="165" align="center" label="所属小区" >
					<template #default="scope">
						<div @mouseleave="communityShow = true">
							<div v-for="item in scope.row.communityNameList" :key="item">{{item}}</div>
						</div>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</el-dialog>
</template>
<script>
import { weChatList, getWeChat, wChatUserRemove } from "@/api/weChat/weChatUser"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { listRole } from "@/api/admin/role"
import { listUserGroup } from "@/api/admin/userGroup"
import { getDictCss, formatDict } from "@/utils/dict"
import weChatUserView from "@/componts/weChat/weChatUserView.vue"
import wxBindMenu from "@/componts/weChat/wxBindMenu.vue"
import errImg from "../../../assets/img/defaultHead.png"
export default {
    components:{ weChatUserView, wxBindMenu },
	props: ['statusList','userGroupList'],
	data() {
		return {
			searchModel: {},
			personList: [],
			ids: [],
			imgServer: import.meta.env.VITE_BASE_API,
			communityId:localStorage.getItem("communityId"),
			statusList:[],
            dialog: {
				show:false,
				title:''
			},
			sexList: [],
			roleList: [],
			certificateTypeTagList: [],
			total:0,
			pageSize: 10,
			errorHeadImg: errImg,
            roleId: null,
			communityShow: true
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		handleChange(e){
			if(e == null){
			this.searchModel.groupId = null
			return
			}
			this.searchModel.groupId = e[e.length-1]
		},
		handleSelectionChange(val) {
            console.log(val);
			let list = []
			for (let item of val) {
				list.push(item.id)
			}
			this.ids = list
		},
		formatCertificateType(row, column, cellValue, index){
			return formatDict(this.certificateTypeTagList, cellValue)
		},
		formatSex(row, column, cellValue, index) {
			return formatDict(this.sexList, cellValue)
		},
		search() {
			// this.searchModel.communityId=this.communityId
            this.searchModel.roleId = this.roleId
			weChatList(this.searchModel)
			.then(res => {
				this.personList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		deleted(id){
			this.$confirm('删除信息, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					wChatUserRemove({userIds:this.ids,roleId:this.roleId})
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			try{
				let role_res = await listRole({status:0})
				this.roleList = role_res.data.result

				let certificateType_res = await listDictByNameEn('certificate_type')
				this.certificateTypeTagList = certificateType_res.data.result

				let sex_res = await listDictByNameEn('sex')
				this.sexList = sex_res.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	},
    mounted(){
        this.$nextTick(function(){
            mitt.on("openwChatUserDelList",(data)=>{
                if(this.$refs.multipleTableRef) this.$refs.multipleTableRef.clearSelection()
                this.roleId = data.id
				this.pageSize = 10
				this.personList = data.result.list
				this.total = data.result.total
                this.dialog.show = true
                this.dialog.title = "微信用户"
            })

        })
    }
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
