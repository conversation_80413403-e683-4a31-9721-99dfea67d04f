<template>
	<log-detail :typeList="typeList"></log-detail>
	<el-row :gutter="20" style="display:flex">
		<!-- <el-button type="primary" v-if="routeQuery" @click="back" style="margin:0 0 10px 10px">返回</el-button> -->
		<div style="display:flex">
			<el-col :span="3">
				<el-select clearable style="width: 100%;" v-model="searchModel.type" placeholder="日志类型">
					<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
				</el-select>
			</el-col>
			<el-col :span="3">
				<el-input v-model="searchModel.typeName" placeholder="操作类别" clearable></el-input>
			</el-col>
			<el-col :span="3">
				<el-input v-model="searchModel.body" placeholder="关键字" clearable></el-input>
			</el-col>
			<el-col :span="3">
				<el-input v-model="searchModel.userName" placeholder="操作人" clearable></el-input>
			</el-col>
			<el-col :span="3">
				<el-select clearable style="width: 100%;" v-model="searchModel.userType" placeholder="用户类型">
					<el-option v-for="item in userTypeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
				</el-select>
			</el-col>
			<el-col :span="9">
				<el-date-picker
					v-model="searchModel.startTime"
					type="date"
					placeholder="选择开始日期"
					value-format="YYYY-MM-DD HH:mm:ss"
					:size="size"
					style="margin-right:10px"
				/>
				<el-date-picker
					style="margin-right:10px"
					v-model="searchModel.endTime"
					type="date"
					placeholder="选择结束日期"
					value-format="YYYY-MM-DD HH:mm:ss"
					:size="size"
				/>
			</el-col>
			<el-col :span="2">
				<el-button type="primary" @click="search">搜 索</el-button>
			</el-col>
		</div>
	</el-row>
	<el-row :gutter="20">
		<el-col>
			<el-table stripe :data="logList" border style="width: 100%">
				<el-table-column prop="type" align="center" :formatter="formatType" label="日志类型" width="180" />
				<el-table-column prop="typeName" align="center" label="操作类别" />
				<!-- <el-table-column prop="request" align="center" label="请求内容" show-overflow-tooltip /> -->
				<el-table-column prop="resopnse" align="center"  label="结果" width="88">
					<template #default="scope">
						<el-tag size="default" :type="tagType(scope.row)">{{tagName(scope.row)}}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" align="center" label="请求时间" width="168" />
				<el-table-column prop="ipAddress" align="center" label="来源IP" width="125" />
				<el-table-column prop="userName" align="center" label="操作人" width="100">
					<template #default="scope">
						{{ scope.row.userName || scope.row.userId }}
					</template>
				</el-table-column>
				<el-table-column prop="userType" align="center" label="用户类型" width="125" />
				<el-table-column align="center" width="200" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="toDetail(scope.row.id)" v-if="hasPerm('sys:log:detail')">查看</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
	
</template>
<script>
import { listLog,deleteLog,getLog } from "@/api/admin/log"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import logDetail from "@/componts/admin/logDetail.vue"
export default {
	components:{ logDetail },
	data() {
		return {
			searchModel: {},
			logList: [],
			typeList:[],
			userTypeList:[],
			total:0,
			pageSize:10,
			routeQuery: ""
		}
	},
	methods: {
		// 格式化
		tagType(row){
			let res = JSON.parse(row.response)
			if(res.code == 0){
				return 'success'
			}else{
				return 'danger'
			}
		},
		tagName(row){
			let res = JSON.parse(row.response)
			if(res.code == 0){
				return '成功'
			}else{
				return '失败'
			}
		},
		search() {
			listLog(this.searchModel)
			.then(res => {
				this.logList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		formatType(row, column, cellValue, index){
			let result = ''
			for(let item of this.typeList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},

		back(){
			this.$router.back()
		},
		//查看
		toDetail(id){
			getLog(id)
			.then(res =>{
				mitt.emit('openLogDetail',res.data.result)
			})
		},
		deleted(id){
			this.$confirm('删除日志, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteLog(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		// 分页
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			this.searchModel.body =this.$route.query.body
			this.routeQuery =this.$route.query.body
			this.search()
			mitt.off('openLogDetail')
			try{
				let res = await listLog(this.searchModel)
				this.logList = res.data.result.list
				this.total = res.data.result.total
				
				let log_res = await listDictByNameEn("log_type")
				this.typeList = log_res.data.result
				let user_type = await listDictByNameEn("user_type")
				this.userTypeList = user_type.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
