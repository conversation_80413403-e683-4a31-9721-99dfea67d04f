import request from '@/utils/request'

export const listCommunity = (data) =>
	request({
		url: '/community',
		method: 'get',
		params: data
	})

export const getAnchorPoint = (data) =>
	request({
		url: '/community/anchorPoint',
		method: 'get',
		params: data
	})

export const eventSubscription = (data) =>
	request({
		url: '/community/eventSubscription/detail',
		method: 'get',
		params: data
	})

export const getCommunity = (id) =>
	request({
		url: '/community/' + id,
		method: 'get'
	})

//巡游路线
export const queryByRoutePoints = (id) =>
	request({
		url: '/community/queryByRoutePoints',
		method: 'get',
		params: {
			communityId: id
		}
	})

export const addCommunity = (data) =>
	request({
		url: '/community',
		method: 'post',
		data: data
	})

export const accountCommunity = () =>
	request({
		url: '/community/accountCommunity',
		method: 'get'
	})

export const editCommunity = (data) =>
	request({
		url: '/community',
		method: 'put',
		data: data
	})

//巡游路线保存，修改
export const updateByRoutePoints = (data) =>
	request({
		url: '/community/updateByRoutePoints',
		method: 'put',
		data: data
	})
	
// 编辑地理围栏
export const updateByPolyCoords = (data) =>
	request({
		url: '/community/updateByPolyCoords',
		method: 'put',
		data: data
	})

export const editEnabled3dCommunity = (data) =>
	request({
		url: '/community/enabled3d',
		method: 'put',
		data: data
	})

export const deleteCommunity = (id) =>
	request({
		url: '/community',
		method: 'delete',
		params: {
			id: id
		}
	})

export const presetCommunityData = (id) =>
	request({
		url: '/community/presetData',
		method: 'post',
		params: {
			id: id
		}
	})

//传communityId  获取樓宇列表
export const getBuildingList = (params) =>
	request({
		url: '/building',
		method: 'get',
		params: params
	})
