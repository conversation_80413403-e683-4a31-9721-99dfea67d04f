<template>
  <el-dialog :close-on-click-modal="false" draggable style="width:70%; height:685px" :append-to-body="false"
    destroy-on-close v-loading="loading" v-model="dialog.show" :title="dialog.title" @close="close">
    <div>
      <div class="div-bottom">
        <div v-if="tilesetLayer != null" class="bottom-btn" @click="showTilesetLayer">
          显示/隐藏模型
        </div>
        <div class="bottom-btn" @click="openBaseMap(0)">天地图影像</div>
        <div class="bottom-btn" @click="openBaseMap(1)">高德基础</div>
        <div class="bottom-btn" @click="openBaseMap(2)">高德影像</div>
      </div>
      <div id="mars3dContainer" class="mars3d-container"></div>

      <div style="
            height: 580px;
            width: 320px;
            overflow: hidden;
            position: absolute;
            top: 66px;
            left: 16px;
          ">
        <div class="alert-marsk">
          <el-form :rules="rules" ref="form" :model="polyCoord" label-width="auto">
            <el-form-item label="区域名称" prop="title">
              <el-input v-model="polyCoord.title" placeholder="区域名称" />
            </el-form-item>
            <el-form-item label="区域高度" prop="areaHeight">
              <el-input v-model="polyCoord.areaHeight" placeholder="区域高度" />
              <span style="position: absolute; right: 10px">米</span>
            </el-form-item>
            <el-form-item label="边框类型" prop="outlineType">
              <el-select v-model="polyCoord.outlineType" placeholder="边框类型">
                <el-option label="普通线" value="0" />
                <el-option label="动态线" value="1" />
              </el-select>
            </el-form-item>
            <el-form-item label="边框宽度" prop="outlineWidth">
              <el-input v-model="polyCoord.outlineWidth" placeholder="边框宽度" />
              <span style="position: absolute; right: 10px">米</span>
            </el-form-item>
            <el-form-item label="边框颜色" prop="outlineColor">
              <!-- <el-input v-model="polyCoord.outlineColor" placeholder="边框颜色" /> -->
              <el-color-picker v-model="polyCoord.outlineColor" show-alpha />
            </el-form-item>
            <el-form-item label="填充颜色" prop="fillColor">
              <!-- <el-input v-model="polyCoord.fillColor" placeholder="填充颜色" /> -->
              <el-color-picker v-model="polyCoord.fillColor" show-alpha />
            </el-form-item>
            <el-form-item label="字体颜色" prop="textColor">
              <!-- <el-input v-model="polyCoord.textColor" placeholder="字体颜色" /> -->
              <el-color-picker v-model="polyCoord.textColor" show-alpha />
            </el-form-item>
            <el-form-item label="透明系数" prop="opacity">
              <!-- <el-input v-model="polyCoord.opacity" placeholder="透明系数" /> -->
              <el-slider v-model="polyCoord.opacity" max="1" step="0.1" />
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="startDrawGraphic">绘制围栏</el-button>
        </div>
      </div>

    </div>

    <!-- <div style="height: 500px; overflow: hidden;">
      <div class="search-container" v-if="isEditMode && !polyCoordsEdit">
        <el-input class="input-box" v-model="lnglat[0]" placeholder="经度:120.xxxx" />
        <el-input class="input-box" v-model="lnglat[1]" placeholder="纬度:31.xxxx" />
        <el-input class="input-box" v-model="lnglat[2]" placeholder="海拔" />
        <el-button type="primary" style="background: white; width: 50px; height: 25px; color: black"
          @click="flyToPoint">定 位
        </el-button>
      </div>

      <div class="search-container" v-if="polyCoordsEdit">
        <el-button type="primary" style="background: white; width: 100px; height: 25px; color: black"
          @click="startDrawGraphic">绘制围栏
        </el-button>
      </div>
    </div> -->

    <el-row v-show="isEditMode" justify="center" style="position: absolute; bottom: 42px; left: 16px">
      <el-button type="primary" style="width: 100px; height: 30px; z-index: 101" @click="onSubmit">
        确 定
      </el-button>
    </el-row>

    <el-row v-show="!contextMenuStatus" style="position: absolute; right: 28px;">
      <div style="color: chartreuse;">
        <div>经度:{{ lnglat[0] }}</div>
        <div>纬度:{{ lnglat[1] }}</div>
        <div>海拔:{{ lnglat[2] }}</div>
      </div>
    </el-row>

  </el-dialog>
</template>

<script>
import mitt from "@/utils/mitt";
//import { onUnmounted } from "vue";
//引入cesium基础库
// import * as Cesium from "mars3d-cesium";
//导入mars3d主库
// import * as mars3d from "mars3d";
//导入mars3d插件（按需使用，需要先npm install）
// import "mars3d-space";

export default {
  emits: ["point"],
  data() {
    return {
      loading: false,
      isEditMode: true, //是否编辑模式
      dialog: {
        show: false,
        title: "地理围栏编辑",
      },
      enabled3d: false,
      showBaseMap: false,
      lnglat: [],
      position: {},
      center: {},
      rotationSet: { x: 0, y: 0, z: 0 },
      scaleSet: 1,
      modeUrl: "",
      editNameStatus: true,
      scrollWall: null,
      polyCoordsEdit: false,
      id: 0, //业务主键ID
      polyCoord: {
        no: 0, //唯一编号
        title: "默认区域",
        areaHeight: 0,
        outlineType: 1,
        outlineWidth: 3,
        outlineColor: "yellow",
        fillColor: "blue",
        textColor: "green",
        opacity: 0.8,
        coords: [] //坐标集
      }, //单个区域的对象
      polyCoords: [], //包含多个区域的对象
      contextMenuStatus: false, //显示右键菜单
      graphicLayer: null,
      tilesetLayer: null,
    };
  },
  mounted() {
    // 定义：在下次 DOM 更新循环结束之后执行延迟回调。在修改数据之后立即使用这个方法，获取更新后的 DOM。
    // Vue是异步执行dom更新的，一旦观察到数据变化，Vue就会开启一个队列，然后把在同一个事件循环 (event loop) 当中观察到数据变化的 watcher 推送进这个队列。
    // 如果这个watcher被触发多次，只会被推送到队列一次。这种缓冲行为可以有效的去掉重复数据造成的不必要的计算和DOm操作。
    // 而在下一个事件循环时，Vue会清空队列，并进行必要的DOM更新。当你设置 vm.someData = 'new value'，DOM 并不会马上更新，而是在异步队列被清除，
    // 也就是下一个事件循环开始时执行更新时才会进行必要的DOM更新。如果此时你想要根据更新的 DOM 状态去做某些事情，就会出现问题。
    // 为了在数据变化之后等待 Vue 完成更新 DOM ，可以在数据变化之后立即使用 Vue.nextTick(callback) 。这样回调函数在 DOM 更新完成后就会调用。
    //this.$nextTick(function () {});
    // this.$nextTick(function () {
    // });
    /**
     * center:中心点
     * isEditMode:是否可提交
     * contextMenuStatus:显示右键菜单
     */
    mitt.on("editGeofencingMap", (e) => {
      if (this.dialog.show) {
        return;
      }
      this.polyCoord = {
        title: e.title,
        areaHeight: 30,
        outlineType: "0",
        outlineWidth: 3,
        outlineColor: "rgba(255, 0, 0, 1)",
        fillColor: "rgba(0, 255, 0, 0.8)",
        textColor: "rgba(0, 0, 255, 1)",
        opacity: 0.6,
        coords: [] //坐标集
      };

      this.dialog.show = true;
      this.isEditMode = e.edit;
      this.enabled3d = e.enabled3d;
      this.rotationSet = e.rotationSet;
      this.scaleSet = e.scaleSet;
      this.showBaseMap = e.showBaseMap;
      if (e.title) this.dialog.title = e.title + "地理围栏编辑";

      this.lnglat = e.point;
      if (e.position) {
        this.position = e.position;
      }
      this.center = e.center;
      this.modeUrl = e.modeUrl != undefined ? e.modeUrl : "";
      this.polyCoordsEdit = e.polyCoordsEdit != undefined ? e.polyCoordsEdit : false;
      this.id = e.id;

      try {
        this.polyCoords = e.polyCoords;
      } catch (error) { }

      this.contextMenuStatus =
        e.contextMenuStatus != undefined ? e.contextMenuStatus : false;

      console.info("initMap");
      // this.initMap();
      setTimeout(() => {
        console.info("initMap");
        this.initMap();
      }, 200);
    });
  },
  onUnmounted() {
    this.close()
  },
  methods: {
    showTilesetLayer() {
      console.log("this.tilesetLayer.show", this.tilesetLayer.show);

      if (this.tilesetLayer) {
        this.tilesetLayer.show = !this.tilesetLayer.show;
      }
    },
    openBaseMap(index) {
      const layersss = window.mapSource.getLayers({
        layers: false, // 不包含layers中配置的所有图层
      });
      layersss[0].show = false;
      layersss[1].show = false;
      layersss[2].show = false;
      layersss[index].show = true;
    },
    // 绑定右键菜单
    bindLayerContextMenu() {
      this.graphicLayer.bindContextMenu([
        // {
        //   text: "开始编辑对象",
        //   icon: "fa fa-edit",
        //   show: function (e) {
        //     const graphic = e.graphic
        //     if (!graphic || !graphic.hasEdit) {
        //       return false
        //     }
        //     return !graphic.isEditing
        //   },
        //   callback: (e) => {
        //     const graphic = e.graphic
        //     if (!graphic) {
        //       return false
        //     }
        //     if (graphic) {
        //       //this.graphicLayer.startEditing(graphic)
        //       graphic.startEditing();
        //     }
        //   }
        // },
        // {
        //   text: "停止编辑对象",
        //   icon: "fa fa-edit",
        //   show: function (e) {
        //     const graphic = e.graphic
        //     if (!graphic || !graphic.hasEdit) {
        //       return false
        //     }
        //     return graphic.isEditing
        //   },
        //   callback: (e) => {
        //     const graphic = e.graphic
        //     if (!graphic) {
        //       return false
        //     }
        //     if (graphic) {
        //       graphic.stopEditing()
        //     }
        //   }
        // },
        // {
        //   text: "还原编辑(还原到初始)",
        //   icon: "fa fa-pencil",
        //   show: (event) => {
        //     function hasRestore(graphic) {
        //       if (!graphic || !graphic.hasEdit || !graphic.isEditing) {
        //         return false
        //       }
        //       return graphic.editing?.hasRestore()
        //     }

        //     const graphic = event.graphic
        //     if (hasRestore(graphic)) {
        //       return true
        //     }
        //     if (graphic.isPrivate && graphic.parent) {
        //       return hasRestore(graphic.parent) // 右击是编辑点时
        //     }
        //     return false
        //   },
        //   callback: (event) => {
        //     const graphic = event.graphic
        //     if (graphic.editing?.restore) {
        //       graphic.editing.restore() // 撤销编辑，可直接调用
        //     } else if (graphic.parent?.editing?.restore) {
        //       graphic.parent.editing.restore() // 右击是编辑点时
        //     }
        //   }
        // },
        // {
        //   text: "撤销编辑(还原到上一步)",
        //   icon: "fa fa-pencil",
        //   show: (event) => {
        //     function hasRevoke(graphic) {
        //       if (!graphic || !graphic.hasEdit || !graphic.isEditing) {
        //         return false
        //       }
        //       return graphic.editing?.hasRevoke()
        //     }

        //     const graphic = event.graphic
        //     if (hasRevoke(graphic)) {
        //       return true
        //     }
        //     if (graphic.isPrivate && graphic.parent) {
        //       return hasRevoke(graphic.parent) // 右击是编辑点时
        //     }
        //     return false
        //   },
        //   callback: (event) => {
        //     const graphic = event.graphic
        //     if (graphic.editing?.revoke) {
        //       graphic.editing.revoke() // 撤销编辑，可直接调用
        //     } else if (graphic.parent?.editing?.revoke) {
        //       graphic.parent.editing.revoke() // 右击是编辑点时
        //     }
        //   }
        // },
        {
          text: "删除对象",
          icon: "fa fa-trash-o",
          show: (event) => {
            const graphic = event.graphic
            if (!graphic || graphic.isDestroy || graphic.isPrivate || graphic.graphicIds) {
              return false
            } else {
              return true
            }
          },
          callback: (e) => {
            const graphic = e.graphic
            if (!graphic) {
              return
            }
            const parent = graphic.parent // 右击是编辑点时
            console.info("右键删除多边形", graphic, graphic.attr);
            if (graphic.attr) {
              this.polyCoords = this.polyCoords.filter(item => item.title != graphic.attr["title"]);
              this.graphicLayer.removeGraphic(graphic)
              if (parent) {
                this.graphicLayer.removeGraphic(parent)
              }
            } else {
              this.$message.warning("区域标题为空，无法删除");
            }
          }
        },
        {
          text: "查看聚合列表",
          icon: "fa fa-info",
          show: (event) => {
            const graphic = event.graphic
            if (graphic.graphicIds) {
              return true
            } else {
              return false
            }
          },
          callback: (e) => {
            const graphic = e.graphic
            if (!graphic) {
              return
            }
            const graphics = graphic.getGraphics() // 对应的grpahic数组，可以自定义显示
            if (graphics) {
              const names = []
              for (let index = 0; index < graphics.length; index++) {
                const g = graphics[index]
                names.push(g.attr.name || g.attr.text || g.id)
              }
              return this.$message(`${names.join(",")}`, `共${graphics.length}个聚合对象`)
            }
          }
        },
        {
          text: "计算周长",
          icon: "fa fa-medium",
          show: (event) => {
            const graphic = event.graphic
            return !graphic.isPoint
          },
          callback: (e) => {
            const graphic = e.graphic
            const strDis = mars3d.MeasureUtil.formatDistance(graphic.distance)
            this.$message.warning("该对象的周长为:" + strDis)
          }
        },
        {
          text: "计算面积",
          icon: "fa fa-reorder",
          show: (event) => {
            const graphic = event.graphic
            return !graphic.isPoint
          },
          callback: (e) => {
            const graphic = e.graphic
            const strArea = mars3d.MeasureUtil.formatArea(graphic.area)
            this.$message.warning("该对象的面积为:" + strArea)
          }
        }
      ])
    },
    flyToPoint() {
      if (this.lnglat[0] && this.lnglat[1]) {
        var position = {
          lng: this.lnglat[0],
          lat: this.lnglat[1],
          alt: this.lnglat[2],
        };

        var options = {
          radius: 500,
          heading: 0,
          pitch: -90,
        };

        window.mapSource.flyToPoint(position, options);

        this.graphicLayer.clear();
        const graphic = new mars3d.graphic.BillboardEntity({
          position: position,
          style: {
            image: "/image/point.png",
            width: 30,
            height: 30,
            scaleByDistance: true,
            scale: 1,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
        });
        this.graphicLayer.addGraphic(graphic);
      }
    },
    async startDrawGraphic() {
      if (!this.polyCoord) {
        this.$message("绘图参数未配置");
        return;
      }

      if (this.polyCoord && this.polyCoord.title && !this.polyCoord.textColor) {
        this.$message.warning("绘图参数文字配置不正确");
        return;
      }

      if (this.polyCoord
        && !this.polyCoord.outlineWidth
        && !this.polyCoord.fillColor
      ) {
        this.$message.warning("绘图参数中外框与填充至少保留一个");
        return;
      }

      if (this.polyCoords.some(item => item.title === this.polyCoord.title)) {
        this.$message.warning("区域名称重复");
        return;
      }

      this.polyCoord.coords = [];

      let polyCoordsGraphic = await this.graphicLayer.startDraw({
        type: this.polyCoord.outlineType == "0" ? "polygonP" : "scrollWall",
        style: {
          color: this.polyCoord.outlineType == "0" ? this.polyCoord.fillColor : this.polyCoord.outlineColor,
          opacity: this.polyCoord.opacity,
          outline: true,
          outlineWidth: this.polyCoord.outlineWidth,
          outlineColor: this.polyCoord.outlineColor,
          diffHeight: this.polyCoord.areaHeight,
          label: {
            text: this.polyCoord.title,
            font_size: 18,
            color: this.polyCoord.textColor,
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0
          }
        },
        attr: { title: this.polyCoord.title }
      });
      // polyCoordsGraphic.positions = mars3d.PointUtil.setPositionsHeight(polyCoordsGraphic.positionsShow, 2000)
      polyCoordsGraphic.points.forEach((item) => {
        let coord = [item._lng, item._lat, item._alt];
        this.polyCoord.coords.push(coord);
      });
      //this.polyCoord.no = polyCoordsGraphic.id;
      console.info("polyCoord", this.polyCoord);
      console.info("polyCoords", this.polyCoords);
      console.info("polyCoordsGraphic", polyCoordsGraphic);
      this.polyCoords.push(JSON.parse(JSON.stringify(this.polyCoord)));
    },
    initMap() {
      var that = this;
      var mapOptions = {
        scene: {
          center: that.center,
          shadows: false, // 是否启用日照阴影
          removeDblClick: true, // 是否移除Cesium默认的双击事件
          // 以下是Cesium.Viewer所支持的options【控件相关的写在另外的control属性中】
          sceneMode: 3, // 3等价于Cesium.SceneMode.SCENE3D,
          // 以下是Cesium.Scene对象相关参数
          showSun: true, // 是否显示太阳
          showMoon: true, // 是否显示月亮
          showSkyBox: true, // 是否显示天空盒
          showSkyAtmosphere: true, // 是否显示地球大气层外光圈
          fog: true, // 是否启用雾化效果
          fxaa: true, // 是否启用抗锯齿
          // 以下是Cesium.Globe对象相关参数
          globe: {
            // depthTestAgainstTerrain: false, // 是否启用深度监测
            baseColor: "#033447", // 地球默认背景色
            // showGroundAtmosphere: true, // 是否在地球上绘制的地面大气
            // enableLighting: false, // 是否显示昼夜区域
          },
          // 以下是Cesium.ScreenSpaceCameraController对象相关参数
          cameraController: {
            zoomFactor: 3.0, // 鼠标滚轮放大的步长参数
            minimumZoomDistance: 1, // 地球放大的最小值（以米为单位）
            maximumZoomDistance: 1115000, // 地球缩小的最大值（以米为单位）
            enableRotate: true, // 2D和3D视图下，是否允许用户旋转相机
            enableTranslate: true, // 2D和哥伦布视图下，是否允许用户平移地图
            enableTilt: true, // 3D和哥伦布视图下，是否允许用户倾斜相机
            enableZoom: true, // 是否允许 用户放大和缩小视图
            enableCollisionDetection: true, // 是否允许 地形相机的碰撞检测
          },
        },
        control: {
          // geocoder: true,
          defaultContextMenu: false, // 右击菜单
          contextmenu: {
            hasDefault: that.contextMenuStatus,
          }, // 右键菜单
          // homeButton: true, // 视角复位按钮
          // navigationHelpButton: true, //帮助按钮
          // fullscreenButton: true, //全屏按钮
          // baseLayerPicker: true, //basemaps底图切换按钮
          // sceneModePicker: true //二三维切换按钮
        },
        basemaps: [
          {
            name: "天地图卫星",
            icon: "img/basemaps/tdt_img.png",
            type: "tdt",
            layer: "img_d",
            show: false,
          },
          {
            name: "高德电子",
            icon: "/img/basemaps/blackMarble.png",
            type: "gaode",
            id: 2017,
            filterColor: "#003A4A",
            layer: "vec",
            show: false,
            brightness: 0.6,
            chinaCRS: "GCJ02",
            contrast: 1.8,
            gamma: 0.3,
            hue: 1,
            invertColor: false,
            name: "暗色底图",
            pid: 10,
            saturation: 0,
          },
          {
            name: "高德影像",
            icon: "img/basemaps/gaode_img.png",
            type: "group",
            layers: [
              { name: "底图", type: "gaode", layer: "img_d" },
              { name: "注记", type: "gaode", layer: "img_z" },
            ],
            show: true,
          }
        ]
      };

      var map = new mars3d.Map("mars3dContainer", mapOptions); //支持的参数请看API文档：http://mars3d.cn/api/Map.html
      window.mapSource = map;
      //呈现模型层
      if (that.modeUrl && that.enabled3d) {
        var tiles3dLayer = new mars3d.layer.TilesetLayer({
          url: that.modeUrl,
          maximumScreenSpaceError: 16,
          maximumMemoryUsage: 1024,
          dynamicScreenSpaceError: true,
          cullWithChildrenBounds: false,
          skipLevelOfDetail: true,
          preferLeaves: true,
          flyTo: true,
          position: that.position,
          show: true,
          rotation: that.rotationSet,
          scale: that.scaleSet,
        });

        map.addLayer(tiles3dLayer);
        this.tilesetLayer = tiles3dLayer;
      }

      this.graphicLayer = new mars3d.layer.GraphicLayer();
      map.addLayer(this.graphicLayer);

      // 绑定右键菜单
      this.bindLayerContextMenu();

      //绘制中心点
      if (this.lnglat.length == 3 && this.lnglat[0]) {
        const graphic = new mars3d.graphic.BillboardEntity({
          position: that.lnglat,
          style: {
            image: "/image/point.png",
            width: 30,
            height: 30,
            scaleByDistance: true,
            scale: 1,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          },
        });
        this.graphicLayer.addGraphic(graphic);
      }
      //绘制多边形
      if (this.polyCoords && this.polyCoords.length > 0) {
        for (var polyCoord of this.polyCoords) {
          if (polyCoord.outlineType == "1") {
            var scrollWall = new mars3d.graphic.ScrollWall({
              positions: polyCoord.coords,
              style: {
                diffHeight: polyCoord.areaHeight, // 高度
                color: polyCoord.fillColor,
                style: 2, // 效果2，默认是1
                speed: 2,
              },
              attr: { title: polyCoord.title }
            });
            this.graphicLayer.addGraphic(scrollWall);
          } else {
            const graphic = new mars3d.graphic.PolygonEntity({
              positions: polyCoord.coords,
              style: {
                color: polyCoord.fillColor,
                opacity: polyCoord.opacity,
                diffHeight: polyCoord.areaHeight, // 立方体高度
                //height: 100, //离地高度
                // closeTop: false,
                // closeBottom: false,
                // clampToGround: true,
                // classificationType: Cesium.ClassificationType.BOTH,
                label: {
                  text: polyCoord.title,
                  font_size: 18,
                  color: polyCoord.textColor,
                  distanceDisplayCondition: true,
                  distanceDisplayCondition_far: 500000,
                  distanceDisplayCondition_near: 0
                },
                // 高亮时的样式（默认为鼠标移入，也可以指定type:'click'单击高亮），构造后也可以openHighlight、closeHighlight方法来手动调用
                highlight: {
                  // type: mars3d.EventType.click,
                  opacity: 0.8
                }
              },
              attr: { title: polyCoord.title }
            })
            this.graphicLayer.addGraphic(graphic);
          }
        }
      }
    },
    close() {
      console.log("销毁");
      if (this.tilesetLayer) {
        this.tilesetLayer = null;
      }
      if (this.graphicLayer) {
        this.graphicLayer.remove();
        this.graphicLayer = null;
      }
      if (window.mapSource) {
        window.mapSource.destroy();
      }
    },
    onSubmit() {
      // if (this.lnglat.length == 0) {
      //   this.$message("未选择点位");
      // }
      //this.$emit("point", this.lnglat);
      console.log("Submit polyCoords", this.polyCoords);
      let formData = {
        "id": this.id,
        "polyCoords": JSON.stringify(this.polyCoords)
      }
      mitt.emit("updateGeoPolyCoords", formData);
      this.dialog.show = false;
    }
  }



};
</script>

<style scoped lang="less">
.mars3d-container {
  position: absolute;
}

#mars3dContainer {
  height: 585px;
  width: 97.5%;
}

/deep/ div .el-menu {
  border: none;
}

::-webkit-scrollbar {
  display: none;
}

/deep/ .is-opened>div {
  color: #568efe !important;
  background-color: rgba(221, 221, 221, 0.7);
}

/deep/ .el-menu--inline>li {
  background-color: rgba(238, 238, 238, 0.7);
}

.alert-marsk {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 0px 16px 16px 5px;



  /deep/ label {
    color: #ccc;
  }

  /deep/ .el-input__wrapper {
    height: 25px;
    // background-color: rgba(255, 255, 255, 0.2);
  }

  /deep/ button {
    height: 25px;
    margin-top: 10px;
    width: 70px;
  }

  /deep/ .el-form-item {
    margin-bottom: 0px;
  }
}

.search-container {
  position: absolute;
  display: flex;
  align-items: center;
  top: 15px;
  left: 130px;

  .input-box {
    width: 120px;
    height: 25px;
    margin-right: 10px;
  }
}

.div-bottom {
  position: absolute;
  width: 95%;
  height: 40px;
  background: white;
  z-index: 100;
  bottom: 0px;
  justify-content: flex-end;
  /* 子元素靠右 */
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;

  .bottom-btn {
    width: 80px;
    height: 30px;
    border: 1px solid RGB(75, 150, 255);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 12px;
  }

  .bottom-btn:hover {
    color: RGB(75, 150, 255);
  }
}
</style>
