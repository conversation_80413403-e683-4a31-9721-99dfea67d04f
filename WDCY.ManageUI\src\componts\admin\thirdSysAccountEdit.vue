<template>
	<el-dialog draggable width="25%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="accountModel" label-width="100px">
			<el-row>
				<el-col :span="24">
					<el-form-item label="第三方系统" prop="thirdSystemId">
						<el-select style="width:100%" v-model="accountModel.thirdSystemId" placeholder="第三方系统ID" clearable>
							<el-option v-for="item in thirdSystemList" :key="item.id" :label="item.sysName"
							:value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="用户名" prop="userName">
						<el-input type="input" v-model="accountModel.userName" placeholder="用户名"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="密码" prop="password">
						<el-input v-model="accountModel.password" placeholder="密码"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="归属账号" prop="userId">
						<el-select v-model="accountModel.userId" placeholder="归属账号" clearable>
							<el-option v-for="item in userList" :key="item.id" :label="item.nickName"
							:value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="归属角色" prop="roleId">
						<el-select v-model="accountModel.roleId" placeholder="归属角色" clearable>
							<el-option v-for="item in roleList" :key="item.id" :label="item.roleName"
							:value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="排序" prop="sort">
						<el-input-number v-model="accountModel.sort" :min="1" @change="handleChange" />
						<!-- <el-input v-model="accountModel.sort" placeholder="排序"></el-input> -->
					</el-form-item>
				</el-col>
			</el-row>
			
			<!-- <el-row>
				<el-col :span="7">
					<el-form-item label="图标代号" prop="icoCode">
						<el-input v-model="accountModel.icoCode" placeholder="图标代号"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="7">
					<el-form-item label="状态" prop="status">
						<el-select style="width: 100%;" v-model="accountModel.status" placeholder="状态">
							<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="7">
					<el-form-item label="图标分类" prop="icoUrl">
						<el-select style="width: 100%;" v-model="accountModel.icoCategory" placeholder="图标分类">
							<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="图标url" prop="icoUrl">


						<el-upload :class="[accountModel.icoUrl ? '' : 'upload']" class="avatar-uploader"
						:action="imgServer + accountModel.icoUrl" :show-file-list="false" :http-request="loadingImg">
						<img v-if="accountModel.icoUrl" :src="imgServer + accountModel.icoUrl" @mouseenter="mask=true" @mouseleave="mask=false" class="avatar" />
						<el-icon v-else class="avatar-uploader-icon">
							<Plus />
						</el-icon>
						<div @mouseenter="mask=true" @mouseleave="mask=false" v-if="mask" style="background:rgba(0, 0, 0, .2);width: 178px; height: 178px;position: absolute;"></div>
						</el-upload>


					</el-form-item>
				</el-col>
			</el-row> -->

			
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交
			</el-button>
		</el-row>
		<!-- <vehicle-number></vehicle-number> -->
	</el-dialog>
</template>

<script>
import { addThirdSysAccount, editThirdSysAccount } from "@/api/admin/thirdSystem"
import mitt from "@/utils/mitt";
export default {
	props: ['userList', 'roleList', 'thirdSystemList'],
	data() {
		return {
			loading: false,
			accountModel: {},
			dialog: {},
			startToEndTime: [],
			imgServer: import.meta.env.VITE_BASE_API,
		}
	},
	methods: {
		onSubmit() {
			console.log(this.accountModel.userId,this.accountModel.roleId);
			if (!this.accountModel.userId && !this.accountModel.roleId) {
				this.$message.error('请选择归属角色或归属用户!')
				return
			}
			this.dialog.show = false
			this.$parent.dialog.show = true
			this.$refs['form'].validate((valid) => {
				if (valid) {
					console.log(this.accountModel,valid);
					if (this.accountModel.id == 0) {
						delete this.accountModel.id
						addThirdSysAccount(this.accountModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.$parent.search()
								this.dialog.show = false
							})
					} else {
						editThirdSysAccount(this.accountModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.$parent.search()
								this.dialog.show = false
							})
					}
				}
			})
		}
	},
	mounted() {
		this.$nextTick(function () {

			mitt.on('openThirdSysAccountEdit', (data) => {
				this.accountModel = data
				this.dialog.show = true
				this.dialog.title = "修改账户"
			})
			mitt.on('openThirdSysAccountAdd', (accountId) => {
				console.log(this.$parent.accountList);
				this.accountModel = {
					id: 0,
					thirdSystemId: accountId,
					sort: this.$parent.accountList.length+1
				}
				this.dialog.show = true
				this.dialog.title = "添加账户"
			})
		})
	},
	watch:{
		"dialog.show"(newVal,oldVal){
			if (newVal == false) {
				this.$parent.dialog.show = true
			}
		}
	}
}
</script>
<style scoped>
.avatar-uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}
</style>
