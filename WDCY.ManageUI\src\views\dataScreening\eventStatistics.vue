<template>
	<el-row :gutter="20">
		<el-col :span="2">
			<el-radio-group style="width:220px" v-model="searchModel.type" >
				<el-radio-button  v-for="item in typeList" :key="item.nameEn" :label="item.nameEn"
				> {{ item.nameCn }}</el-radio-button>
			</el-radio-group>
		</el-col>
        <el-col :span="6" style="display:flex">
			<el-date-picker
				v-model="searchModel.startTime"
				type="date"
				placeholder="选择开始日期"
				value-format="YYYY-MM-DD"
				:size="size"
				style="margin-right:10px"
			/>
			<el-date-picker
				style="margin-right:10px"
				v-model="searchModel.endTime"
				type="date"
				placeholder="选择结束日期"
				value-format="YYYY-MM-DD"
				:size="size"
			/>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table stripe :data="parkingList" border height="calc(100vh - 258px)" style="width: 100%">
				<!-- <el-table-column label="图标" align="center" prop="icoUrl" width="75">
                    <template #default="scope">
                        <el-image style="width:45px;height:45px;background: rgba(136, 186, 255,.3);" :src="imgServer+scope.row.icoUrl" fit="contain"></el-image>
                    </template>
                </el-table-column> -->
                <el-table-column type="expand">
                <template #default="props">
                    <el-table :data="props.row.dataFlowPushPoliceCommunityDtoList" border style="margin-left:50px;width: calc(100% - 50px)">
                        <el-table-column type="expand">
                        <template #default="props">
                            <el-table :data="props.row.dataFlowPushPoliceDtoList" border style="margin-left:50px;width: calc(100% - 50px)" :row-class-name="rowStyle">
                                <!-- <el-table-column label="小区" prop="communityId" >
                                    <template #default="scope">
                                        {{ formatCommunity(scope.row.communityId) }}
                                    </template>
                                </el-table-column> -->
                                <el-table-column label="设施名称" prop="devName" />
                                <!-- <el-table-column label="时间" prop="date" /> -->
                                <el-table-column label="设施ID" prop="devId" />
                                <el-table-column label="设施事件总数" prop="eventCount" />
                                <el-table-column label="推送公安总数" prop="eventPushCount" />
                                <el-table-column label="事件类型" prop="eventType">
                                    <template #default="scope">
                                        {{ scope.row.eventType==0?'人流':'车流' }}

                                    </template>
                                </el-table-column>
                                <el-table-column label="推送时间" prop="pushDate" >
									<template #default="scope">
										{{scope.row.pushDate.replace("00:00:00","")}} {{scope.row.pushHour<10?"0"+scope.row.pushHour:scope.row.pushHour}}:00
									</template>
								</el-table-column>
                            </el-table>
                        </template>
                        </el-table-column>
                        <el-table-column label="小区" prop="communityId" >
                            <template #default="scope">
                                {{ formatCommunity(scope.row.communityId) }}
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
                </el-table-column>
				<el-table-column prop="time" align="left" label="统计时间"/>
			</el-table>
		</el-col>
	</el-row>
</template>

<script>
import { ElLoading } from 'element-plus'
import { accountCommunity } from "@/api/base/community";
import { getEventStatisticsList } from "@/api/dataScreening/eventStatistics"
import mapSelectPoint from "@/componts/map/mapSelectPoint.vue";
import { listDictByNameEn } from "@/api/admin/dict"
import { getDictCss, formatDict } from "@/utils/dict"

export default {
	components:{   },
	data() {
		return {
			searchModel: { },
			parkingList: [],
			communityList: [],
			typeList:[
                {nameEn:0,nameCn:' 年 '},
                {nameEn:1,nameCn:' 月 '},
                {nameEn:2,nameCn:' 日 '}
            ],
			total:0,
			imgServer: import.meta.env.VITE_BASE_API,
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
        formatCommunity(id) {
            let name = ''
            for (let i = 0; i < this.communityList.length; i++) {
                // console.log(this.communityList[i]);
                if (this.communityList[i].id == id) {
                    name = this.communityList[i].communityName
                }
            }
			return name
		},
		// 列表样式
        rowStyle({row}){
            console.log(row);
            if (row.eventCount == row.eventPushCount) {
                return 'normal-class'
            } else {
                return 'isRed'
            }
        },
		search() {
			getEventStatisticsList(this.searchModel)
			.then(res => {
				this.parkingList = res.data.result
			})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			try{
                await accountCommunity().then(res => {
                    this.communityList = res.data.result
                })
                this.searchModel.type = 1
				let res = await getEventStatisticsList(this.searchModel)
                this.parkingList = res.data.result
                // let time = []
                // let communityList = []
                // for (const item in res.data.result) {
                //     time.push(item)
                //     for (const secondItem in res.data.result[item]) {
                //         communityList.push(secondItem)
                //     }
                // }
                // time.map((item) => {
                //     let data = {};
                //     data.time = item
                //     // data.list = res.data.result[item]
                //     data.list = [{'test':1,'test1':11}]
                //     this.parkingList.push(data);
                // })
                

                console.log(this.parkingList);
				// let parking_type = await listDictByNameEn('parking_type')
				// this.parkingList = parking_type.data.result
			}catch(err){
                console.log(err);
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped lang="less">
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
/deep/ .diaLogClass{
	height: 780px;
	width: 1124px;
	background-color: rgb(241, 245, 255);
	overflow-x: auto;
	position: relative;
	border-radius: 16px;
}
/deep/ .el-dialog__body{
	padding: 0 34px;
}
	/* 隐藏滚动条 */
::-webkit-scrollbar{
display: none;
}
/deep/ .isRed{
    background-color: rgb(252, 39, 39);
    color: rgb(255, 226, 226);
}
/deep/ .isRed:hover>td{
    background-color: rgb(155, 0, 0)!important;
}
</style>
