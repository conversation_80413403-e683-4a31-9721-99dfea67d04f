import request from '@/utils/request'

export const listConfig = (data) =>
	request({
		url: '/config',
		method: 'get',
		params: data
	})
export const getConfig = (id) =>
	request({
		url: '/config/'+id,
		method: 'get'
	})
export const addConfig = (data) =>
	request({
		url: '/config',
		method: 'post',
		data: data
	})
export const editConfig = (data) =>
	request({
		url: '/config',
		method: 'put',
		data: data
	})
export const deleteConfig = (id) =>
	request({
		url: '/config',
		method: 'delete',
		params: {
			id: id
		}
	})
