<template>
	<el-dialog draggable width="50%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title" append-to-body>
		<el-form :rules="rules" ref="form" :model="roomModel" label-width="80px">
			<el-row>
				<el-col :span="8">
					<el-form-item label="房间编号" prop="roomNumber">
						<el-input v-model="roomModel.roomNumber" placeholder="房间编号"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="楼层编号" prop="floor">
						<el-input v-model="roomModel.floor" placeholder="楼层编号"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="面积" prop="acreage">
						<el-input-number v-model="roomModel.acreage" controls-position="right"></el-input-number>
						&nbsp;m²
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="户型" prop="type">
						<el-select style="width: 100%;" v-model="roomModel.type" filterable clearable placeholder="住户类型">
							<el-option v-for="item in roomTypeList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="openId" prop="openId">
						<el-input v-model="roomModel.openId" placeholder="openId"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="状态" prop="status">
						<el-select style="width: 100%;" v-model="roomModel.status" filterable clearable placeholder="请选择状态">
							<el-option v-for="item in roomStatusList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<!-- <el-col :span="8">
					<el-form-item label="群组房" prop="isGroupOrientedLeasing">
						<el-radio-group v-model="roomModel.isGroupOrientedLeasing" class="ml-4">
						<el-radio :label="true">是</el-radio>
						<el-radio :label="false">否</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="是否租赁" prop="isOrientedLeasing">
						<el-radio-group v-model="roomModel.isOrientedLeasing" class="ml-4">
						<el-radio :label="true">是</el-radio>
						<el-radio :label="false">否</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col> -->
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="房间图片">
          <el-scrollbar style="height:198px">
						<el-upload
							multiple
							accept="image/jpeg,image/jpg,image/png"
							v-model:file-list="fileList"
							action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
							list-type="picture-card"
							:on-preview="handlePictureCardPreview"
							:on-remove="deletePic"
							:http-request="loadingImg"
							:before-upload="beforeUploadImg"
						>
							<el-icon><Plus /></el-icon>
						</el-upload>

						<div style="display:flex;position: relative;top:-10px">
							<div v-for="(item, idx) in fileList" :key="idx" style="width:146px;display:flex;flex-shrink:0;margin-right:10px">
								<el-input
								v-model="item.name"
								style="width: 100%"
								placeholder="请输入文件名"
								></el-input>
							</div>
						</div>
          </el-scrollbar>
						<el-dialog v-model="dialogVisible" top="3vh" class="picView">
							<img w-full style="max-height:700px;" :src="dialogImageUrl" alt="Preview Image" />
						</el-dialog>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
		</el-row>
	</el-dialog>
	<el-dialog draggable width="50%" v-loading="loading" v-model="dialogView.show" destroy-on-close :title="dialogView.title" append-to-body>
		<el-form :rules="rules" ref="form" :model="roomModel" label-width="80px">

		<el-row>
				<el-col :span="8">
					<el-form-item label="房间编号 :" prop="">
						{{roomModel.roomNumber}}
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="楼层编号 :" prop="floor">
						{{roomModel.floor}}
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="面积 :" prop="acreage">
						{{roomModel.acreage}}
						&nbsp;m²
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="户型 :" prop="type">
						{{formatDict(roomTypeList,roomModel.type)}}
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="状态 :" prop="status">
						{{formatDict(roomStatusList,roomModel.status)}}

					</el-form-item>
				</el-col>
			</el-row>
			<el-row v-if="fileList.length>0">
          <el-col :span="24">
            <el-form-item label="相关图片:">
              <el-scrollbar
                class="pic--video"
                style="height: 198px; width: 100%"
              >
                <div style="display: flex">
                  <div
                    v-for="(item, idx) in fileList"
                    :key="idx"
                    style="
                      width: 146px;
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                      margin-right: 10px;
                    "
                  >
                    <div
                      class="mask"
                      style="
                        height: 146px;
                        width: 146px;
                        display: flex;
                        align-items: center;
                        justify-content:center;
                        border-radius: 5px;
                      "
                    >
                      <el-image
                      style="max-height:146px;max-width:146px"
                        :initial-index="idx"
                        :preview-src-list="previewList"
                        :src="item.url"
                      ></el-image>
                    </div>
                    <div style="width: 100%; overflow: hidden; height: 32px">
                      {{ item.name }}
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </el-form-item>
          </el-col>
        </el-row>
		</el-form>
	</el-dialog>
</template>

<script>
import { editRoom,addRoom } from "@/api/base/room"
import mitt from "@/utils/mitt";
import { fileUpload, fileRemove } from "@/api/admin/file";
import { getDictCss, formatDict } from "@/utils/dict";
export default {
	props:['roomTypeList', 'roomStatusList'],
	data() {
		return {
     		uploadImgType:['.jpg','.jpeg','.png','.gif','.JPG','.JPEG','.PNG','.GIF'],
			imgServer: import.meta.env.VITE_BASE_API,
			loading: false,
			activeName:'first',
			roomModel: {
				expandParams:[]
			},
			fileList: [],
			previewList: [],
			dialogImageUrl: "",
			dialogVisible: false,
			dialog:{},
			dialogView:{},
			rules: {
				roomNumber: [{
					required: true,
					message: '请输入房间编号',
					trigger: 'blur',
				}],
				// type: [{
				// 	required: true,
				// 	message: '请选择户型',
				// 	trigger: 'change',
				// }],
			}
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
		return getDictCss(dicList, cellValue);
		},
		formatDict(dicList, cellValue) {
		return formatDict(dicList, cellValue);
		},
		formatPreviewList(data){
			console.log(data);
		const list = []
		if (data.length) {
			for (const key in data) {
			list.push(this.imgServer+''+data[key].url)
			}
		}
		console.log(list);
		return list
		},
		deletePic(uploadFile, uploadFiles){
			console.log(uploadFile.url.replace(this.imgServer,""));
			fileRemove({fileUrl:uploadFile.url.replace(this.imgServer,"")})
      		this.roomModel.expandParams = uploadFiles
		},
		// 上传前的钩子
		beforeUploadImg(file) {
		const type = file.name.substring(file.name.lastIndexOf('.')) // 获取文件后缀，取文件格式
		const isLt10M = file.size / 1024 / 1024 < 25
		if (!this.uploadImgType.includes(type)) {
			this.$message({ type: 'error', message: '只支持jpg,jpeg,png,gif,JPG,JPEG,PNG,GIF文件格式！' })
			return false
		}
		if (!isLt10M) {
			this.$message({
			message: '上传文件大小不能超过 25MB!',
			type: 'warning'
			})
			return false
		}
		},
		// 查看图片
		handlePictureCardPreview (uploadFile) {
			console.log(uploadFile);
			this.dialogImageUrl = uploadFile.url
			this.dialogVisible = true
		},
		// 上传
		loadingImg(files) {
			let form = new FormData();
      		form.append("needCompress", false)
			form.append("file", files.file);
			form.append("modulesName", 'base');
			form.append("functionName", 'warnRecord');
			form.append("communityId", localStorage.getItem('communityId'));
			fileUpload(form).then((res) => {
				this.roomModel.expandParams.push(res.data.result);
					if (res.data.code == 0) {
					}
			});
			console.log(this.fileList);
		},
		// 大图数组格式化
		swiper(pic) {
			let list = []
			for (let i = 0; i < pic.length; i++) {
				list[i] = this.imgServer + pic[i]
			}
			return list
		},
		onSubmit(){
			this.$refs['form'].validate((valid) =>{
				if(valid){

					// 处理扩展参数 保存
					const list = []
					for (const key in this.fileList) {
						if (this.fileList[key].url.includes(this.imgServer)) {
							list.push({name:this.fileList[key].name.split(".")[0], url:this.roomModel.expandParams[key].url.replace(this.imgServer,"")})
						}else{
							list.push({name:this.fileList[key].name.split(".")[0], url:this.roomModel.expandParams[key].url})
						}
					}
					this.roomModel.expandParams = JSON.stringify(list)


					if(this.roomModel.id == 0){
						addRoom(this.roomModel)
						.then(res =>{
							this.$message.success(res.data.msg)
							this.$emit("search")
							this.dialog.show = false
						})
					}else{
						editRoom(this.roomModel)
						.then(res =>{
							this.$message.success(res.data.msg)
							this.$emit("search")
							this.dialog.show = false
						})
					}
				}
			})
		}
	
	},
	mounted(){
		this.$nextTick(function() {
			mitt.on('openRoomEdit', (room) => {
				this.roomModel = room

				// 格式化扩展参数并展示
				if (!this.roomModel.expandParams) {
					this.roomModel.expandParams = []
				} else {
					this.fileList = []
					for (const item of JSON.parse(this.roomModel.expandParams)) {
						if (item.url.includes(this.imgServer)) {
							this.fileList.push({name:item.name,url:item.url})
						}else {
							this.fileList.push({name:item.name,url:this.imgServer + '' + item.url})
						}
					}
					console.log(this.fileList);
					// this.fileList = 
					this.roomModel.expandParams = JSON.parse(this.roomModel.expandParams)
				}
				console.log(this.fileList);
				this.dialog.show = true
				this.dialog.title = "修改信息"
			})
			mitt.on('openRoomView', (room) => {
				this.previewList = []
				this.roomModel = room

				// 格式化扩展参数并展示
				this.fileList = []
				if (!this.roomModel.expandParams) {
					this.roomModel.expandParams = []
				} else {
					this.fileList = []
					for (const item of JSON.parse(this.roomModel.expandParams)) {
						if (item.url.includes(this.imgServer)) {
							this.fileList.push({name:item.name,url:item.url})
						}else {
							this.fileList.push({name:item.name,url:this.imgServer + '' + item.url})
						}
					}
					console.log(this.fileList);
					for (const item of this.fileList) {
						this.previewList.push(item.url)
					}
					console.log(this.previewList);
					// this.fileList = 
					this.roomModel.expandParams = JSON.parse(this.roomModel.expandParams)

				}
				this.dialogView.show = true
				this.dialogView.title = "查看详情"
			})
			mitt.on('openRoomAdd', (roomModel) => {
				this.roomModel = roomModel
				this.roomModel.type = null
				this.roomModel.status = 0
				this.roomModel.acreage = null
				this.roomModel.roomNumber = null
				this.roomModel.expandParams = []
				this.fileList = []
				this.dialog.show = true
				this.dialog.title = "添加房间"
			})
		})
	}
}
</script>
<style scoped lang="less">
div /deep/.picView .el-dialog__header{
	background-color: #fff;
	box-shadow: none;
}
/deep/.picView{
	display: flex;
	justify-content: center;
	align-items: center;
}
div /deep/ .picView .el-dialog__close{
	color: #ccc;
}
div /deep/ .el-upload-list{
  display: flex;
  width:100%;
  flex-wrap: nowrap;
  >li,div{
    display: flex;
    flex-shrink: 0;
  }
}
</style>
