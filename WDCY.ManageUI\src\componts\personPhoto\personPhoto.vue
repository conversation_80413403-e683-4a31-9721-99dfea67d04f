<template>
    <canvas style="position: absolute; left: -9999px" ref="canvas" id="canvas" width="640" height="480"></canvas>
    <el-tab-pane label="照片" name="second">
        <el-row>
            <el-col :span="24">
                <el-form-item label-width="0">
                    <el-card class="box-card">
                        <template #header>
                            <div style="display: flex; justify-content: space-between">
                                <span>照片</span>
                                <div>
                                    <el-button @click="uploadImg" class="button" type="text">
                                        <input style="
                                      position: fixed;
                                      left: -9999px;
                                      display: none;
                                    " type="file" accept="image/*" id="imgReader" @change="loadingImg" />裁剪上传
                                        <!-- <el-button @click="uploadImg">上传</el-button> -->
                                    </el-button>
                                    <el-button class="button" type="text">
                                        <el-upload :show-file-list="false" :http-request="loadingImg2" accept="image/jpeg,image/jpg,image/png">原图上传</el-upload>
                                    </el-button>
                                    <el-button @click="callCamera" class="button" type="text">采集</el-button>
                                    <!--canvas截取流-->
                                    <!--图片展示-->
                                    <!--确认-->
                                    <el-button class="button" type="text" @click="deletePhoto">删除</el-button>
                                </div>
                            </div>
                        </template>
                        <el-image id="canvas" fit="contain" style="height: 200px; width: 200px"
                            :preview-src-list="[imgServer + personModel.photo]" :src="imgServer + personModel.photo">
                            <template
                                v-if="personModel.photo == undefined || personModel.photo == null || personModel.photo == ''"
                                #error>
                                <div style="line-height: 200px; text-align: center;background-color: #eee;color: #999;">
                                    暂无数据
                                </div>
                            </template>
                        </el-image>
                    </el-card>
                </el-form-item>
            </el-col>
        </el-row>

        <el-dialog draggable width="50%" v-loading="loading" v-model="videoDialog.show" destroy-on-close
            :title="videoDialog.title">
            <div style="
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                      ">
                <video ref="video" width="640" height="480" autoplay></video>
                <el-button size="default" style="width: 120px; margin-top: 15px" type="primary"
                    @click="photograph">确认</el-button>
            </div>
        </el-dialog>

        <el-dialog draggable v-model="viewPic" title="裁剪头像" style="display:flex">
            <div style="display: flex">
                <img id="cropImg" style="width: 300px; height: 300px" />
            </div>
            <div>
                <div class="previewText">裁剪预览</div>
                <div style="display: flex">
                    <div style="height: 100px; width: 100px; position: relative">
                        <div class="previewBox"></div>
                    </div>
                    <div style="
                            height: 100px;
                            width: 100px;
                            position: relative;
                            border-radius: 50%;
                            overflow: hidden;
                          ">
                        <!-- <div class="previewBoxRound"></div> -->
                    </div>
                </div>
                <div style="display: flex; margin-top: 10px">
                    <el-button type="primary" @click="GetData">确认</el-button>
                </div>
            </div>
        </el-dialog>
    </el-tab-pane>
</template>

<script>
import { fileUpload } from "@/api/admin/file";
import "cropperjs/dist/cropper.css";
import Cropper from "cropperjs";
import CROPPER from "../../utils/myUtils.js";
import { truncate } from "lodash";
export default {
    props: ["personModel"],
    data() {
        return {
            imgServer: import.meta.env.VITE_BASE_API,
            videoDialog: {},
            viewPic: false,
            CROPPER: CROPPER,

        }
    },
    methods: {
        uploadImg() {
            document.querySelector("#imgReader").click();
            if (this.CROPPER) {
                this.CROPPER.destroy();
            }
        },
        uploadImg2() {
            document.querySelector("#imgReader2").click();
        },
        loadingImg(eve) {
            console.log(eve,'222');
            console.log(event.target.files,'111');
            if (!eve.target.files[0]) {
                return
            }
            this.viewPic = truncate;
            //读取上传文件
            let reader = new FileReader();
            // if (event.target.files[0]) {
                //readAsDataURL方法可以将File对象转化为data:URL格式的字符串（base64编码）
                reader.readAsDataURL(eve.target.files[0]);
                reader.onload = (e) => {
                    let dataURL = reader.result;
                    //将img的src改为刚上传的文件的转换格式
                    document.querySelector("#cropImg").src = dataURL;

                    const image = document.getElementById("cropImg");
                    //创建cropper实例-----------------------------------------
                    let CROPPER = new Cropper(image, {
                        // aspectRatio: 16 / 16,
                        initialAspectRatio: 2 / 3,
                        viewMode: 1,
                        autoCropArea: 0.95,
                        minCanvasWidth: 100,
                        minCanvasHeight: 100,
                        // minContainerWidth:500,
                        // minContainerHeight:500,
                        dragMode: "move",
                        preview: [
                            document.querySelector(".previewBox"),
                            // document.querySelector(".previewBoxRound"),
                        ],
                    });
                    this.CROPPER = CROPPER;
                };
            // }
        },
        loadingImg2(files) {
            let form = new FormData();
            form.append("file", files.file);
            fileUpload(form).then((res) => {
                this.personModel.photo = res.data.result.url;
                if (res.data.code == 0) {
                    this.$message.success("上传成功");
                }
            });
        },
        callCamera() {
            // H5调用电脑摄像头API
            navigator.mediaDevices
            .getUserMedia({
                video: true,
            })
            .then((success) => {
                // 摄像头开启成功
                    this.videoDialog.show = true;
                    this.videoDialog.title = "拍摄头像";
                    console.log(this.$refs);
                    console.log(this.$refs["video"]);
                    this.$refs["video"].srcObject = success;
                    // 实时拍照效果
                    this.$refs["video"].play();
                })
                .catch((error) => {
                    console.log(error);
                    console.error("摄像头开启失败，请检查摄像头是否可用！");
                    this.$message.error("摄像头开启失败，请检查摄像头是否可用！");
                });
        },
        deletePhoto() {
            this.personModel.photo = null;
        },
        // 拍照
        photograph() {
            this.videoDialog.show = false;
            let ctx = this.$refs["canvas"].getContext("2d");
            // 把当前视频帧内容渲染到canvas上
            ctx.drawImage(this.$refs["video"], 0, 0, 640, 480);
            // 转base64格式、图片格式转换、图片质量压缩
            let imgBase64 = this.$refs["canvas"].toDataURL("image/jpeg", 1.0);

            document.querySelector("#canvas").src = imgBase64;

            const image = document.getElementById("canvas");
            image.toBlob((blob) => {
                //然后调用浏览器原生的toBlob方法将canvas数据转换成blob数据

                //之后就可以愉快的将blob数据发送至后端啦，可根据自己情况进行发送，我这里用的是axios
                const form = new FormData();
                // 第三个参数为文件名，可选填.
                form.append("file", blob, "example.jpg");
                console.log(form);
                fileUpload(form).then((res) => {
                    this.personModel.photo = res.data.result.url;
                    if (res.data.code == 0) {
                        this.$message.success("上传成功");
                        this.closeCamera()
                    }
                });
            }, "image/jpeg", 0.95);
        },
        // 关闭摄像头
        closeCamera() {
            console.log('进入', '··················');
            if (!this.$refs["video"].srcObject) {
                this.dialogCamera = false;
                return;
            }
            let stream = this.$refs["video"].srcObject;
            let tracks = stream.getTracks();
            tracks.forEach((track) => {
                track.stop();
            });
            this.$refs["video"].srcObject = null;
        },
        GetData() {
            this.viewPic = false;
            //getCroppedCanvas方法可以将裁剪区域的数据转换成canvas数据
            this.CROPPER.getCroppedCanvas({
                maxWidth: 480,
                maxHeight: 480,
                fillColor: "#fff",
                imageSmoothingEnabled: true,
                imageSmoothingQuality: "high",
            }).toBlob((blob) => {
                //然后调用浏览器原生的toBlob方法将canvas数据转换成blob数据

                //之后就可以愉快的将blob数据发送至后端啦，可根据自己情况进行发送，我这里用的是axios
                const form = new FormData();
                // 第三个参数为文件名，可选填.
                form.append("file", blob, "example.jpg");
                fileUpload(form).then((res) => {
                    this.personModel.photo = res.data.result.url;
                    if (res.data.code == 0) {
                        this.$message.success("上传成功");
                    }
                });
            }, "image/jpeg", 0.95);
        },
    },
    mounted(){
        console.log(this.$refs);
    }
}
</script>
<style lang="less" scoped>
.box-card {
    width: 100%;
}
.previewBox {
    background-color: #eee;
    box-shadow: 0 0 5px #adadad;
    width: 100px;
    height: 100px;
    position: absolute;
    left: 50%;
    /* top: 50%; */
    margin-left: -50px;
    margin-top: -50px;
    margin-right: 10px;
    margin-top: 10px;
    overflow: hidden;
}

#cropImg {
  height: 300px;
  width: 300px;
  display: block;
  overflow: hidden;
  box-shadow: 0 0 5px #adadad;
}

.previewText {
  margin-top: 10px;
}
</style>
