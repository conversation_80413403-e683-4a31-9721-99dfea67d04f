import request from '@/utils/request'

export const listMenu = (data) =>
	request({
		url: '/menu',
		method: 'get',
		params: data
	})
export const homeMenu = (data) =>
	request({
		url: '/menu/homeMenu',
		method: 'get',
		params: data
	})
export const myListMenu = () =>
	request({
		url: '/menu/myList',
		method: 'get',
		params:{
			menuCategory:0
		}
	})
export const addMenu = (data) =>
	request({
		url: '/menu',
		method: 'post',
		data: data
	})
export const editMenu = (data) =>
	request({
		url: '/menu',
		method: 'put',
		data: data
	})
export const deleteMenu = (id) =>
	request({
		url: '/menu',
		method: 'delete',
		params: {
			id: id
		}
	})
