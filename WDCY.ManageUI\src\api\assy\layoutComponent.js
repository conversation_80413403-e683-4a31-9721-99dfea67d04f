import request from '@/utils/request'

// 查询版面组件列表
export function listLayoutComponent2(query) {
  return request({
    url: '/sapi/v3_8_0/assy/layoutComponent/list',
    method: 'get',
    params: query
  })
}

// 查询版面组件详细
export function getLayoutComponent2(id) {
  return request({
    url: '/sapi/v3_8_0/assy/layoutComponent/' + id,
    method: 'get'
  })
}

// 新增版面组件
export function addLayoutComponent2(data) {
  return request({
    url: '/sapi/v3_8_0/assy/layoutComponent',
    method: 'post',
    data: data
  })
}

// 修改版面组件
export function updateLayoutComponent2(data) {
  return request({
    url: '/sapi/v3_8_0/assy/layoutComponent',
    method: 'put',
    data: data
  })
}

// 删除版面组件
export function delLayoutComponent2(id) {
  return request({
    url: '/sapi/v3_8_0/assy/layoutComponent/' + id,
    method: 'delete'
  })
}
