<template>
	<el-row :gutter="20">
		<el-col :span="8" style="display:flex">
			<el-input style="margin-right:10px" v-model="searchModel.resCode" @keydown.enter="search" placeholder="事件源编号" clearable />
			<el-input style="margin-right:10px" v-model="searchModel.resName" @keydown.enter="search" placeholder="事件源名称" clearable />
			<el-input v-model="searchModel.eventType" @keydown.enter="search" placeholder="事件类型" clearable />
		</el-col>
		<el-col :span="8" style="display:flex">
			<el-select clearable style="margin-right:10px;width:25%" v-model="eventLevelValue.value" class="m-2" placeholder="事件等级" >
					<el-option
					v-for="(item, index) in eventLevelList"
					:key="index"
					:label="item.nameCn"
					:value="item.nameEn"
					/>
				</el-select>
			<el-date-picker
				v-model="searchModel.startTime"
				type="date"
				placeholder="选择开始日期"
				value-format="YYYY-MM-DD"
				:size="size"
				style="margin-right:10px"
			/>
			<el-date-picker
				style="margin-right:10px"
				v-model="searchModel.endTime"
				type="date"
				placeholder="选择结束日期"
				value-format="YYYY-MM-DD"
				:size="size"
			/>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table :data="eventList" border height="calc(100vh - 300px)" style="width: 100%">
				<el-table-column prop="eventId" align="center" label="事件ID" />
				<el-table-column prop="resCode" align="center" label="事件源编号" />
				<el-table-column prop="resName" align="center" label="事件源名称" />
				<el-table-column prop="eventLevelValue" style="background-color:red" align="center" label="事件等级" >
					<template #default="scope">
					  	<div :style="'background-color:' + getDictCss(eventLevelList, scope.row.eventLevelValue)">{{ formatDict(eventLevelList, scope.row.eventLevelValue) }}</div>
					</template>
				</el-table-column>
				<el-table-column prop="eventLocation" align="center" label="事件发生地点" />
				<el-table-column prop="eventType" align="center" label="事件类型" />
				<el-table-column prop="eventTime" align="center" label="事件时间" />
				<el-table-column prop="picUrls" align="center" width="80" label="照片">
					<template #default="scope">
						<div style="background-color:#eee" v-if="scope.row.picUrls == null || scope.row.picUrls == '' || scope.row.picUrls == undefined">暂无数据</div>
						<el-image preview-teleported 
							v-else fit="contain" style="width: 50px; height: 50px" 
						 	:src="imgServer+scope.row.picUrls[0]" :preview-src-list="swiper(scope.row.picUrls)">
						</el-image>
					</template>
				</el-table-column>
				<el-table-column align="center" width="80" label="操作" v-if="hasPerm('warn:warnAnalysisJudg:delete')">
					<template #default="scope">
							<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('warn:warnAnalysisJudg:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>

<script>
import { eventList, eventListDelete } from "@/api/warn/warn"
import { listDictByNameEn } from "@/api/admin/dict"
import { getDictCss, formatDict } from "@/utils/dict"
export default {
	data() {
		return {
			searchModel: {},
			imgServer: import.meta.env.VITE_BASE_API,
			communityId: localStorage.getItem("communityId"),
			eventList: [],
			total: 0,
			pageSize: 10,
			eventLevelList: [],
			eventLevelValue: {}
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		},
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			this.searchModel.communityId = this.communityId
			this.searchModel.eventLevelValue = this.eventLevelValue.value
			this.eventList.picUrls = JSON.stringify(this.eventList.picUrls)
			console.log(this.searchModel);
			eventList(this.searchModel)
				.then(res => {
					this.eventList = res.data.result.list
					for (let i = 0; i < this.eventList.length; i++) {
						this.eventList[i].picUrls = JSON.parse(this.eventList[i].picUrls)
					}
					this.total = res.data.result.total
				})
		},
		swiper(pic) {
			let list = []
			for (let i = 0; i < pic.length; i++) {
				list[i] = this.imgServer + pic[i]
			}
			return list
		},
		deleted(id) {
			this.$confirm('删除信息, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				eventListDelete(id)
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num) {
			this.searchModel.pageSize = num
			this.search()
		},
		async init() {
			try {
				let eventLevel_res = await listDictByNameEn('event_level')
				this.eventLevelList = eventLevel_res.data.result

				this.searchModel.communityId = this.communityId
				let res = await eventList(this.searchModel)
				this.eventList = res.data.result.list
				for (let i = 0; i < this.eventList.length; i++) {
					this.eventList[i].picUrls = JSON.parse(this.eventList[i].picUrls)
				}
				this.total = res.data.result.total
			} catch (err) {
			}
		}
	},
	created() {
		this.init()
		console.log(this.$route);
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
