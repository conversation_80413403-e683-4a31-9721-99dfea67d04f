/**
 * Mars3D平台插件,支持气象 风向图 功能插件  mars3d-wind
 *
 * 版本信息：v3.8.13
 * 编译日期：2025-01-09 16:08
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：火星科技免费公开版 ，2024-08-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-wind"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';(function(_0x1decdc,_0x39f797){function _0x2875a7(_0x4cb14f,_0x3619f6){return _0x39e2(_0x3619f6- -0x32,_0x4cb14f);}const _0x2dbfdf=_0x1decdc();function _0x368f47(_0x5cf431,_0x4eca08){return _0x39e2(_0x4eca08- -0x46,_0x5cf431);}while(!![]){try{const _0x4a9044=parseInt(_0x2875a7(0x129,0x10a))/0x1+-parseInt(_0x2875a7(0x8d,0x99))/0x2*(-parseInt(_0x2875a7(0x109,0xfd))/0x3)+parseInt(_0x368f47(0xa0,0xb8))/0x4*(parseInt(_0x368f47(0x11f,0x11c))/0x5)+-parseInt(_0x2875a7(0x9a,0xa9))/0x6+-parseInt(_0x2875a7(0xe1,0xbf))/0x7+-parseInt(_0x2875a7(0xd9,0x77))/0x8+parseInt(_0x368f47(0x137,0xd2))/0x9;if(_0x4a9044===_0x39f797)break;else _0x2dbfdf['push'](_0x2dbfdf['shift']());}catch(_0x5ecac8){_0x2dbfdf['push'](_0x2dbfdf['shift']());}}}(_0x18e9,0xde778));function _interopNamespace(_0x2b7223){function _0x18d054(_0x4db944,_0x3532b3){return _0x39e2(_0x4db944-0x30,_0x3532b3);}if(_0x2b7223&&_0x2b7223[_0x18d054(0x1b9,0x1d1)])return _0x2b7223;var _0x1e4b45=Object[_0x3f2335(0x1eb,0x270)](null);_0x2b7223&&Object['keys'](_0x2b7223)[_0x3f2335(0x222,0x21b)](function(_0x5639b9){function _0x19c874(_0x5c4f1d,_0x15a8e6){return _0x18d054(_0x15a8e6- -0x387,_0x5c4f1d);}function _0x2b66fa(_0x2ee7b7,_0x459409){return _0x18d054(_0x459409- -0xea,_0x2ee7b7);}if(_0x5639b9!==_0x2b66fa(0xef,0xdd)){var _0x80bbf5=Object['getOwnPropertyDescriptor'](_0x2b7223,_0x5639b9);Object[_0x19c874(-0x17e,-0x1df)](_0x1e4b45,_0x5639b9,_0x80bbf5['get']?_0x80bbf5:{'enumerable':!![],'get':function(){return _0x2b7223[_0x5639b9];}});}});function _0x3f2335(_0x21b4d2,_0x43bbb3){return _0x39e2(_0x21b4d2-0x127,_0x43bbb3);}return _0x1e4b45[_0x3f2335(0x2be,0x311)]=_0x2b7223,_0x1e4b45;}var mars3d__namespace=_interopNamespace(mars3d);const Cesium$2=mars3d__namespace[_0x4e5d3a(0x1f0,0x185)];function getU(_0x34841d,_0x3e265d){const _0x33b06a=_0x34841d*Math['cos'](Cesium$2[_0x422aa7(0xcb,0xe6)]['toRadians'](_0x3e265d));function _0x422aa7(_0x26f663,_0x3163f1){return _0x4e5d3a(_0x26f663,_0x3163f1- -0x65);}return _0x33b06a;}function getV(_0x543d9a,_0x420e44){const _0xa10e04=_0x543d9a*Math['sin'](Cesium$2['Math']['toRadians'](_0x420e44));return _0xa10e04;}function getSpeed(_0x2841c7,_0x283433){const _0x100325=Math[_0x44f65e(0x4fa,0x4cc)](Math[_0x473cfb(-0x11f,-0x1a3)](_0x2841c7,0x2)+Math['pow'](_0x283433,0x2));function _0x44f65e(_0x5d345e,_0x4b4e79){return _0x4e5d3a(_0x4b4e79,_0x5d345e-0x386);}function _0x473cfb(_0x20d523,_0x4d4643){return _0x4e5d3a(_0x4d4643,_0x20d523- -0x268);}return _0x100325;}function getDirection(_0x35a710,_0x235294){function _0x398957(_0x377688,_0x3e3fe9){return _0x4e5d3a(_0x377688,_0x3e3fe9- -0x24a);}let _0xaa0c38=Cesium$2[_0x49e4f9(0x45a,0x3e4)][_0x398957(-0x55,-0x84)](Math['atan2'](_0x235294,_0x35a710));_0xaa0c38+=_0xaa0c38<0x0?0x168:0x0;function _0x49e4f9(_0x2f0299,_0x2aae25){return _0x4e5d3a(_0x2f0299,_0x2aae25-0x299);}return _0xaa0c38;}const _0x13dc0b={};_0x13dc0b['__proto__']=null,_0x13dc0b[_0x4e5d3a(0x198,0x15c)]=getU,_0x13dc0b['getV']=getV,_0x13dc0b['getSpeed']=getSpeed,_0x13dc0b['getDirection']=getDirection;var WindUtil=_0x13dc0b,updatePositionShader='#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x0auniform\x20sampler2D\x20particlesSpeed;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20获取当前粒子的位置\x0a\x20\x20vec2\x20currentPos\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rg;\x0a\x20\x20\x20\x20//\x20获取粒子的速度\x0a\x20\x20vec2\x20speed\x20=\x20texture(particlesSpeed,\x20v_textureCoordinates).rg;\x0a\x20\x20\x20\x20//\x20计算下一个位置\x0a\x20\x20vec2\x20nextPos\x20=\x20currentPos\x20+\x20speed;\x0a\x0a\x20\x20\x20\x20//\x20将新的位置写入\x20fragColor\x0a\x20\x20fragColor\x20=\x20vec4(nextPos,\x200.0f,\x201.0f);\x0a}\x0a',calculateSpeedShader=_0x2319dd(-0x252,-0x25f),postProcessingPositionFragmentShader=_0x4e5d3a(0xbc,0x11a),renderParticlesFragmentShader='#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0ain\x20vec4\x20speed;\x0ain\x20float\x20v_segmentPosition;\x0ain\x20vec2\x20textureCoordinate;\x0a\x0auniform\x20vec2\x20domain;\x0auniform\x20vec2\x20displayRange;\x0auniform\x20sampler2D\x20colorTable;\x0auniform\x20sampler2D\x20segmentsDepthTexture;\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20const\x20float\x20zero\x20=\x200.0f;\x0a\x20\x20if(speed.a\x20>\x20zero\x20&&\x20speed.b\x20>\x20displayRange.x\x20&&\x20speed.b\x20<\x20displayRange.y)\x20{\x0a\x20\x20\x20\x20float\x20speedLength\x20=\x20clamp(speed.b,\x20domain.x,\x20domain.y);\x0a\x20\x20\x20\x20float\x20normalizedSpeed\x20=\x20(speedLength\x20-\x20domain.x)\x20/\x20(domain.y\x20-\x20domain.x);\x0a\x20\x20\x20\x20vec4\x20baseColor\x20=\x20texture(colorTable,\x20vec2(normalizedSpeed,\x20zero));\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20使用更平滑的渐变效果\x0a\x20\x20\x20\x20float\x20alpha\x20=\x20smoothstep(0.0f,\x201.0f,\x20v_segmentPosition);\x0a\x20\x20\x20\x20alpha\x20=\x20pow(alpha,\x201.5f);\x20//\x20调整透明度渐变曲线\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20根据速度调整透明度\x0a\x20\x20\x20\x20float\x20speedAlpha\x20=\x20mix(0.3f,\x201.0f,\x20speed.a);\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20组合颜色和透明度\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(baseColor.rgb,\x20baseColor.a\x20*\x20alpha\x20*\x20speedAlpha);\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(zero);\x0a\x20\x20}\x0a\x0a\x20\x20float\x20segmentsDepth\x20=\x20texture(segmentsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20float\x20globeDepth\x20=\x20czm_unpackDepth(texture(czm_globeDepthTexture,\x20textureCoordinate));\x0a\x20\x20if(segmentsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(zero);\x0a\x20\x20}\x0a}\x0a',renderParticlesVertexShader=_0x2319dd(-0x22d,-0x1d9);function _0x18e9(){const _0x2a51ec=['Cartesian2','vmin','FLOAT','cartesianToCartographic','uniformMap','_canrefresh','vertexShaderSource','RED','frameRateAdjustment','8609825RMCzJQ','clear','clearCommand','createParticlesTextures','longitude','tlat','owner','west','Draw','setData','forEach','dynamic','reCreateWindTextures','1342268QCqhYp','currentParticlesPosition','visibility','updatePosition','sceneMode','canvasHeight','createRenderingTextures','segmentsColor','attributeLocations','componentsPerAttribute','south','bindEvent','commandList','onmessage','geometry','outputTexture','shaderProgram','initFrameRate','particles','visible','_addedHook','flipY','pow','getSegmentDrawFragmentShader','Math','rawRenderState','6895962AHlCSp','lng','redraw','primitives','_updateIng2','_tomap','wind','textures','OPAQUE','bufferUsage','ColorRamp','values','NEAREST','blending','colorTable','getU','umin','hasOwnProperty','canvasWidth','_drawLines','speedRate','commandToExecute','blendFuncDestination','9PoBeDF','windTextures','maxAge','keys','getPostProcessingPositionShader','colors','unbindEvent','createCommand','log','framebuffer','fixedHeight','push','particlesTextures','735330ywHCJG','rows','lonRange','sqrt','random','fromDegrees','#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0ain\x20vec2\x20st;\x0ain\x20vec3\x20normal;\x0a\x0auniform\x20sampler2D\x20previousParticlesPosition;\x0auniform\x20sampler2D\x20currentParticlesPosition;\x0auniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20particlesSpeed;\x0a\x0auniform\x20float\x20frameRateAdjustment;\x0auniform\x20float\x20particleHeight;\x0auniform\x20float\x20aspect;\x0auniform\x20float\x20pixelSize;\x0auniform\x20vec2\x20lineWidth;\x0auniform\x20vec2\x20lineLength;\x0auniform\x20vec2\x20domain;\x0auniform\x20bool\x20is3D;\x0a\x0a//\x20添加输出变量传递给片元着色器\x0aout\x20vec4\x20speed;\x0aout\x20float\x20v_segmentPosition;\x0aout\x20vec2\x20textureCoordinate;\x0a\x0a//\x20添加结构体定义\x0astruct\x20adjacentPoints\x20{\x0a\x20\x20vec4\x20previous;\x0a\x20\x20vec4\x20current;\x0a\x20\x20vec4\x20next;\x0a};\x0a\x0avec3\x20convertCoordinate(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20WGS84\x20(lon,\x20lat,\x20lev)\x20->\x20ECEF\x20(x,\x20y,\x20z)\x0a\x20\x20\x20\x20//\x20read\x20https://en.wikipedia.org/wiki/Geographic_coordinate_conversion#From_geodetic_to_ECEF_coordinates\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20WGS\x2084\x20geometric\x20constants\x0a\x20\x20float\x20a\x20=\x206378137.0f;\x20//\x20Semi-major\x20axis\x0a\x20\x20float\x20b\x20=\x206356752.3142f;\x20//\x20Semi-minor\x20axis\x0a\x20\x20float\x20e2\x20=\x206.69437999014e-3f;\x20//\x20First\x20eccentricity\x20squared\x0a\x0a\x20\x20float\x20latitude\x20=\x20radians(lonLat.y);\x0a\x20\x20float\x20longitude\x20=\x20radians(lonLat.x);\x0a\x0a\x20\x20float\x20cosLat\x20=\x20cos(latitude);\x0a\x20\x20float\x20sinLat\x20=\x20sin(latitude);\x0a\x20\x20float\x20cosLon\x20=\x20cos(longitude);\x0a\x20\x20float\x20sinLon\x20=\x20sin(longitude);\x0a\x0a\x20\x20float\x20N_Phi\x20=\x20a\x20/\x20sqrt(1.0f\x20-\x20e2\x20*\x20sinLat\x20*\x20sinLat);\x0a\x20\x20float\x20h\x20=\x20particleHeight;\x20//\x20it\x20should\x20be\x20high\x20enough\x20otherwise\x20the\x20particle\x20may\x20not\x20pass\x20the\x20terrain\x20depth\x20test\x0a\x20\x20vec3\x20cartesian\x20=\x20vec3(0.0f);\x0a\x20\x20cartesian.x\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20cosLon;\x0a\x20\x20cartesian.y\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20sinLon;\x0a\x20\x20cartesian.z\x20=\x20((b\x20*\x20b)\x20/\x20(a\x20*\x20a)\x20*\x20N_Phi\x20+\x20h)\x20*\x20sinLat;\x0a\x20\x20return\x20cartesian;\x0a}\x0a\x0avec4\x20calculateProjectedCoordinate(vec2\x20lonLat)\x20{\x0a\x20\x20if(is3D)\x20{\x0a\x20\x20\x20\x20vec3\x20particlePosition\x20=\x20convertCoordinate(lonLat);\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20使用\x20modelViewProjection\x20矩阵进行投影变换\x0a\x20\x20\x20\x20vec4\x20projectedPosition\x20=\x20czm_modelViewProjection\x20*\x20vec4(particlePosition,\x201.0f);\x0a\x20\x20\x20\x20return\x20projectedPosition;\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20vec3\x20position2D\x20=\x20vec3(radians(lonLat.x),\x20radians(lonLat.y),\x200.0f);\x0a\x20\x20\x20\x20return\x20czm_modelViewProjection\x20*\x20vec4(position2D,\x201.0f);\x0a\x20\x20}\x0a}\x0a\x0avec4\x20calculateOffsetOnNormalDirection(vec4\x20pointA,\x20vec4\x20pointB,\x20float\x20offsetSign,\x20float\x20widthFactor)\x20{\x0a\x20\x20vec2\x20aspectVec2\x20=\x20vec2(aspect,\x201.0f);\x0a\x20\x20vec2\x20pointA_XY\x20=\x20(pointA.xy\x20/\x20pointA.w)\x20*\x20aspectVec2;\x0a\x20\x20vec2\x20pointB_XY\x20=\x20(pointB.xy\x20/\x20pointB.w)\x20*\x20aspectVec2;\x0a\x0a\x20\x20\x20\x20//\x20计算方向向量\x0a\x20\x20vec2\x20direction\x20=\x20normalize(pointB_XY\x20-\x20pointA_XY);\x0a\x0a\x20\x20\x20\x20//\x20计算法向量\x0a\x20\x20vec2\x20normalVector\x20=\x20vec2(-direction.y,\x20direction.x);\x0a\x20\x20normalVector.x\x20=\x20normalVector.x\x20/\x20aspect;\x0a\x0a\x20\x20\x20\x20//\x20使用\x20widthFactor\x20调整宽度\x0a\x20\x20float\x20offsetLength\x20=\x20widthFactor\x20*\x20lineWidth.y;\x0a\x20\x20normalVector\x20=\x20offsetLength\x20*\x20normalVector;\x0a\x0a\x20\x20vec4\x20offset\x20=\x20vec4(offsetSign\x20*\x20normalVector,\x200.0f,\x200.0f);\x0a\x20\x20return\x20offset;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20翻转\x20Y\x20轴坐标\x0a\x20\x20vec2\x20flippedIndex\x20=\x20vec2(st.x,\x201.0f\x20-\x20st.y);\x0a\x0a\x20\x20vec2\x20particleIndex\x20=\x20flippedIndex;\x0a\x20\x20speed\x20=\x20texture(particlesSpeed,\x20particleIndex);\x0a\x0a\x20\x20vec2\x20previousPosition\x20=\x20texture(previousParticlesPosition,\x20particleIndex).rg;\x0a\x20\x20vec2\x20currentPosition\x20=\x20texture(currentParticlesPosition,\x20particleIndex).rg;\x0a\x20\x20vec2\x20nextPosition\x20=\x20texture(postProcessingPosition,\x20particleIndex).rg;\x0a\x0a\x20\x20float\x20isAnyRandomPointUsed\x20=\x20texture(postProcessingPosition,\x20particleIndex).a\x20+\x0a\x20\x20\x20\x20texture(currentParticlesPosition,\x20particleIndex).a\x20+\x0a\x20\x20\x20\x20texture(previousParticlesPosition,\x20particleIndex).a;\x0a\x0a\x20\x20adjacentPoints\x20projectedCoordinates;\x0a\x20\x20if(isAnyRandomPointUsed\x20>\x200.0f)\x20{\x0a\x20\x20\x20\x20projectedCoordinates.previous\x20=\x20calculateProjectedCoordinate(previousPosition);\x0a\x20\x20\x20\x20projectedCoordinates.current\x20=\x20projectedCoordinates.previous;\x0a\x20\x20\x20\x20projectedCoordinates.next\x20=\x20projectedCoordinates.previous;\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20projectedCoordinates.previous\x20=\x20calculateProjectedCoordinate(previousPosition);\x0a\x20\x20\x20\x20projectedCoordinates.current\x20=\x20calculateProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20projectedCoordinates.next\x20=\x20calculateProjectedCoordinate(nextPosition);\x0a\x20\x20}\x0a\x0a\x20\x20int\x20pointToUse\x20=\x20int(normal.x);\x0a\x20\x20float\x20offsetSign\x20=\x20normal.y;\x0a\x20\x20vec4\x20offset\x20=\x20vec4(0.0f);\x0a\x0a\x20\x20\x20\x20//\x20计算速度相关的宽度和长度因子\x0a\x20\x20float\x20speedLength\x20=\x20clamp(speed.b,\x20domain.x,\x20domain.y);\x0a\x20\x20float\x20normalizedSpeed\x20=\x20(speedLength\x20-\x20domain.x)\x20/\x20(domain.y\x20-\x20domain.x);\x0a\x0a\x20\x20\x20\x20//\x20根据速度计算宽度\x0a\x20\x20float\x20widthFactor\x20=\x20mix(lineWidth.x,\x20lineWidth.y,\x20normalizedSpeed);\x0a\x20\x20widthFactor\x20*=\x20(pointToUse\x20<\x200\x20?\x201.0f\x20:\x200.5f);\x20//\x20头部更宽，尾部更窄\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20length\x20based\x20on\x20speed\x0a\x20\x20float\x20lengthFactor\x20=\x20mix(lineLength.x,\x20lineLength.y,\x20normalizedSpeed)\x20*\x20pixelSize;\x0a\x0a\x20\x20if(pointToUse\x20==\x201)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20头部位置\x0a\x20\x20\x20\x20offset\x20=\x20pixelSize\x20*\x20calculateOffsetOnNormalDirection(projectedCoordinates.previous,\x20projectedCoordinates.current,\x20offsetSign,\x20widthFactor);\x0a\x20\x20\x20\x20gl_Position\x20=\x20projectedCoordinates.previous\x20+\x20offset;\x0a\x20\x20\x20\x20v_segmentPosition\x20=\x200.0f;\x20//\x20头部\x0a\x20\x20}\x20else\x20if(pointToUse\x20==\x20-1)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20Get\x20direction\x20and\x20normalize\x20it\x20to\x20length\x201.0\x0a\x20\x20\x20\x20vec4\x20direction\x20=\x20normalize(projectedCoordinates.next\x20-\x20projectedCoordinates.current);\x0a\x20\x20\x20\x20vec4\x20extendedPosition\x20=\x20projectedCoordinates.current\x20+\x20direction\x20*\x20lengthFactor;\x0a\x0a\x20\x20\x20\x20offset\x20=\x20pixelSize\x20*\x20calculateOffsetOnNormalDirection(projectedCoordinates.current,\x20extendedPosition,\x20offsetSign,\x20widthFactor);\x0a\x20\x20\x20\x20gl_Position\x20=\x20extendedPosition\x20+\x20offset;\x0a\x20\x20\x20\x20v_segmentPosition\x20=\x201.0f;\x20//\x20尾部\x0a\x20\x20}\x0a\x0a\x20\x20textureCoordinate\x20=\x20st;\x0a}\x0a','mouseMove','init','pickEllipsoid','calculateSpeed','east','setDate','createComputingPrimitives','speed','register','floor','scene','processWindData','onColorTableChange','Cesium','preExecute','getUVByXY','primitiveType','mouseDown','autoClear','grid','SCENE3D','DEPTH_COMPONENT','options','camera','stroke','bind','displayRange','minificationFilter','isDestroy','value','color','5usLftu','postProcessingPosition','show','lineWidth','createRenderingFramebuffers','_setOptionsHook','IDENTITY','pixelSize','ONE_MINUS_SRC_ALPHA','speedFactor','_onMapWhellEvent','bounds','createColorTableTexture','beginPath','talt','getParticles','mouse_move','min','UNSIGNED_INT','Compute','setOptions','_map','defineProperty','CanvasWindLayer','container','nextParticlesPosition','cols','max','applyViewerParameters','useViewerBounds','postMessage','RGBA','removeChild','context','sources','dropRate','mode','length','type','__esModule','layer','canvas','isInExtent','_colorRamp','particlesTextureSize','array','viewerParameters','toDegrees','xmin','pointerEvents','windData','data','stringify','default','depthTexture','LayerUtil','updateWindData','original','mouseHidden','pixelDatatype','refreshTimer','_maxAge','updateOptions','changed','_updateIng','computing','EventType','segmentsDepth','style','north','update','updateViewerParameters','createWindTextures','clientHeight','blue','_animateFrame','clientWidth','_bilinearInterpolation','rendering','warn','ymin','isArray','width','lineTo','1709072SGBRgg','segments','getCalculateSpeedShader','particleSystem','resize','setGeometry','modelMatrix','_onMouseMoveEvent','now','position','removeEventListener','destroy','wheel','pixelFormat','getColor','createRawRenderState','_removedHook','add','vertexArray','#version\x20300\x20es\x0a\x0a//\x20the\x20size\x20of\x20UV\x20textures:\x20width\x20=\x20lon,\x20height\x20=\x20lat\x0auniform\x20sampler2D\x20U;\x20//\x20eastward\x20wind\x0auniform\x20sampler2D\x20V;\x20//\x20northward\x20wind\x0auniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0a\x0auniform\x20vec2\x20uRange;\x20//\x20(min,\x20max)\x0auniform\x20vec2\x20vRange;\x20//\x20(min,\x20max)\x0auniform\x20vec2\x20speedRange;\x20//\x20(min,\x20max)\x0auniform\x20vec2\x20dimension;\x20//\x20(lon,\x20lat)\x0auniform\x20vec2\x20minimum;\x20//\x20minimum\x20of\x20each\x20dimension\x0auniform\x20vec2\x20maximum;\x20//\x20maximum\x20of\x20each\x20dimension\x0a\x0auniform\x20float\x20speedScaleFactor;\x0auniform\x20float\x20frameRateAdjustment;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20getInterval(vec2\x20maximum,\x20vec2\x20minimum,\x20vec2\x20dimension)\x20{\x0a\x20\x20return\x20(maximum\x20-\x20minimum)\x20/\x20(dimension\x20-\x201.0f);\x0a}\x0a\x0avec2\x20mapPositionToNormalizedIndex2D(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20range\x20of\x20longitude\x20and\x20latitude\x0a\x20\x20lonLat.x\x20=\x20clamp(lonLat.x,\x20minimum.x,\x20maximum.x);\x0a\x20\x20lonLat.y\x20=\x20clamp(lonLat.y,\x20minimum.y,\x20maximum.y);\x0a\x0a\x20\x20vec2\x20interval\x20=\x20getInterval(maximum,\x20minimum,\x20dimension);\x0a\x0a\x20\x20vec2\x20index2D\x20=\x20vec2(0.0f);\x0a\x20\x20index2D.x\x20=\x20(lonLat.x\x20-\x20minimum.x)\x20/\x20interval.x;\x0a\x20\x20index2D.y\x20=\x20(lonLat.y\x20-\x20minimum.y)\x20/\x20interval.y;\x0a\x0a\x20\x20vec2\x20normalizedIndex2D\x20=\x20vec2(index2D.x\x20/\x20dimension.x,\x20index2D.y\x20/\x20dimension.y);\x0a\x20\x20return\x20normalizedIndex2D;\x0a}\x0a\x0afloat\x20getWindComponent(sampler2D\x20componentTexture,\x20vec2\x20lonLat)\x20{\x0a\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLat);\x0a\x20\x20float\x20result\x20=\x20texture(componentTexture,\x20normalizedIndex2D).r;\x0a\x20\x20return\x20result;\x0a}\x0a\x0avec2\x20getWindComponents(vec2\x20lonLat)\x20{\x0a\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLat);\x0a\x20\x20float\x20u\x20=\x20texture(U,\x20normalizedIndex2D).r;\x0a\x20\x20float\x20v\x20=\x20texture(V,\x20normalizedIndex2D).r;\x0a\x20\x20return\x20vec2(u,\x20v);\x0a}\x0a\x0avec2\x20bilinearInterpolation(vec2\x20lonLat)\x20{\x0a\x20\x20float\x20lon\x20=\x20lonLat.x;\x0a\x20\x20float\x20lat\x20=\x20lonLat.y;\x0a\x0a\x20\x20vec2\x20interval\x20=\x20getInterval(maximum,\x20minimum,\x20dimension);\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20grid\x20cell\x20coordinates\x0a\x20\x20float\x20lon0\x20=\x20floor(lon\x20/\x20interval.x)\x20*\x20interval.x;\x0a\x20\x20float\x20lon1\x20=\x20lon0\x20+\x20interval.x;\x0a\x20\x20float\x20lat0\x20=\x20floor(lat\x20/\x20interval.y)\x20*\x20interval.y;\x0a\x20\x20float\x20lat1\x20=\x20lat0\x20+\x20interval.y;\x0a\x0a\x20\x20\x20\x20//\x20Get\x20wind\x20vectors\x20at\x20four\x20corners\x0a\x20\x20vec2\x20v00\x20=\x20getWindComponents(vec2(lon0,\x20lat0));\x0a\x20\x20vec2\x20v10\x20=\x20getWindComponents(vec2(lon1,\x20lat0));\x0a\x20\x20vec2\x20v01\x20=\x20getWindComponents(vec2(lon0,\x20lat1));\x0a\x20\x20vec2\x20v11\x20=\x20getWindComponents(vec2(lon1,\x20lat1));\x0a\x0a\x20\x20\x20\x20//\x20Check\x20if\x20all\x20wind\x20vectors\x20are\x20zero\x0a\x20\x20if(length(v00)\x20==\x200.0f\x20&&\x20length(v10)\x20==\x200.0f\x20&&\x20length(v01)\x20==\x200.0f\x20&&\x20length(v11)\x20==\x200.0f)\x20{\x0a\x20\x20\x20\x20return\x20vec2(0.0f,\x200.0f);\x0a\x20\x20}\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20interpolation\x20weights\x0a\x20\x20float\x20s\x20=\x20(lon\x20-\x20lon0)\x20/\x20interval.x;\x0a\x20\x20float\x20t\x20=\x20(lat\x20-\x20lat0)\x20/\x20interval.y;\x0a\x0a\x20\x20\x20\x20//\x20Perform\x20bilinear\x20interpolation\x20on\x20vector\x20components\x0a\x20\x20vec2\x20v0\x20=\x20mix(v00,\x20v10,\x20s);\x0a\x20\x20vec2\x20v1\x20=\x20mix(v01,\x20v11,\x20s);\x0a\x20\x20return\x20mix(v0,\x20v1,\x20t);\x0a}\x0a\x0avec2\x20lengthOfLonLat(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20unit\x20conversion:\x20meters\x20->\x20longitude\x20latitude\x20degrees\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_system#Length_of_a_degree\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20the\x20length\x20of\x20a\x20degree\x20of\x20latitude\x20and\x20longitude\x20in\x20meters\x0a\x20\x20float\x20latitude\x20=\x20radians(lonLat.y);\x0a\x0a\x20\x20float\x20term1\x20=\x20111132.92f;\x0a\x20\x20float\x20term2\x20=\x20559.82f\x20*\x20cos(2.0f\x20*\x20latitude);\x0a\x20\x20float\x20term3\x20=\x201.175f\x20*\x20cos(4.0f\x20*\x20latitude);\x0a\x20\x20float\x20term4\x20=\x200.0023f\x20*\x20cos(6.0f\x20*\x20latitude);\x0a\x20\x20float\x20latLength\x20=\x20term1\x20-\x20term2\x20+\x20term3\x20-\x20term4;\x0a\x0a\x20\x20float\x20term5\x20=\x20111412.84f\x20*\x20cos(latitude);\x0a\x20\x20float\x20term6\x20=\x2093.5f\x20*\x20cos(3.0f\x20*\x20latitude);\x0a\x20\x20float\x20term7\x20=\x200.118f\x20*\x20cos(5.0f\x20*\x20latitude);\x0a\x20\x20float\x20longLength\x20=\x20term5\x20-\x20term6\x20+\x20term7;\x0a\x0a\x20\x20return\x20vec2(longLength,\x20latLength);\x0a}\x0a\x0avec2\x20convertSpeedUnitToLonLat(vec2\x20lonLat,\x20vec2\x20speed)\x20{\x0a\x20\x20vec2\x20lonLatLength\x20=\x20lengthOfLonLat(lonLat);\x0a\x20\x20float\x20u\x20=\x20speed.x\x20/\x20lonLatLength.x;\x0a\x20\x20float\x20v\x20=\x20speed.y\x20/\x20lonLatLength.y;\x0a\x20\x20vec2\x20windVectorInLonLat\x20=\x20vec2(u,\x20v);\x0a\x0a\x20\x20return\x20windVectorInLonLat;\x0a}\x0a\x0avec2\x20calculateSpeedByRungeKutta2(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Runge%E2%80%93Kutta_methods#Second-order_methods_with_two_stages\x20for\x20detail\x0a\x20\x20const\x20float\x20h\x20=\x200.5f;\x0a\x0a\x20\x20vec2\x20y_n\x20=\x20lonLat;\x0a\x20\x20vec2\x20f_n\x20=\x20bilinearInterpolation(lonLat);\x0a\x20\x20vec2\x20midpoint\x20=\x20y_n\x20+\x200.5f\x20*\x20h\x20*\x20convertSpeedUnitToLonLat(y_n,\x20f_n)\x20*\x20speedScaleFactor;\x0a\x20\x20vec2\x20speed\x20=\x20h\x20*\x20bilinearInterpolation(midpoint)\x20*\x20speedScaleFactor;\x0a\x0a\x20\x20return\x20speed;\x0a}\x0a\x0avec2\x20calculateWindNorm(vec2\x20speed)\x20{\x0a\x20\x20float\x20speedLength\x20=\x20length(speed.xy);\x0a\x20\x20if(speedLength\x20==\x200.0f)\x20{\x0a\x20\x20\x20\x20return\x20vec2(0.0f);\x0a\x20\x20}\x0a\x0a\x20\x20\x20\x20//\x20Clamp\x20speedLength\x20to\x20range\x0a\x20\x20float\x20clampedSpeed\x20=\x20clamp(speedLength,\x20speedRange.x,\x20speedRange.y);\x0a\x20\x20float\x20normalizedSpeed\x20=\x20(clampedSpeed\x20-\x20speedRange.x)\x20/\x20(speedRange.y\x20-\x20speedRange.x);\x0a\x20\x20return\x20vec2(speedLength,\x20normalizedSpeed);\x0a}\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20vec2\x20lonLat\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rg;\x0a\x20\x20vec2\x20speedOrigin\x20=\x20bilinearInterpolation(lonLat);\x0a\x20\x20vec2\x20speed\x20=\x20calculateSpeedByRungeKutta2(lonLat)\x20*\x20frameRateAdjustment;\x0a\x20\x20vec2\x20speedInLonLat\x20=\x20convertSpeedUnitToLonLat(lonLat,\x20speed);\x0a\x0a\x20\x20fragColor\x20=\x20vec4(speedInLonLat,\x20calculateWindNorm(speedOrigin));\x0a}\x0a','off','abs','changeOptions','latRange','_calcUV','canvasContext','_calc_speedRate','create','addEventListener','magnificationFilter','globe','previousParticlesPosition','pointer-events','height','656538LaQabh','xmax','worker','tlng','frameRateMonitor','getSegmentDrawVertexShader','clearFramebuffers','0px','fragmentShaderSource','vmax','ymax','framebuffers','hidden','_onMouseUpEvent','windField','_pointerEvents','2802534QtwQjA','WindLayer','isDestroyed','dropRateBump','lat','createSegmentsGeometry','fromGeometry','remove','BaseLayer','mouse_down','#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0auniform\x20sampler2D\x20nextParticlesPosition;\x0auniform\x20sampler2D\x20particlesSpeed;\x20//\x20(u,\x20v,\x20norm)\x0a\x0a//\x20range\x20(min,\x20max)\x0auniform\x20vec2\x20lonRange;\x0auniform\x20vec2\x20latRange;\x0a\x0a//\x20range\x20(min,\x20max)\x0auniform\x20vec2\x20dataLonRange;\x0auniform\x20vec2\x20dataLatRange;\x0a\x0auniform\x20float\x20randomCoefficient;\x0auniform\x20float\x20dropRate;\x0auniform\x20float\x20dropRateBump;\x0a\x0a//\x20添加新的\x20uniform\x20变量\x0auniform\x20bool\x20useViewerBounds;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0a//\x20pseudo-random\x20generator\x0aconst\x20vec3\x20randomConstants\x20=\x20vec3(12.9898f,\x2078.233f,\x204375.85453f);\x0aconst\x20vec2\x20normalRange\x20=\x20vec2(0.0f,\x201.0f);\x0afloat\x20rand(vec2\x20seed,\x20vec2\x20range)\x20{\x0a\x20\x20vec2\x20randomSeed\x20=\x20randomCoefficient\x20*\x20seed;\x0a\x20\x20float\x20temp\x20=\x20dot(randomConstants.xy,\x20randomSeed);\x0a\x20\x20temp\x20=\x20fract(sin(temp)\x20*\x20(randomConstants.z\x20+\x20temp));\x0a\x20\x20return\x20temp\x20*\x20(range.y\x20-\x20range.x)\x20+\x20range.x;\x0a}\x0a\x0avec2\x20generateRandomParticle(vec2\x20seed)\x20{\x0a\x20\x20vec2\x20range;\x0a\x20\x20float\x20randomLon,\x20randomLat;\x0a\x0a\x20\x20if(useViewerBounds)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20在当前视域范围内生成粒子\x0a\x20\x20\x20\x20randomLon\x20=\x20rand(seed,\x20lonRange);\x0a\x20\x20\x20\x20randomLat\x20=\x20rand(-seed,\x20latRange);\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20在数据范围内生成粒子\x0a\x20\x20\x20\x20randomLon\x20=\x20rand(seed,\x20dataLonRange);\x0a\x20\x20\x20\x20randomLat\x20=\x20rand(-seed,\x20dataLatRange);\x0a\x20\x20}\x0a\x0a\x20\x20return\x20vec2(randomLon,\x20randomLat);\x0a}\x0a\x0abool\x20particleOutbound(vec2\x20particle)\x20{\x0a\x20\x20return\x20particle.y\x20<\x20dataLatRange.x\x20||\x20particle.y\x20>\x20dataLatRange.y\x20||\x20particle.x\x20<\x20dataLonRange.x\x20||\x20particle.x\x20>\x20dataLonRange.y;\x0a}\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20vec2\x20nextParticle\x20=\x20texture(nextParticlesPosition,\x20v_textureCoordinates).rg;\x0a\x20\x20vec4\x20nextSpeed\x20=\x20texture(particlesSpeed,\x20v_textureCoordinates);\x0a\x20\x20float\x20speedNorm\x20=\x20nextSpeed.a;\x0a\x20\x20float\x20particleDropRate\x20=\x20dropRate\x20+\x20dropRateBump\x20*\x20speedNorm;\x0a\x0a\x20\x20vec2\x20seed1\x20=\x20nextParticle.xy\x20+\x20v_textureCoordinates;\x0a\x20\x20vec2\x20seed2\x20=\x20nextSpeed.rg\x20+\x20v_textureCoordinates;\x0a\x20\x20vec2\x20randomParticle\x20=\x20generateRandomParticle(seed1);\x0a\x20\x20float\x20randomNumber\x20=\x20rand(seed2,\x20normalRange);\x0a\x0a\x20\x20if(randomNumber\x20<\x20particleDropRate\x20||\x20particleOutbound(nextParticle))\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(randomParticle,\x200.0f,\x201.0f);\x20//\x201.0\x20means\x20this\x20is\x20a\x20random\x20particle\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(nextParticle,\x200.0f,\x200.0f);\x0a\x20\x20}\x0a}\x0a','_createCanvas','lighter'];_0x18e9=function(){return _0x2a51ec;};return _0x18e9();}const {ShaderSource:ShaderSource$1}=mars3d__namespace[_0x2319dd(-0x1d1,-0x1cb)];class ShaderManager{static[_0x2319dd(-0x237,-0x270)](){const _0x375cb8={};function _0x3040d1(_0x2bd7a5,_0x3f89be){return _0x4e5d3a(_0x2bd7a5,_0x3f89be-0x125);}return _0x375cb8[_0x3040d1(0x258,0x2de)]=[calculateSpeedShader],new ShaderSource$1(_0x375cb8);}static['getUpdatePositionShader'](){const _0x273cfe={};return _0x273cfe['sources']=[updatePositionShader],new ShaderSource$1(_0x273cfe);}static[_0x2319dd(-0x2c2,-0x24b)](){const _0x7f5c4a={};return _0x7f5c4a['sources']=[renderParticlesVertexShader],new ShaderSource$1(_0x7f5c4a);}static['getSegmentDrawFragmentShader'](){const _0x5b5e6a={};return _0x5b5e6a['sources']=[renderParticlesFragmentShader],new ShaderSource$1(_0x5b5e6a);}static['getPostProcessingPositionShader'](){const _0x439495={};return _0x439495['sources']=[postProcessingPositionFragmentShader],new ShaderSource$1(_0x439495);}}const {BufferUsage:BufferUsage$1,ClearCommand:ClearCommand$1,Color:Color$2,ComputeCommand,DrawCommand,Geometry:Geometry$1,Matrix4,Pass:Pass$1,PrimitiveType:PrimitiveType$1,RenderState,ShaderProgram,ShaderSource,VertexArray:VertexArray$1,defaultValue,defined,destroyObject}=mars3d__namespace[_0x4e5d3a(0x1b3,0x185)];class CustomPrimitive{constructor(_0x4ffca6){this['commandType']=_0x4ffca6['commandType'],this[_0xd82804(-0x138,-0x197)]=_0x4ffca6['geometry'],this[_0xd82804(-0x13e,-0x14a)]=_0x4ffca6['attributeLocations'];function _0xd82804(_0x1ffb56,_0x5c2a2e){return _0x4e5d3a(_0x5c2a2e,_0x1ffb56- -0x279);}this['primitiveType']=_0x4ffca6['primitiveType'],this[_0xd82804(-0x158,-0x17b)]=_0x4ffca6[_0x2ee11e(0x2be,0x314)]||{},this[_0xd82804(-0x156,-0xe5)]=_0x4ffca6[_0x2ee11e(0x299,0x316)],this['fragmentShaderSource']=_0x4ffca6['fragmentShaderSource'],this[_0x2ee11e(0x342,0x33f)]=_0x4ffca6['rawRenderState'],this['framebuffer']=_0x4ffca6[_0x2ee11e(0x3c4,0x360)],this['outputTexture']=_0x4ffca6[_0xd82804(-0x137,-0xcd)];function _0x2ee11e(_0x2e8055,_0x16ecad){return _0x2319dd(_0x2e8055,_0x16ecad-0x543);}this['autoClear']=defaultValue(_0x4ffca6[_0x2ee11e(0x3fd,0x37d)],![]),this['preExecute']=_0x4ffca6['preExecute'],this['show']=!![],this['commandToExecute']=undefined,this[_0x2ee11e(0x303,0x31b)]=undefined,this['isDynamic']=_0x4ffca6['isDynamic']??(()=>!![]),this['autoClear']&&(this[_0xd82804(-0x151,-0x1d4)]=new ClearCommand$1({'color':new Color$2(0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':this[_0xd82804(-0x10c,-0x189)],'pass':Pass$1['OPAQUE']}));}[_0x2319dd(-0x1b3,-0x1e5)](_0x301a26){function _0x2e5460(_0x6dbec9,_0x4049ec){return _0x4e5d3a(_0x6dbec9,_0x4049ec-0x162);}function _0x5a46c1(_0x2717ec,_0x730033){return _0x4e5d3a(_0x730033,_0x2717ec- -0x38b);}if(this['commandType']===_0x5a46c1(-0x25d,-0x22a)){const _0x4a764a={};_0x4a764a[_0x2e5460(0x328,0x31a)]=_0x301a26,_0x4a764a[_0x5a46c1(-0x24a,-0x22d)]=this['geometry'],_0x4a764a['attributeLocations']=this[_0x2e5460(0x2ca,0x29d)],_0x4a764a[_0x2e5460(0x32f,0x2b8)]=BufferUsage$1['STATIC_DRAW'];const _0x1732ed=VertexArray$1['fromGeometry'](_0x4a764a),_0x28192b={};_0x28192b['context']=_0x301a26,_0x28192b['vertexShaderSource']=this['vertexShaderSource'],_0x28192b['fragmentShaderSource']=this['fragmentShaderSource'],_0x28192b['attributeLocations']=this['attributeLocations'];const _0x25dcd1=ShaderProgram['fromCache'](_0x28192b),_0x542ff2=RenderState['fromCache'](this['rawRenderState']),_0x20af57={};return _0x20af57['owner']=this,_0x20af57['vertexArray']=_0x1732ed,_0x20af57[_0x5a46c1(-0x203,-0x1c0)]=this['primitiveType'],_0x20af57[_0x5a46c1(-0x2a7,-0x268)]=Matrix4[_0x5a46c1(-0x1ee,-0x237)],_0x20af57['renderState']=_0x542ff2,_0x20af57['shaderProgram']=_0x25dcd1,_0x20af57['framebuffer']=this['framebuffer'],_0x20af57['uniformMap']=this[_0x2e5460(0x280,0x283)],_0x20af57['pass']=Pass$1['OPAQUE'],new DrawCommand(_0x20af57);}else{if(this['commandType']==='Compute'){const _0x5afc9f={};return _0x5afc9f[_0x5a46c1(-0x25f,-0x23b)]=this,_0x5afc9f[_0x5a46c1(-0x283,-0x2fe)]=this[_0x2e5460(0x279,0x26a)],_0x5afc9f[_0x2e5460(0x272,0x283)]=this['uniformMap'],_0x5afc9f['outputTexture']=this[_0x2e5460(0x2b9,0x2a4)],_0x5afc9f['persists']=!![],new ComputeCommand(_0x5afc9f);}else throw new Error('Unknown\x20command\x20type');}}[_0x4e5d3a(0x7c,0xe3)](_0x7f192b,_0x57ff39){this['geometry']=_0x57ff39;function _0x5df2d3(_0x465202,_0x2e3608){return _0x4e5d3a(_0x2e3608,_0x465202-0x261);}function _0x2cfc56(_0xf7c9a3,_0x3dc175){return _0x2319dd(_0x3dc175,_0xf7c9a3-0x604);}defined(this[_0x2cfc56(0x416,0x3a3)])&&(this[_0x5df2d3(0x3c3,0x43a)]['vertexArray']=VertexArray$1[_0x5df2d3(0x377,0x3aa)]({'context':_0x7f192b,'geometry':this['geometry'],'attributeLocations':this[_0x2cfc56(0x3ef,0x458)],'bufferUsage':BufferUsage$1['STATIC_DRAW']}));}[_0x4e5d3a(0x182,0x1dd)](_0x55560a){if(!this['isDynamic']())return;if(!this['show']||!defined(_0x55560a))return;function _0x5435f2(_0x4757be,_0x2ebffd){return _0x4e5d3a(_0x2ebffd,_0x4757be-0x1b2);}!defined(this['commandToExecute'])&&(this['commandToExecute']=this['createCommand'](_0x55560a[_0x377005(0x2ff,0x312)]));defined(this['preExecute'])&&this[_0x377005(0x2cd,0x320)]();if(!_0x55560a[_0x377005(0x286,0x2ee)]){console['warn']('frameState.commandList\x20is\x20undefined');return;}defined(this['clearCommand'])&&_0x55560a[_0x5435f2(0x2f1,0x276)]['push'](this['clearCommand']);function _0x377005(_0x3563d3,_0x53571d){return _0x4e5d3a(_0x53571d,_0x3563d3-0x147);}defined(this['commandToExecute'])&&_0x55560a['commandList'][_0x377005(0x2b6,0x2f0)](this['commandToExecute']);}[_0x2319dd(-0x1fd,-0x23e)](){return![];}['destroy'](){function _0x5e6256(_0x2957dd,_0x5b13b6){return _0x2319dd(_0x2957dd,_0x5b13b6-0x16c);}function _0x4e293d(_0x1e4778,_0x427d23){return _0x4e5d3a(_0x1e4778,_0x427d23- -0x324);}if(defined(this[_0x4e293d(-0x17a,-0x1c2)])){var _0x3a4159;(_0x3a4159=this['commandToExecute'][_0x4e293d(-0x169,-0x1e1)])===null||_0x3a4159===void 0x0||_0x3a4159[_0x4e293d(-0x287,-0x23b)](),this['commandToExecute'][_0x5e6256(-0x6d,-0xa1)]=undefined;}return destroyObject(this);}}function deepMerge(_0x1844d9,_0x6eba5e){if(!_0x1844d9)return _0x6eba5e;if(!_0x6eba5e)return _0x1844d9;const _0x3bb7ef={..._0x6eba5e};function _0x3ec0b9(_0x4a56ae,_0x15bc92){return _0x4e5d3a(_0x4a56ae,_0x15bc92-0x292);}const _0x2627b3=_0x3bb7ef;function _0x30fd59(_0x4f2dbe,_0x6eb02a){return _0x2319dd(_0x6eb02a,_0x4f2dbe-0x51d);}for(const _0x4c0ba7 in _0x1844d9){if(Object['prototype'][_0x3ec0b9(0x3b9,0x3f0)]['call'](_0x1844d9,_0x4c0ba7)){const _0x5c16b7=_0x1844d9[_0x4c0ba7],_0x4d59de=_0x6eba5e[_0x4c0ba7];if(Array[_0x30fd59(0x3b5,0x3ce)](_0x5c16b7)){_0x2627b3[_0x4c0ba7]=_0x5c16b7['slice']();continue;}if(_0x5c16b7&&typeof _0x5c16b7==='object'){_0x2627b3[_0x4c0ba7]=deepMerge(_0x5c16b7,_0x4d59de||{});continue;}_0x5c16b7!==undefined&&(_0x2627b3[_0x4c0ba7]=_0x5c16b7);}}return _0x2627b3;}const {Cartesian2:Cartesian2$1,FrameRateMonitor,PixelDatatype:PixelDatatype$1,PixelFormat:PixelFormat$1,Sampler:Sampler$1,Texture:Texture$1,TextureMagnificationFilter:TextureMagnificationFilter$1,TextureMinificationFilter:TextureMinificationFilter$1}=mars3d__namespace['Cesium'];class WindParticlesComputing{constructor(_0x2a1f12,_0x580716,_0x46bf0f,_0xbc8a81,_0x450b64){this['context']=_0x2a1f12,this['options']=_0x46bf0f,this[_0x2185fe(0x2c2,0x347)]=_0xbc8a81,this['windData']=_0x580716,this['frameRate']=0x3c,this['frameRateAdjustment']=0x1;const _0x32f0f3={};_0x32f0f3[_0x15eefd(-0x10,-0x29)]=_0x450b64,_0x32f0f3['samplingWindow']=0x1;function _0x2185fe(_0x1ab12a,_0x15bd03){return _0x2319dd(_0x15bd03,_0x1ab12a-0x44d);}_0x32f0f3['quietPeriod']=0x0;function _0x15eefd(_0x14d209,_0x34b9a3){return _0x2319dd(_0x34b9a3,_0x14d209-0x1be);}this['frameRateMonitor']=new FrameRateMonitor(_0x32f0f3),this['initFrameRate'](),this[_0x2185fe(0x2dc,0x363)](),this['createParticlesTextures'](),this[_0x2185fe(0x27b,0x2e0)]();}[_0x2319dd(-0x18c,-0x20c)](){const _0x26fc91=()=>{function _0xfbeaa2(_0x5bdc01,_0xe4dcd0){return _0x39e2(_0xe4dcd0- -0x16d,_0x5bdc01);}function _0x4cba61(_0x14fcca,_0x547421){return _0x39e2(_0x14fcca-0x1eb,_0x547421);}this[_0x4cba61(0x2ba,0x27a)]['lastFramesPerSecond']>0x14&&(this['frameRate']=this['frameRateMonitor']['lastFramesPerSecond'],this[_0x4cba61(0x2db,0x2bc)]=0x3c/Math['max'](this['frameRate'],0x1));};_0x26fc91();function _0x5bda02(_0x18b26,_0x512d56){return _0x2319dd(_0x18b26,_0x512d56-0x24a);}const _0x2ee6e4=setInterval(_0x26fc91,0x3e8),_0x24d659=this['destroy']['bind'](this);this[_0x5bda02(-0x37,-0x1d)]=()=>{clearInterval(_0x2ee6e4),_0x24d659();};}[_0x2319dd(-0x14d,-0x171)](){function _0x738590(_0x3fb58b,_0x414930){return _0x4e5d3a(_0x414930,_0x3fb58b- -0x3a0);}const _0x4c8b15={};_0x4c8b15[_0x24c5ce(0x23f,0x25e)]=TextureMinificationFilter$1['LINEAR'],_0x4c8b15[_0x738590(-0x2a5,-0x289)]=TextureMagnificationFilter$1['LINEAR'];function _0x24c5ce(_0x120a04,_0x30f3d5){return _0x2319dd(_0x120a04,_0x30f3d5-0x41b);}const _0x590197={'context':this[_0x738590(-0x1e8,-0x1fd)],'width':this['windData']['width'],'height':this['windData']['height'],'pixelFormat':PixelFormat$1[_0x738590(-0x27c,-0x286)],'pixelDatatype':PixelDatatype$1['FLOAT'],'flipY':this[_0x24c5ce(0x29e,0x259)][_0x738590(-0x258,-0x2ab)]??![],'sampler':new Sampler$1(_0x4c8b15)};this['windTextures']={'U':new Texture$1({..._0x590197,'source':{'arrayBufferView':new Float32Array(this['windData']['u'][_0x24c5ce(0x214,0x28f)])}}),'V':new Texture$1({..._0x590197,'source':{'arrayBufferView':new Float32Array(this[_0x24c5ce(0x2e6,0x294)]['v'][_0x738590(-0x1dc,-0x159)])}})};}['createParticlesTextures'](){function _0x5af08f(_0xe02967,_0x4713bb){return _0x2319dd(_0xe02967,_0x4713bb-0x1e);}const _0x12d0db={};function _0x3e25b1(_0x1c18f8,_0x2f79aa){return _0x2319dd(_0x2f79aa,_0x1c18f8-0x37a);}_0x12d0db[_0x3e25b1(0x1bd,0x215)]=TextureMinificationFilter$1[_0x3e25b1(0x183,0x17c)],_0x12d0db['magnificationFilter']=TextureMagnificationFilter$1['NEAREST'];const _0x50b5b2={'context':this[_0x3e25b1(0x1e2,0x263)],'width':this['options'][_0x5af08f(-0x1e7,-0x16f)],'height':this['options'][_0x3e25b1(0x1ed,0x204)],'pixelFormat':PixelFormat$1['RGBA'],'pixelDatatype':PixelDatatype$1[_0x5af08f(-0x20c,-0x213)],'flipY':![],'source':{'arrayBufferView':new Float32Array(this['options']['particlesTextureSize']*this['options']['particlesTextureSize']*0x4)['fill'](0x0)},'sampler':new Sampler$1(_0x12d0db)};this['particlesTextures']={'previousParticlesPosition':new Texture$1(_0x50b5b2),'currentParticlesPosition':new Texture$1(_0x50b5b2),'nextParticlesPosition':new Texture$1(_0x50b5b2),'postProcessingPosition':new Texture$1(_0x50b5b2),'particlesSpeed':new Texture$1(_0x50b5b2)};}['destroyParticlesTextures'](){function _0x111916(_0x13935f,_0x1138c7){return _0x2319dd(_0x1138c7,_0x13935f-0x2e3);}function _0xe1bd84(_0x2a0fd5,_0x5b09b6){return _0x2319dd(_0x5b09b6,_0x2a0fd5-0x146);}Object['values'](this[_0x111916(0x103,0x101)])[_0xe1bd84(-0xda,-0x9a)](_0x430619=>_0x430619[_0x111916(0x7c,0x8d)]());}['createComputingPrimitives'](){function _0x64942a(_0x2dc0cc,_0x31e30a){return _0x4e5d3a(_0x2dc0cc,_0x31e30a- -0x21a);}function _0x476d1d(_0x1ab4e0,_0x5382f7){return _0x2319dd(_0x5382f7,_0x1ab4e0-0xe1);}this['primitives']={'calculateSpeed':new CustomPrimitive({'commandType':_0x476d1d(-0xc5,-0x48),'uniformMap':{'U':()=>this['windTextures']['U'],'V':()=>this['windTextures']['V'],'uRange':()=>new Cartesian2$1(this['windData']['u'][_0x476d1d(-0xc7,-0xf4)],this['windData']['u']['max']),'vRange':()=>new Cartesian2$1(this['windData']['v'][_0x64942a(-0x5d,-0x72)],this['windData']['v'][_0x64942a(-0x46,-0x68)]),'speedRange':()=>new Cartesian2$1(this['windData']['speed']['min'],this[_0x64942a(-0xd3,-0x51)][_0x64942a(-0x56,-0x9b)]['max']),'currentParticlesPosition':()=>this['particlesTextures']['currentParticlesPosition'],'speedScaleFactor':()=>{function _0x5e87e2(_0x4b9750,_0x40dca7){return _0x476d1d(_0x40dca7-0x34,_0x4b9750);}return(this['viewerParameters']['pixelSize']+0x32)*this['options'][_0x5e87e2(-0x30,-0x9b)];},'frameRateAdjustment':()=>this['frameRateAdjustment'],'dimension':()=>new Cartesian2$1(this['windData'][_0x476d1d(-0x193,-0x14b)],this[_0x476d1d(-0xa6,-0x127)][_0x64942a(-0x95,-0x11b)]),'minimum':()=>new Cartesian2$1(this['windData']['bounds']['west'],this['windData'][_0x476d1d(-0xcd,-0x4e)]['south']),'maximum':()=>new Cartesian2$1(this['windData'][_0x476d1d(-0xcd,-0x105)]['east'],this['windData'][_0x64942a(-0x44,-0x78)][_0x476d1d(-0x93,-0x81)])},'fragmentShaderSource':ShaderManager['getCalculateSpeedShader'](),'outputTexture':this['particlesTextures']['particlesSpeed'],'preExecute':()=>{const _0x1f0022=this['particlesTextures'][_0xb5d4f5(-0x1a8,-0x18a)];function _0xb5d4f5(_0x15d9fd,_0x12fead){return _0x476d1d(_0x15d9fd- -0x36,_0x12fead);}this[_0xb5d4f5(-0x135,-0xf4)][_0x4b66c4(0x358,0x381)]=this['particlesTextures'][_0xb5d4f5(-0x171,-0x1b8)],this[_0xb5d4f5(-0x135,-0xfc)]['currentParticlesPosition']=this['particlesTextures']['postProcessingPosition'],this['particlesTextures']['postProcessingPosition']=_0x1f0022;function _0x4b66c4(_0x476622,_0x1a32f3){return _0x476d1d(_0x1a32f3-0x4f3,_0x476622);}this['primitives'][_0x4b66c4(0x388,0x3ff)]['commandToExecute']&&(this['primitives'][_0x4b66c4(0x392,0x3ff)]['commandToExecute'][_0xb5d4f5(-0x163,-0x18c)]=this[_0x4b66c4(0x432,0x3f4)]['particlesSpeed']);},'isDynamic':()=>this['options'][_0x64942a(-0xcb,-0xe9)]}),'updatePosition':new CustomPrimitive({'commandType':'Compute','uniformMap':{'currentParticlesPosition':()=>this[_0x476d1d(-0xff,-0x10e)]['currentParticlesPosition'],'particlesSpeed':()=>this[_0x64942a(-0xda,-0xaa)]['particlesSpeed']},'fragmentShaderSource':ShaderManager['getUpdatePositionShader'](),'outputTexture':this['particlesTextures'][_0x64942a(-0x7c,-0x6a)],'preExecute':()=>{function _0x2ebd2a(_0x115a43,_0x34c02d){return _0x476d1d(_0x34c02d-0x296,_0x115a43);}function _0x480788(_0x5baa03,_0x26c1d1){return _0x476d1d(_0x26c1d1-0x4a4,_0x5baa03);}this['primitives']['updatePosition'][_0x480788(0x368,0x397)]&&(this['primitives']['updatePosition']['commandToExecute']['outputTexture']=this[_0x2ebd2a(0x1f9,0x197)]['nextParticlesPosition']);},'isDynamic':()=>this['options']['dynamic']}),'postProcessingPosition':new CustomPrimitive({'commandType':_0x476d1d(-0xc5,-0x40),'uniformMap':{'nextParticlesPosition':()=>this[_0x64942a(-0x71,-0xaa)]['nextParticlesPosition'],'particlesSpeed':()=>this['particlesTextures']['particlesSpeed'],'lonRange':()=>this[_0x64942a(0x3,-0x55)]['lonRange'],'latRange':()=>this['viewerParameters']['latRange'],'dataLonRange':()=>new Cartesian2$1(this[_0x476d1d(-0xa6,-0x4f)]['bounds'][_0x476d1d(-0x142,-0x15c)],this['windData']['bounds']['east']),'dataLatRange':()=>new Cartesian2$1(this[_0x476d1d(-0xa6,-0x55)][_0x64942a(-0x6a,-0x78)][_0x64942a(-0x128,-0xdd)],this['windData']['bounds'][_0x476d1d(-0x93,-0x74)]),'randomCoefficient':function(){return Math['random']();},'dropRate':()=>this[_0x64942a(-0x4e,-0x8c)][_0x64942a(-0xb1,-0x60)],'dropRateBump':()=>this['options'][_0x64942a(-0x112,-0x107)],'useViewerBounds':()=>this['options'][_0x64942a(-0x74,-0x66)]},'fragmentShaderSource':ShaderManager[_0x476d1d(-0x107,-0xc2)](),'outputTexture':this['particlesTextures'][_0x476d1d(-0xd7,-0xde)],'preExecute':()=>{function _0x4a0cf7(_0x24c7f6,_0x273e2c){return _0x476d1d(_0x273e2c-0x4f4,_0x24c7f6);}function _0x26d215(_0x475bd3,_0x4bf12f){return _0x476d1d(_0x4bf12f-0x60,_0x475bd3);}this[_0x4a0cf7(0x3a4,0x3d5)]['postProcessingPosition']['commandToExecute']&&(this['primitives']['postProcessingPosition'][_0x4a0cf7(0x39b,0x3e7)]['outputTexture']=this['particlesTextures'][_0x4a0cf7(0x3ea,0x41d)]);},'isDynamic':()=>this['options']['dynamic']})};}['reCreateWindTextures'](){function _0x1bbac5(_0x3f9ab9,_0x2a0eaf){return _0x4e5d3a(_0x2a0eaf,_0x3f9ab9- -0x2c8);}this[_0x1bbac5(-0x163,-0x13e)]['U']['destroy']();function _0x156679(_0x2a3c73,_0x61629b){return _0x4e5d3a(_0x2a3c73,_0x61629b-0xd5);}this['windTextures']['V'][_0x156679(0x22f,0x1be)](),this['createWindTextures']();}[_0x2319dd(-0x182,-0x181)](_0x5ad8dc){this['windData']=_0x5ad8dc,this['reCreateWindTextures']();}['updateOptions'](_0x24c3c0){const _0x48da41=_0x24c3c0['flipY']!==undefined&&_0x24c3c0[_0x1c456f(-0x113,-0x141)]!==this['options'][_0x1c456f(-0x1c4,-0x141)];function _0x1c456f(_0x400118,_0x45f18f){return _0x4e5d3a(_0x400118,_0x45f18f- -0x289);}function _0x1d8c2a(_0x40f23b,_0x10a00c){return _0x2319dd(_0x40f23b,_0x10a00c-0x49e);}this['options']=deepMerge(_0x24c3c0,this['options']),_0x48da41&&this[_0x1d8c2a(0x2c8,0x280)]();}[_0x2319dd(-0x1b9,-0x1cd)](_0x1c1ca9){const {array:_0x54c57a}=_0x1c1ca9;let {min:_0x276c05,max:_0x32e36e}=_0x1c1ca9;const _0x872828=new Float32Array(_0x54c57a[_0x2f3644(0x153,0x133)]);_0x276c05===undefined&&(console[_0x2f3644(0x17d,0x163)]('min\x20is\x20undefined,\x20calculate\x20min'),_0x276c05=Math[_0x2f3644(0x13f,0xd4)](..._0x54c57a));function _0xb464f2(_0x1b23ff,_0x31f7e4){return _0x2319dd(_0x1b23ff,_0x31f7e4-0x121);}function _0x2f3644(_0x71aa27,_0x46de92){return _0x4e5d3a(_0x46de92,_0x71aa27- -0x69);}_0x32e36e===undefined&&(console['warn']('max\x20is\x20undefined,\x20calculate\x20max'),_0x32e36e=Math['max'](..._0x54c57a));const _0x7bf398=Math['max'](Math[_0x2f3644(0x8a,0xf5)](_0x276c05),Math[_0xb464f2(-0x11f,-0x13c)](_0x32e36e));for(let _0x16d63e=0x0;_0x16d63e<_0x54c57a['length'];_0x16d63e++){const _0x310b=_0x54c57a[_0x16d63e]/_0x7bf398;_0x872828[_0x16d63e]=_0x310b;}return _0x872828;}[_0x4e5d3a(0xe8,0xe9)](){function _0x1841ac(_0x52d8b3,_0x3dadf7){return _0x2319dd(_0x3dadf7,_0x52d8b3-0x41b);}function _0x5ce439(_0x2e2e09,_0x41b395){return _0x2319dd(_0x2e2e09,_0x41b395-0x187);}Object[_0x1841ac(0x223,0x25a)](this['windTextures'])['forEach'](_0x1b1ef6=>_0x1b1ef6[_0x1841ac(0x1b4,0x144)]()),Object[_0x1841ac(0x223,0x256)](this[_0x5ce439(-0x8f,-0x59)])[_0x5ce439(-0xb5,-0x99)](_0x1d9d1d=>_0x1d9d1d['destroy']()),Object['values'](this['primitives'])[_0x1841ac(0x1fb,0x181)](_0xa69110=>_0xa69110['destroy']()),this[_0x1841ac(0x1cf,0x23d)][_0x5ce439(-0x118,-0xe0)]();}}const {Appearance,BufferUsage,Cartesian2,Color:Color$1,ComponentDatatype,Framebuffer,Geometry,GeometryAttribute,GeometryAttributes,PixelDatatype,PixelFormat,PrimitiveType,Sampler,SceneMode,Texture,TextureMagnificationFilter,TextureMinificationFilter,TextureWrap,VertexArray}=mars3d__namespace['Cesium'];class WindParticlesRendering{constructor(_0x1e12c5,_0x13fbf2,_0x45f6d7,_0x280064){this['context']=_0x1e12c5,this['options']=_0x13fbf2,this[_0x311791(0x47c,0x428)]=_0x45f6d7,this['computing']=_0x280064;(typeof this[_0x553058(-0xce,-0x49)][_0x553058(-0x99,-0x110)]!=='number'||this['options'][_0x311791(0x4a9,0x426)]<=0x0)&&(console['error']('Invalid\x20particlesTextureSize.\x20Using\x20default\x20value\x20of\x20256.'),this['options'][_0x311791(0x493,0x426)]=0x100);this['colorTable']=this[_0x553058(-0xb9,-0xb1)](),this[_0x311791(0x3c1,0x3b7)]=this[_0x553058(-0x123,-0x14c)]();function _0x311791(_0x4ae759,_0x37132d){return _0x4e5d3a(_0x4ae759,_0x37132d-0x263);}function _0x553058(_0x50bbdb,_0x3eaa77){return _0x4e5d3a(_0x3eaa77,_0x50bbdb- -0x25c);}this[_0x553058(-0x151,-0x15b)]=this[_0x311791(0x462,0x3fe)](),this['primitives']=this['createPrimitives']();}[_0x2319dd(-0x198,-0x217)](){const _0x923c16={};_0x923c16['context']=this[_0x1671aa(0x25d,0x209)],_0x923c16[_0x12a294(0x3e8,0x427)]=this['context']['drawingBufferWidth'],_0x923c16['height']=this[_0x1671aa(0x1f3,0x209)]['drawingBufferHeight'],_0x923c16[_0x12a294(0x3f7,0x3d4)]=PixelFormat['RGBA'],_0x923c16[_0x1671aa(0x217,0x223)]=PixelDatatype['UNSIGNED_BYTE'];const _0x5a3328=_0x923c16,_0xffaf41={};_0xffaf41[_0x12a294(0x4c4,0x4eb)]=this[_0x12a294(0x4c4,0x47e)],_0xffaf41['width']=this[_0x1671aa(0x19f,0x209)]['drawingBufferWidth'];function _0x12a294(_0x5706ef,_0x3fb767){return _0x4e5d3a(_0x3fb767,_0x5706ef-0x30c);}_0xffaf41[_0x1671aa(0x1cf,0x150)]=this['context']['drawingBufferHeight'];function _0x1671aa(_0x2a8f5c,_0x5ee662){return _0x2319dd(_0x2a8f5c,_0x5ee662-0x3a1);}_0xffaf41['pixelFormat']=PixelFormat[_0x12a294(0x499,0x4e6)],_0xffaf41['pixelDatatype']=PixelDatatype[_0x1671aa(0x1d1,0x1fa)];const _0x2bf86a=_0xffaf41;return{'segmentsColor':new Texture(_0x5a3328),'segmentsDepth':new Texture(_0x2bf86a)};}['createRenderingFramebuffers'](){function _0x52dcf6(_0x36a5b4,_0x2a6dbb){return _0x2319dd(_0x2a6dbb,_0x36a5b4-0x64b);}function _0x266a12(_0x3172e3,_0x5dd199){return _0x2319dd(_0x3172e3,_0x5dd199-0x235);}const _0x157219={};return _0x157219[_0x52dcf6(0x4b3,0x49d)]=this[_0x266a12(0xf2,0x9d)],_0x157219['colorTextures']=[this['textures'][_0x266a12(0x60,0x1f)]],_0x157219[_0x266a12(0x83,0xb2)]=this[_0x52dcf6(0x44f,0x4c4)][_0x266a12(0x55,0xbf)],{'segments':new Framebuffer(_0x157219)};}['destoryRenderingFramebuffers'](){function _0xf93207(_0x34ef93,_0x27a7e0){return _0x2319dd(_0x27a7e0,_0x34ef93-0x375);}Object[_0xf93207(0x17d,0x1d5)](this['framebuffers'])['forEach'](_0x571737=>{function _0x249bb6(_0x27a36b,_0x43224b){return _0xf93207(_0x27a36b- -0x3c6,_0x43224b);}_0x571737[_0x249bb6(-0x2b8,-0x327)]();});}[_0x2319dd(-0x1a2,-0x1ad)](){const _0x5302f0=new Float32Array(this['options']['colors']['flatMap'](_0x22904c=>{function _0x31801d(_0x2fa971,_0x2ffed0){return _0x39e2(_0x2fa971-0x1de,_0x2ffed0);}const _0x239697=Color$1['fromCssColorString'](_0x22904c);return[_0x239697['red'],_0x239697['green'],_0x239697[_0x31801d(0x38a,0x35b)],_0x239697['alpha']];})),_0x12f0ef={};function _0x5791ab(_0x43f4e9,_0x4935df){return _0x2319dd(_0x43f4e9,_0x4935df-0x405);}_0x12f0ef[_0x5791ab(0x233,0x248)]=TextureMinificationFilter['LINEAR'],_0x12f0ef[_0x53eb0d(0x2e3,0x2d5)]=TextureMagnificationFilter['LINEAR'],_0x12f0ef['wrapS']=TextureWrap['CLAMP_TO_EDGE'],_0x12f0ef['wrapT']=TextureWrap['CLAMP_TO_EDGE'];function _0x53eb0d(_0x7ee7de,_0x4e2f21){return _0x4e5d3a(_0x4e2f21,_0x7ee7de-0x1e8);}return new Texture({'context':this['context'],'width':this[_0x53eb0d(0x376,0x38f)][_0x5791ab(0x24c,0x21e)][_0x53eb0d(0x3a4,0x349)],'height':0x1,'pixelFormat':PixelFormat[_0x53eb0d(0x39e,0x352)],'pixelDatatype':PixelDatatype[_0x53eb0d(0x307,0x2b4)],'sampler':new Sampler(_0x12f0ef),'source':{'width':this['options'][_0x53eb0d(0x351,0x334)][_0x53eb0d(0x3a4,0x337)],'height':0x1,'arrayBufferView':_0x5302f0}});}[_0x4e5d3a(0x18b,0x115)](){const _0x54dd00=0x4,_0x478596=this[_0x1d3b94(0x31e,0x33e)]['particlesTextureSize'];let _0x3400bf=[];for(let _0x5c7fbf=0x0;_0x5c7fbf<_0x478596;_0x5c7fbf++){for(let _0x56eed9=0x0;_0x56eed9<_0x478596;_0x56eed9++){for(let _0x2d61bc=0x0;_0x2d61bc<_0x54dd00;_0x2d61bc++){_0x3400bf[_0x1a5d27(0x284,0x2df)](_0x5c7fbf/_0x478596),_0x3400bf['push'](_0x56eed9/_0x478596);}}}_0x3400bf=new Float32Array(_0x3400bf);const _0x49899a=this['options'][_0x1d3b94(0x353,0x34a)]**0x2;let _0x546ea5=[];for(let _0x519e6e=0x0;_0x519e6e<_0x49899a;_0x519e6e++){_0x546ea5['push'](-0x1,-0x1,0x0,-0x1,0x1,0x0,0x1,-0x1,0x0,0x1,0x1,0x0);}_0x546ea5=new Float32Array(_0x546ea5);let _0xcc453c=[];for(let _0x547492=0x0,_0x43d764=0x0;_0x547492<_0x49899a;_0x547492++){_0xcc453c[_0x1a5d27(0x284,0x2ff)](_0x43d764+0x0,_0x43d764+0x1,_0x43d764+0x2,_0x43d764+0x2,_0x43d764+0x1,_0x43d764+0x3),_0x43d764+=_0x54dd00;}_0xcc453c=new Uint32Array(_0xcc453c);const _0x4cabe5={};_0x4cabe5['componentDatatype']=ComponentDatatype[_0x1d3b94(0x2af,0x2dd)],_0x4cabe5['componentsPerAttribute']=0x2,_0x4cabe5['values']=_0x3400bf;function _0x1a5d27(_0xe22d9f,_0x35b336){return _0x4e5d3a(_0x35b336,_0xe22d9f-0x115);}const _0xf24742={};_0xf24742['componentDatatype']=ComponentDatatype[_0x1d3b94(0x2af,0x22f)],_0xf24742[_0x1a5d27(0x251,0x1f5)]=0x3,_0xf24742['values']=_0x546ea5;function _0x1d3b94(_0x27fa89,_0x456aae){return _0x2319dd(_0x456aae,_0x27fa89-0x4e0);}const _0x6499a5=new Geometry({'attributes':new GeometryAttributes({'st':new GeometryAttribute(_0x4cabe5),'normal':new GeometryAttribute(_0xf24742)}),'indices':_0xcc453c});return _0x6499a5;}[_0x4e5d3a(0xf2,0xed)](_0x4ce231){const _0x277c6c={'viewport':undefined,'depthTest':undefined,'depthMask':undefined,'blending':undefined,..._0x4ce231};return Appearance['getDefaultRenderState'](!![],![],_0x277c6c);}['createPrimitives'](){const _0x17af08={};_0x17af08['st']=0x0,_0x17af08['normal']=0x1;function _0x36bab8(_0xece339,_0x45b20a){return _0x2319dd(_0x45b20a,_0xece339-0x1a1);}const _0x516aa6={};_0x516aa6['enabled']=!![];const _0x3d037d={};_0x3d037d['enabled']=!![],_0x3d037d['blendEquation']=WebGLRenderingContext['FUNC_ADD'],_0x3d037d['blendFuncSource']=WebGLRenderingContext['SRC_ALPHA'],_0x3d037d[_0x36bab8(-0x4c,0x1d)]=WebGLRenderingContext[_0x36bab8(-0x10,0x62)];const _0x473906={};_0x473906['viewport']=undefined,_0x473906['depthTest']=_0x516aa6,_0x473906['depthMask']=!![];function _0x377b4a(_0x2a3dec,_0x419deb){return _0x2319dd(_0x2a3dec,_0x419deb-0x275);}_0x473906[_0x36bab8(-0x55,-0x8a)]=_0x3d037d;const _0x55787b=new CustomPrimitive({'commandType':_0x36bab8(-0x81,-0x28),'attributeLocations':_0x17af08,'geometry':this['createSegmentsGeometry'](),'primitiveType':PrimitiveType['TRIANGLES'],'uniformMap':{'previousParticlesPosition':()=>this['computing']['particlesTextures'][_0x36bab8(-0xb2,-0x42)],'currentParticlesPosition':()=>this[_0x377b4a(0xf2,0xfd)]['particlesTextures'][_0x377b4a(0x85,0x59)],'postProcessingPosition':()=>this['computing'][_0x377b4a(0x77,0x95)][_0x377b4a(0xb4,0xbd)],'particlesSpeed':()=>this['computing'][_0x36bab8(-0x3f,-0x38)]['particlesSpeed'],'frameRateAdjustment':()=>this[_0x36bab8(0x29,0xe)]['frameRateAdjustment'],'colorTable':()=>this[_0x36bab8(-0x54,-0x59)],'domain':()=>{var _0x5ec3f1,_0x5447e5;function _0x1e4860(_0x207872,_0x1ef643){return _0x377b4a(_0x1ef643,_0x207872-0x3c2);}const _0x502213=new Cartesian2(((_0x5ec3f1=this[_0x1e4860(0x475,0x4d0)]['domain'])===null||_0x5ec3f1===void 0x0?void 0x0:_0x5ec3f1['min'])??this[_0x1e4860(0x4bf,0x456)]['windData'][_0xddde6c(0xa7,0x6a)][_0x1e4860(0x48f,0x44e)],((_0x5447e5=this['options']['domain'])===null||_0x5447e5===void 0x0?void 0x0:_0x5447e5['max'])??this['computing'][_0xddde6c(0x63,0xb4)][_0xddde6c(0x2d,0x6a)][_0x1e4860(0x499,0x44b)]);function _0xddde6c(_0x567667,_0xc00e9){return _0x377b4a(_0x567667,_0xc00e9- -0x3a);}return _0x502213;},'displayRange':()=>{function _0x4aebf4(_0x2d4b25,_0x32259b){return _0x36bab8(_0x32259b-0x18a,_0x2d4b25);}var _0x5b689d,_0x578c96;function _0x3e9251(_0x466054,_0x332446){return _0x377b4a(_0x466054,_0x332446-0x35f);}const _0x45ce25=new Cartesian2(((_0x5b689d=this[_0x3e9251(0x39e,0x412)]['displayRange'])===null||_0x5b689d===void 0x0?void 0x0:_0x5b689d['min'])??this['computing']['windData']['speed'][_0x4aebf4(0x1c2,0x183)],((_0x578c96=this[_0x3e9251(0x392,0x412)][_0x4aebf4(0x1df,0x16d)])===null||_0x578c96===void 0x0?void 0x0:_0x578c96[_0x3e9251(0x472,0x436)])??this['computing']['windData']['speed']['max']);return _0x45ce25;},'particleHeight':()=>this['options'][_0x36bab8(-0x41,0x3d)]||0x0,'aspect':()=>this[_0x377b4a(0xce,0xdd)]['drawingBufferWidth']/this[_0x36bab8(0x9,0x8b)]['drawingBufferHeight'],'pixelSize':()=>this['viewerParameters'][_0x377b4a(0x50,0xc3)],'lineWidth':()=>{function _0x4d2570(_0x3c6d8b,_0x1833d7){return _0x377b4a(_0x1833d7,_0x3c6d8b- -0x11e);}const _0x28ddf2={};function _0x4ae83d(_0x5d3251,_0xf055ba){return _0x36bab8(_0xf055ba- -0xff,_0x5d3251);}_0x28ddf2['min']=0x1,_0x28ddf2[_0x4d2570(-0x47,-0x71)]=0x2;const _0x2bd9d4=this['options']['lineWidth']||_0x28ddf2;return new Cartesian2(_0x2bd9d4['min'],_0x2bd9d4[_0x4d2570(-0x47,-0x40)]);},'lineLength':()=>{function _0x29eb7c(_0x177c2d,_0xbdb5d5){return _0x36bab8(_0xbdb5d5- -0x1ab,_0x177c2d);}const _0x3c4801={};_0x3c4801[_0x29eb7c(-0x1e8,-0x1b2)]=0x14;function _0x4fb327(_0x245512,_0x187c56){return _0x377b4a(_0x245512,_0x187c56-0xcc);}_0x3c4801[_0x29eb7c(-0x1ee,-0x1a8)]=0x64;const _0x1e00b3=this['options']['lineLength']||_0x3c4801;return new Cartesian2(_0x1e00b3[_0x29eb7c(-0x1e5,-0x1b2)],_0x1e00b3['max']);},'is3D':()=>this[_0x36bab8(0x16,-0x18)][_0x36bab8(-0x78,-0x6)]===SceneMode[_0x36bab8(-0x23,-0x18)],'segmentsDepthTexture':()=>this[_0x36bab8(-0x5b,0x22)]['segmentsDepth']},'vertexShaderSource':ShaderManager['getSegmentDrawVertexShader'](),'fragmentShaderSource':ShaderManager[_0x377b4a(0x24,0x6f)](),'rawRenderState':this['createRawRenderState'](_0x473906)}),_0x151a57={};return _0x151a57['segments']=_0x55787b,_0x151a57;}['onParticlesTextureSizeChange'](){const _0x11e368=this['createSegmentsGeometry']();this['primitives']['segments']['geometry']=_0x11e368;function _0x57c310(_0x57017a,_0x4e7471){return _0x4e5d3a(_0x57017a,_0x4e7471-0x3a9);}const _0x59aa34={};function _0x191dfd(_0x46ed32,_0x40bd4c){return _0x4e5d3a(_0x46ed32,_0x40bd4c-0x1e6);}_0x59aa34[_0x191dfd(0x3b3,0x39e)]=this[_0x191dfd(0x32b,0x39e)],_0x59aa34[_0x191dfd(0x330,0x327)]=_0x11e368,_0x59aa34[_0x191dfd(0x389,0x321)]=this['primitives'][_0x191dfd(0x2cd,0x2c5)]['attributeLocations'],_0x59aa34[_0x191dfd(0x36a,0x33c)]=BufferUsage['STATIC_DRAW'];const _0x22e75c=VertexArray[_0x57c310(0x4d8,0x4bf)](_0x59aa34);this['primitives'][_0x57c310(0x419,0x488)]['commandToExecute']&&(this['primitives']['segments'][_0x57c310(0x507,0x50b)][_0x57c310(0x43d,0x499)]=_0x22e75c);}[_0x4e5d3a(0x141,0x184)](){this['colorTable'][_0x4f05ec(-0x163,-0x1b8)]();function _0x4f05ec(_0x589921,_0x3e970a){return _0x2319dd(_0x589921,_0x3e970a-0xaf);}function _0x1b57f0(_0x3baf41,_0x554062){return _0x2319dd(_0x554062,_0x3baf41-0x28e);}this['colorTable']=this[_0x4f05ec(-0x155,-0xfe)]();}['updateOptions'](_0x4d0b62){const _0x17fade=_0x4d0b62[_0x50a50c(0x3f8,0x461)]&&JSON['stringify'](_0x4d0b62[_0x40048f(0x2bb,0x26b)])!==JSON[_0x40048f(0x2b3,0x2cd)](this[_0x50a50c(0x41d,0x3fd)]['colors']);this['options']=deepMerge(_0x4d0b62,this['options']);function _0x50a50c(_0x48b764,_0xcf8b3){return _0x4e5d3a(_0xcf8b3,_0x48b764-0x28f);}function _0x40048f(_0x177944,_0x5456d0){return _0x2319dd(_0x177944,_0x5456d0-0x452);}_0x17fade&&this['onColorTableChange']();}['destroy'](){Object['values'](this[_0x5124d0(-0x2d,0x4a)])['forEach'](_0x3bf38a=>{_0x3bf38a['destroy']();}),Object['values'](this[_0x5dfe53(-0x1e8,-0x246)])['forEach'](_0x203fd8=>{function _0x4101e5(_0x52462a,_0x6a1c59){return _0x5124d0(_0x6a1c59,_0x52462a-0x12c);}_0x203fd8[_0x4101e5(0x154,0x172)]();});function _0x5124d0(_0x459328,_0x308321){return _0x4e5d3a(_0x459328,_0x308321- -0xc1);}function _0x5dfe53(_0x37d220,_0x254c6a){return _0x4e5d3a(_0x254c6a,_0x37d220- -0x338);}this['colorTable'][_0x5124d0(-0x34,0x28)]();}}const {ClearCommand,Color,Pass}=mars3d__namespace[_0x2319dd(-0x173,-0x1cb)];class WindParticleSystem{constructor(_0xdef03f,_0x4442e8,_0x2b7298,_0x2213d6,_0xa190c5){this[_0x2e961f(0x9f,0x5a)]=_0xdef03f,this['options']=_0x2b7298;function _0x4e6e43(_0xf45dcf,_0x3cc00b){return _0x2319dd(_0xf45dcf,_0x3cc00b-0x6e3);}function _0x2e961f(_0x469d29,_0x2afed7){return _0x2319dd(_0x469d29,_0x2afed7-0x1f2);}this['viewerParameters']=_0x2213d6,this['computing']=new WindParticlesComputing(_0xdef03f,_0x4442e8,_0x2b7298,_0x2213d6,_0xa190c5),this[_0x2e961f(0xb1,0x87)]=new WindParticlesRendering(_0xdef03f,_0x2b7298,_0x2213d6,this[_0x4e6e43(0x5b1,0x56b)]),this['clearFramebuffers']();}['getPrimitives'](){function _0x23f292(_0x183da7,_0x205564){return _0x2319dd(_0x183da7,_0x205564-0x5c7);}const _0x208b44=[this['computing']['primitives']['calculateSpeed'],this[_0x23f292(0x3f4,0x44f)][_0x23f292(0x39b,0x3c7)][_0x369a6d(0x2bb,0x2b9)],this['computing'][_0x23f292(0x35e,0x3c7)]['postProcessingPosition'],this['rendering'][_0x23f292(0x392,0x3c7)][_0x369a6d(0x264,0x27d)]];function _0x369a6d(_0x409750,_0x6a6a60){return _0x4e5d3a(_0x6a6a60,_0x409750-0x185);}return _0x208b44;}[_0x2319dd(-0x1ca,-0x24a)](){function _0x2cddd3(_0x5a7449,_0x2b708f){return _0x4e5d3a(_0x5a7449,_0x2b708f- -0x41);}function _0x8b0f41(_0x188776,_0x519855){return _0x4e5d3a(_0x519855,_0x188776- -0x7a);}const _0x5eaa0a=new ClearCommand({'color':new Color(0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':undefined,'pass':Pass[_0x8b0f41(0xdb,0xcb)]});Object[_0x2cddd3(0x112,0x126)](this['rendering']['framebuffers'])['forEach'](_0x3c6f8a=>{_0x5eaa0a['framebuffer']=this['rendering'][_0x48c924(-0x2c2,-0x306)][_0x3c6f8a];function _0x1e9ec9(_0x21d041,_0x534f54){return _0x8b0f41(_0x21d041-0x24f,_0x534f54);}function _0x48c924(_0x150681,_0xfd6358){return _0x8b0f41(_0xfd6358- -0x397,_0x150681);}_0x5eaa0a['execute'](this[_0x1e9ec9(0x38d,0x414)]);});}[_0x4e5d3a(0x15c,0xf4)](_0x4846ea){let _0x3c75fd=![];_0x4846ea[_0x14e1c5(0x1e2,0x203)]&&this['options'][_0x14e1c5(0x1e2,0x1ba)]!==_0x4846ea['particlesTextureSize']&&(_0x3c75fd=!![]);const _0x22d1c5=deepMerge(_0x4846ea,this['options']);if(_0x22d1c5[_0x20bae0(0x11c,0xad)]<0x1)throw new Error('particlesTextureSize\x20must\x20be\x20greater\x20than\x200');function _0x14e1c5(_0x54ee2a,_0x1b00fe){return _0x2319dd(_0x1b00fe,_0x54ee2a-0x36f);}function _0x20bae0(_0x3e16d7,_0x3234d4){return _0x2319dd(_0x3234d4,_0x3e16d7-0x2a9);}this['options']=_0x22d1c5,this[_0x20bae0(0x13e,0x1b1)][_0x14e1c5(0x1f4,0x270)](_0x4846ea),this['computing']['updateOptions'](_0x4846ea),_0x3c75fd&&(this[_0x20bae0(0x131,0x157)]['destroyParticlesTextures'](),this['computing'][_0x20bae0(0x82,0x4a)](),this['rendering']['onParticlesTextureSizeChange']());}[_0x4e5d3a(0x1e3,0x1b3)](_0x2e91ea){function _0x3f26f8(_0x197f67,_0x1b3e10){return _0x4e5d3a(_0x1b3e10,_0x197f67-0x3ac);}this[_0x4b923f(0xde,0xf3)]=_0x2e91ea;function _0x4b923f(_0x30b988,_0x36a067){return _0x2319dd(_0x36a067,_0x30b988-0x269);}this['computing'][_0x4b923f(0xde,0x143)]=_0x2e91ea,this['rendering'][_0x3f26f8(0x571,0x5ed)]=_0x2e91ea;}[_0x2319dd(-0x2c3,-0x267)](){function _0x5932bc(_0x4a5b0b,_0x5924ff){return _0x4e5d3a(_0x5924ff,_0x4a5b0b-0x2cf);}this['computing'][_0x5932bc(0x3b8,0x421)](),this['rendering']['destroy']();}}const Cesium$1=mars3d__namespace[_0x2319dd(-0x1d7,-0x1cb)],BaseLayer$1=mars3d__namespace[_0x2319dd(-0x1e1,-0x191)]['BaseLayer'],_0x42863d={};_0x42863d[_0x2319dd(-0x186,-0x1a8)]=0x1,_0x42863d['max']=0x2;const _0x4fe210={};_0x4fe210['min']=0x14,_0x4fe210['max']=0x64;function _0x39e2(_0x4b0b51,_0x2e6051){const _0x18e9ba=_0x18e9();return _0x39e2=function(_0x39e299,_0x26e277){_0x39e299=_0x39e299-0xa7;let _0x38099c=_0x18e9ba[_0x39e299];return _0x38099c;},_0x39e2(_0x4b0b51,_0x2e6051);}const _0x3d9a68={};_0x3d9a68['particlesTextureSize']=0x64,_0x3d9a68[_0x4e5d3a(0x192,0x16e)]=0x0;function _0x2319dd(_0x33e855,_0x5e7495){return _0x39e2(_0x5e7495- -0x31b,_0x33e855);}_0x3d9a68['lineWidth']=_0x42863d,_0x3d9a68['lineLength']=_0x4fe210,_0x3d9a68['speedFactor']=0x1,_0x3d9a68[_0x4e5d3a(0x23b,0x1ba)]=0.003,_0x3d9a68['dropRateBump']=0.001,_0x3d9a68['colors']=['rgb(206,255,255)'],_0x3d9a68[_0x2319dd(-0x1d8,-0x208)]=![],_0x3d9a68[_0x2319dd(-0x264,-0x21f)]=!![];const DEF_OPTIONS=_0x3d9a68;class WindLayer extends BaseLayer$1{constructor(_0x1aa0ef={}){function _0x13dfe(_0x450c76,_0x2fc716){return _0x2319dd(_0x2fc716,_0x450c76-0x4ad);}_0x1aa0ef={...DEF_OPTIONS,..._0x1aa0ef},super(_0x1aa0ef),this[_0x13dfe(0x2f9,0x2ff)](_0x1aa0ef,_0x1aa0ef);}get['layer'](){return this['primitives'];}get['data'](){return this['options']['data'];}set['data'](_0x50e23a){this['options']['data']=_0x50e23a,this['setData'](_0x50e23a);}get['colors'](){function _0x2e3889(_0x51b156,_0xb455fe){return _0x4e5d3a(_0x51b156,_0xb455fe-0x237);}return this[_0x2e3889(0x386,0x3c5)]['colors'];}set[_0x2319dd(-0x1d2,-0x1e7)](_0x1f3133){function _0x180571(_0x4100ef,_0x25b022){return _0x2319dd(_0x25b022,_0x4100ef-0x33d);}this[_0x180571(0x17b,0x112)]['colors']=_0x1f3133;const _0x2f1de5={};_0x2f1de5[_0x180571(0x156,0x135)]=_0x1f3133;function _0x4e03a2(_0x35e169,_0x4504f3){return _0x4e5d3a(_0x4504f3,_0x35e169-0x33f);}this['_setOptionsHook'](this[_0x180571(0x17b,0x121)],_0x2f1de5);}['_mountedHook'](){}[_0x2319dd(-0x24e,-0x209)](){this['scene']=this[_0xd039fd(0x331,0x36d)]['scene'];function _0x51539d(_0x1fcf01,_0x1c1a81){return _0x2319dd(_0x1fcf01,_0x1c1a81-0x481);}this['camera']=this['_map'][_0x51539d(0x279,0x2c0)];this['options'][_0x51539d(0x37f,0x2fb)]&&this['setData'](this[_0x51539d(0x29e,0x2bf)]['data']);if(!this[_0xd039fd(0x3c3,0x38a)])return;this[_0x51539d(0x351,0x2f6)]={'lonRange':new Cesium$1[(_0x51539d(0x1f2,0x24e))](-0xb4,0xb4),'latRange':new Cesium$1[(_0x51539d(0x2bd,0x24e))](-0x5a,0x5a),'pixelSize':0x3e8,'sceneMode':this[_0x51539d(0x2a6,0x2b3)][_0x51539d(0x29e,0x2ec)]},this[_0xd039fd(0x3f7,0x39f)](),this[_0x51539d(0x23b,0x212)]=new WindParticleSystem(this[_0xd039fd(0x38b,0x343)]['context'],this['windData'],this['options'],this[_0xd039fd(0x32e,0x386)],this[_0x51539d(0x28c,0x2b3)]),this['primitives']=this['particleSystem']['getPrimitives'](),this[_0xd039fd(0x2b9,0x311)]['forEach'](_0x57c20b=>{function _0xf7f953(_0x1aa68a,_0x334447){return _0xd039fd(_0x1aa68a,_0x334447- -0x10a);}function _0x54a073(_0x1a1643,_0x1ee4ce){return _0x51539d(_0x1a1643,_0x1ee4ce-0x215);}this['scene'][_0xf7f953(0x199,0x207)][_0x54a073(0x3d7,0x435)](_0x57c20b);});function _0xd039fd(_0x2b24c9,_0x5d1554){return _0x2319dd(_0x2b24c9,_0x5d1554-0x511);}this[_0xd039fd(0x367,0x350)]['percentageChanged']=0.01,this[_0xd039fd(0x2e6,0x350)]['changed']['addEventListener'](this[_0xd039fd(0x382,0x39f)]['bind'](this)),this[_0x51539d(0x312,0x2b3)]['morphComplete']['addEventListener'](this['updateViewerParameters'][_0xd039fd(0x311,0x352)](this)),window[_0x51539d(0x277,0x22b)](_0xd039fd(0x234,0x2a3),this['updateViewerParameters']['bind'](this));}['_removedHook'](){this['camera'][_0x56cd82(0x2fb,0x281)][_0x56cd82(0x1c1,0x193)](this[_0x56cd82(0x2a5,0x289)]['bind'](this)),this['scene']['morphComplete']['removeEventListener'](this[_0x1329c9(0x387,0x364)]['bind'](this)),window['removeEventListener'](_0x1329c9(0x28b,0x207),this[_0x56cd82(0x203,0x289)]['bind'](this));this[_0x1329c9(0x2f9,0x31d)]&&(this['primitives'][_0x56cd82(0x15e,0x1db)](_0x138927=>{function _0x2d9633(_0x419037,_0x455792){return _0x1329c9(_0x455792-0x41,_0x419037);}function _0x127235(_0x4440d9,_0x1acc7b){return _0x56cd82(_0x1acc7b,_0x4440d9- -0xe0);}this[_0x2d9633(0x2ea,0x36c)]['primitives'][_0x2d9633(0x341,0x301)](_0x138927);}),delete this[_0x1329c9(0x2f9,0x2f9)]);function _0x1329c9(_0x67379d,_0x46a8cf){return _0x4e5d3a(_0x46a8cf,_0x67379d-0x1a9);}function _0x56cd82(_0x3cc481,_0x158132){return _0x2319dd(_0x3cc481,_0x158132-0x3fb);}this[_0x56cd82(0x135,0x18c)]&&(this[_0x56cd82(0x17c,0x18c)]['destroy'](),delete this['particleSystem']);}['setData'](_0x5442e4,_0x134c9a){function _0x262ef8(_0x55404a,_0x4280dc){return _0x4e5d3a(_0x4280dc,_0x55404a-0x9e);}this[_0x243c2b(-0x1b4,-0x165)]=this[_0x262ef8(0x221,0x231)](_0x5442e4);function _0x243c2b(_0x55aec2,_0x34c478){return _0x4e5d3a(_0x34c478,_0x55aec2- -0x37d);}if(_0x134c9a){this[_0x262ef8(0x18c,0x1f0)](),this[_0x243c2b(-0x236,-0x27f)]();return;}this[_0x262ef8(0x17f,0x166)]?(this['particleSystem']['computing'][_0x262ef8(0x26d,0x2da)](this['windData']),this[_0x243c2b(-0x1fb,-0x23b)]['requestRender']()):this['_addedHook']();}[_0x4e5d3a(0x13c,0x19c)](_0x32cbcf,_0x2fb6e6){function _0x384f41(_0x4e9d02,_0x416074){return _0x2319dd(_0x4e9d02,_0x416074-0x5c4);}function _0x29fe5a(_0x3621df,_0x26b67d){return _0x2319dd(_0x3621df,_0x26b67d- -0xb6);}this['particleSystem']&&(this[_0x29fe5a(-0x2d0,-0x325)][_0x384f41(0x38f,0x368)](_0x2fb6e6),this['scene']['requestRender']());}[_0x2319dd(-0x1ed,-0x1cd)](_0x3aca89){var _0x542579,_0x5159ea;const _0x144d9={..._0x3aca89};function _0x18edf7(_0x5d0313,_0x23af18){return _0x2319dd(_0x23af18,_0x5d0313-0x3e9);}const _0x4261a3=_0x144d9;!_0x4261a3['height']&&_0x4261a3['rows']&&(_0x4261a3['height']=_0x4261a3['rows']);!_0x4261a3[_0x18edf7(0x175,0x17a)]&&_0x4261a3['cols']&&(_0x4261a3['width']=_0x4261a3['cols']);function _0x5439ae(_0x2a3a6a,_0x14afbf){return _0x4e5d3a(_0x14afbf,_0x2a3a6a-0x13a);}!_0x4261a3['bounds']&&(_0x4261a3['bounds']={'west':_0x4261a3[_0x5439ae(0x301,0x2a0)],'south':_0x4261a3['ymin'],'east':_0x4261a3[_0x18edf7(0x19a,0x20c)],'north':_0x4261a3[_0x18edf7(0x1a3,0x20c)]});if(!_0x4261a3['u']){const _0x255495={};_0x255495[_0x5439ae(0x2fe,0x374)]=_0x3aca89['udata'],_0x255495['min']=_0x3aca89[_0x18edf7(0x1f6,0x173)],_0x255495['max']=_0x3aca89['umax'],_0x4261a3['u']=_0x255495;}if(!_0x4261a3['v']){const _0x16d976={};_0x16d976[_0x18edf7(0x25d,0x24d)]=_0x3aca89['vdata'],_0x16d976[_0x18edf7(0x241,0x241)]=_0x3aca89[_0x18edf7(0x1b7,0x1a9)],_0x16d976['max']=_0x3aca89[_0x18edf7(0x1a2,0x18c)],_0x4261a3['v']=_0x16d976;}if(((_0x542579=_0x4261a3['speed'])===null||_0x542579===void 0x0?void 0x0:_0x542579[_0x5439ae(0x2e2,0x268)])===undefined||((_0x5159ea=_0x4261a3[_0x5439ae(0x2b9,0x234)])===null||_0x5159ea===void 0x0?void 0x0:_0x5159ea[_0x18edf7(0x24b,0x2ca)])===undefined||_0x4261a3['speed']['array']===undefined){const _0xf182ac={'array':new Float32Array(_0x4261a3['u']['array']['length']),'min':Number['MAX_VALUE'],'max':Number['MIN_VALUE']};for(let _0x3f9c00=0x0;_0x3f9c00<_0x4261a3['u']['array']['length'];_0x3f9c00++){_0xf182ac[_0x5439ae(0x2fe,0x331)][_0x3f9c00]=Math['sqrt'](_0x4261a3['u']['array'][_0x3f9c00]*_0x4261a3['u']['array'][_0x3f9c00]+_0x4261a3['v']['array'][_0x3f9c00]*_0x4261a3['v']['array'][_0x3f9c00]),_0xf182ac['array'][_0x3f9c00]!==0x0&&(_0xf182ac[_0x5439ae(0x2e2,0x2e1)]=Math[_0x18edf7(0x241,0x256)](_0xf182ac['min'],_0xf182ac['array'][_0x3f9c00]),_0xf182ac['max']=Math['max'](_0xf182ac['max'],_0xf182ac[_0x5439ae(0x2fe,0x305)][_0x3f9c00]));}_0x4261a3[_0x18edf7(0x218,0x1e3)]=_0xf182ac;}return _0x4261a3;}['updateViewerParameters'](){var _0x43711a;const _0x5deaf5=this['scene'],_0x52852f=_0x5deaf5['canvas'],_0x14ef88={};_0x14ef88['x']=0x0,_0x14ef88['y']=0x0;const _0x447a97={};_0x447a97['x']=0x0,_0x447a97['y']=_0x52852f['clientHeight'];const _0x566a5a={};_0x566a5a['x']=_0x52852f['clientWidth'],_0x566a5a['y']=0x0;const _0x41909f={};_0x41909f['x']=_0x52852f[_0xbf2ad5(0x3f6,0x45a)],_0x41909f['y']=_0x52852f['clientHeight'];const _0x54f99d=[_0x14ef88,_0x447a97,_0x566a5a,_0x41909f];let _0x47013a=0xb4;function _0x37904f(_0x585276,_0x87cd39){return _0x2319dd(_0x585276,_0x87cd39-0x508);}let _0x470f6a=-0xb4,_0x6c8a6a=0x5a,_0x54fc47=-0x5a;function _0xbf2ad5(_0x1b1fdb,_0x21212e){return _0x2319dd(_0x21212e,_0x1b1fdb-0x563);}let _0x331c04=![];for(const _0x10e73e of _0x54f99d){const _0x76e29f=_0x5deaf5[_0xbf2ad5(0x3a2,0x362)][_0xbf2ad5(0x38d,0x3fe)](new Cesium$1['Cartesian2'](_0x10e73e['x'],_0x10e73e['y']),_0x5deaf5[_0xbf2ad5(0x30f,0x299)]['ellipsoid']);if(!_0x76e29f){_0x331c04=!![];break;}const _0x5f3d43=_0x5deaf5['globe']['ellipsoid'][_0xbf2ad5(0x333,0x337)](_0x76e29f),_0x17717b=Cesium$1['Math']['toDegrees'](_0x5f3d43[_0x37904f(0x2df,0x2e2)]),_0x3d1c4b=Cesium$1['Math']['toDegrees'](_0x5f3d43['latitude']);_0x47013a=Math[_0xbf2ad5(0x3bb,0x340)](_0x47013a,_0x17717b),_0x470f6a=Math['max'](_0x470f6a,_0x17717b),_0x6c8a6a=Math['min'](_0x6c8a6a,_0x3d1c4b),_0x54fc47=Math[_0x37904f(0x375,0x36a)](_0x54fc47,_0x3d1c4b);}if(!_0x331c04){const _0x27c993=new Cesium$1[(_0xbf2ad5(0x330,0x34d))](Math['max'](this['windData']['bounds']['west'],_0x47013a),Math['min'](this['windData']['bounds']['east'],_0x470f6a)),_0x26b216=new Cesium$1['Cartesian2'](Math['max'](this['windData'][_0xbf2ad5(0x3b5,0x3a5)]['south'],_0x6c8a6a),Math[_0x37904f(0x36a,0x360)](this[_0x37904f(0x2ff,0x381)][_0xbf2ad5(0x3b5,0x357)][_0xbf2ad5(0x3ef,0x37d)],_0x54fc47)),_0x522746=(_0x27c993['y']-_0x27c993['x'])*0.05,_0x329dd3=(_0x26b216['y']-_0x26b216['x'])*0.05;_0x27c993['x']=Math[_0x37904f(0x328,0x36a)](this['windData'][_0x37904f(0x3ae,0x35a)]['west'],_0x27c993['x']-_0x522746),_0x27c993['y']=Math[_0xbf2ad5(0x3bb,0x357)](this['windData'][_0xbf2ad5(0x3b5,0x3e1)][_0xbf2ad5(0x38f,0x32b)],_0x27c993['y']+_0x522746),_0x26b216['x']=Math['max'](this[_0x37904f(0x35b,0x381)]['bounds']['south'],_0x26b216['x']-_0x329dd3),_0x26b216['y']=Math['min'](this[_0x37904f(0x315,0x381)]['bounds']['north'],_0x26b216['y']+_0x329dd3),this[_0xbf2ad5(0x3d8,0x437)][_0x37904f(0x331,0x32b)]=_0x27c993,this['viewerParameters'][_0xbf2ad5(0x308,0x2e7)]=_0x26b216;const _0x173f2d=this[_0x37904f(0x348,0x381)]['bounds']['east']-this[_0x37904f(0x31b,0x381)]['bounds'][_0xbf2ad5(0x340,0x2c5)],_0x580d59=this[_0xbf2ad5(0x3dc,0x383)][_0xbf2ad5(0x3b5,0x3f8)]['north']-this[_0x37904f(0x30d,0x381)]['bounds'][_0x37904f(0x323,0x2f5)],_0x41182d=(_0x27c993['y']-_0x27c993['x'])/_0x173f2d,_0x30f013=(_0x26b216['y']-_0x26b216['x'])/_0x580d59,_0x4e0071=Math['min'](_0x41182d,_0x30f013),_0x3673f8=0x3e8*_0x4e0071;_0x3673f8>0x0&&(this['viewerParameters']['pixelSize']=Math['max'](0x0,Math[_0x37904f(0x3c2,0x360)](0x3e8,_0x3673f8)));}this['viewerParameters'][_0x37904f(0x360,0x2ef)]=this['scene']['mode'],(_0x43711a=this['particleSystem'])===null||_0x43711a===void 0x0||_0x43711a[_0xbf2ad5(0x3c6,0x368)](this['viewerParameters']);}['getDataAtLonLat'](_0x385a3f,_0x2011b4){const {bounds:_0xd9e8b5,width:_0x84afb,height:_0x3d67e2,u:_0x463098,v:_0x45fe98,speed:_0x125423}=this['windData'],{flipY:_0xd5d106}=this['options'];if(_0x385a3f<_0xd9e8b5['west']||_0x385a3f>_0xd9e8b5['east']||_0x2011b4<_0xd9e8b5['south']||_0x2011b4>_0xd9e8b5['north'])return null;const _0x18b62f=(_0x385a3f-_0xd9e8b5[_0x196910(0x2cb,0x284)])/(_0xd9e8b5['east']-_0xd9e8b5['west'])*(_0x84afb-0x1);let _0x305b40=(_0x2011b4-_0xd9e8b5[_0x23e9bb(0x30c,0x34c)])/(_0xd9e8b5[_0x23e9bb(0x3ab,0x418)]-_0xd9e8b5[_0x196910(0x2db,0x2f1)])*(_0x3d67e2-0x1);_0xd5d106&&(_0x305b40=_0x3d67e2-0x1-_0x305b40);const _0x4febac=Math[_0x23e9bb(0x350,0x343)](_0x18b62f),_0x2b1e82=Math[_0x196910(0x31f,0x368)](_0x305b40),_0x5af2ab=Math['floor'](_0x18b62f),_0x268afb=Math[_0x23e9bb(0x377,0x302)](_0x5af2ab+0x1,_0x84afb-0x1),_0x2c5b54=Math['floor'](_0x305b40);function _0x23e9bb(_0x22fc44,_0x5827bc){return _0x2319dd(_0x5827bc,_0x22fc44-0x51f);}const _0x41e0a0=Math['min'](_0x2c5b54+0x1,_0x3d67e2-0x1),_0x24445c=_0x18b62f-_0x5af2ab,_0x2669b7=_0x305b40-_0x2c5b54,_0x3a7948=_0x2b1e82*_0x84afb+_0x4febac,_0xa8a385=_0x2c5b54*_0x84afb+_0x5af2ab,_0x3291fd=_0x2c5b54*_0x84afb+_0x268afb,_0x4d282f=_0x41e0a0*_0x84afb+_0x5af2ab,_0x1dcaa0=_0x41e0a0*_0x84afb+_0x268afb,_0x5e3a65=_0x463098[_0x196910(0x362,0x31c)][_0xa8a385],_0x4aea73=_0x463098['array'][_0x3291fd],_0x2ef6f1=_0x463098[_0x23e9bb(0x393,0x37e)][_0x4d282f],_0x129f26=_0x463098['array'][_0x1dcaa0],_0x4e4d97=(0x1-_0x24445c)*(0x1-_0x2669b7)*_0x5e3a65+_0x24445c*(0x1-_0x2669b7)*_0x4aea73+(0x1-_0x24445c)*_0x2669b7*_0x2ef6f1+_0x24445c*_0x2669b7*_0x129f26,_0x37dccc=_0x45fe98['array'][_0xa8a385],_0x28d23e=_0x45fe98[_0x23e9bb(0x393,0x397)][_0x3291fd],_0x1bb605=_0x45fe98['array'][_0x4d282f],_0xdb156=_0x45fe98['array'][_0x1dcaa0],_0x56c207=(0x1-_0x24445c)*(0x1-_0x2669b7)*_0x37dccc+_0x24445c*(0x1-_0x2669b7)*_0x28d23e+(0x1-_0x24445c)*_0x2669b7*_0x1bb605+_0x24445c*_0x2669b7*_0xdb156,_0x3baa95=Math['sqrt'](_0x4e4d97*_0x4e4d97+_0x56c207*_0x56c207),_0x29d043={};_0x29d043['u']=_0x463098['array'][_0x3a7948],_0x29d043['v']=_0x45fe98['array'][_0x3a7948],_0x29d043[_0x196910(0x31d,0x2ed)]=_0x125423[_0x23e9bb(0x393,0x38b)][_0x3a7948];function _0x196910(_0x355458,_0x156142){return _0x4e5d3a(_0x156142,_0x355458-0x19e);}const _0x10c6c4={};_0x10c6c4['u']=_0x4e4d97,_0x10c6c4['v']=_0x56c207,_0x10c6c4[_0x196910(0x31d,0x298)]=_0x3baa95;const _0x45340b={};return _0x45340b[_0x196910(0x36e,0x386)]=_0x29d043,_0x45340b['interpolated']=_0x10c6c4,_0x45340b;}}mars3d__namespace[_0x2319dd(-0x146,-0x182)]['register'](_0x2319dd(-0x244,-0x1fd),WindLayer),mars3d__namespace['layer']['WindLayer']=WindLayer;class CanvasParticle{constructor(){this['lng']=null,this['lat']=null;function _0x1b15d0(_0x5e624c,_0x1bc93e){return _0x2319dd(_0x5e624c,_0x1bc93e- -0x7a);}this[_0x1b15d0(-0x273,-0x2c7)]=null,this['tlat']=null,this['age']=null,this['speed']=null;}[_0x2319dd(-0x261,-0x267)](){for(const _0x65ade3 in this){delete this[_0x65ade3];}}}class CanvasWindField{constructor(_0x220187){function _0x20fdc9(_0x40c51d,_0x29fef3){return _0x4e5d3a(_0x29fef3,_0x40c51d- -0x12d);}this[_0x20fdc9(0x7e,0xed)](_0x220187);}get['speedRate'](){return this['_speedRate'];}set['speedRate'](_0x39ffb1){this['_speedRate']=(0x64-(_0x39ffb1>0x63?0x63:_0x39ffb1))*0x64;function _0x294a73(_0x439437,_0x34ea56){return _0x4e5d3a(_0x34ea56,_0x439437- -0x7b);}function _0x154ed2(_0x21d9b0,_0x28413a){return _0x2319dd(_0x28413a,_0x21d9b0-0x415);}this['_calc_speedRate']=[(this['xmax']-this[_0x154ed2(0x28c,0x2fa)])/this['_speedRate'],(this['ymax']-this[_0x154ed2(0x2ac,0x269)])/this['_speedRate']];}get['maxAge'](){function _0x519681(_0x591e92,_0x3c77c1){return _0x2319dd(_0x591e92,_0x3c77c1-0x32c);}return this[_0x519681(0x192,0x1b0)];}set['maxAge'](_0x158dc5){this['_maxAge']=_0x158dc5;}['setOptions'](_0x1412be){this['options']=_0x1412be,this[_0x1b5ff6(0x1cd,0x1bc)]=_0x1412be[_0x1b5ff6(0x1cd,0x1a6)]||0x78,this['speedRate']=_0x1412be[_0x1b5ff6(0x1c8,0x145)]||0x32;function _0x140831(_0x44c94b,_0x4a79b0){return _0x4e5d3a(_0x44c94b,_0x4a79b0-0x311);}this[_0x1b5ff6(0x1ac,0x12e)]=[];const _0x43e46f=_0x1412be['particlesNumber']||0x1000;function _0x1b5ff6(_0x1a895d,_0x68f45d){return _0x2319dd(_0x68f45d,_0x1a895d-0x3b7);}for(let _0xa49d5e=0x0;_0xa49d5e<_0x43e46f;_0xa49d5e++){const _0x27dde2=this['_randomParticle'](new CanvasParticle());this['particles']['push'](_0x27dde2);}}[_0x2319dd(-0x1b4,-0x1d3)](_0x330f6a){this['rows']=_0x330f6a['rows'];function _0x131b2c(_0x545900,_0x575ae4){return _0x2319dd(_0x545900,_0x575ae4- -0xcc);}this['cols']=_0x330f6a[_0x131b2c(-0x294,-0x26b)],this[_0x2bed08(-0x44,-0xa9)]=_0x330f6a[_0x2bed08(-0x44,0x16)],this['xmax']=_0x330f6a['xmax'],this['ymin']=_0x330f6a['ymin'],this['ymax']=_0x330f6a['ymax'],this[_0x2bed08(-0x80,-0x12)]=[];const _0x49a4b5=_0x330f6a['udata'],_0xfb3cd3=_0x330f6a['vdata'];let _0x37236e=![];_0x49a4b5[_0x131b2c(-0x20c,-0x260)]===this['rows']&&_0x49a4b5[0x0]['length']===this['cols']&&(_0x37236e=!![]);let _0x23f499=0x0,_0x16a33f=null,_0xab8db8=null;function _0x2bed08(_0x1330d2,_0x5712df){return _0x4e5d3a(_0x5712df,_0x1330d2- -0x20b);}for(let _0xa72f2d=0x0;_0xa72f2d<this['rows'];_0xa72f2d++){_0x16a33f=[];for(let _0x845436=0x0;_0x845436<this['cols'];_0x845436++,_0x23f499++){_0x37236e?_0xab8db8=this[_0x2bed08(-0x115,-0x16a)](_0x49a4b5[_0xa72f2d][_0x845436],_0xfb3cd3[_0xa72f2d][_0x845436]):_0xab8db8=this[_0x2bed08(-0x115,-0x187)](_0x49a4b5[_0x23f499],_0xfb3cd3[_0x23f499]),_0x16a33f['push'](_0xab8db8);}this['grid'][_0x2bed08(-0x9c,-0xfb)](_0x16a33f);}!this[_0x131b2c(-0x26b,-0x28e)]['flipY']&&this[_0x2bed08(-0x80,-0x80)]['reverse']();}[_0x2319dd(-0x265,-0x229)](){delete this['rows'],delete this['cols'],delete this['xmin'];function _0x3f014c(_0x52f696,_0xdb9305){return _0x2319dd(_0xdb9305,_0x52f696-0x9);}delete this['xmax'],delete this['ymin'],delete this[_0x3f014c(-0x23d,-0x283)],delete this['grid'],delete this['particles'];}['toGridXY'](_0x37e783,_0x541cbd){function _0x18827a(_0x15c2d3,_0x3d2150){return _0x2319dd(_0x3d2150,_0x15c2d3-0x17f);}const _0x2ad505=(_0x37e783-this[_0x20fded(0x411,0x3c3)])/(this[_0x18827a(-0xd0,-0x13e)]-this[_0x18827a(-0xa,-0x68)])*(this[_0x18827a(-0x20,-0xc)]-0x1);function _0x20fded(_0x1e1587,_0x7a2703){return _0x4e5d3a(_0x1e1587,_0x7a2703-0x1fc);}const _0x125e66=(this['ymax']-_0x541cbd)/(this['ymax']-this['ymin'])*(this[_0x18827a(-0x5f,-0xa6)]-0x1);return[_0x2ad505,_0x125e66];}['getUVByXY'](_0xd41866,_0x38bb7b){if(_0xd41866<0x0||_0xd41866>=this['cols']||_0x38bb7b>=this[_0x49a04b(0x502,0x4a5)])return[0x0,0x0,0x0];const _0x181be6=Math['floor'](_0xd41866),_0xd45163=Math['floor'](_0x38bb7b);if(_0x181be6===_0xd41866&&_0xd45163===_0x38bb7b)return this[_0x49a04b(0x51b,0x509)][_0x38bb7b][_0xd41866];const _0x3a6fe9=_0x181be6+0x1,_0xb112e9=_0xd45163+0x1,_0x53828a=this['getUVByXY'](_0x181be6,_0xd45163);function _0x201f1f(_0x72b5b9,_0x503e32){return _0x2319dd(_0x503e32,_0x72b5b9-0x3ba);}const _0x56d393=this[_0x201f1f(0x1f1,0x16b)](_0x3a6fe9,_0xd45163),_0xabbedd=this['getUVByXY'](_0x181be6,_0xb112e9),_0x11f7d8=this[_0x49a04b(0x517,0x529)](_0x3a6fe9,_0xb112e9);function _0x49a04b(_0x384782,_0x49d339){return _0x4e5d3a(_0x49d339,_0x384782-0x390);}let _0x2b57b1=null;try{_0x2b57b1=this[_0x49a04b(0x574,0x5a9)](_0xd41866-_0x181be6,_0x38bb7b-_0xd45163,_0x53828a,_0x56d393,_0xabbedd,_0x11f7d8);}catch(_0x495b91){console[_0x49a04b(0x4fc,0x582)](_0xd41866,_0x38bb7b);}return _0x2b57b1;}['_bilinearInterpolation'](_0x47a50c,_0x217281,_0x543cb6,_0xb52f50,_0x19cb9f,_0x583222){const _0x17ae79=0x1-_0x47a50c,_0x44ae0e=0x1-_0x217281,_0x45fc10=_0x17ae79*_0x44ae0e,_0x35595c=_0x47a50c*_0x44ae0e,_0x5c6f7b=_0x17ae79*_0x217281,_0x5c949f=_0x47a50c*_0x217281,_0x3c4c0f=_0x543cb6[0x0]*_0x45fc10+_0xb52f50[0x0]*_0x35595c+_0x19cb9f[0x0]*_0x5c6f7b+_0x583222[0x0]*_0x5c949f,_0x2d20bb=_0x543cb6[0x1]*_0x45fc10+_0xb52f50[0x1]*_0x35595c+_0x19cb9f[0x1]*_0x5c6f7b+_0x583222[0x1]*_0x5c949f;function _0x2911f1(_0x5903e8,_0x4c80fe){return _0x2319dd(_0x4c80fe,_0x5903e8-0x6ce);}return this[_0x2911f1(0x474,0x4ba)](_0x3c4c0f,_0x2d20bb);}['_calcUV'](_0xfb5f80,_0x274216){return[+_0xfb5f80,+_0x274216,Math['sqrt'](_0xfb5f80*_0xfb5f80+_0x274216*_0x274216)];}['getUVByPoint'](_0x3f02db,_0x2505fb){if(!this['isInExtent'](_0x3f02db,_0x2505fb))return null;const _0x3c2720=this['toGridXY'](_0x3f02db,_0x2505fb),_0x233994=this['getUVByXY'](_0x3c2720[0x0],_0x3c2720[0x1]);return _0x233994;}[_0x4e5d3a(0x147,0x1c1)](_0x174bcf,_0x58741f){function _0x14879a(_0x334dfe,_0x2a581e){return _0x4e5d3a(_0x2a581e,_0x334dfe- -0x71);}return _0x174bcf>=this['xmin']&&_0x174bcf<=this[_0x14879a(0x90,0x97)]&&_0x58741f>=this['ymin']&&_0x58741f<=this['ymax']?!![]:![];}['getRandomLatLng'](){const _0x2d036e=fRandomByfloat(this[_0x137e4f(0x22,0xa1)],this['xmax']);function _0x137e4f(_0x1be5ff,_0x3a4a4e){return _0x2319dd(_0x1be5ff,_0x3a4a4e-0x22a);}const _0x2005f5=fRandomByfloat(this[_0x137e4f(0x8f,0xc1)],this[_0x137e4f(0x60,-0x1c)]),_0x292fb1={};_0x292fb1['lat']=_0x2005f5;function _0x315cad(_0x124790,_0x4d308b){return _0x2319dd(_0x4d308b,_0x124790-0x7b);}return _0x292fb1[_0x137e4f(0x97,0x28)]=_0x2d036e,_0x292fb1;}[_0x2319dd(-0x191,-0x1aa)](){function _0x74ca08(_0x4d39c0,_0x514603){return _0x4e5d3a(_0x4d39c0,_0x514603- -0x296);}let _0x1723a5,_0x3d741b,_0x4955b6;function _0x237d82(_0x2f6b56,_0xc883c1){return _0x2319dd(_0x2f6b56,_0xc883c1-0x61c);}for(let _0x5acba4=0x0,_0x1f3381=this[_0x74ca08(-0xe6,-0x151)]['length'];_0x5acba4<_0x1f3381;_0x5acba4++){let _0x26e26b=this[_0x74ca08(-0x185,-0x151)][_0x5acba4];_0x26e26b['age']<=0x0&&(_0x26e26b=this['_randomParticle'](_0x26e26b));if(_0x26e26b['age']>0x0){const _0x49fa8a=_0x26e26b[_0x237d82(0x351,0x3cf)],_0x15da59=_0x26e26b[_0x74ca08(-0x1a7,-0x16b)];_0x4955b6=this['getUVByPoint'](_0x49fa8a,_0x15da59),_0x4955b6?(_0x1723a5=_0x49fa8a+this[_0x237d82(0x39c,0x3c4)][0x0]*_0x4955b6[0x0],_0x3d741b=_0x15da59+this['_calc_speedRate'][0x1]*_0x4955b6[0x1],_0x26e26b['lng']=_0x49fa8a,_0x26e26b[_0x74ca08(-0x1f6,-0x182)]=_0x15da59,_0x26e26b['tlng']=_0x1723a5,_0x26e26b['tlat']=_0x3d741b,_0x26e26b[_0x74ca08(-0x131,-0x117)]=_0x4955b6[0x2],_0x26e26b['age']--):_0x26e26b['age']=0x0;}}return this['particles'];}['_randomParticle'](_0x411432){let _0xbeb105,_0x475bcc;for(let _0x1729c7=0x0;_0x1729c7<0x1e;_0x1729c7++){_0xbeb105=this['getRandomLatLng'](),_0x475bcc=this['getUVByPoint'](_0xbeb105['lng'],_0xbeb105['lat']);if(_0x475bcc&&_0x475bcc[0x2]>0x0)break;}if(!_0x475bcc)return _0x411432;const _0x2b4d57=_0xbeb105['lng']+this['_calc_speedRate'][0x0]*_0x475bcc[0x0],_0x240d04=_0xbeb105['lat']+this['_calc_speedRate'][0x1]*_0x475bcc[0x1];_0x411432[_0x13e703(0x475,0x41a)]=_0xbeb105[_0x11dcd0(0x13e,0x10c)],_0x411432[_0x13e703(0x43f,0x3e0)]=_0xbeb105['lat'],_0x411432['tlng']=_0x2b4d57,_0x411432['tlat']=_0x240d04,_0x411432['age']=Math['round'](0xa+Math[_0x11dcd0(0x165,0x100)]()*this['maxAge']),_0x411432['speed']=_0x475bcc[0x2];function _0x13e703(_0x4a51c0,_0xfccd14){return _0x2319dd(_0x4a51c0,_0xfccd14-0x61c);}function _0x11dcd0(_0x469582,_0x565b57){return _0x4e5d3a(_0x565b57,_0x469582- -0x10);}return _0x411432;}[_0x2319dd(-0x24a,-0x267)](){for(const _0x30295d in this){delete this[_0x30295d];}}}function fRandomByfloat(_0x44db13,_0x5f57c4){function _0x57bdd3(_0x486da0,_0x49fc89){return _0x2319dd(_0x49fc89,_0x486da0-0x325);}return _0x44db13+Math[_0x57bdd3(0x14a,0xc5)]()*(_0x5f57c4-_0x44db13);}const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer'][_0x4e5d3a(0x111,0x118)];class CanvasWindLayer extends BaseLayer{constructor(_0x11ebd5={}){super(_0x11ebd5);function _0x120338(_0x38d0de,_0x35bd58){return _0x2319dd(_0x38d0de,_0x35bd58-0x63b);}this['_setOptionsHook'](_0x11ebd5),this[_0x120338(0x4f6,0x4ab)]=null;function _0x3858a1(_0x46d052,_0x29a1a2){return _0x2319dd(_0x29a1a2,_0x46d052-0x59a);}_0x11ebd5['colors']&&_0x11ebd5['steps']&&(this['_colorRamp']=new mars3d__namespace[(_0x3858a1(0x3a1,0x412))](_0x11ebd5));}['_setOptionsHook'](_0x479a38,_0x50f779){function _0x2bb855(_0x3289ec,_0x5b41ff){return _0x4e5d3a(_0x5b41ff,_0x3289ec- -0x2a3);}this['frameTime']=0x3e8/(_0x479a38['frameRate']||0xa),this['_pointerEvents']=this[_0x2bb855(-0x115,-0x12a)][_0x2bb855(-0xdb,-0x6b)]??![],this['color']=_0x479a38['color']||'#ffffff',this['lineWidth']=_0x479a38['lineWidth']||0x1,this['fixedHeight']=_0x479a38[_0x2bb855(-0x135,-0x145)]??0x0,this[_0x2bb855(-0x15b,-0x114)]=_0x479a38['flipY']??![];function _0x3b11f6(_0x3612a0,_0x1a3af9){return _0x4e5d3a(_0x1a3af9,_0x3612a0- -0x2fd);}this['windField']&&this['windField']['setOptions'](_0x479a38);}get['layer'](){return this['canvas'];}get['canvasWidth'](){function _0x2e7cfa(_0x49fc13,_0x489fa2){return _0x4e5d3a(_0x49fc13,_0x489fa2-0x2f);}return this['_map'][_0x2e7cfa(0x235,0x1b1)]['canvas']['clientWidth'];}get[_0x4e5d3a(0x140,0x138)](){function _0x283b5c(_0x2e86bc,_0x470752){return _0x2319dd(_0x470752,_0x2e86bc- -0xb5);}function _0x3b2496(_0x1e02dc,_0x3315f4){return _0x4e5d3a(_0x3315f4,_0x1e02dc- -0x3a);}return this[_0x283b5c(-0x259,-0x1e8)]['scene'][_0x283b5c(-0x245,-0x22b)]['clientHeight'];}get[_0x2319dd(-0x1c5,-0x188)](){function _0x17bfae(_0xc4c05f,_0x51e2b9){return _0x2319dd(_0xc4c05f,_0x51e2b9-0x59c);}return this[_0x17bfae(0x3a1,0x35b)];}set[_0x4e5d3a(0x1d7,0x1c8)](_0x1275db){this['_pointerEvents']=_0x1275db;function _0xdac8ce(_0xc7c230,_0x52d8ae){return _0x4e5d3a(_0x52d8ae,_0xc7c230-0x33f);}function _0x2a1160(_0xc4f64c,_0x3d4bd1){return _0x4e5d3a(_0x3d4bd1,_0xc4f64c-0x1c0);}if(!this[_0x2a1160(0x380,0x3cb)])return;_0x1275db?this['canvas']['style']['pointer-events']='all':this[_0xdac8ce(0x4ff,0x48e)][_0x2a1160(0x39b,0x3f6)][_0xdac8ce(0x43d,0x41e)]='none';}get['particlesNumber'](){return this['options']['particlesNumber'];}set['particlesNumber'](_0x5bedaf){this[_0x18f969(0xb5,0x123)]['particlesNumber']=_0x5bedaf,clearTimeout(this['_canrefresh']);function _0xe8610a(_0x580130,_0x2dc803){return _0x4e5d3a(_0x2dc803,_0x580130-0xa1);}function _0x18f969(_0x811676,_0xc7f6c6){return _0x2319dd(_0xc7f6c6,_0x811676-0x277);}this[_0x18f969(0x49,-0x19)]=setTimeout(()=>{function _0x25ed86(_0xee8930,_0xaa64f1){return _0xe8610a(_0xaa64f1-0x172,_0xee8930);}this[_0x25ed86(0x3bd,0x362)]();},0x1f4);}get['speedRate'](){function _0x35ab9e(_0x13ebbb,_0x2af3db){return _0x4e5d3a(_0x2af3db,_0x13ebbb- -0x38d);}return this['options'][_0x35ab9e(-0x22c,-0x242)];}set['speedRate'](_0x589758){function _0x3591a2(_0x53e8e7,_0xe8f5f4){return _0x2319dd(_0xe8f5f4,_0x53e8e7-0x436);}this[_0x25f936(0xb8,0xff)][_0x25f936(0x106,0xd2)]=_0x589758;function _0x25f936(_0x4b14db,_0xab58c3){return _0x2319dd(_0x4b14db,_0xab58c3-0x2c1);}this[_0x25f936(0xe9,0x7f)]&&(this['windField'][_0x25f936(0xe8,0xd2)]=_0x589758);}get['maxAge'](){function _0x4b0c07(_0xfc125f,_0x3a3e8f){return _0x4e5d3a(_0xfc125f,_0x3a3e8f-0x59);}return this['options'][_0x4b0c07(0x153,0x1bf)];}set[_0x4e5d3a(0x119,0x166)](_0x4228f4){this['options']['maxAge']=_0x4228f4;function _0x175f1f(_0x260fe8,_0x3287e2){return _0x4e5d3a(_0x3287e2,_0x260fe8-0x1a6);}function _0x22b443(_0x1bc408,_0x181c9e){return _0x4e5d3a(_0x181c9e,_0x1bc408- -0x2cf);}this['windField']&&(this[_0x22b443(-0x1c1,-0x146)][_0x175f1f(0x30c,0x350)]=_0x4228f4);}get['data'](){return this['windData'];}set[_0x2319dd(-0x1ba,-0x186)](_0x5cd4ec){function _0x5860ff(_0x48d11a,_0x227c51){return _0x2319dd(_0x48d11a,_0x227c51-0x5b0);}this[_0x5860ff(0x39c,0x38f)](_0x5cd4ec);}get['rectangle'](){let _0x3c3c8b=this['windData']['xmin'];function _0x1b4770(_0x213280,_0x50b080){return _0x4e5d3a(_0x213280,_0x50b080-0x1f4);}let _0x76b0ee=this['windData']['xmax'],_0x46a87e=this[_0x4f9060(-0x26,-0x8a)]['ymin'];function _0x4f9060(_0xe546d3,_0x30bfb1){return _0x2319dd(_0xe546d3,_0x30bfb1-0xfd);}let _0x1ff4d6=this['windData'][_0x1b4770(0x2fe,0x2fe)];return _0x76b0ee>=0x167&&_0x3c3c8b===0x0&&(_0x3c3c8b=-0xb4,_0x76b0ee=0xb4),_0x3c3c8b=Math[_0x1b4770(0x3cb,0x3a6)](_0x3c3c8b,-0xb4),_0x76b0ee=Math['min'](_0x76b0ee,0xb4),_0x46a87e=Math['max'](_0x46a87e,-0x5a),_0x1ff4d6=Math[_0x4f9060(-0x3e,-0xab)](_0x1ff4d6,0x5a),Cesium['Rectangle'][_0x1b4770(0x3ea,0x36a)](_0x3c3c8b,_0x46a87e,_0x76b0ee,_0x1ff4d6);}['_showHook'](_0x23360e){function _0x279b00(_0x5d1152,_0x523551){return _0x2319dd(_0x5d1152,_0x523551- -0xb1);}function _0x5c7ebd(_0x4f81b4,_0x26e8a1){return _0x2319dd(_0x4f81b4,_0x26e8a1-0x67e);}_0x23360e?this[_0x279b00(-0x334,-0x2ba)]():(this['windData']&&(this[_0x5c7ebd(0x4f5,0x4bc)][_0x279b00(-0x253,-0x237)]=this['windData']),this['_removedHook']());}['_mountedHook'](){this['options']['worker']?this['initWorker']():this['windField']=new CanvasWindField(this['options']);}[_0x2319dd(-0x193,-0x209)](){this[_0x54eb90(0xc5,0x89)]=this['_createCanvas']();const _0xa8c268={};_0xa8c268['willReadFrequently']=!![],this['canvasContext']=this['canvas']['getContext']('2d',_0xa8c268);function _0x286229(_0x143b2e,_0x457117){return _0x4e5d3a(_0x457117,_0x143b2e- -0x58);}function _0x54eb90(_0xf9e48e,_0x4839b1){return _0x4e5d3a(_0x4839b1,_0xf9e48e- -0xfb);}this[_0x286229(0xe6,0x10a)](),this[_0x286229(0x136,0x145)]['data']&&this[_0x54eb90(0x34,0x70)](this['options']['data']);}['_removedHook'](){function _0x35ac65(_0x183964,_0x55061c){return _0x4e5d3a(_0x183964,_0x55061c- -0x3a5);}this['clear']();function _0x201db2(_0x231291,_0x482d58){return _0x4e5d3a(_0x231291,_0x482d58- -0x125);}this[_0x201db2(-0x3e,0x45)](),this[_0x201db2(0xdc,0x9b)]&&(this['_map'][_0x35ac65(-0x21e,-0x1f6)][_0x201db2(0xb8,0x92)](this[_0x201db2(0xa0,0x9b)]),delete this[_0x35ac65(-0x193,-0x1e5)]);}[_0x2319dd(-0x1c0,-0x235)](){function _0xb3a0df(_0x1e9c5e,_0x376469){return _0x4e5d3a(_0x1e9c5e,_0x376469- -0x29c);}const _0x4c695b=mars3d__namespace['DomUtil']['create']('canvas','mars3d-canvasWind',this[_0xb3a0df(-0x9d,-0xf0)]['container']);function _0x594f93(_0x52e64d,_0x5b1f3a){return _0x2319dd(_0x52e64d,_0x5b1f3a-0x33c);}return _0x4c695b[_0xb3a0df(-0xc2,-0xc1)][_0xb3a0df(-0x1fa,-0x1b5)]='absolute',_0x4c695b[_0x594f93(0x21e,0x1c7)]['top']='0px',_0x4c695b[_0x594f93(0x1cd,0x1c7)]['left']=_0xb3a0df(-0x14b,-0x195),_0x4c695b['style']['width']=this['_map'][_0xb3a0df(-0x130,-0x11a)]['canvas']['clientWidth']+'px',_0x4c695b[_0xb3a0df(-0xde,-0xc1)]['height']=this[_0x594f93(0x175,0x198)][_0x594f93(0x1a0,0x16e)]['canvas']['clientHeight']+'px',_0x4c695b['style'][_0x594f93(0x163,0x1b4)]=this[_0xb3a0df(-0x196,-0x18d)]?'auto':'none',_0x4c695b['style']['zIndex']=this['options']['zIndex']??0x9,_0x4c695b[_0x594f93(0x45,0xc8)]=this[_0xb3a0df(-0x143,-0xf0)]['scene']['canvas']['clientWidth'],_0x4c695b['height']=this[_0x594f93(0x153,0x198)]['scene'][_0x594f93(0x215,0x1ac)]['clientHeight'],_0x4c695b;}[_0x4e5d3a(0x102,0xe2)](){function _0x518d84(_0x517a86,_0x5a65db){return _0x4e5d3a(_0x517a86,_0x5a65db- -0x3a8);}function _0x187149(_0x45383a,_0x4d2fbf){return _0x4e5d3a(_0x45383a,_0x4d2fbf- -0x204);}this['canvas']&&(this[_0x518d84(-0x231,-0x1e8)][_0x187149(-0xad,-0x29)]['width']=this['_map'][_0x518d84(-0x2a1,-0x226)]['canvas']['clientWidth']+'px',this['canvas']['style']['height']=this['_map']['scene']['canvas'][_0x187149(-0x36,-0x24)]+'px',this['canvas']['width']=this['_map'][_0x518d84(-0x27b,-0x226)][_0x518d84(-0x1c6,-0x1e8)]['clientWidth'],this['canvas']['height']=this['_map'][_0x518d84(-0x1ed,-0x226)][_0x518d84(-0x192,-0x1e8)]['clientHeight']);}['bindEvent'](){const _0x47b17d=this;let _0x52ab5e=Date['now']();(function _0x1778e4(){if(_0x47b17d[_0x401361(-0x1cc,-0x150)])return;function _0x6e7d01(_0x28066b,_0x1db9b3){return _0x39e2(_0x28066b-0x246,_0x1db9b3);}_0x47b17d['_animateFrame']=window['requestAnimationFrame'](_0x1778e4);function _0x401361(_0x4326ca,_0x453693){return _0x39e2(_0x4326ca- -0x32b,_0x453693);}if(_0x47b17d['show']&&_0x47b17d['windField']){const _0x3746c8=Date[_0x401361(-0x27a,-0x27c)](),_0x11e4e8=_0x3746c8-_0x52ab5e;_0x11e4e8>_0x47b17d['frameTime']&&(_0x52ab5e=_0x3746c8-_0x11e4e8%_0x47b17d['frameTime'],_0x47b17d[_0x6e7d01(0x3ee,0x46d)]());}}(),window['addEventListener'](_0x105014(0x2dc,0x315),this[_0x105014(0x2e8,0x315)]['bind'](this),![]),this['mouse_down']=![]);function _0x338dc2(_0x3c8bd8,_0x502897){return _0x4e5d3a(_0x3c8bd8,_0x502897- -0x115);}function _0x105014(_0x2cf1ec,_0x2b4902){return _0x2319dd(_0x2cf1ec,_0x2b4902-0x583);}this['mouse_move']=![],this['options'][_0x338dc2(0xfd,0xbc)]&&(this['_map']['on'](mars3d__namespace[_0x105014(0x38e,0x40c)][_0x338dc2(-0xb,-0x2b)],this['_onMapWhellEvent'],this),this['_map']['on'](mars3d__namespace['EventType'][_0x338dc2(0xb5,0x74)],this['_onMouseDownEvent'],this),this[_0x105014(0x393,0x3df)]['on'](mars3d__namespace['EventType']['mouseUp'],this[_0x338dc2(0x0,-0x8)],this));}[_0x2319dd(-0x244,-0x1e6)](){window['cancelAnimationFrame'](this[_0x58f909(0x4a3,0x49f)]);function _0x3d1056(_0x48e915,_0x3ba4a0){return _0x2319dd(_0x3ba4a0,_0x48e915-0x1a0);}delete this[_0x3d1056(0x32,0x68)],window[_0x58f909(0x3a9,0x3b0)](_0x58f909(0x3a3,0x397),this['resize']);function _0x58f909(_0xd6b66d,_0x425638){return _0x4e5d3a(_0x425638,_0xd6b66d-0x2c1);}this[_0x58f909(0x44f,0x44e)]['mouseHidden']&&(this['_map']['off'](mars3d__namespace['EventType'][_0x58f909(0x3ab,0x351)],this[_0x58f909(0x462,0x45e)],this),this['_map']['off'](mars3d__namespace[_0x58f909(0x49a,0x512)]['mouseDown'],this['_onMouseDownEvent'],this),this['_map'][_0x3d1056(-0xbe,-0xd2)](mars3d__namespace[_0x58f909(0x49a,0x44b)]['mouseUp'],this[_0x58f909(0x3ce,0x389)],this),this[_0x3d1056(-0x4,0x4d)][_0x3d1056(-0xbe,-0x117)](mars3d__namespace[_0x58f909(0x49a,0x425)]['mouseMove'],this[_0x58f909(0x3a6,0x41b)],this));}['_onMapWhellEvent'](_0x48e960){function _0x3eb540(_0x338ed5,_0x519491){return _0x2319dd(_0x338ed5,_0x519491-0xaf);}function _0x4d4c77(_0xffa721,_0x1298e9){return _0x4e5d3a(_0x1298e9,_0xffa721-0x25a);}clearTimeout(this['refreshTimer']);if(!this['show']||!this['canvas'])return;this[_0x4d4c77(0x41a,0x3ee)][_0x4d4c77(0x435,0x41a)]['visibility']=_0x4d4c77(0x366,0x366),this[_0x3eb540(-0xdf,-0xce)]=setTimeout(()=>{if(!this['show'])return;function _0x45c9f6(_0x23a4ca,_0x364bf7){return _0x3eb540(_0x23a4ca,_0x364bf7-0x113);}function _0x5ca382(_0x59e1af,_0x4f6b61){return _0x4d4c77(_0x4f6b61-0x112,_0x59e1af);}this['redraw'](),this[_0x45c9f6(0x3c,0x32)]['style'][_0x5ca382(0x441,0x4a1)]=_0x5ca382(0x483,0x4b2);},0xc8);}['_onMouseDownEvent'](_0x48c980){this['mouse_down']=!![];function _0x1a9808(_0x576ecb,_0x5c7997){return _0x2319dd(_0x576ecb,_0x5c7997-0x6c8);}function _0x3706df(_0x3875b0,_0x1cc87b){return _0x2319dd(_0x1cc87b,_0x3875b0-0x34f);}this[_0x1a9808(0x4b7,0x524)][_0x1a9808(0x443,0x46a)](mars3d__namespace[_0x1a9808(0x5c4,0x551)][_0x1a9808(0x4c8,0x4f0)],this['_onMouseMoveEvent'],this),this['_map']['on'](mars3d__namespace['EventType'][_0x3706df(0x177,0x114)],this[_0x3706df(0xe4,0x9c)],this);}[_0x4e5d3a(0xae,0xe5)](_0x4640aa){function _0x3090ed(_0x22cff4,_0x8d183a){return _0x4e5d3a(_0x22cff4,_0x8d183a- -0x232);}if(!this[_0x43637f(0x1e5,0x1d4)]||!this[_0x3090ed(-0x98,-0x72)])return;function _0x43637f(_0x50e74b,_0x22d2bd){return _0x2319dd(_0x22d2bd,_0x50e74b-0x39c);}this['mouse_down']&&(this['canvas'][_0x3090ed(-0xbd,-0x57)]['visibility']='hidden',this['mouse_move']=!![]);}[_0x2319dd(-0x2b5,-0x243)](_0x52805f){function _0x4c9dae(_0xb3220a,_0x49db27){return _0x2319dd(_0xb3220a,_0x49db27-0xf5);}if(!this['show']||!this['canvas'])return;function _0x496928(_0x22922e,_0x50cdfe){return _0x2319dd(_0x22922e,_0x50cdfe- -0x20);}this['_map']['off'](mars3d__namespace['EventType']['mouseMove'],this[_0x4c9dae(-0x14f,-0x176)],this),this['mouse_down']&&this['mouse_move']&&this['redraw'](),this['canvas']['style']['visibility']='visible',this[_0x496928(-0x204,-0x257)]=![],this[_0x496928(-0x22b,-0x1c9)]=![];}['setData'](_0x5c3227){this[_0x3df2a9(-0x2d7,-0x292)]();function _0x3df2a9(_0x1247aa,_0x1ef4f8){return _0x4e5d3a(_0x1ef4f8,_0x1247aa- -0x3fe);}function _0x3d4494(_0xfd01ac,_0x328a86){return _0x2319dd(_0x328a86,_0xfd01ac-0x2b5);}this['windData']=_0x5c3227,this['windField'][_0x3d4494(0xe2,0xea)](_0x5c3227),this['redraw']();}[_0x2319dd(-0x236,-0x201)](){if(!this[_0x26aa18(0xc4,0x4b)])return;this[_0x3c751d(-0x1c4,-0x17e)][_0x3c751d(-0x127,-0xc1)](this['options']);function _0x26aa18(_0x1cde3d,_0x8a5eb4){return _0x2319dd(_0x8a5eb4,_0x1cde3d-0x27b);}function _0x3c751d(_0x18a7c8,_0x103ed6){return _0x4e5d3a(_0x103ed6,_0x18a7c8- -0x2d2);}this['update']();}[_0x4e5d3a(0x163,0x1dd)](){if(this[_0x5d9a88(0x9b,0xef)])return;this['_updateIng']=!![];function _0x5d9a88(_0x53a053,_0x1eb328){return _0x2319dd(_0x53a053,_0x1eb328-0x268);}function _0x29eadd(_0x158a18,_0x200f7b){return _0x4e5d3a(_0x200f7b,_0x158a18- -0x3c);}if(this[_0x5d9a88(0x95,0x1a)])this[_0x5d9a88(-0x28,0x26)]['update']();else{const _0x95d11a=this['windField']['getParticles']();this['_drawLines'](_0x95d11a);}this['_updateIng']=![];}['_drawLines'](_0x264121){function _0xa829bd(_0x82ea90,_0xa312a7){return _0x2319dd(_0xa312a7,_0x82ea90- -0x4e);}this['_canvasParticles']=_0x264121,this['canvasContext']['globalCompositeOperation']='destination-in',this['canvasContext']['fillRect'](0x0,0x0,this['canvasWidth'],this['canvasHeight']),this['canvasContext']['globalCompositeOperation']=_0xa829bd(-0x282,-0x27d);function _0x341a61(_0x542309,_0x1e1ecd){return _0x4e5d3a(_0x542309,_0x1e1ecd- -0x2a0);}this[_0x341a61(-0x20c,-0x1a9)]['globalAlpha']=0.9;const _0x1d3c21=this[_0xa829bd(-0x1f2,-0x184)][_0x341a61(-0xbe,-0x11e)]['mode']!==Cesium['SceneMode']['SCENE3D'],_0x38b851=this[_0x341a61(-0x133,-0x141)]*0.25;if(this[_0xa829bd(-0x1dc,-0x1b6)])for(let _0x119892=0x0,_0x4215dd=_0x264121[_0xa829bd(-0x1e2,-0x1f9)];_0x119892<_0x4215dd;_0x119892++){const _0x4d9b5c=_0x264121[_0x119892],_0x47b3b1=this['_tomap'](_0x4d9b5c,_0x4d9b5c[_0xa829bd(-0x250,-0x23e)],_0x4d9b5c['lat'],_0x4d9b5c['alt']),_0x317c8c=this[_0x341a61(-0x17a,-0x14e)](_0x4d9b5c,_0x4d9b5c['tlng'],_0x4d9b5c['tlat'],_0x4d9b5c[_0xa829bd(-0x1f9,-0x199)]);if(!_0x47b3b1||!_0x317c8c)continue;if(_0x1d3c21&&Math['abs'](_0x47b3b1[0x0]-_0x317c8c[0x0])>=_0x38b851)continue;this['canvasContext']['beginPath'](),this['canvasContext'][_0xa829bd(-0x204,-0x1b7)]=this[_0xa829bd(-0x204,-0x1a4)],this['canvasContext']['strokeStyle']=this['_colorRamp'][_0xa829bd(-0x2b2,-0x22e)](_0x4d9b5c['speed']),this['canvasContext']['moveTo'](_0x47b3b1[0x0],_0x47b3b1[0x1]),this[_0xa829bd(-0x2a7,-0x2db)]['lineTo'](_0x317c8c[0x0],_0x317c8c[0x1]),this['canvasContext'][_0xa829bd(-0x20e,-0x1b2)]();}else{this['canvasContext'][_0x341a61(-0x81,-0xfc)](),this['canvasContext'][_0x341a61(-0xe1,-0x106)]=this[_0xa829bd(-0x204,-0x1a9)],this['canvasContext']['strokeStyle']=this[_0xa829bd(-0x208,-0x21e)];for(let _0x5728ff=0x0,_0x4207bb=_0x264121[_0xa829bd(-0x1e2,-0x19b)];_0x5728ff<_0x4207bb;_0x5728ff++){const _0x4e67a8=_0x264121[_0x5728ff],_0xf60c4b=this[_0x341a61(-0x12c,-0x14e)](_0x4e67a8,_0x4e67a8[_0xa829bd(-0x250,-0x2af)],_0x4e67a8['lat'],_0x4e67a8['alt']),_0x45b144=this['_tomap'](_0x4e67a8,_0x4e67a8[_0x341a61(-0x1d5,-0x19d)],_0x4e67a8['tlat'],_0x4e67a8['talt']);if(!_0xf60c4b||!_0x45b144)continue;if(_0x1d3c21&&Math[_0x341a61(-0x224,-0x1ad)](_0xf60c4b[0x0]-_0x45b144[0x0])>=_0x38b851)continue;this['canvasContext']['moveTo'](_0xf60c4b[0x0],_0xf60c4b[0x1]),this[_0x341a61(-0x16d,-0x1a9)][_0xa829bd(-0x2c1,-0x348)](_0x45b144[0x0],_0x45b144[0x1]);}this[_0x341a61(-0x19a,-0x1a9)]['stroke']();}}['_tomap'](_0x409482,_0x423bdd,_0x74ffe2,_0xcaeb0b){function _0x3fba8e(_0x5af208,_0x43b401){return _0x2319dd(_0x43b401,_0x5af208-0xb7);}const _0x34a3c7=Cesium['Cartesian3']['fromDegrees'](_0x423bdd,_0x74ffe2,_0xcaeb0b??this[_0x3fba8e(-0x12b,-0x154)]),_0x29d5dc=this['_map'][_0x1a74a2(-0x1b8,-0x197)];if(_0x29d5dc['mode']===Cesium['SceneMode']['SCENE3D']){const _0x2315b7=new Cesium['EllipsoidalOccluder'](_0x29d5dc['globe']['ellipsoid'],_0x29d5dc['camera']['positionWC']),_0x2ed147=_0x2315b7['isPointVisible'](_0x34a3c7);if(!_0x2ed147)return _0x409482['age']=0x0,null;}function _0x1a74a2(_0x223a2a,_0xa614e){return _0x2319dd(_0xa614e,_0x223a2a-0x16);}const _0x3313c7=mars3d__namespace['PointTrans']['toWindowCoordinates'](this['_map'][_0x1a74a2(-0x1b8,-0x1b3)],_0x34a3c7);return _0x3313c7?[_0x3313c7['x'],_0x3313c7['y']]:null;}['clear'](){function _0x57e9e4(_0x5a3322,_0x5b11c9){return _0x4e5d3a(_0x5b11c9,_0x5a3322- -0xa5);}this[_0x57e9e4(0x69,0xb9)]['clear'](),delete this['windData'];}['initWorker'](){this['worker']=new Worker(this['options']['worker']);function _0x58db60(_0x1e08f0,_0x29e46e){return _0x4e5d3a(_0x1e08f0,_0x29e46e-0x34c);}this['worker'][_0x433cff(0x57,-0x1e)]=_0x5eb87b=>{this[_0x365354(-0x1cb,-0x17e)](_0x5eb87b['data'][_0x365354(-0x1e6,-0x199)]);function _0x365354(_0x2cfc68,_0x4273b5){return _0x433cff(_0x2cfc68,_0x4273b5- -0x180);}function _0x5932f8(_0x4403c0,_0x169b17){return _0x433cff(_0x4403c0,_0x169b17-0x17e);}this[_0x365354(-0x1f0,-0x18d)]=![];};function _0x433cff(_0x2d4073,_0x155dca){return _0x4e5d3a(_0x2d4073,_0x155dca- -0x15e);}this[_0x58db60(0x3e5,0x45a)]={'init':_0x5ebf85=>{const _0x2493c7={};_0x2493c7['type']='init';function _0xd9ffb8(_0x14681b,_0x333ece){return _0x58db60(_0x14681b,_0x333ece- -0x4bf);}function _0x4ec908(_0x186758,_0x2d9331){return _0x58db60(_0x186758,_0x2d9331-0x2);}_0x2493c7[_0xd9ffb8(-0x19,0x1b)]=_0x5ebf85,this['worker'][_0xd9ffb8(0x1f,0x42)](_0x2493c7);},'setOptions':_0xb6b550=>{function _0x42fb74(_0x44b67d,_0x5aa2f6){return _0x433cff(_0x44b67d,_0x5aa2f6-0x438);}const _0x344033={};_0x344033[_0x42fb74(0x432,0x497)]='setOptions';function _0x3808b0(_0x2f55b9,_0x449881){return _0x58db60(_0x2f55b9,_0x449881- -0x634);}_0x344033['options']=_0xb6b550,this[_0x3808b0(-0x1a7,-0x1e6)]['postMessage'](_0x344033);},'setDate':_0x2aed2e=>{const _0x18a87a={};_0x18a87a[_0x30c155(0x254,0x2d1)]=_0x341f41(0x103,0x186);function _0x341f41(_0x1d0fe7,_0x4e9d3b){return _0x58db60(_0x1d0fe7,_0x4e9d3b- -0x343);}_0x18a87a[_0x30c155(0x2f7,0x2de)]=_0x2aed2e;function _0x30c155(_0x5a32eb,_0xcda7a8){return _0x58db60(_0x5a32eb,_0xcda7a8- -0x238);}this['worker']['postMessage'](_0x18a87a);},'update':()=>{if(this['_updateIng2'])return;this['_updateIng2']=!![];const _0x283f41={};_0x283f41[_0x18733c(0x27b,0x211)]='update';function _0x18733c(_0x14985a,_0x4a23fe){return _0x433cff(_0x4a23fe,_0x14985a-0x21c);}this['worker']['postMessage'](_0x283f41);},'clear':()=>{const _0x26c8e7={};function _0x193a2(_0x3d69e9,_0xb93602){return _0x58db60(_0xb93602,_0x3d69e9- -0x41b);}_0x26c8e7['type']=_0x193a2(0x58,0x7),this['worker']['postMessage'](_0x26c8e7);}},this['windField'][_0x433cff(-0x42,0x1b)](this['options']);}}mars3d__namespace['LayerUtil'][_0x4e5d3a(0x1ee,0x180)]('canvasWind',CanvasWindLayer),mars3d__namespace[_0x4e5d3a(0x19f,0x1bf)][_0x4e5d3a(0x1a6,0x1ae)]=CanvasWindLayer,mars3d__namespace['CanvasWindField']=CanvasWindField,mars3d__namespace['WindUtil']=WindUtil,exports['CanvasWindField']=CanvasWindField;function _0x4e5d3a(_0x39a79a,_0x5e37da){return _0x39e2(_0x5e37da-0x35,_0x39a79a);}exports[_0x2319dd(-0x124,-0x1a2)]=CanvasWindLayer,exports[_0x4e5d3a(0xfe,0x111)]=WindLayer,exports['WindUtil']=WindUtil;const _0x47df01={};_0x47df01[_0x4e5d3a(0x20a,0x195)]=!![],Object[_0x4e5d3a(0x188,0x1ad)](exports,'__esModule',_0x47df01);
}));
