<template>
  <el-dialog draggable
    top="3vh"
    width="1000px"
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form :rules="rules" :model="warnEventSourceModel" label-width="120px">
      <el-row>
        <el-col>
          <el-form-item label="触发类型" prop="triggerType">
            <el-radio
              v-model="warnEventSourceModel.triggerType"
              :label="0"
              @click="refresh()"
              >设施状态</el-radio
            >
            <el-radio
              v-model="warnEventSourceModel.triggerType"
              :label="1"
              @click="refresh()"
              >事件感知</el-radio
            >
          </el-form-item>
        </el-col>
        <el-col>
            <el-form-item label="状态" prop="status">
                <el-radio-group v-model="warnEventSourceModel.status">
                    <el-radio v-for="item in statusList" :key="item.nameEn"
                        :label="parseInt(item.nameEn)">{{ item.nameCn }}</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="关联事件源" prop="sourceTypeId">
            <el-select
              style="width: 100%"
              v-model="warnEventSourceModel.sourceTypeId"
              placeholder="事件源名称"
            >
              <el-option
                v-for="item in warnEventSourceModel.triggerType == 0
                  ? deviceList
                  : eventList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="事件源名称" prop="name">
            <el-input
              v-model="warnEventSourceModel.name"
              placeholder="事件源名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item prop="note">
            <template v-slot:label>
              <span>
                描述模板
                <el-tooltip>
                  <template #content
                    >【规则项占位标识（#规则序号#）】
                    <br />【特殊项占位标识（安装地址： $ADDRESS$、设施名称：$DEVICE_NAME$）】
                    <br />例：间隔#2#天的#3#时间，检测安装于$ADDRESS$位置的$DEVICE_NAME$设施#1#出入记录</template
                  >
                  <el-icon><info-filled /></el-icon>
                </el-tooltip>
              </span>
            </template>
            <el-input
              :rows="4"
              type="textarea"
              v-model="warnEventSourceModel.note"
              placeholder="描述模板"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="规则模板" prop="note" style=" height: 380px;">
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="warnEventSourceModel.warnEventDetectRules"
                  style="width: 100%;height: 335px; overflow:auto"
                  border
                >
                  <el-table-column align="center" label="序号" width="70">
                    <template #default="scope">
                      <!-- <el-input-number v-model="scope.row.sort" :min="1" size="default" style="width: 80px" ></el-input-number> -->
                      <el-tag>#{{scope.row.sort}}#</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="检测标识" width="190">
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.detectCode"
                        @change="codeChange(scope.$index, scope.row)"
                        placeholder="检测标识"
                      >
                        <el-option
                          v-for="item in checkTypeList"
                          :key="item.code"
                          :title="item.note"
                          :label="item.name"
                          :value="item.code"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="数据类型" width="130">
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.valDataType"
                        placeholder="数据类型"
                        @change="valDataTypeChange(scope.row)"
                      >
                        <el-option
                          v-for="item in dataTypeList"
                          :key="item.nameEn"
                          :label="item.nameCn"
                          :value="item.nameEn"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="比较类型" width="108">
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.compareType"
                        placeholder="比较类型"
                      >
                        <el-option
                          v-for="item in compareTypeList"
                          :key="item.code"
                          :label="item.name"
                          :value="item.code"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="候选阈值">
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.optionVal"
                        :placeholder="optionValPlaceholder(scope.row)"
                        :title="scope.row.optionVal"
                      >
                      </el-input>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="状态" width="70">
                    <template #default="scope">
                      <el-switch
                        :active-value="1"
                        :inactive-value="0"
                        v-model="scope.row.status"
                      >
                      </el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="操作" width="70">
                    <template #default="scope">
                      <el-button
                        type="text"
                        size="default"
                        @click="remove(scope.$index)"
                        >移除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col
                  :span="24"
                  style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  "
                >
                <el-button
                  style="font-size: 25px; width: 100%; border-radius: 0; margin-top: 10px;"
                  size="default"
                  @click="add"
                >
                  <el-icon :size="20">
                    <plus></plus>
                  </el-icon>
                </el-button>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center">
      <el-button style="width: 100px" :disabled="isDisabled=='0'" type="primary" @click="onSubmit"
        >{{ submitText }}</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script setup>
import { WarningFilled, InfoFilled, Plus } from "@element-plus/icons-vue";
</script>

<script>
import {
  warnEventSourceInfoAdd,
  warnEventSourceInfoEdit,
  deviceTypeList,
  eventTypeList,
} from "@/api/warn/warn";
import mitt from "@/utils/mitt";
import { listDictByNameEn, queryListDictBySuffix } from "@/api/admin/dict"

export default {
  props: ["statusList", "tagList", "checkTypeList"],
  data() {
    return {
      isDisabled: '1',
      submitText: '提 交',
      loading: false,
      warnEventSourceModel: {},
      deviceList: [],
      communityId: localStorage.getItem("communityId"),
      eventList: [],
      compareTypeList: [
        { code: "==", name: "等于" },
        { code: ">", name: "大于" },
        { code: "<", name: "小于" },
        { code: "!=", name: "不等于" },
      ],
      dataTypeList: [
        { nameEn: "BOOL", nameCn: "布尔" },
        { nameEn: "TEXT", nameCn: "文本" },
        { nameEn: "NUMBER", nameCn: "数值" },
        { nameEn: "DATETIME", nameCn: "日期时间" },
        { nameEn: "DATE", nameCn: "日期" },
        { nameEn: "TIME", nameCn: "时间" },
        { nameEn: "SELECT", nameCn: "选择项" },
      ],
      dialog: {},
      rules: {
        triggerType: [
          {
            required: true,
            message: "请输入触发类型",
            trigger: "blur",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: "blur",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入事件源名称",
            trigger: "blur",
          },
        ],
      },
      newDictList:[]
    };
  },
  methods: {
    // 阈值Placeholder
    optionValPlaceholder(row){
      let placeholder = '可不填写'
      switch(row.valDataType){
        case 'BOOL':
          placeholder = '名1:true|名2:false'
          break;
        case 'SELECT':
          placeholder = '名1:值1|名2:值2'
          break;
        default:
          break;
      }
      return placeholder
    },
    /**设置候选阈值 */
    async setOptionVal(row) {
      console.log(row);
      let _this = this
      // 设施状态且是选择项以及设施检测因子、检测依据
      if (row.valDataType == 'SELECT') {
        let key = '';
        switch (row.detectCode) {
          case 'DETECT_STATE_FACTOR': //设施检测因子
            // 读取设施类型代号
            let devInfo = this.deviceList.find(m => m.id == this.warnEventSourceModel.sourceTypeId);
            key = devInfo.code + '_detect_state_factor';
            break;
          case 'DETECT_DATA_POOL': //检测依据
            key = 'detect_data_pool';
            break;
          default:
            break;
        }
        if (key) {
          console.log(key);
          //组装字典key，获取的字典项作为候选项
          listDictByNameEn(key).then(resp => {
            var kvList = resp.data.result.filter(p => {
              if (!p.nameEn.includes('+')) {
                console.log(p.nameCn);
                return p.nameCn + ':' + p.nameEn
              }
            }).map(p => p.nameCn + ':' + p.nameEn)
            _this.newDictList = kvList
            console.log(kvList);

            resp.data.result.forEach(element => {

               if (element.nameEn.includes('+')) {
                //child里面有 '+' ,  截取 '+' 后面的字符并转为小写
                let keySuffix = element.nameEn.substr(element.nameEn.indexOf('+')+1).toLowerCase();
                  queryListDictBySuffix(keySuffix).then( cres => {
                    _this.newDictList = _this.newDictList.concat(cres.data.result.map(c => c.nameCn + ':' + c.nameEn))
                    // this.newDictList = [...kvList,...cres.data.result.map(c => c.nameCn + ':' + c.nameEn)]
                    console.log(_this.newDictList);
                    row.optionVal = _this.newDictList.join('|');
                  })
              }else{

                // 否则直接添加child字典
                // _this.newDictList = kvList.concat(resp.data.result.map(p => p.nameCn + ':' + p.nameEn))
                row.optionVal = _this.newDictList.join('|');
              }
            });
            console.log(_this.newDictList);

            
            // row.optionVal = kvList.join('|');
          })
        } else {
          row.optionVal = '';
        }
      } else if (row.valDataType == 'BOOL') {
        row.optionVal = '有:true|无:false';
      } else {
        row.optionVal = '';
      }
    },
    /**值类型改变 */
    valDataTypeChange(row){
      this.setOptionVal(row);
    },
    /**代号改变 */
    codeChange(index, row) {
      for (let item of this.checkTypeList) {
        if (item.code == row.detectCode) {
          this.warnEventSourceModel.warnEventDetectRules[index].detectName = item.name;
        }
      }
      
      this.setOptionVal(row);
    },
    refresh() {
      this.warnEventSourceModel.sourceTypeId = null;
    },
    remove(i) {
      this.warnEventSourceModel.warnEventDetectRules.splice(i, 1);
    },
    add() {
      if (this.warnEventSourceModel.warnEventDetectRules == undefined) {
        this.warnEventSourceModel.warnEventDetectRules = [];
      }
      let nextSort = 1
      for (const item in this.warnEventSourceModel.warnEventDetectRules) {
        if (!this.warnEventSourceModel.warnEventDetectRules.some(m => m.sort == nextSort)) {
          break
        }
        nextSort++
      }
      this.warnEventSourceModel.warnEventDetectRules.push({
        sort: nextSort,
        status: 1,
      });
      this.warnEventSourceModel.warnEventDetectRules.sort((a, b) => a.sort - b.sort);
      //console.log(this.warnEventSourceModel.warnEventDetectRules)
    },
    onSubmit() {
      if (this.warnEventSourceModel.tags) {
        this.warnEventSourceModel.tags = JSON.stringify(this.warnEventSourceModel.tags);
      }
      this.warnEventSourceModel.communityId = this.communityId;
      if (this.warnEventSourceModel.id == 0) {
        this.submitText = '提交中...'
        this.isDisabled = '0'
        warnEventSourceInfoAdd(this.warnEventSourceModel).then((res) => {
          this.submitText = '提 交'
          this.isDisabled = '1'
          this.$message.success(res.data.msg);
          this.$emit("search");
          this.dialog.show = false;
        });
      } else {
        this.submitText = '提交中...'
        this.isDisabled = '0'
        warnEventSourceInfoEdit(this.warnEventSourceModel).then((res) => {
          this.submitText = '提 交'
          this.isDisabled = '1'
          this.$message.success(res.data.msg);
          this.$emit("search");
          this.dialog.show = false;
        }).catch((err)=>{ 
          this.submitText = '提 交'
          this.isDisabled = '1'
          console.log(err);
        } )
      }
    },
    loadTriggerType() {
      let param = {
        communityId: this.communityId,
      };
      deviceTypeList(param).then((res) => {
        this.deviceList = res.data.result;
      });
      eventTypeList(param).then((res) => {
        this.eventList = res.data.result;
      });
    },
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openWarnEventSourceInfoEdit", (person) => {
        this.loadTriggerType();
        if (person.tags) {
          person.tags = JSON.parse(person.tags);
        }
        this.warnEventSourceModel =  JSON.parse(JSON.stringify(person));;
        console.log(this.warnEventSourceModel);
        this.dialog.show = true;
        this.dialog.title = "修改";
      });
      mitt.on("openWarnEventSourceInfoAdd", () => {
        this.loadTriggerType();
        this.warnEventSourceModel = {
          id: 0,
          triggerType: 1,
          status:1
        };
        this.dialog.show = true;
        this.dialog.title = "添加";
      });
    });
  },
};
</script>
