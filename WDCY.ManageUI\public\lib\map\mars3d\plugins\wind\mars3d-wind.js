/**
 * Mars3D平台插件,支持气象 风向图 功能插件  mars3d-wind
 *
 * 版本信息：v3.7.22
 * 编译日期：2024-07-15 21:21:02
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2024-01-15
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-wind"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';(function(_0x4df497,_0x15e5a0){function _0x5e1fca(_0x34efd8,_0x5c781f){return _0x54dc(_0x34efd8-0x2f9,_0x5c781f);}function _0x3e43bc(_0x26980d,_0x2bb36a){return _0x54dc(_0x26980d-0x163,_0x2bb36a);}const _0x3a2215=_0x4df497();while(!![]){try{const _0x5afb03=-parseInt(_0x5e1fca(0x405,0x3fe))/0x1+-parseInt(_0x3e43bc(0x226,0x24f))/0x2*(parseInt(_0x5e1fca(0x472,0x48a))/0x3)+-parseInt(_0x5e1fca(0x47b,0x484))/0x4+-parseInt(_0x3e43bc(0x307,0x289))/0x5+parseInt(_0x5e1fca(0x465,0x4fa))/0x6*(parseInt(_0x3e43bc(0x2d4,0x32a))/0x7)+-parseInt(_0x5e1fca(0x391,0x318))/0x8*(parseInt(_0x5e1fca(0x4b4,0x4aa))/0x9)+-parseInt(_0x5e1fca(0x3d9,0x42d))/0xa*(-parseInt(_0x5e1fca(0x3b2,0x3dd))/0xb);if(_0x5afb03===_0x15e5a0)break;else _0x3a2215['push'](_0x3a2215['shift']());}catch(_0x5f5b0d){_0x3a2215['push'](_0x3a2215['shift']());}}}(_0x9c0b,0xc13dc));function _interopNamespace(_0x29b8b3){if(_0x29b8b3&&_0x29b8b3[_0x5720d1(0x26,-0x1e)])return _0x29b8b3;function _0x5720d1(_0x23d9f4,_0x291cf0){return _0x54dc(_0x291cf0- -0x19d,_0x23d9f4);}var _0x49c7d3=Object['create'](null);_0x29b8b3&&Object['keys'](_0x29b8b3)['forEach'](function(_0x2957bb){if(_0x2957bb!=='default'){var _0xfe6aba=Object['getOwnPropertyDescriptor'](_0x29b8b3,_0x2957bb);Object['defineProperty'](_0x49c7d3,_0x2957bb,_0xfe6aba['get']?_0xfe6aba:{'enumerable':!![],'get':function(){return _0x29b8b3[_0x2957bb];}});}});function _0x128285(_0x4ca93d,_0x5e60cd){return _0x54dc(_0x5e60cd- -0x310,_0x4ca93d);}return _0x49c7d3[_0x5720d1(-0x73,-0x94)]=_0x29b8b3,_0x49c7d3;}var mars3d__namespace=_interopNamespace(mars3d);const Cesium$7=mars3d__namespace['Cesium'];function getU(_0x5012b3,_0x9c2d07){const _0x5e2176=_0x5012b3*Math['cos'](Cesium$7['Math']['toRadians'](_0x9c2d07));return _0x5e2176;}function getV(_0xdbe2b4,_0x65118c){const _0x2bd363=_0xdbe2b4*Math[_0x205015(0x39d,0x32f)](Cesium$7['Math'][_0x191eaa(0x190,0x13a)](_0x65118c));function _0x205015(_0x58975a,_0x1af42f){return _0x54dc(_0x58975a-0x217,_0x1af42f);}function _0x191eaa(_0x1bd53d,_0x5d15ff){return _0x54dc(_0x5d15ff-0x3f,_0x1bd53d);}return _0x2bd363;}function getSpeed(_0xa7ce23,_0x1250fd){function _0x244508(_0x1d5ebe,_0x2b910e){return _0x54dc(_0x2b910e-0x292,_0x1d5ebe);}const _0x14429f=Math['sqrt'](Math['pow'](_0xa7ce23,0x2)+Math[_0x244508(0x2d3,0x35c)](_0x1250fd,0x2));return _0x14429f;}function getDirection(_0x2ba91d,_0x13fb87){let _0x1fcaf1=Cesium$7[_0x15142b(0x214,0x29d)]['toDegrees'](Math['atan2'](_0x13fb87,_0x2ba91d));_0x1fcaf1+=_0x1fcaf1<0x0?0x168:0x0;function _0x15142b(_0x226d3d,_0x55bad5){return _0x54dc(_0x55bad5-0x16c,_0x226d3d);}return _0x1fcaf1;}const _0xc350cb={};_0xc350cb[_0x420c92(-0x239,-0x2c0)]=null,_0xc350cb['getU']=getU,_0xc350cb['getV']=getV,_0xc350cb[_0x2d2794(0x61,0x69)]=getSpeed,_0xc350cb['getDirection']=getDirection;var WindUtil=_0xc350cb;const Cesium$6=mars3d__namespace[_0x2d2794(0x6b,0x48)];class CustomPrimitive{constructor(_0x3657be){this['commandType']=_0x3657be['commandType'],this['geometry']=_0x3657be['geometry'],this[_0x2a8893(-0x26b,-0x20d)]=_0x3657be[_0x1f7438(0x31c,0x398)],this['primitiveType']=_0x3657be[_0x1f7438(0x37f,0x303)];function _0x1f7438(_0xb75524,_0xc14832){return _0x2d2794(_0xb75524,_0xc14832-0x27b);}this['uniformMap']=_0x3657be[_0x1f7438(0x37e,0x367)],this['vertexShaderSource']=_0x3657be['vertexShaderSource'],this['fragmentShaderSource']=_0x3657be['fragmentShaderSource'],this[_0x2a8893(-0x23e,-0x289)]=_0x3657be['rawRenderState'],this['framebuffer']=_0x3657be['framebuffer'],this['outputTexture']=_0x3657be['outputTexture'],this['autoClear']=_0x3657be[_0x1f7438(0x2ce,0x291)]??![],this['preExecute']=_0x3657be['preExecute'],this['show']=!![],this['commandToExecute']=undefined;function _0x2a8893(_0x4c89b2,_0x5ed555){return _0x2d2794(_0x4c89b2,_0x5ed555- -0x32a);}this['clearCommand']=undefined,this[_0x1f7438(0x2c3,0x291)]&&(this['clearCommand']=new Cesium$6['ClearCommand']({'color':new Cesium$6[(_0x2a8893(-0x30e,-0x31d))](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':this['framebuffer'],'pass':Cesium$6[_0x2a8893(-0x287,-0x296)]['OPAQUE']}));}[_0x2d2794(0xb0,0x127)](_0x2620a8){function _0x4d6a22(_0x5a68ca,_0x394546){return _0x2d2794(_0x394546,_0x5a68ca-0x434);}function _0x36f9f5(_0x273c77,_0x104d27){return _0x420c92(_0x273c77,_0x104d27-0xd4);}switch(this['commandType']){case _0x36f9f5(-0x264,-0x1e5):{const _0x21bef2=Cesium$6[_0x4d6a22(0x536,0x51d)][_0x36f9f5(-0x117,-0x14b)]({'context':_0x2620a8,'geometry':this['geometry'],'attributeLocations':this['attributeLocations'],'bufferUsage':Cesium$6[_0x4d6a22(0x535,0x515)][_0x36f9f5(-0x215,-0x1f4)]}),_0x16d6f8={};_0x16d6f8['context']=_0x2620a8,_0x16d6f8[_0x4d6a22(0x551,0x4e9)]=this['attributeLocations'],_0x16d6f8[_0x36f9f5(-0x1f0,-0x1f9)]=this[_0x4d6a22(0x4b3,0x4af)],_0x16d6f8[_0x4d6a22(0x49e,0x4ba)]=this[_0x4d6a22(0x49e,0x462)];const _0x35d323=Cesium$6[_0x36f9f5(-0x2d3,-0x240)]['fromCache'](_0x16d6f8),_0x467d91=Cesium$6[_0x4d6a22(0x545,0x542)][_0x4d6a22(0x522,0x4b9)](this['rawRenderState']);return new Cesium$6['DrawCommand']({'primitiveType':this['primitiveType'],'shaderProgram':_0x35d323,'vertexArray':_0x21bef2,'modelMatrix':Cesium$6['Matrix4'][_0x36f9f5(-0x151,-0x1df)],'renderState':_0x467d91,'uniformMap':this['uniformMap'],'castShadows':![],'receiveShadows':![],'framebuffer':this['framebuffer'],'pass':Cesium$6[_0x4d6a22(0x4c8,0x487)]['OPAQUE'],'pickOnly':!![],'owner':this});}case'Compute':{const _0x44573f={};return _0x44573f['owner']=this,_0x44573f['fragmentShaderSource']=this['fragmentShaderSource'],_0x44573f['uniformMap']=this['uniformMap'],_0x44573f['outputTexture']=this['outputTexture'],_0x44573f['persists']=!![],new Cesium$6['ComputeCommand'](_0x44573f);}}}[_0x420c92(-0x1a7,-0x226)](_0x17162e,_0x1c6145){function _0x469a4a(_0x2110cc,_0x4d19a1){return _0x420c92(_0x4d19a1,_0x2110cc-0x500);}function _0x42c01c(_0x54475f,_0x4303de){return _0x420c92(_0x4303de,_0x54475f-0x31);}this['geometry']=_0x1c6145;const _0xb816e9=Cesium$6['VertexArray']['fromGeometry']({'context':_0x17162e,'geometry':this['geometry'],'attributeLocations':this['attributeLocations'],'bufferUsage':Cesium$6[_0x469a4a(0x2b5,0x224)][_0x469a4a(0x238,0x1b0)]});this['commandToExecute']['vertexArray']=_0xb816e9;}['update'](_0x3a722a){function _0x3eba64(_0x2d9421,_0x353a27){return _0x420c92(_0x2d9421,_0x353a27-0x57a);}if(!this['show'])return;function _0x3c9f35(_0x2570cd,_0x493c5c){return _0x2d2794(_0x493c5c,_0x2570cd- -0x29c);}if(_0x3a722a['mode']!==Cesium$6['SceneMode'][_0x3c9f35(-0x215,-0x289)])return;!Cesium$6['defined'](this['commandToExecute'])&&(this[_0x3c9f35(-0x252,-0x26b)]=this[_0x3eba64(0x3b1,0x355)](_0x3a722a[_0x3eba64(0x2aa,0x284)])),Cesium$6['defined'](this[_0x3eba64(0x335,0x2ef)])&&this[_0x3eba64(0x28f,0x2ef)](),Cesium$6['defined'](this['clearCommand'])&&_0x3a722a['commandList']['push'](this[_0x3c9f35(-0x1e0,-0x1d6)]),_0x3a722a['commandList'][_0x3c9f35(-0x1a8,-0x185)](this['commandToExecute']);}['isDestroyed'](){return![];}['destroy'](){if(this[_0x3b755b(0x190,0x112)]){var _0x22230a,_0x5af34a;(_0x22230a=this[_0x3b755b(0x190,0x19d)])!==null&&_0x22230a!==void 0x0&&_0x22230a[_0x47a9ee(0x46b,0x4e6)]&&this[_0x47a9ee(0x43e,0x3e6)]['vertexArray'][_0x47a9ee(0x459,0x4da)](),(_0x5af34a=this['clearCommand'])!==null&&_0x5af34a!==void 0x0&&_0x5af34a[_0x47a9ee(0x47a,0x499)]&&this[_0x47a9ee(0x43e,0x447)]['shaderProgram']['destroy'](),delete this['clearCommand'];}function _0x3b755b(_0x3d7b83,_0x160392){return _0x2d2794(_0x160392,_0x3d7b83-0xd4);}this[_0x47a9ee(0x3cc,0x3ba)]&&(this['commandToExecute'][_0x3b755b(0x1bd,0x1f4)]&&this['commandToExecute']['vertexArray']['destroy'](),this['commandToExecute'][_0x3b755b(0x1cc,0x15e)]&&this[_0x3b755b(0x11e,0x156)][_0x3b755b(0x1cc,0x199)][_0x47a9ee(0x459,0x40e)](),delete this[_0x47a9ee(0x3cc,0x35c)]);function _0x47a9ee(_0x530235,_0x37394b){return _0x2d2794(_0x37394b,_0x530235-0x382);}return Cesium$6['destroyObject'](this);}}const Cesium$5=mars3d__namespace['Cesium'],Util=(function(){const _0x498d7c=function(){function _0x3a9240(_0x554c9c,_0x5ddc09){return _0x54dc(_0x554c9c- -0x2cb,_0x5ddc09);}const _0x158933=new Cesium$5[(_0x3a9240(-0x21f,-0x294))]({'attributes':new Cesium$5['GeometryAttributes']({'position':new Cesium$5['GeometryAttribute']({'componentDatatype':Cesium$5[_0xd264e1(0x21d,0x27c)]['FLOAT'],'componentsPerAttribute':0x3,'values':new Float32Array([-0x1,-0x1,0x0,0x1,-0x1,0x0,0x1,0x1,0x0,-0x1,0x1,0x0])}),'st':new Cesium$5['GeometryAttribute']({'componentDatatype':Cesium$5[_0x3a9240(-0x1f6,-0x1f9)][_0x3a9240(-0x1e9,-0x190)],'componentsPerAttribute':0x2,'values':new Float32Array([0x0,0x0,0x1,0x0,0x1,0x1,0x0,0x1])})}),'indices':new Uint32Array([0x3,0x2,0x0,0x0,0x2,0x1])});function _0xd264e1(_0x1586b9,_0x28c7e7){return _0x54dc(_0x28c7e7-0x1a7,_0x1586b9);}return _0x158933;},_0x173bdc=function(_0x19bd18,_0xef618f){function _0x2f2b8b(_0x280165,_0x20a215){return _0x54dc(_0x20a215-0x3e8,_0x280165);}function _0x232688(_0x11d934,_0x28d160){return _0x54dc(_0x28d160- -0x2b1,_0x11d934);}if(Cesium$5[_0x2f2b8b(0x512,0x4a2)](_0xef618f)){const _0x205987={};_0x205987[_0x232688(-0x1ac,-0x1f4)]=_0xef618f,_0x19bd18['source']=_0x205987;}const _0x2b6755=new Cesium$5['Texture'](_0x19bd18);return _0x2b6755;},_0x59ed52=function(_0x5da5d7,_0x59a39e,_0x13f18b){const _0x4f3a6a={};_0x4f3a6a['context']=_0x5da5d7,_0x4f3a6a['colorTextures']=[_0x59a39e],_0x4f3a6a['depthTexture']=_0x13f18b;const _0x44e742=new Cesium$5['Framebuffer'](_0x4f3a6a);return _0x44e742;},_0x3b0e13=function(_0x4cb15d){function _0x2c82b2(_0x2baef3,_0x48414e){return _0x54dc(_0x2baef3-0x76,_0x48414e);}function _0x4a0f61(_0x12ab87,_0x1d2229){return _0x54dc(_0x1d2229- -0x392,_0x12ab87);}const _0x772f1a=!![],_0x4259fb=![],_0x47500d={};_0x47500d['viewport']=_0x4cb15d['viewport'],_0x47500d['depthTest']=_0x4cb15d[_0x4a0f61(-0x1d7,-0x1f5)],_0x47500d['depthMask']=_0x4cb15d[_0x2c82b2(0x18b,0x1b0)],_0x47500d['blending']=_0x4cb15d[_0x4a0f61(-0x1cc,-0x21f)];const _0x669a6c=_0x47500d,_0xb86d61=Cesium$5[_0x4a0f61(-0x2fb,-0x2b5)]['getDefaultRenderState'](_0x772f1a,_0x4259fb,_0x669a6c);return _0xb86d61;},_0x430b74=function(_0x3614a0){function _0x208ce6(_0x584784,_0x261d46){return _0x54dc(_0x584784-0x259,_0x261d46);}const _0x12ff73={},_0x296996=Cesium$5[_0x1c53bb(0x41f,0x457)][_0x208ce6(0x32a,0x2bc)](_0x3614a0['west'],Cesium$5['Math']['TWO_PI']),_0x24eb38=Cesium$5['Math']['mod'](_0x3614a0[_0x1c53bb(0x3d9,0x429)],Cesium$5['Math']['TWO_PI']),_0x517b6c=_0x3614a0['width'];let _0x704a61;function _0x1c53bb(_0x2fc3b2,_0x138678){return _0x54dc(_0x2fc3b2-0x2ee,_0x138678);}let _0x18d7cb;_0x517b6c>Cesium$5[_0x208ce6(0x38a,0x40e)]['THREE_PI_OVER_TWO']?(_0x704a61=0x0,_0x18d7cb=Cesium$5[_0x1c53bb(0x41f,0x479)][_0x1c53bb(0x46a,0x4f2)]):_0x24eb38-_0x296996<_0x517b6c?(_0x704a61=_0x296996,_0x18d7cb=_0x296996+_0x517b6c):(_0x704a61=_0x296996,_0x18d7cb=_0x24eb38);_0x12ff73['lon']={'min':Cesium$5[_0x1c53bb(0x41f,0x404)][_0x1c53bb(0x3a9,0x382)](_0x704a61),'max':Cesium$5[_0x208ce6(0x38a,0x392)][_0x208ce6(0x314,0x2fd)](_0x18d7cb)};const _0x25b7dc=_0x3614a0['south'],_0x1c0d15=_0x3614a0[_0x1c53bb(0x3bb,0x423)],_0x5ea6bc=_0x3614a0[_0x1c53bb(0x486,0x433)],_0x3667d0=_0x5ea6bc>Cesium$5[_0x1c53bb(0x41f,0x41d)]['PI']/0xc?_0x5ea6bc/0x2:0x0;let _0x3fa000=Cesium$5[_0x1c53bb(0x41f,0x435)][_0x208ce6(0x387,0x322)](_0x25b7dc-_0x3667d0),_0x54e417=Cesium$5[_0x1c53bb(0x41f,0x41c)]['clampToLatitudeRange'](_0x1c0d15+_0x3667d0);return _0x3fa000<-Cesium$5[_0x1c53bb(0x41f,0x45e)]['PI_OVER_THREE']&&(_0x3fa000=-Cesium$5['Math'][_0x1c53bb(0x48f,0x4c1)]),_0x54e417>Cesium$5[_0x208ce6(0x38a,0x350)]['PI_OVER_THREE']&&(_0x54e417=Cesium$5['Math'][_0x208ce6(0x3fa,0x468)]),_0x12ff73['lat']={'min':Cesium$5['Math'][_0x1c53bb(0x3a9,0x428)](_0x3fa000),'max':Cesium$5[_0x1c53bb(0x41f,0x49e)][_0x208ce6(0x314,0x2d4)](_0x54e417)},_0x12ff73;},_0x33de04={};_0x33de04[_0x4f4ac9(0x42d,0x3ab)]=_0x498d7c,_0x33de04['createTexture']=_0x173bdc,_0x33de04['createFramebuffer']=_0x59ed52,_0x33de04['createRawRenderState']=_0x3b0e13,_0x33de04[_0x4f4ac9(0x38f,0x3a1)]=_0x430b74;function _0x4f4ac9(_0x2180e8,_0x5663a4){return _0x420c92(_0x2180e8,_0x5663a4-0x5c5);}function _0x1f8ab4(_0x1bc6fa,_0x3d57ff){return _0x2d2794(_0x1bc6fa,_0x3d57ff- -0x52);}return _0x33de04;}());var segmentDraw_vert=_0x420c92(-0x26c,-0x301),segmentDraw_frag=_0x420c92(-0x332,-0x2a7),fullscreen_vert=_0x2d2794(0xff,0xca),trailDraw_frag=_0x2d2794(0x89,0x47),screenDraw_frag=_0x420c92(-0x20e,-0x22d);const Cesium$4=mars3d__namespace['Cesium'];function _0x54dc(_0x15af5d,_0x4e1d02){const _0x9c0ba5=_0x9c0b();return _0x54dc=function(_0x54dc14,_0x30f497){_0x54dc14=_0x54dc14-0x98;let _0x355b8e=_0x9c0ba5[_0x54dc14];return _0x355b8e;},_0x54dc(_0x15af5d,_0x4e1d02);}class ParticlesRendering{constructor(_0x28831d,_0x31cacd,_0x31ad3f,_0x48e3f1,_0x1461b2){this[_0x10fa49(0x81,0x40)](_0x28831d,_0x31cacd,_0x31ad3f[_0x10fa49(-0xbc,-0x68)]),this['createRenderingFramebuffers'](_0x28831d);function _0x1ea591(_0x567633,_0x3235f6){return _0x2d2794(_0x3235f6,_0x567633-0x7d);}function _0x10fa49(_0x4a450b,_0x3359bc){return _0x420c92(_0x4a450b,_0x3359bc-0x276);}this['createRenderingPrimitives'](_0x28831d,_0x31ad3f,_0x48e3f1,_0x1461b2);}['createRenderingTextures'](_0x6ac555,_0x21a485,_0x228886){function _0x34df87(_0x4a9426,_0x41c28e){return _0x2d2794(_0x41c28e,_0x4a9426- -0x24e);}const _0x145749={};_0x145749[_0x34df87(-0x1f8,-0x21d)]=_0x6ac555,_0x145749[_0x34df87(-0x191,-0x120)]=_0x6ac555['drawingBufferWidth'],_0x145749[_0x2ebe87(0x3c0,0x32b)]=_0x6ac555[_0x34df87(-0x1bd,-0x240)];function _0x2ebe87(_0x11dc1f,_0x25bd8d){return _0x420c92(_0x11dc1f,_0x25bd8d-0x56e);}_0x145749['pixelFormat']=Cesium$4[_0x34df87(-0x12a,-0xff)][_0x34df87(-0x1d5,-0x246)],_0x145749[_0x2ebe87(0x3c2,0x343)]=Cesium$4[_0x2ebe87(0x306,0x2c0)][_0x34df87(-0x1db,-0x232)];const _0x58af8b=_0x145749,_0x541d90={};_0x541d90[_0x2ebe87(0x294,0x278)]=_0x6ac555,_0x541d90['width']=_0x6ac555[_0x34df87(-0x1dc,-0x1f2)],_0x541d90['height']=_0x6ac555['drawingBufferHeight'],_0x541d90[_0x2ebe87(0x2cd,0x2e8)]=Cesium$4['PixelFormat'][_0x2ebe87(0x2d2,0x241)],_0x541d90['pixelDatatype']=Cesium$4['PixelDatatype']['UNSIGNED_INT'];const _0x15b962=_0x541d90,_0x4370f5=_0x228886[_0x2ebe87(0x2be,0x23e)],_0x22a443=new Float32Array(_0x4370f5*0x3);for(let _0x41ca72=0x0;_0x41ca72<_0x4370f5;_0x41ca72++){const _0x28f07a=Cesium$4['Color']['fromCssColorString'](_0x228886[_0x41ca72]);_0x22a443[0x3*_0x41ca72]=_0x28f07a[_0x34df87(-0x243,-0x27d)],_0x22a443[0x3*_0x41ca72+0x1]=_0x28f07a[_0x34df87(-0x1b1,-0x183)],_0x22a443[0x3*_0x41ca72+0x2]=_0x28f07a[_0x34df87(-0x21c,-0x1f2)];}const _0x3a7211={'context':_0x6ac555,'width':_0x4370f5,'height':0x1,'pixelFormat':Cesium$4[_0x34df87(-0x12a,-0x124)]['RGB'],'pixelDatatype':Cesium$4[_0x2ebe87(0x23d,0x2c0)]['FLOAT'],'sampler':new Cesium$4[(_0x2ebe87(0x2ac,0x2d2))]({'minificationFilter':Cesium$4[_0x2ebe87(0x34e,0x2fa)][_0x34df87(-0x190,-0x197)],'magnificationFilter':Cesium$4['TextureMagnificationFilter']['LINEAR']})};this['textures']={'segmentsColor':Util['createTexture'](_0x58af8b),'segmentsDepth':Util[_0x2ebe87(0x28d,0x2e2)](_0x15b962),'currentTrailsColor':Util[_0x2ebe87(0x370,0x2e2)](_0x58af8b),'currentTrailsDepth':Util['createTexture'](_0x15b962),'nextTrailsColor':Util['createTexture'](_0x58af8b),'nextTrailsDepth':Util['createTexture'](_0x15b962),'colorTable':Util['createTexture'](_0x3a7211,_0x22a443)};}['createRenderingFramebuffers'](_0x4857fd){function _0x5d6ea6(_0x4cef91,_0x2e5120){return _0x420c92(_0x4cef91,_0x2e5120-0x1ca);}function _0x3ea8a2(_0xe24ec4,_0x106075){return _0x2d2794(_0xe24ec4,_0x106075- -0x1c5);}this[_0x3ea8a2(-0x1b1,-0x176)]={'segments':Util['createFramebuffer'](_0x4857fd,this['textures']['segmentsColor'],this['textures']['segmentsDepth']),'currentTrails':Util['createFramebuffer'](_0x4857fd,this[_0x5d6ea6(-0xe8,-0xb5)]['currentTrailsColor'],this['textures']['currentTrailsDepth']),'nextTrails':Util['createFramebuffer'](_0x4857fd,this['textures']['nextTrailsColor'],this['textures']['nextTrailsDepth'])};}[_0x420c92(-0x205,-0x234)](_0x4b050a){const _0x280d01=0x4;let _0x438479=[];for(let _0xa7c422=0x0;_0xa7c422<_0x4b050a[_0x291271(0x7c,0x85)];_0xa7c422++){for(let _0x4ce8a2=0x0;_0x4ce8a2<_0x4b050a['particlesTextureSize'];_0x4ce8a2++){for(let _0x4e577d=0x0;_0x4e577d<_0x280d01;_0x4e577d++){_0x438479[_0x291271(0xe4,0xa3)](_0xa7c422/_0x4b050a['particlesTextureSize']),_0x438479['push'](_0x4ce8a2/_0x4b050a['particlesTextureSize']);}}}_0x438479=new Float32Array(_0x438479);let _0x2569ef=[];function _0x291271(_0x190668,_0x18c77c){return _0x2d2794(_0x190668,_0x18c77c- -0x51);}const _0x26e471=[-0x1,0x1],_0x4c83da=[-0x1,0x1];for(let _0x2821f1=0x0;_0x2821f1<_0x4b050a['maxParticles'];_0x2821f1++){for(let _0x3a27db=0x0;_0x3a27db<_0x280d01/0x2;_0x3a27db++){for(let _0x5b822b=0x0;_0x5b822b<_0x280d01/0x2;_0x5b822b++){_0x2569ef['push'](_0x26e471[_0x3a27db]),_0x2569ef[_0x131f28(-0xf6,-0xe3)](_0x4c83da[_0x5b822b]),_0x2569ef['push'](0x0);}}}_0x2569ef=new Float32Array(_0x2569ef);const _0x106a12=0x6*_0x4b050a[_0x131f28(-0x10a,-0xa9)],_0x383107=new Uint32Array(_0x106a12);for(let _0x40cd51=0x0,_0x1ceef2=0x0,_0x5ac1f4=0x0;_0x40cd51<_0x4b050a[_0x131f28(-0x111,-0xa9)];_0x40cd51++){_0x383107[_0x1ceef2++]=_0x5ac1f4+0x0,_0x383107[_0x1ceef2++]=_0x5ac1f4+0x1,_0x383107[_0x1ceef2++]=_0x5ac1f4+0x2,_0x383107[_0x1ceef2++]=_0x5ac1f4+0x2,_0x383107[_0x1ceef2++]=_0x5ac1f4+0x1,_0x383107[_0x1ceef2++]=_0x5ac1f4+0x3,_0x5ac1f4+=0x4;}const _0x569447=new Cesium$4['Geometry']({'attributes':new Cesium$4['GeometryAttributes']({'st':new Cesium$4['GeometryAttribute']({'componentDatatype':Cesium$4[_0x291271(-0x8b,-0xb)]['FLOAT'],'componentsPerAttribute':0x2,'values':_0x438479}),'normal':new Cesium$4['GeometryAttribute']({'componentDatatype':Cesium$4['ComponentDatatype'][_0x291271(-0x49,0x2)],'componentsPerAttribute':0x3,'values':_0x2569ef})}),'indices':_0x383107});function _0x131f28(_0x1811de,_0x2af631){return _0x420c92(_0x1811de,_0x2af631-0x175);}return _0x569447;}[_0x2d2794(0x42,0xe)](_0x1b14af,_0xad535a,_0x316656,_0x3e4711){const _0x204df7=this,_0x424216={};_0x424216['st']=0x0,_0x424216['normal']=0x1;const _0x3ff4c1={};_0x3ff4c1['sources']=[segmentDraw_vert];const _0x2ed240={};_0x2ed240['sources']=[segmentDraw_frag];const _0x459756={};_0x459756[_0x29a173(0x4f8,0x57e)]=!![];const _0x5c05cb={};_0x5c05cb[_0x29a173(0x4ce,0x53d)]=undefined,_0x5c05cb['depthTest']=_0x459756,_0x5c05cb['depthMask']=!![];const _0x345abf={};_0x345abf[_0x41d9c1(0x3a7,0x408)]=0x0,_0x345abf['st']=0x1;const _0x3f1f7a={};_0x3f1f7a[_0x29a173(0x4f6,0x54b)]=[_0x41d9c1(0x400,0x484)],_0x3f1f7a[_0x41d9c1(0x407,0x41a)]=[fullscreen_vert];const _0x3e9a08={};_0x3e9a08['defines']=['DISABLE_LOG_DEPTH_FRAGMENT_WRITE'],_0x3e9a08['sources']=[trailDraw_frag];const _0x43ce52={};_0x43ce52['position']=0x0;function _0x41d9c1(_0x5ba858,_0x27a84a){return _0x420c92(_0x27a84a,_0x5ba858-0x650);}function _0x29a173(_0x92e9dc,_0x634c3){return _0x2d2794(_0x634c3,_0x92e9dc-0x44d);}_0x43ce52['st']=0x1;const _0x44613d={};_0x44613d['defines']=[_0x29a173(0x549,0x5ac)],_0x44613d['sources']=[fullscreen_vert];const _0x4f60b5={};_0x4f60b5['defines']=['DISABLE_LOG_DEPTH_FRAGMENT_WRITE'],_0x4f60b5[_0x41d9c1(0x407,0x44d)]=[screenDraw_frag];const _0x3cf635={};_0x3cf635['enabled']=![];const _0x25a5cd={};_0x25a5cd[_0x29a173(0x4f8,0x499)]=!![],this[_0x41d9c1(0x422,0x45e)]={'segments':new CustomPrimitive({'commandType':'Draw','attributeLocations':_0x424216,'geometry':this['createSegmentsGeometry'](_0xad535a),'primitiveType':Cesium$4[_0x41d9c1(0x356,0x36e)]['TRIANGLES'],'uniformMap':{'currentParticlesPosition':function(){function _0xe79d39(_0x4e8fb6,_0x56d092){return _0x41d9c1(_0x56d092- -0x142,_0x4e8fb6);}return _0x3e4711[_0xe79d39(0x2db,0x2c0)]['currentParticlesPosition'];},'postProcessingPosition':function(){function _0xe77597(_0x1c9b20,_0x45b774){return _0x41d9c1(_0x45b774-0xaa,_0x1c9b20);}return _0x3e4711['particlesTextures'][_0xe77597(0x4ec,0x4be)];},'postProcessingSpeed':function(){return _0x3e4711['particlesTextures']['postProcessingSpeed'];},'colorTable':function(){return _0x204df7['textures']['colorTable'];},'aspect':function(){function _0x2167ed(_0x56d0b0,_0x2f2eb){return _0x29a173(_0x56d0b0- -0x27f,_0x2f2eb);}return _0x1b14af[_0x2167ed(0x240,0x1cf)]/_0x1b14af['drawingBufferHeight'];},'pixelSize':function(){function _0x282372(_0x5eddaf,_0x2c2379){return _0x41d9c1(_0x5eddaf- -0x4e6,_0x2c2379);}return _0x316656[_0x282372(-0x128,-0x98)];},'lineWidth':function(){return _0xad535a['lineWidth'];},'particleHeight':function(){return _0xad535a['particleHeight'];}},'vertexShaderSource':new Cesium$4['ShaderSource'](_0x3ff4c1),'fragmentShaderSource':new Cesium$4[(_0x29a173(0x457,0x40c))](_0x2ed240),'rawRenderState':Util[_0x29a173(0x4a2,0x4fe)](_0x5c05cb),'framebuffer':this['framebuffers']['segments'],'autoClear':!![]}),'trails':new CustomPrimitive({'commandType':'Draw','attributeLocations':_0x345abf,'geometry':Util['getFullscreenQuad'](),'primitiveType':Cesium$4['PrimitiveType']['TRIANGLES'],'uniformMap':{'segmentsColorTexture':function(){return _0x204df7['textures']['segmentsColor'];},'segmentsDepthTexture':function(){function _0xfc3c9c(_0x11f8bd,_0x2afd74){return _0x29a173(_0x2afd74- -0x577,_0x11f8bd);}return _0x204df7[_0xfc3c9c(-0x33,-0x5d)]['segmentsDepth'];},'currentTrailsColor':function(){return _0x204df7['framebuffers']['currentTrails']['getColorTexture'](0x0);},'trailsDepthTexture':function(){return _0x204df7['framebuffers']['currentTrails']['depthTexture'];},'fadeOpacity':function(){return _0xad535a['fadeOpacity'];}},'vertexShaderSource':new Cesium$4[(_0x29a173(0x457,0x471))](_0x3f1f7a),'fragmentShaderSource':new Cesium$4[(_0x29a173(0x457,0x42b))](_0x3e9a08),'rawRenderState':Util[_0x29a173(0x4a2,0x474)]({'viewport':undefined,'depthTest':{'enabled':!![],'func':Cesium$4[_0x29a173(0x46e,0x43d)]['ALWAYS']},'depthMask':!![]}),'framebuffer':this['framebuffers']['nextTrails'],'autoClear':!![],'preExecute':function(){const _0x44a383=_0x204df7[_0x5cbd26(0x492,0x477)][_0x372460(0x2ce,0x273)];function _0x372460(_0x52ed8c,_0x5549f2){return _0x41d9c1(_0x52ed8c- -0x10f,_0x5549f2);}function _0x5cbd26(_0x38626e,_0x2f2825){return _0x29a173(_0x38626e- -0xa,_0x2f2825);}_0x204df7['framebuffers']['currentTrails']=_0x204df7['framebuffers']['nextTrails'],_0x204df7['framebuffers']['nextTrails']=_0x44a383,_0x204df7['primitives']['trails']['commandToExecute']['framebuffer']=_0x204df7['framebuffers']['nextTrails'],_0x204df7[_0x372460(0x313,0x29f)]['trails']['clearCommand']['framebuffer']=_0x204df7[_0x372460(0x244,0x28e)]['nextTrails'];}}),'screen':new CustomPrimitive({'commandType':'Draw','attributeLocations':_0x43ce52,'geometry':Util[_0x41d9c1(0x436,0x40a)](),'primitiveType':Cesium$4['PrimitiveType']['TRIANGLES'],'uniformMap':{'trailsColorTexture':function(){function _0x4e12d1(_0x3fa1d1,_0x24c251){return _0x41d9c1(_0x3fa1d1- -0x3a,_0x24c251);}return _0x204df7['framebuffers'][_0x4e12d1(0x2d9,0x27b)]['getColorTexture'](0x0);},'trailsDepthTexture':function(){function _0x4aec58(_0x50c7fb,_0x29e279){return _0x41d9c1(_0x29e279- -0x3b7,_0x50c7fb);}return _0x204df7['framebuffers'][_0x4aec58(-0x134,-0xa4)]['depthTexture'];}},'vertexShaderSource':new Cesium$4[(_0x41d9c1(0x30e,0x2ea))](_0x44613d),'fragmentShaderSource':new Cesium$4['ShaderSource'](_0x4f60b5),'rawRenderState':Util['createRawRenderState']({'viewport':undefined,'depthTest':_0x3cf635,'depthMask':!![],'blending':_0x25a5cd}),'framebuffer':undefined})};}}var getWind_frag='//\x20the\x20size\x20of\x20UV\x20textures:\x20width\x20=\x20lon,\x20height\x20=\x20lat*lev\x0auniform\x20sampler2D\x20U;\x20//\x20eastward\x20wind\x0auniform\x20sampler2D\x20V;\x20//\x20northward\x20wind\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0a\x0auniform\x20vec3\x20dimension;\x20//\x20(lon,\x20lat,\x20lev)\x0auniform\x20vec3\x20minimum;\x20//\x20minimum\x20of\x20each\x20dimension\x0auniform\x20vec3\x20maximum;\x20//\x20maximum\x20of\x20each\x20dimension\x0auniform\x20vec3\x20interval;\x20//\x20interval\x20of\x20each\x20dimension\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20mapPositionToNormalizedIndex2D(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20range\x20of\x20longitude\x20and\x20latitude\x0a\x20\x20\x20\x20lonLatLev.x\x20=\x20mod(lonLatLev.x,\x20360.0);\x0a\x20\x20\x20\x20lonLatLev.y\x20=\x20clamp(lonLatLev.y,\x20-90.0,\x2090.0);\x0a\x0a\x20\x20\x20\x20vec3\x20index3D\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20index3D.x\x20=\x20(lonLatLev.x\x20-\x20minimum.x)\x20/\x20interval.x;\x0a\x20\x20\x20\x20index3D.y\x20=\x20(lonLatLev.y\x20-\x20minimum.y)\x20/\x20interval.y;\x0a\x20\x20\x20\x20index3D.z\x20=\x20(lonLatLev.z\x20-\x20minimum.z)\x20/\x20interval.z;\x0a\x0a\x20\x20\x20\x20//\x20the\x20st\x20texture\x20coordinate\x20corresponding\x20to\x20(col,\x20row)\x20index\x0a\x20\x20\x20\x20//\x20example\x0a\x20\x20\x20\x20//\x20data\x20array\x20is\x20[0,\x201,\x202,\x203,\x204,\x205],\x20width\x20=\x203,\x20height\x20=\x202\x0a\x20\x20\x20\x20//\x20the\x20content\x20of\x20texture\x20will\x20be\x0a\x20\x20\x20\x20//\x20t\x201.0\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x20\x203\x204\x205\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x20\x200\x201\x202\x0a\x20\x20\x20\x20//\x20\x20\x200.0------1.0\x20s\x0a\x0a\x20\x20\x20\x20vec2\x20index2D\x20=\x20vec2(index3D.x,\x20index3D.z\x20*\x20dimension.y\x20+\x20index3D.y);\x0a\x20\x20\x20\x20vec2\x20normalizedIndex2D\x20=\x20vec2(index2D.x\x20/\x20dimension.x,\x20index2D.y\x20/\x20(dimension.y\x20*\x20dimension.z));\x0a\x20\x20\x20\x20return\x20normalizedIndex2D;\x0a}\x0a\x0afloat\x20getWind(sampler2D\x20windTexture,\x20vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLatLev);\x0a\x20\x20\x20\x20float\x20result\x20=\x20texture(windTexture,\x20normalizedIndex2D).r;\x0a\x20\x20\x20\x20return\x20result;\x0a}\x0a\x0aconst\x20mat4\x20kernelMatrix\x20=\x20mat4(\x0a\x20\x20\x20\x200.0,\x20-1.0,\x202.0,\x20-1.0,\x20//\x20first\x20column\x0a\x20\x20\x20\x202.0,\x200.0,\x20-5.0,\x203.0,\x20//\x20second\x20column\x0a\x20\x20\x20\x200.0,\x201.0,\x204.0,\x20-3.0,\x20//\x20third\x20column\x0a\x20\x20\x20\x200.0,\x200.0,\x20-1.0,\x201.0\x20//\x20fourth\x20column\x0a);\x0afloat\x20oneDimensionInterpolation(float\x20t,\x20float\x20p0,\x20float\x20p1,\x20float\x20p2,\x20float\x20p3)\x20{\x0a\x20\x20\x20\x20vec4\x20tVec4\x20=\x20vec4(1.0,\x20t,\x20t\x20*\x20t,\x20t\x20*\x20t\x20*\x20t);\x0a\x20\x20\x20\x20tVec4\x20=\x20tVec4\x20/\x202.0;\x0a\x20\x20\x20\x20vec4\x20pVec4\x20=\x20vec4(p0,\x20p1,\x20p2,\x20p3);\x0a\x20\x20\x20\x20return\x20dot((tVec4\x20*\x20kernelMatrix),\x20pVec4);\x0a}\x0a\x0afloat\x20calculateB(sampler2D\x20windTexture,\x20float\x20t,\x20float\x20lon,\x20float\x20lat,\x20float\x20lev)\x20{\x0a\x20\x20\x20\x20float\x20lon0\x20=\x20floor(lon)\x20-\x201.0\x20*\x20interval.x;\x0a\x20\x20\x20\x20float\x20lon1\x20=\x20floor(lon);\x0a\x20\x20\x20\x20float\x20lon2\x20=\x20floor(lon)\x20+\x201.0\x20*\x20interval.x;\x0a\x20\x20\x20\x20float\x20lon3\x20=\x20floor(lon)\x20+\x202.0\x20*\x20interval.x;\x0a\x0a\x20\x20\x20\x20float\x20p0\x20=\x20getWind(windTexture,\x20vec3(lon0,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p1\x20=\x20getWind(windTexture,\x20vec3(lon1,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p2\x20=\x20getWind(windTexture,\x20vec3(lon2,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p3\x20=\x20getWind(windTexture,\x20vec3(lon3,\x20lat,\x20lev));\x0a\x0a\x20\x20\x20\x20return\x20oneDimensionInterpolation(t,\x20p0,\x20p1,\x20p2,\x20p3);\x0a}\x0a\x0afloat\x20interpolateOneTexture(sampler2D\x20windTexture,\x20vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20float\x20lon\x20=\x20lonLatLev.x;\x0a\x20\x20\x20\x20float\x20lat\x20=\x20lonLatLev.y;\x0a\x20\x20\x20\x20float\x20lev\x20=\x20lonLatLev.z;\x0a\x0a\x20\x20\x20\x20float\x20lat0\x20=\x20floor(lat)\x20-\x201.0\x20*\x20interval.y;\x0a\x20\x20\x20\x20float\x20lat1\x20=\x20floor(lat);\x0a\x20\x20\x20\x20float\x20lat2\x20=\x20floor(lat)\x20+\x201.0\x20*\x20interval.y;\x0a\x20\x20\x20\x20float\x20lat3\x20=\x20floor(lat)\x20+\x202.0\x20*\x20interval.y;\x0a\x0a\x20\x20\x20\x20vec2\x20coefficient\x20=\x20lonLatLev.xy\x20-\x20floor(lonLatLev.xy);\x0a\x20\x20\x20\x20float\x20b0\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat0,\x20lev);\x0a\x20\x20\x20\x20float\x20b1\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat1,\x20lev);\x0a\x20\x20\x20\x20float\x20b2\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat2,\x20lev);\x0a\x20\x20\x20\x20float\x20b3\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat3,\x20lev);\x0a\x0a\x20\x20\x20\x20return\x20oneDimensionInterpolation(coefficient.y,\x20b0,\x20b1,\x20b2,\x20b3);\x0a}\x0a\x0avec3\x20bicubic(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20https://en.wikipedia.org/wiki/Bicubic_interpolation#Bicubic_convolution_algorithm\x0a\x20\x20\x20\x20float\x20u\x20=\x20interpolateOneTexture(U,\x20lonLatLev);\x0a\x20\x20\x20\x20float\x20v\x20=\x20interpolateOneTexture(V,\x20lonLatLev);\x0a\x20\x20\x20\x20float\x20w\x20=\x200.0;\x0a\x20\x20\x20\x20return\x20vec3(u,\x20v,\x20w);\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20lonLatLev\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20windVector\x20=\x20bicubic(lonLatLev);\x0a\x20\x20\x20\x20out_FragColor\x20=\x20vec4(windVector,\x200.0);\x0a}\x0a',updateSpeed_frag=_0x2d2794(0xf0,0x7c),updatePosition_frag='uniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0auniform\x20sampler2D\x20currentParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20lengthOfLonLat(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20unit\x20conversion:\x20meters\x20->\x20longitude\x20latitude\x20degrees\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_system#Length_of_a_degree\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20the\x20length\x20of\x20a\x20degree\x20of\x20latitude\x20and\x20longitude\x20in\x20meters\x0a\x20\x20\x20\x20float\x20latitude\x20=\x20radians(lonLatLev.y);\x0a\x0a\x20\x20\x20\x20float\x20term1\x20=\x20111132.92;\x0a\x20\x20\x20\x20float\x20term2\x20=\x20559.82\x20*\x20cos(2.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term3\x20=\x201.175\x20*\x20cos(4.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term4\x20=\x200.0023\x20*\x20cos(6.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20latLength\x20=\x20term1\x20-\x20term2\x20+\x20term3\x20-\x20term4;\x0a\x0a\x20\x20\x20\x20float\x20term5\x20=\x20111412.84\x20*\x20cos(latitude);\x0a\x20\x20\x20\x20float\x20term6\x20=\x2093.5\x20*\x20cos(3.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term7\x20=\x200.118\x20*\x20cos(5.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20longLength\x20=\x20term5\x20-\x20term6\x20+\x20term7;\x0a\x0a\x20\x20\x20\x20return\x20vec2(longLength,\x20latLength);\x0a}\x0a\x0avoid\x20updatePosition(vec3\x20lonLatLev,\x20vec3\x20speed)\x20{\x0a\x20\x20\x20\x20vec2\x20lonLatLength\x20=\x20lengthOfLonLat(lonLatLev);\x0a\x20\x20\x20\x20float\x20u\x20=\x20speed.x\x20/\x20lonLatLength.x;\x0a\x20\x20\x20\x20float\x20v\x20=\x20speed.y\x20/\x20lonLatLength.y;\x0a\x20\x20\x20\x20float\x20w\x20=\x200.0;\x0a\x20\x20\x20\x20vec3\x20windVectorInLonLatLev\x20=\x20vec3(u,\x20v,\x20w);\x0a\x0a\x20\x20\x20\x20vec3\x20nextParticle\x20=\x20lonLatLev\x20+\x20windVectorInLonLatLev;\x0a\x0a\x20\x20\x20\x20out_FragColor\x20=\x20vec4(nextParticle,\x200.0);\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20lonLatLev\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20speed\x20=\x20texture(currentParticlesSpeed,\x20v_textureCoordinates).rgb;\x0a\x0a\x20\x20\x20\x20updatePosition(lonLatLev,\x20speed);\x0a}\x0a',postProcessingPosition_frag='uniform\x20sampler2D\x20nextParticlesPosition;\x0auniform\x20sampler2D\x20nextParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0a\x0a//\x20range\x20(min,\x20max)\x0auniform\x20vec2\x20lonRange;\x0auniform\x20vec2\x20latRange;\x0a\x0auniform\x20float\x20randomCoefficient;\x20//\x20use\x20to\x20improve\x20the\x20pseudo-random\x20generator\x0auniform\x20float\x20dropRate;\x20//\x20drop\x20rate\x20is\x20a\x20chance\x20a\x20particle\x20will\x20restart\x20at\x20random\x20position\x20to\x20avoid\x20degeneration\x0auniform\x20float\x20dropRateBump;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0a//\x20pseudo-random\x20generator\x0aconst\x20vec3\x20randomConstants\x20=\x20vec3(12.9898,\x2078.233,\x204375.85453);\x0aconst\x20vec2\x20normalRange\x20=\x20vec2(0.0,\x201.0);\x0afloat\x20rand(vec2\x20seed,\x20vec2\x20range)\x20{\x0a\x20\x20\x20\x20vec2\x20randomSeed\x20=\x20randomCoefficient\x20*\x20seed;\x0a\x20\x20\x20\x20float\x20temp\x20=\x20dot(randomConstants.xy,\x20randomSeed);\x0a\x20\x20\x20\x20temp\x20=\x20fract(sin(temp)\x20*\x20(randomConstants.z\x20+\x20temp));\x0a\x20\x20\x20\x20return\x20temp\x20*\x20(range.y\x20-\x20range.x)\x20+\x20range.x;\x0a}\x0a\x0avec3\x20generateRandomParticle(vec2\x20seed,\x20float\x20lev)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20longitude\x20is\x20in\x20[0,\x20360]\x0a\x20\x20\x20\x20float\x20randomLon\x20=\x20mod(rand(seed,\x20lonRange),\x20360.0);\x0a\x20\x20\x20\x20float\x20randomLat\x20=\x20rand(-seed,\x20latRange);\x0a\x0a\x20\x20\x20\x20return\x20vec3(randomLon,\x20randomLat,\x20lev);\x0a}\x0a\x0abool\x20particleOutbound(vec3\x20particle)\x20{\x0a\x20\x20\x20\x20return\x20particle.y\x20<\x20-90.0\x20||\x20particle.y\x20>\x2090.0;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec3\x20nextParticle\x20=\x20texture(nextParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec4\x20nextSpeed\x20=\x20texture(nextParticlesSpeed,\x20v_textureCoordinates);\x0a\x20\x20\x20\x20float\x20particleDropRate\x20=\x20dropRate\x20+\x20dropRateBump\x20*\x20nextSpeed.a;\x0a\x0a\x20\x20\x20\x20vec2\x20seed1\x20=\x20nextParticle.xy\x20+\x20v_textureCoordinates;\x0a\x20\x20\x20\x20vec2\x20seed2\x20=\x20nextSpeed.xy\x20+\x20v_textureCoordinates;\x0a\x20\x20\x20\x20vec3\x20randomParticle\x20=\x20generateRandomParticle(seed1,\x20nextParticle.z);\x0a\x20\x20\x20\x20float\x20randomNumber\x20=\x20rand(seed2,\x20normalRange);\x0a\x0a\x20\x20\x20\x20if\x20(randomNumber\x20<\x20particleDropRate\x20||\x20particleOutbound(nextParticle))\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(randomParticle,\x201.0);\x20//\x201.0\x20means\x20this\x20is\x20a\x20random\x20particle\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(nextParticle,\x200.0);\x0a\x20\x20\x20\x20}\x0a}\x0a',postProcessingSpeed_frag='uniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20nextParticlesSpeed;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20randomParticle\x20=\x20texture(postProcessingPosition,\x20v_textureCoordinates);\x0a\x20\x20\x20\x20vec4\x20particleSpeed\x20=\x20texture(nextParticlesSpeed,\x20v_textureCoordinates);\x0a\x0a\x20\x20\x20\x20if\x20(randomParticle.a\x20>\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20particleSpeed;\x0a\x20\x20\x20\x20}\x0a}\x0a';const Cesium$3=mars3d__namespace[_0x2d2794(0x30,0x48)];class ParticlesComputing{constructor(_0x54bb29,_0x4aac45,_0x5048ac,_0x1b9b46){this[_0x11c43f(-0x41,-0x28)]=_0x4aac45;function _0x11c43f(_0x596531,_0x20e082){return _0x420c92(_0x20e082,_0x596531-0x279);}this['createWindTextures'](_0x54bb29,_0x4aac45);function _0x3c6d9c(_0x42cdd6,_0x2497bf){return _0x2d2794(_0x42cdd6,_0x2497bf- -0x141);}this[_0x3c6d9c(-0x37,-0xa6)](_0x54bb29,_0x5048ac,_0x1b9b46),this['createComputingPrimitives'](_0x4aac45,_0x5048ac,_0x1b9b46);}['createWindTextures'](_0x11459e,_0x31fcc9){function _0x3c50ac(_0x5a5c17,_0x214777){return _0x420c92(_0x5a5c17,_0x214777-0x24);}const _0x30d3d6={'context':_0x11459e,'width':_0x31fcc9['dimensions']['lon'],'height':_0x31fcc9['dimensions'][_0x3c50ac(-0x339,-0x2ff)]*(_0x31fcc9[_0x5713a9(-0x205,-0x204)]['lev']||0x1),'pixelFormat':Cesium$3[_0x5713a9(-0x194,-0x1ac)]['LUMINANCE'],'pixelDatatype':Cesium$3['PixelDatatype'][_0x5713a9(-0x265,-0x27f)],'flipY':![],'sampler':new Cesium$3['Sampler']({'minificationFilter':Cesium$3[_0x3c50ac(-0x2ca,-0x250)]['NEAREST'],'magnificationFilter':Cesium$3[_0x3c50ac(-0x27d,-0x271)]['NEAREST']})};function _0x5713a9(_0x393e39,_0x220e6b){return _0x2d2794(_0x220e6b,_0x393e39- -0x2b8);}this['windTextures']={'U':Util['createTexture'](_0x30d3d6,_0x31fcc9['U']['array']),'V':Util['createTexture'](_0x30d3d6,_0x31fcc9['V']['array'])};}[_0x2d2794(0x106,0x9b)](_0x3cfea3,_0x11c979,_0x543f8d){const _0x1dc2db={'context':_0x3cfea3,'width':_0x11c979['particlesTextureSize'],'height':_0x11c979['particlesTextureSize'],'pixelFormat':Cesium$3['PixelFormat'][_0xdfac3d(0x36a,0x325)],'pixelDatatype':Cesium$3['PixelDatatype']['FLOAT'],'flipY':![],'sampler':new Cesium$3['Sampler']({'minificationFilter':Cesium$3['TextureMinificationFilter']['NEAREST'],'magnificationFilter':Cesium$3['TextureMagnificationFilter'][_0xdfac3d(0x39e,0x3f9)]})},_0x215837=this[_0xdfac3d(0x336,0x2cf)](_0x11c979['maxParticles'],_0x543f8d),_0x9379f0=new Float32Array(0x4*_0x11c979['maxParticles'])['fill'](0x0);function _0x552de2(_0x259037,_0x2189e4){return _0x420c92(_0x2189e4,_0x259037-0x412);}function _0xdfac3d(_0x57bbb6,_0x56d035){return _0x420c92(_0x56d035,_0x57bbb6-0x63d);}this['particlesTextures']={'particlesWind':Util[_0x552de2(0x186,0x213)](_0x1dc2db),'currentParticlesPosition':Util['createTexture'](_0x1dc2db,_0x215837),'nextParticlesPosition':Util[_0xdfac3d(0x3b1,0x37a)](_0x1dc2db,_0x215837),'currentParticlesSpeed':Util[_0x552de2(0x186,0x156)](_0x1dc2db,_0x9379f0),'nextParticlesSpeed':Util['createTexture'](_0x1dc2db,_0x9379f0),'postProcessingPosition':Util['createTexture'](_0x1dc2db,_0x215837),'postProcessingSpeed':Util[_0x552de2(0x186,0x1d4)](_0x1dc2db,_0x9379f0)};}['randomizeParticles'](_0x10430d,_0x58d7de){const _0x5085ad=new Float32Array(0x4*_0x10430d);for(let _0x25159e=0x0;_0x25159e<_0x10430d;_0x25159e++){_0x5085ad[0x4*_0x25159e]=Cesium$3[_0x5c9c46(0x4d2,0x473)][_0x5c9c46(0x3fe,0x3e4)](_0x58d7de[_0xa8af24(0x2c6,0x2f8)]['x'],_0x58d7de['lonRange']['y']),_0x5085ad[0x4*_0x25159e+0x1]=Cesium$3[_0x5c9c46(0x468,0x473)][_0x5c9c46(0x361,0x3e4)](_0x58d7de['latRange']['x'],_0x58d7de[_0xa8af24(0x2f0,0x2af)]['y']),_0x5085ad[0x4*_0x25159e+0x2]=Cesium$3[_0xa8af24(0x2a5,0x2f3)][_0x5c9c46(0x3e9,0x3e4)](this['data'][_0xa8af24(0x2c7,0x356)][_0x5c9c46(0x4ab,0x44c)],this[_0xa8af24(0x305,0x2e3)]['lev'][_0xa8af24(0x393,0x36c)]),_0x5085ad[0x4*_0x25159e+0x3]=0x0;}function _0xa8af24(_0x4f2f27,_0xb10858){return _0x2d2794(_0x4f2f27,_0xb10858-0x251);}function _0x5c9c46(_0x385100,_0x16b527){return _0x420c92(_0x385100,_0x16b527-0x71d);}return _0x5085ad;}[_0x420c92(-0x1f7,-0x21d)](){function _0x2916a9(_0x36e697,_0x4ab6fe){return _0x2d2794(_0x4ab6fe,_0x36e697-0x3ca);}Object[_0x2916a9(0x430,0x3bb)](this['particlesTextures'])['forEach'](_0x2ec2a8=>{this['particlesTextures'][_0x2ec2a8]['destroy']();});}['createComputingPrimitives'](_0x25f555,_0x46670d,_0x2c7ff3){const _0x363273=new Cesium$3['Cartesian3'](_0x25f555[_0x36ab62(-0x28d,-0x29c)]['lon'],_0x25f555[_0x4cdfc5(0x34d,0x38a)]['lat'],_0x25f555[_0x36ab62(-0x249,-0x29c)][_0x4cdfc5(0x39f,0x343)]),_0x196de6=new Cesium$3[(_0x4cdfc5(0x385,0x330))](_0x25f555['lon']['min'],_0x25f555['lat'][_0x4cdfc5(0x315,0x36a)],_0x25f555['lev']['min']),_0x144945=new Cesium$3[(_0x4cdfc5(0x385,0x391))](_0x25f555['lon']['max'],_0x25f555[_0x36ab62(-0x33c,-0x326)][_0x4cdfc5(0x3b5,0x3f2)],_0x25f555['lev']['max']),_0x15dc47=new Cesium$3['Cartesian3']((_0x144945['x']-_0x196de6['x'])/(_0x363273['x']-0x1),(_0x144945['y']-_0x196de6['y'])/(_0x363273['y']-0x1),_0x363273['z']>0x1?(_0x144945['z']-_0x196de6['z'])/(_0x363273['z']-0x1):0x1),_0x58cf8b=new Cesium$3['Cartesian2'](_0x25f555['U'][_0x36ab62(-0x33e,-0x2d4)],_0x25f555['U']['max']),_0x1e62a5=new Cesium$3['Cartesian2'](_0x25f555['V']['min'],_0x25f555['V']['max']),_0x886123=this,_0x1c6a47={};_0x1c6a47['U']=function(){function _0x4f53bf(_0x155520,_0x102e19){return _0x36ab62(_0x155520,_0x102e19-0x24e);}return _0x886123[_0x4f53bf(-0x45,-0x9e)]['U'];},_0x1c6a47['V']=function(){return _0x886123['windTextures']['V'];},_0x1c6a47[_0x4cdfc5(0x327,0x38c)]=function(){function _0x3c2683(_0x3621dc,_0x1c096b){return _0x4cdfc5(_0x1c096b- -0x6,_0x3621dc);}return _0x886123['particlesTextures'][_0x3c2683(0x2ae,0x321)];},_0x1c6a47['dimension']=function(){return _0x363273;},_0x1c6a47[_0x4cdfc5(0x39e,0x3c9)]=function(){return _0x196de6;},_0x1c6a47['maximum']=function(){return _0x144945;},_0x1c6a47[_0x36ab62(-0x2b2,-0x260)]=function(){return _0x15dc47;};const _0x229afa={};_0x229afa['sources']=[getWind_frag];const _0x5d5816={};_0x5d5816[_0x36ab62(-0x2d8,-0x24c)]=[updateSpeed_frag];const _0x517d18={};function _0x36ab62(_0x323f49,_0x1d4e2d){return _0x2d2794(_0x323f49,_0x1d4e2d- -0x34f);}_0x517d18[_0x4cdfc5(0x39d,0x38f)]=[updatePosition_frag];const _0x1b70b6={};_0x1b70b6[_0x4cdfc5(0x39d,0x418)]=[postProcessingPosition_frag];const _0x762ec6={};_0x762ec6[_0x36ab62(-0x23d,-0x24c)]=[postProcessingSpeed_frag];function _0x4cdfc5(_0x1861c7,_0x136f49){return _0x2d2794(_0x136f49,_0x1861c7-0x29a);}this[_0x4cdfc5(0x3b8,0x3dd)]={'getWind':new CustomPrimitive({'commandType':'Compute','uniformMap':_0x1c6a47,'fragmentShaderSource':new Cesium$3[(_0x4cdfc5(0x2a4,0x335))](_0x229afa),'outputTexture':this['particlesTextures'][_0x36ab62(-0x2c7,-0x335)],'preExecute':function(){function _0x4c57fa(_0x14a22a,_0x2e57c4){return _0x36ab62(_0x2e57c4,_0x14a22a-0x3cb);}_0x886123[_0x4c57fa(0x19a,0x152)]['getWind']['commandToExecute']['outputTexture']=_0x886123['particlesTextures']['particlesWind'];}}),'updateSpeed':new CustomPrimitive({'commandType':'Compute','uniformMap':{'currentParticlesSpeed':function(){function _0x335184(_0x159b05,_0x19c7cc){return _0x4cdfc5(_0x19c7cc-0x122,_0x159b05);}return _0x886123[_0x335184(0x47b,0x4ba)]['currentParticlesSpeed'];},'particlesWind':function(){function _0x14993b(_0x1364d1,_0x2f79c2){return _0x4cdfc5(_0x1364d1-0x1dd,_0x2f79c2);}function _0x54b1bf(_0x145bb2,_0x2e0273){return _0x36ab62(_0x2e0273,_0x145bb2-0x508);}return _0x886123[_0x14993b(0x575,0x5bc)][_0x54b1bf(0x1d3,0x18d)];},'uSpeedRange':function(){return _0x58cf8b;},'vSpeedRange':function(){return _0x1e62a5;},'pixelSize':function(){function _0x5571bb(_0x484ca3,_0x29c782){return _0x4cdfc5(_0x484ca3-0xdf,_0x29c782);}return _0x2c7ff3[_0x5571bb(0x433,0x477)];},'speedFactor':function(){function _0x474c79(_0x34b271,_0x36ce55){return _0x4cdfc5(_0x36ce55-0x13e,_0x34b271);}return _0x46670d[_0x474c79(0x4fa,0x4c9)];}},'fragmentShaderSource':new Cesium$3['ShaderSource'](_0x5d5816),'outputTexture':this['particlesTextures']['nextParticlesSpeed'],'preExecute':function(){const _0x7a1daa=_0x886123[_0x4e9aa8(0x6,-0x8e)]['currentParticlesSpeed'];_0x886123['particlesTextures']['currentParticlesSpeed']=_0x886123['particlesTextures']['postProcessingSpeed'];function _0x5f01c9(_0x2eea1c,_0x80f73b){return _0x4cdfc5(_0x80f73b-0xb,_0x2eea1c);}function _0x4e9aa8(_0x9e1bbe,_0x3eff02){return _0x36ab62(_0x9e1bbe,_0x3eff02-0x1c3);}_0x886123['particlesTextures']['postProcessingSpeed']=_0x7a1daa,_0x886123['primitives'][_0x5f01c9(0x399,0x30a)][_0x4e9aa8(-0x15e,-0x142)][_0x5f01c9(0x32c,0x36c)]=_0x886123['particlesTextures'][_0x4e9aa8(-0xc8,-0xd6)];}}),'updatePosition':new CustomPrimitive({'commandType':_0x36ab62(-0x2f4,-0x2a7),'uniformMap':{'currentParticlesPosition':function(){return _0x886123['particlesTextures']['currentParticlesPosition'];},'currentParticlesSpeed':function(){function _0x434240(_0x52de47,_0x9fdc90){return _0x4cdfc5(_0x9fdc90-0xd0,_0x52de47);}return _0x886123[_0x434240(0x4e7,0x468)]['currentParticlesSpeed'];}},'fragmentShaderSource':new Cesium$3['ShaderSource'](_0x517d18),'outputTexture':this['particlesTextures'][_0x4cdfc5(0x30e,0x37a)],'preExecute':function(){const _0x43fe3d=_0x886123['particlesTextures']['currentParticlesPosition'];function _0x3b1cce(_0x2f930b,_0x206b7f){return _0x36ab62(_0x206b7f,_0x2f930b-0x81);}_0x886123['particlesTextures']['currentParticlesPosition']=_0x886123[_0x3b1cce(-0x1d0,-0x17b)]['postProcessingPosition'],_0x886123['particlesTextures'][_0x3b1cce(-0x1be,-0x12b)]=_0x43fe3d;function _0x10f9a6(_0xdbba25,_0x498773){return _0x4cdfc5(_0x498773- -0x106,_0xdbba25);}_0x886123[_0x10f9a6(0x307,0x2b2)]['updatePosition'][_0x3b1cce(-0x284,-0x257)][_0x10f9a6(0x1eb,0x25b)]=_0x886123['particlesTextures'][_0x10f9a6(0x1d8,0x208)];}}),'postProcessingPosition':new CustomPrimitive({'commandType':_0x4cdfc5(0x342,0x35e),'uniformMap':{'nextParticlesPosition':function(){function _0x54190e(_0x419497,_0x580a69){return _0x4cdfc5(_0x580a69- -0x284,_0x419497);}return _0x886123['particlesTextures'][_0x54190e(0xb7,0x8a)];},'nextParticlesSpeed':function(){function _0x151961(_0x323669,_0x11684a){return _0x4cdfc5(_0x11684a- -0x563,_0x323669);}function _0x52a9e3(_0x2791a1,_0x187acf){return _0x36ab62(_0x2791a1,_0x187acf-0x241);}return _0x886123[_0x151961(-0x1b2,-0x1cb)][_0x151961(-0x280,-0x213)];},'lonRange':function(){function _0x26731b(_0x55ff3d,_0x56c567){return _0x4cdfc5(_0x56c567- -0x5db,_0x55ff3d);}return _0x2c7ff3[_0x26731b(-0x216,-0x29a)];},'latRange':function(){return _0x2c7ff3['latRange'];},'randomCoefficient':function(){const _0x47c38b=Math['random']();return _0x47c38b;},'dropRate':function(){return _0x46670d['dropRate'];},'dropRateBump':function(){return _0x46670d['dropRateBump'];}},'fragmentShaderSource':new Cesium$3[(_0x36ab62(-0x353,-0x345))](_0x1b70b6),'outputTexture':this[_0x36ab62(-0x1c3,-0x251)][_0x36ab62(-0x1ca,-0x23f)],'preExecute':function(){function _0x1e13e0(_0x120bdf,_0xa72d7a){return _0x4cdfc5(_0xa72d7a- -0x117,_0x120bdf);}function _0x448c94(_0x39e3ae,_0x4ba213){return _0x36ab62(_0x39e3ae,_0x4ba213-0x495);}_0x886123[_0x1e13e0(0x20b,0x2a1)][_0x1e13e0(0x26e,0x293)][_0x448c94(0x17e,0x190)][_0x1e13e0(0x228,0x24a)]=_0x886123['particlesTextures']['postProcessingPosition'];}}),'postProcessingSpeed':new CustomPrimitive({'commandType':'Compute','uniformMap':{'postProcessingPosition':function(){function _0x1f985a(_0x29fd2a,_0x32b32d){return _0x4cdfc5(_0x32b32d- -0x39,_0x29fd2a);}return _0x886123[_0x1f985a(0x392,0x35f)]['postProcessingPosition'];},'nextParticlesSpeed':function(){return _0x886123['particlesTextures']['nextParticlesSpeed'];}},'fragmentShaderSource':new Cesium$3['ShaderSource'](_0x762ec6),'outputTexture':this['particlesTextures']['postProcessingSpeed'],'preExecute':function(){function _0x4080e1(_0x3a0d76,_0x33eca0){return _0x4cdfc5(_0x3a0d76- -0x549,_0x33eca0);}function _0x1c309a(_0x2e7a09,_0x2160e2){return _0x36ab62(_0x2e7a09,_0x2160e2-0x687);}_0x886123[_0x1c309a(0x4c2,0x456)]['postProcessingSpeed']['commandToExecute'][_0x1c309a(0x426,0x3ff)]=_0x886123[_0x1c309a(0x3e5,0x436)]['postProcessingSpeed'];}})};}}const Cesium$2=mars3d__namespace[_0x2d2794(-0x16,0x48)];class ParticleSystem{constructor(_0x2a93e6,_0x326c59,_0xbcc7a4,_0x7bc39d){this[_0x5a9b07(0x392,0x34f)]=_0x2a93e6,_0x326c59={..._0x326c59};_0x326c59[_0x5a9b07(0x2ce,0x35d)]&&_0x326c59['vdata']&&(_0x326c59['dimensions']={},_0x326c59[_0x2f835e(-0x3,0x65)]['lon']=_0x326c59[_0x2f835e(-0x6c,0x1f)],_0x326c59[_0x5a9b07(0x33a,0x3ac)]['lat']=_0x326c59['rows'],_0x326c59['dimensions']['lev']=_0x326c59['lev']||0x1,_0x326c59['lon']={},_0x326c59['lon']['min']=_0x326c59['xmin'],_0x326c59[_0x2f835e(0xed,0x85)][_0x5a9b07(0x3e1,0x414)]=_0x326c59['xmax'],_0x326c59['lat']={},_0x326c59['lat']['min']=_0x326c59[_0x5a9b07(0x307,0x332)],_0x326c59[_0x5a9b07(0x32c,0x322)]['max']=_0x326c59['ymax'],_0x326c59[_0x5a9b07(0x37c,0x3fe)]={},_0x326c59[_0x5a9b07(0x410,0x3fe)][_0x2f835e(0x7,0x2d)]=_0x326c59['levmin']??0x1,_0x326c59['lev'][_0x5a9b07(0x3f8,0x414)]=_0x326c59[_0x2f835e(0x3f,-0x1d)]??0x1,_0x326c59['U']={},_0x326c59['U']['array']=new Float32Array(_0x326c59[_0x5a9b07(0x3e5,0x35d)]),_0x326c59['U'][_0x2f835e(0x88,0x2d)]=_0x326c59['umin']??Math[_0x5a9b07(0x38d,0x374)](..._0x326c59['udata']),_0x326c59['U']['max']=_0x326c59['umax']??Math['max'](..._0x326c59['udata']),_0x326c59['V']={},_0x326c59['V']['array']=new Float32Array(_0x326c59[_0x2f835e(0xb3,0xc6)]),_0x326c59['V'][_0x2f835e(0x61,0x2d)]=_0x326c59[_0x2f835e(0xe4,0x6b)]??Math['min'](..._0x326c59[_0x5a9b07(0x3e3,0x40d)]),_0x326c59['V']['max']=_0x326c59[_0x5a9b07(0x39e,0x32c)]??Math['max'](..._0x326c59[_0x2f835e(0x38,0xc6)]));this['data']=_0x326c59,this['options']=_0xbcc7a4,this['viewerParameters']=_0x7bc39d;function _0x5a9b07(_0x5c2a63,_0x142086){return _0x2d2794(_0x5c2a63,_0x142086-0x2f9);}function _0x2f835e(_0x277282,_0x49f105){return _0x420c92(_0x277282,_0x49f105-0x2fe);}this[_0x2f835e(-0x62,0x30)]=new ParticlesComputing(this[_0x2f835e(-0x62,0x8)],this['data'],this[_0x5a9b07(0x3ba,0x400)],this['viewerParameters']),this['particlesRendering']=new ParticlesRendering(this[_0x2f835e(0x27,0x8)],this['data'],this[_0x5a9b07(0x3cf,0x400)],this[_0x2f835e(-0x29,0x28)],this[_0x5a9b07(0x371,0x377)]);}[_0x420c92(-0x250,-0x267)](_0x265fe4){function _0x34d997(_0x1852ba,_0x23e3e1){return _0x2d2794(_0x1852ba,_0x23e3e1-0x445);}this['particlesComputing'][_0x45049a(0x233,0x1ce)]();function _0x45049a(_0x2cfccb,_0x51feef){return _0x420c92(_0x51feef,_0x2cfccb-0x450);}Object[_0x34d997(0x53e,0x4ab)](this['particlesComputing']['windTextures'])['forEach'](_0x451bd3=>{function _0x92b52b(_0x1b1ee7,_0x17137d){return _0x45049a(_0x1b1ee7-0x75,_0x17137d);}function _0x3d3e04(_0xbf26ca,_0x38947e){return _0x45049a(_0x38947e-0x35b,_0xbf26ca);}this[_0x3d3e04(0x53a,0x4dd)]['windTextures'][_0x451bd3][_0x3d3e04(0x59d,0x536)]();}),this[_0x34d997(0x444,0x4a0)]['textures'][_0x34d997(0x534,0x4d5)][_0x34d997(0x4a8,0x51c)](),Object[_0x45049a(0x16a,0x1a6)](this['particlesRendering']['framebuffers'])['forEach'](_0x449356=>{function _0x2d4707(_0x151550,_0x37f23f){return _0x34d997(_0x151550,_0x37f23f- -0x339);}this[_0x2d4707(0x159,0x167)]['framebuffers'][_0x449356]['destroy']();}),this[_0x34d997(0x415,0x49b)]=_0x265fe4,this['particlesComputing']=new ParticlesComputing(this['context'],this['data'],this['options'],this[_0x45049a(0x17a,0xee)]),this['particlesRendering']=new ParticlesRendering(this[_0x45049a(0x15a,0x1a0)],this['data'],this['options'],this['viewerParameters'],this['particlesComputing']);}[_0x420c92(-0x1f0,-0x271)](){function _0x191d61(_0x1c2ec8,_0x9e3ae7){return _0x420c92(_0x9e3ae7,_0x1c2ec8-0x667);}function _0x19729d(_0x3f6767,_0x378d20){return _0x420c92(_0x3f6767,_0x378d20-0x4ec);}const _0x492471=new Cesium$2['ClearCommand']({'color':new Cesium$2[(_0x191d61(0x328,0x2cb))](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':undefined,'pass':Cesium$2['Pass'][_0x19729d(0x280,0x262)]});Object['keys'](this['particlesRendering'][_0x19729d(0x1aa,0x1ef)])['forEach'](_0x1fe962=>{function _0x1d835a(_0x512d63,_0x398e4d){return _0x19729d(_0x398e4d,_0x512d63-0x7e);}_0x492471[_0x2a096f(-0x15e,-0x1ef)]=this[_0x2a096f(-0x165,-0x148)][_0x1d835a(0x26d,0x225)][_0x1fe962];function _0x2a096f(_0x251ac4,_0x410ed5){return _0x19729d(_0x410ed5,_0x251ac4- -0x360);}_0x492471['execute'](this[_0x2a096f(-0x16a,-0xe4)]);});}['refreshParticles'](_0x157fae){this['clearFramebuffers']();function _0x511043(_0x40cd77,_0x2764af){return _0x2d2794(_0x40cd77,_0x2764af-0x45c);}function _0x5ac7fd(_0x2a7f51,_0x47523a){return _0x420c92(_0x47523a,_0x2a7f51-0xd);}this[_0x511043(0x4dd,0x4da)][_0x511043(0x502,0x58b)](),this[_0x511043(0x4d0,0x4da)][_0x511043(0x4e0,0x4f7)](this[_0x511043(0x4d4,0x4b2)],this['options'],this['viewerParameters']);if(_0x157fae){var _0x3cb446;const _0x416b12=this[_0x5ac7fd(-0x2e4,-0x29c)]['createSegmentsGeometry'](this[_0x5ac7fd(-0x238,-0x242)]);this[_0x511043(0x442,0x4b7)]['primitives']['segments'][_0x5ac7fd(-0x273,-0x1f1)]=_0x416b12;const _0xa20857=Cesium$2[_0x5ac7fd(-0x23d,-0x233)][_0x5ac7fd(-0x212,-0x1c0)]({'context':this['context'],'geometry':_0x416b12,'attributeLocations':this['particlesRendering'][_0x5ac7fd(-0x221,-0x1b1)]['segments']['attributeLocations'],'bufferUsage':Cesium$2['BufferUsage'][_0x5ac7fd(-0x2bb,-0x266)]});(_0x3cb446=this[_0x5ac7fd(-0x2e4,-0x2dc)]['primitives'])!==null&&_0x3cb446!==void 0x0&&(_0x3cb446=_0x3cb446['segments'])!==null&&_0x3cb446!==void 0x0&&_0x3cb446['commandToExecute']&&(this[_0x511043(0x50f,0x4b7)]['primitives'][_0x511043(0x4dc,0x557)]['commandToExecute']['vertexArray']=_0xa20857);}}['setOptions'](_0x2386f4){function _0x4f3a90(_0x293ebb,_0x142f6d){return _0x2d2794(_0x142f6d,_0x293ebb- -0x41);}let _0x5e6a3a=![];function _0x173d66(_0x4ca30c,_0x4890ff){return _0x2d2794(_0x4890ff,_0x4ca30c- -0x31);}this[_0x173d66(0xd6,0xa0)]['maxParticles']!==_0x2386f4[_0x4f3a90(0xed,0x14d)]&&(_0x5e6a3a=!![]),Object[_0x173d66(0x35,0x41)](_0x2386f4)[_0x4f3a90(-0x21,0x37)](_0x10f31d=>{function _0x53cb69(_0x419d04,_0x17fe21){return _0x173d66(_0x419d04- -0x13a,_0x17fe21);}this[_0x53cb69(-0x64,0x10)][_0x10f31d]=_0x2386f4[_0x10f31d];}),this['refreshParticles'](_0x5e6a3a);}[_0x420c92(-0x346,-0x2eb)](_0x2c649b){function _0x106b21(_0x33b5cf,_0x4db9fe){return _0x2d2794(_0x4db9fe,_0x33b5cf-0xb8);}Object['keys'](_0x2c649b)[_0x106b21(0xd8,0x84)](_0x216c5e=>{this['viewerParameters'][_0x216c5e]=_0x2c649b[_0x216c5e];}),this['refreshParticles'](![]);}['destroy'](){clearTimeout(this['canrefresh']),this['particlesComputing']['destroyParticlesTextures'](),Object['keys'](this[_0x52f056(0x22,-0x4d)][_0x384f4e(-0x32b,-0x2e9)])['forEach'](_0x228460=>{function _0x2af94f(_0x26996c,_0x2132fb){return _0x384f4e(_0x26996c,_0x2132fb-0xbc);}this['particlesComputing'][_0x2af94f(-0x1fa,-0x22d)][_0x228460]['destroy']();}),this['particlesRendering']['textures']['colorTable']['destroy']();function _0x384f4e(_0x356abc,_0xb9911e){return _0x2d2794(_0x356abc,_0xb9911e- -0x34c);}function _0x52f056(_0x2f4693,_0x33e632){return _0x2d2794(_0x2f4693,_0x33e632- -0xcb);}Object[_0x52f056(-0x8c,-0x65)](this[_0x52f056(-0x30,-0x70)]['framebuffers'])[_0x384f4e(-0x39a,-0x32c)](_0x38ce6f=>{this['particlesRendering']['framebuffers'][_0x38ce6f]['destroy']();});for(const _0x33f459 in this){delete this[_0x33f459];}}}const Cesium$1=mars3d__namespace[_0x420c92(-0x386,-0x304)],BaseLayer$1=mars3d__namespace[_0x420c92(-0x274,-0x2b0)]['BaseLayer'],_0x11081b={};_0x11081b[_0x2d2794(0x11f,0xbb)]=0x1000,_0x11081b['fixedHeight']=0x0,_0x11081b['fadeOpacity']=0.996,_0x11081b['dropRate']=0.003,_0x11081b[_0x420c92(-0x1fd,-0x278)]=0.01,_0x11081b['speedFactor']=0.5,_0x11081b['lineWidth']=0x2,_0x11081b['colors']=[_0x420c92(-0x2cc,-0x298)];function _0x420c92(_0x2d7d2a,_0x38a640){return _0x54dc(_0x38a640- -0x3db,_0x2d7d2a);}const DEF_OPTIONS=_0x11081b;function _0x2d2794(_0x3764e0,_0x22ca39){return _0x54dc(_0x22ca39- -0x8f,_0x3764e0);}class WindLayer extends BaseLayer$1{constructor(_0x2a70f6={}){_0x2a70f6={...DEF_OPTIONS,..._0x2a70f6},super(_0x2a70f6),this['_setOptionsHook'](_0x2a70f6);}get[_0x2d2794(0xc7,0x9c)](){return this['primitives'];}get[_0x2d2794(0xab,0x92)](){function _0x41fa58(_0x27e537,_0x43bcd6){return _0x420c92(_0x27e537,_0x43bcd6-0x149);}return this[_0x41fa58(-0x194,-0x1ea)];}set['data'](_0x189466){this['setData'](_0x189466);}get[_0x2d2794(0x9e,0x6e)](){function _0x17095e(_0x225cf7,_0x5bded5){return _0x2d2794(_0x225cf7,_0x5bded5-0x213);}return this['options'][_0x17095e(0x251,0x281)];}set[_0x2d2794(0x3c,0x6e)](_0x167e0f){this[_0x5d42e4(-0xea,-0x15d)]['colors']=_0x167e0f;if(this['particleSystem']){const _0x18b4f3={};_0x18b4f3['colors']=_0x167e0f,this[_0x4a1608(-0x22d,-0x213)][_0x5d42e4(-0x19d,-0x22a)](_0x18b4f3);}function _0x4a1608(_0x23da6c,_0x17baa7){return _0x2d2794(_0x17baa7,_0x23da6c- -0x323);}function _0x5d42e4(_0x3e3b48,_0x296264){return _0x2d2794(_0x296264,_0x3e3b48- -0x1f1);}this[_0x5d42e4(-0x11c,-0x1a0)]();}['_mountedHook'](){}[_0x2d2794(0x146,0xd0)](){this['scene']=this[_0x4241d6(-0x2a7,-0x271)][_0x15d66b(-0x2aa,-0x2e6)],this['camera']=this['_map'][_0x4241d6(-0x333,-0x2b3)],this['primitives']=new Cesium$1['PrimitiveCollection'](),this[_0x4241d6(-0x2f4,-0x271)]['scene']['primitives'][_0x15d66b(-0x2f3,-0x2d8)](this[_0x4241d6(-0x1e5,-0x1d6)]);function _0x4241d6(_0x5acd3f,_0x4e2ad1){return _0x2d2794(_0x5acd3f,_0x4e2ad1- -0x2f4);}this[_0x4241d6(-0x22a,-0x27e)]={'lonRange':new Cesium$1['Cartesian2'](),'latRange':new Cesium$1['Cartesian2'](),'pixelSize':0x0},this['globeBoundingSphere']=new Cesium$1['BoundingSphere'](Cesium$1['Cartesian3'][_0x15d66b(-0x22c,-0x262)],0.99*0x615299),this['updateViewerParameters'](),window[_0x4241d6(-0x245,-0x1e5)]('resize',this['resize'][_0x4241d6(-0x27b,-0x297)](this),![]),this['mouse_down']=![],this[_0x4241d6(-0x280,-0x25c)]=![];function _0x15d66b(_0x41beb9,_0x243002){return _0x2d2794(_0x41beb9,_0x243002- -0x308);}this['_map']['on'](mars3d__namespace['EventType'][_0x4241d6(-0x1f3,-0x1da)],this['_onMapWhellEvent'],this),this['_map']['on'](mars3d__namespace['EventType'][_0x15d66b(-0x236,-0x25a)],this['_onMouseDownEvent'],this),this['_map']['on'](mars3d__namespace['EventType'][_0x4241d6(-0x286,-0x211)],this['_onMouseUpEvent'],this),this[_0x4241d6(-0x215,-0x271)]['on'](mars3d__namespace['EventType']['mouseMove'],this['_onMouseMoveEvent'],this),this['_data']&&this['setData'](this['_data']);}['_removedHook'](){function _0x3d15f5(_0x545a7d,_0x403181){return _0x420c92(_0x403181,_0x545a7d-0x47f);}window[_0x4a5a18(0x81,0x2)](_0x4a5a18(0xbc,0x122),this['resize']);function _0x4a5a18(_0x4f91db,_0x15f5df){return _0x2d2794(_0x15f5df,_0x4f91db- -0x19);}this['_map'][_0x4a5a18(0x69,0xaf)](mars3d__namespace['EventType']['preRender'],this['_onMap_preRenderEvent'],this),this[_0x4a5a18(0x6a,0xe4)][_0x4a5a18(0x69,0x4d)](mars3d__namespace[_0x4a5a18(0x117,0x156)]['wheel'],this['_onMapWhellEvent'],this),this['_map']['off'](mars3d__namespace[_0x4a5a18(0x117,0xfc)][_0x3d15f5(0x1e1,0x156)],this['_onMouseDownEvent'],this),this['_map'][_0x3d15f5(0x1b5,0x1cf)](mars3d__namespace['EventType']['mouseUp'],this['_onMouseUpEvent'],this),this[_0x4a5a18(0x6a,0xfb)][_0x4a5a18(0x69,0x42)](mars3d__namespace[_0x3d15f5(0x263,0x20e)][_0x3d15f5(0x1a3,0x1a9)],this['_onMouseMoveEvent'],this),this[_0x4a5a18(0x105,0xa7)]['removeAll'](),this['_map'][_0x4a5a18(0x9,-0x81)]['primitives'][_0x4a5a18(0x3f,-0x11)](this['primitives']);}['resize'](){function _0x2c885c(_0x3595fb,_0x1f2aac){return _0x2d2794(_0x1f2aac,_0x3595fb-0x45d);}if(!this[_0x2b0950(0x41a,0x3f0)]||!this['particleSystem'])return;this['primitives']['show']=![];function _0x2b0950(_0x4b96a0,_0x1a77ba){return _0x2d2794(_0x4b96a0,_0x1a77ba-0x388);}this['primitives']['removeAll'](),this[_0x2b0950(0x435,0x40b)]['once'](mars3d__namespace[_0x2b0950(0x516,0x4b8)][_0x2b0950(0x3db,0x3b5)],this[_0x2c885c(0x521,0x57f)],this);}[_0x420c92(-0x2a6,-0x288)](_0x4dc3d8){function _0x51d060(_0x1a5687,_0x1531a9){return _0x420c92(_0x1a5687,_0x1531a9-0x337);}function _0x3c2140(_0x21fb02,_0x42df5f){return _0x420c92(_0x21fb02,_0x42df5f-0x25e);}this['particleSystem']['canvasResize'](this['scene'][_0x3c2140(-0xe2,-0x98)]),this[_0x3c2140(-0x10a,-0xb2)](),this['primitives']['show']=!![];}[_0x420c92(-0x20b,-0x27b)](_0x46b859){function _0x4ab4a(_0x43e4a1,_0x13c2fa){return _0x420c92(_0x13c2fa,_0x43e4a1-0x7bb);}clearTimeout(this['refreshTimer']);function _0x2e1dc4(_0x2170c9,_0x546b27){return _0x2d2794(_0x546b27,_0x2170c9- -0x1e);}if(!this['show']||!this[_0x4ab4a(0x565,0x59a)])return;this[_0x4ab4a(0x58d,0x502)][_0x4ab4a(0x4d7,0x4bd)]=![],this['refreshTimer']=setTimeout(()=>{if(!this['show'])return;this['redraw']();},0xc8);}['_onMouseDownEvent'](_0x3621ca){function _0xeebb2d(_0x3b5d00,_0x4c9a6e){return _0x2d2794(_0x3b5d00,_0x4c9a6e-0x2aa);}this[_0xeebb2d(0x2b8,0x2ed)]=!![];}['_onMouseMoveEvent'](_0x1f5e9e){if(!this['show']||!this['particleSystem'])return;this['mouse_down']&&(this['primitives']['show']=![],this['mouse_move']=!![]);}[_0x2d2794(0x80,0xcb)](_0x59e220){if(!this[_0x806d1(-0x26d,-0x25b)]||!this[_0x812818(0x527,0x4ba)])return;this[_0x812818(0x37f,0x407)]&&this[_0x812818(0x3dd,0x45c)]&&this[_0x806d1(-0x1f4,-0x1a7)]();this['primitives'][_0x806d1(-0x26d,-0x23c)]=!![],this[_0x812818(0x461,0x407)]=![];function _0x806d1(_0x3b76d7,_0x4bebba){return _0x2d2794(_0x4bebba,_0x3b76d7- -0x2d5);}function _0x812818(_0x80a456,_0x49f647){return _0x2d2794(_0x80a456,_0x49f647-0x3c4);}this[_0x812818(0x3f1,0x45c)]=![];}[_0x2d2794(0x15a,0xe1)](){function _0x1fbc8e(_0x34dc56,_0x5caa39){return _0x2d2794(_0x5caa39,_0x34dc56-0xd9);}if(!this[_0x577b18(0x3c9,0x345)]||!this[_0x577b18(0x3ae,0x3ad)])return;function _0x577b18(_0x2f1479,_0x26f612){return _0x2d2794(_0x26f612,_0x2f1479-0x346);}this['updateViewerParameters'](),this[_0x1fbc8e(0x1cf,0x261)]['applyViewerParameters'](this[_0x1fbc8e(0x14f,0x134)]),this['primitives']['show']=!![];}['setData'](_0x33eb99){function _0x58e8c6(_0x5961dd,_0xc0f51c){return _0x420c92(_0xc0f51c,_0x5961dd-0x79);}this['_data']=_0x33eb99;this[_0x4410d1(0x105,0x14d)]&&this['particleSystem']['destroy']();function _0x4410d1(_0x4fd153,_0x554006){return _0x2d2794(_0x554006,_0x4fd153-0xf);}this[_0x58e8c6(-0x1dd,-0x239)]=new ParticleSystem(this['scene'][_0x4410d1(0x65,0x22)],_0x33eb99,this[_0x4410d1(0x4e,0x31)](),this[_0x58e8c6(-0x25d,-0x272)]),this['addPrimitives']();}['_setOptionsHook'](_0x1d5dc2,_0x2a48cb){function _0x37f97a(_0xb69156,_0x8535f8){return _0x2d2794(_0xb69156,_0x8535f8- -0x2d4);}if(_0x1d5dc2)for(const _0x3c4434 in _0x1d5dc2){this[_0x3c4434]=_0x1d5dc2[_0x3c4434];}this['particleSystem']&&this['particleSystem'][_0x37f97a(-0x2e5,-0x280)](this['getOptions']());}[_0x2d2794(-0x24,0x3f)](){const _0x13dfd5=Math[_0x3aa70b(0x2ec,0x2b0)](Math[_0x3aa70b(0x264,0x2bd)](this[_0x3aa70b(0x365,0x2f3)]));function _0x46b61d(_0x222e01,_0x3c83f3){return _0x420c92(_0x3c83f3,_0x222e01-0x2e4);}this[_0x46b61d(0x53,-0x31)]=_0x13dfd5*_0x13dfd5;function _0x3aa70b(_0x286f0b,_0x19765b){return _0x2d2794(_0x286f0b,_0x19765b-0x238);}const _0x593c4e={};return _0x593c4e['particlesTextureSize']=_0x13dfd5,_0x593c4e[_0x46b61d(0xc6,0xe6)]=this[_0x46b61d(0x53,-0x1a)],_0x593c4e['particleHeight']=this['fixedHeight'],_0x593c4e['fadeOpacity']=this['fadeOpacity'],_0x593c4e['dropRate']=this[_0x46b61d(0x38,-0x5c)],_0x593c4e[_0x46b61d(0x6c,0x44)]=this['dropRateBump'],_0x593c4e[_0x3aa70b(0x364,0x329)]=this[_0x3aa70b(0x398,0x329)],_0x593c4e[_0x3aa70b(0x2b4,0x332)]=this[_0x46b61d(0x92,0xc5)],_0x593c4e[_0x3aa70b(0x240,0x2a6)]=this[_0x3aa70b(0x326,0x2a6)],_0x593c4e;}['addPrimitives'](){this['primitives']['add'](this['particleSystem'][_0x5a8628(0x1d7,0x19a)][_0x4394a5(0x2c,-0x30)]['getWind']),this['primitives']['add'](this['particleSystem']['particlesComputing'][_0x4394a5(0x2c,0x87)]['updateSpeed']);function _0x5a8628(_0x50b5bf,_0x2fb243){return _0x2d2794(_0x2fb243,_0x50b5bf-0x159);}function _0x4394a5(_0x207a71,_0x12bbb3){return _0x2d2794(_0x12bbb3,_0x207a71- -0xf2);}this[_0x4394a5(0x2c,0x27)][_0x5a8628(0x189,0x217)](this['particleSystem']['particlesComputing'][_0x4394a5(0x2c,-0x55)][_0x4394a5(-0xbc,-0xf3)]),this[_0x5a8628(0x277,0x28b)]['add'](this[_0x4394a5(0x4,0x65)]['particlesComputing'][_0x5a8628(0x277,0x2c6)]['postProcessingPosition']),this['primitives'][_0x5a8628(0x189,0x111)](this[_0x4394a5(0x4,0x3c)]['particlesComputing']['primitives']['postProcessingSpeed']),this[_0x5a8628(0x277,0x2ba)]['add'](this['particleSystem'][_0x4394a5(-0x97,-0x41)]['primitives']['segments']),this['primitives']['add'](this[_0x4394a5(0x4,0x48)]['particlesRendering']['primitives']['trails']),this[_0x4394a5(0x2c,0x51)][_0x5a8628(0x189,0x129)](this[_0x5a8628(0x24f,0x2a0)]['particlesRendering']['primitives'][_0x5a8628(0x24e,0x2de)]);}[_0x420c92(-0x298,-0x2b6)](){let _0x7b8711=this['camera'][_0x4cdf8d(0x467,0x44e)](this[_0x4cdf8d(0x471,0x473)]['globe']['ellipsoid']);function _0x4cdf8d(_0x2c5527,_0x48b53c){return _0x420c92(_0x48b53c,_0x2c5527-0x79b);}if(!_0x7b8711){const _0xcd9001=this['_map']['getExtent']();_0x7b8711=Cesium$1['Rectangle']['fromDegrees'](_0xcd9001['xmin'],_0xcd9001[_0x4cdf8d(0x488,0x4a3)],_0xcd9001['xmax'],_0xcd9001[_0x4cdf8d(0x537,0x51e)]);}const _0x23c1e4=Util[_0x4cdf8d(0x577,0x52f)](_0x7b8711);this['viewerParameters'][_0x4cdf8d(0x4f6,0x502)]['x']=_0x23c1e4[_0x4cdf8d(0x522,0x4d4)][_0x3f58c3(0x39f,0x3cf)],this['viewerParameters']['lonRange']['y']=_0x23c1e4['lon']['max'],this['viewerParameters']['latRange']['x']=_0x23c1e4['lat']['min'];function _0x3f58c3(_0x5ce931,_0x277ea5){return _0x420c92(_0x277ea5,_0x5ce931-0x670);}this[_0x4cdf8d(0x4c5,0x471)][_0x3f58c3(0x382,0x3a2)]['y']=_0x23c1e4['lat'][_0x4cdf8d(0x56a,0x504)];const _0x41598=this['camera']['getPixelSize'](this[_0x3f58c3(0x371,0x39b)],this['scene']['drawingBufferWidth'],this[_0x4cdf8d(0x471,0x441)][_0x3f58c3(0x3b5,0x379)]);_0x41598>0x0&&(this['viewerParameters']['pixelSize']=_0x41598);}}mars3d__namespace['LayerUtil'][_0x2d2794(0x135,0x10b)]('wind',WindLayer),mars3d__namespace[_0x2d2794(0x35,0x9c)][_0x420c92(-0x39d,-0x315)]=WindLayer;class CanvasParticle{constructor(){this['lng']=null,this['lat']=null,this[_0x9cae70(0x230,0x298)]=null;function _0x9cae70(_0xf2a7fd,_0x3464b4){return _0x420c92(_0xf2a7fd,_0x3464b4-0x4c8);}this['tlat']=null,this['age']=null,this['speed']=null;}['destroy'](){for(const _0x346e94 in this){delete this[_0x346e94];}}}class CanvasWindField{constructor(_0x597e97){this['setOptions'](_0x597e97);}get['speedRate'](){return this['_speedRate'];}set['speedRate'](_0x5bcb36){function _0x1e38b7(_0x150d24,_0x599e84){return _0x2d2794(_0x150d24,_0x599e84-0x3a2);}this[_0x3898b8(0x39a,0x369)]=(0x64-(_0x5bcb36>0x63?0x63:_0x5bcb36))*0x64;function _0x3898b8(_0x38e1ff,_0x3c7f0d){return _0x2d2794(_0x38e1ff,_0x3c7f0d-0x2de);}this[_0x3898b8(0x3ee,0x3f7)]=[(this['xmax']-this['xmin'])/this['_speedRate'],(this['ymax']-this[_0x3898b8(0x2e3,0x317)])/this['_speedRate']];}get['maxAge'](){return this['_maxAge'];}set['maxAge'](_0x37a1d5){this['_maxAge']=_0x37a1d5;}['setOptions'](_0x58f92f){this['options']=_0x58f92f;function _0x4533e4(_0xccf639,_0x1ab9df){return _0x420c92(_0x1ab9df,_0xccf639-0x229);}this['maxAge']=_0x58f92f['maxAge']||0x78,this['speedRate']=_0x58f92f[_0x1b8b80(0x371,0x324)]||0x32,this['particles']=[];function _0x1b8b80(_0x14d820,_0x43e767){return _0x420c92(_0x43e767,_0x14d820-0x59a);}const _0x54fb1d=_0x58f92f['particlesNumber']||0x1000;for(let _0x4b4df1=0x0;_0x4b4df1<_0x54fb1d;_0x4b4df1++){const _0x10eec5=this['_randomParticle'](new CanvasParticle());this[_0x4533e4(-0x3d,-0x8)]['push'](_0x10eec5);}}[_0x2d2794(0x17a,0x122)](_0x988c84){this[_0x1fd2fb(0x38e,0x3c7)]=_0x988c84['rows'];function _0x1fd2fb(_0xa8249f,_0x18d0db){return _0x2d2794(_0x18d0db,_0xa8249f-0x2ea);}this['cols']=_0x988c84['cols'],this[_0x2a7fc8(-0x221,-0x216)]=_0x988c84[_0x2a7fc8(-0x1cc,-0x216)],this['xmax']=_0x988c84['xmax'],this['ymin']=_0x988c84[_0x1fd2fb(0x323,0x37c)],this['ymax']=_0x988c84['ymax'],this[_0x1fd2fb(0x2fa,0x286)]=[];const _0x30e077=_0x988c84[_0x1fd2fb(0x34e,0x3cc)],_0x461774=_0x988c84['vdata'];let _0x40d8fe=![];_0x30e077[_0x1fd2fb(0x306,0x327)]===this[_0x1fd2fb(0x38e,0x401)]&&_0x30e077[0x0][_0x2a7fc8(-0x31d,-0x2af)]===this[_0x1fd2fb(0x357,0x314)]&&(_0x40d8fe=!![]);let _0x3618bb=0x0,_0x502061=null;function _0x2a7fc8(_0x36bdcb,_0x3713b2){return _0x420c92(_0x36bdcb,_0x3713b2-0x81);}let _0x49f92a=null;for(let _0x48bd49=0x0;_0x48bd49<this['rows'];_0x48bd49++){_0x502061=[];for(let _0x3d6594=0x0;_0x3d6594<this['cols'];_0x3d6594++,_0x3618bb++){_0x40d8fe?_0x49f92a=this[_0x1fd2fb(0x301,0x2ce)](_0x30e077[_0x48bd49][_0x3d6594],_0x461774[_0x48bd49][_0x3d6594]):_0x49f92a=this[_0x2a7fc8(-0x2c7,-0x2b4)](_0x30e077[_0x3618bb],_0x461774[_0x3618bb]),_0x502061['push'](_0x49f92a);}this[_0x1fd2fb(0x2fa,0x36a)][_0x2a7fc8(-0x206,-0x1d7)](_0x502061);}this[_0x2a7fc8(-0x255,-0x1c4)]['reverseY']&&this[_0x1fd2fb(0x2fa,0x30d)]['reverse']();}[_0x2d2794(0xf8,0x125)](){delete this['rows'],delete this['cols'],delete this[_0x5bfa24(-0x203,-0x24c)],delete this['xmax'],delete this['ymin'];function _0x1e4c16(_0xae4acd,_0x577e41){return _0x420c92(_0xae4acd,_0x577e41-0x517);}delete this['ymax'],delete this[_0x5bfa24(-0x2a8,-0x291)];function _0x5bfa24(_0x593f40,_0x3302c0){return _0x420c92(_0x3302c0,_0x593f40-0x94);}delete this['particles'];}[_0x2d2794(0xaf,0x3d)](_0x1ca8a6,_0x12cfdd){const _0x472df4=(_0x1ca8a6-this['xmin'])/(this['xmax']-this['xmin'])*(this[_0x43baba(-0x4f,0x3e)]-0x1),_0x586e1b=(this['ymax']-_0x12cfdd)/(this['ymax']-this['ymin'])*(this['rows']-0x1);function _0x43baba(_0x50d263,_0x4a8ac9){return _0x420c92(_0x4a8ac9,_0x50d263-0x290);}return[_0x472df4,_0x586e1b];}['getUVByXY'](_0x562a73,_0x4132c4){if(_0x562a73<0x0||_0x562a73>=this[_0x223318(0x217,0x21d)]||_0x4132c4>=this[_0x223318(0x24e,0x2c9)])return[0x0,0x0,0x0];const _0x56dd44=Math['floor'](_0x562a73),_0x243ab4=Math['floor'](_0x4132c4);if(_0x56dd44===_0x562a73&&_0x243ab4===_0x4132c4)return this['grid'][_0x4132c4][_0x562a73];const _0x5dbf0f=_0x56dd44+0x1,_0x543a13=_0x243ab4+0x1;function _0x1de973(_0x13a7cf,_0xc72412){return _0x420c92(_0xc72412,_0x13a7cf-0x1f7);}const _0x24068b=this[_0x1de973(-0x149,-0x11f)](_0x56dd44,_0x243ab4),_0x50099f=this['getUVByXY'](_0x5dbf0f,_0x243ab4),_0x3402e4=this[_0x223318(0x1b6,0x1e7)](_0x56dd44,_0x543a13);function _0x223318(_0x54f0d5,_0x2ad02d){return _0x2d2794(_0x2ad02d,_0x54f0d5-0x1aa);}const _0x93cc3c=this[_0x223318(0x1b6,0x1cc)](_0x5dbf0f,_0x543a13);let _0x57c243=null;try{_0x57c243=this['_bilinearInterpolation'](_0x562a73-_0x56dd44,_0x4132c4-_0x243ab4,_0x24068b,_0x50099f,_0x3402e4,_0x93cc3c);}catch(_0x20e64c){console['log'](_0x562a73,_0x4132c4);}return _0x57c243;}[_0x420c92(-0x22f,-0x29b)](_0x1a64a2,_0x13a765,_0xbb3a99,_0x4a92a6,_0x4a3044,_0x47fbac){const _0x58997a=0x1-_0x1a64a2,_0x66ff44=0x1-_0x13a765,_0x4e8f0d=_0x58997a*_0x66ff44,_0x9b5ab8=_0x1a64a2*_0x66ff44,_0x58edcc=_0x58997a*_0x13a765,_0x4660c1=_0x1a64a2*_0x13a765,_0x10adae=_0xbb3a99[0x0]*_0x4e8f0d+_0x4a92a6[0x0]*_0x9b5ab8+_0x4a3044[0x0]*_0x58edcc+_0x47fbac[0x0]*_0x4660c1;function _0x39a8e5(_0x15325a,_0x5721b5){return _0x2d2794(_0x5721b5,_0x15325a-0x28d);}const _0x41cc0a=_0xbb3a99[0x1]*_0x4e8f0d+_0x4a92a6[0x1]*_0x9b5ab8+_0x4a3044[0x1]*_0x58edcc+_0x47fbac[0x1]*_0x4660c1;return this[_0x39a8e5(0x2a4,0x275)](_0x10adae,_0x41cc0a);}['_calcUV'](_0x17330b,_0x5f4ed4){return[+_0x17330b,+_0x5f4ed4,Math['sqrt'](_0x17330b*_0x17330b+_0x5f4ed4*_0x5f4ed4)];}[_0x2d2794(0x10d,0x133)](_0x460c1e,_0x5d9e09){if(!this[_0x3ddd79(0x10a,0x8c)](_0x460c1e,_0x5d9e09))return null;const _0x109907=this['toGridXY'](_0x460c1e,_0x5d9e09);function _0x24b885(_0x59280b,_0x17e906){return _0x2d2794(_0x59280b,_0x17e906- -0x345);}const _0x2eeeb6=this[_0x24b885(-0x35a,-0x339)](_0x109907[0x0],_0x109907[0x1]);function _0x3ddd79(_0x19a1fe,_0x2c42d3){return _0x2d2794(_0x2c42d3,_0x19a1fe-0xab);}return _0x2eeeb6;}['isInExtent'](_0x548f02,_0x12c1df){function _0x3a6f5c(_0xdd5364,_0x3f274d){return _0x420c92(_0xdd5364,_0x3f274d-0x6f4);}function _0x1a0a6c(_0x53adf5,_0x4d4952){return _0x420c92(_0x4d4952,_0x53adf5-0x60c);}return _0x548f02>=this['xmin']&&_0x548f02<=this[_0x3a6f5c(0x4b4,0x4bb)]&&_0x12c1df>=this['ymin']&&_0x12c1df<=this[_0x1a0a6c(0x3a8,0x3cc)]?!![]:![];}[_0x420c92(-0x2ca,-0x331)](){const _0x137b19=fRandomByfloat(this[_0x4f9c4f(0x308,0x355)],this[_0x2a7bdf(0x4c2,0x4dc)]);function _0x4f9c4f(_0x598b7b,_0x2649b1){return _0x2d2794(_0x598b7b,_0x2649b1-0x2a0);}const _0x491322=fRandomByfloat(this['ymin'],this[_0x2a7bdf(0x469,0x4b1)]),_0x15b8a0={};_0x15b8a0[_0x4f9c4f(0x2e2,0x2c9)]=_0x491322;function _0x2a7bdf(_0x52d452,_0x328e6c){return _0x420c92(_0x52d452,_0x328e6c-0x715);}return _0x15b8a0['lng']=_0x137b19,_0x15b8a0;}[_0x420c92(-0x213,-0x235)](){function _0x1c4697(_0x112317,_0x4400d9){return _0x420c92(_0x112317,_0x4400d9-0x705);}function _0x160267(_0x50343e,_0x377a7c){return _0x2d2794(_0x50343e,_0x377a7c- -0x90);}let _0x50604e,_0x3092b7,_0x37208c;for(let _0x2f4188=0x0,_0xf561dd=this['particles']['length'];_0x2f4188<_0xf561dd;_0x2f4188++){let _0x3a95b6=this[_0x1c4697(0x519,0x49f)][_0x2f4188];_0x3a95b6['age']<=0x0&&(_0x3a95b6=this['_randomParticle'](_0x3a95b6));if(_0x3a95b6['age']>0x0){const _0x314d20=_0x3a95b6['tlng'],_0x4de052=_0x3a95b6[_0x160267(0xb8,0x4e)];_0x37208c=this[_0x1c4697(0x4b6,0x4ec)](_0x314d20,_0x4de052),_0x37208c?(_0x50604e=_0x314d20+this['_calc_speedRate'][0x0]*_0x37208c[0x0],_0x3092b7=_0x4de052+this['_calc_speedRate'][0x1]*_0x37208c[0x1],_0x3a95b6['lng']=_0x314d20,_0x3a95b6['lat']=_0x4de052,_0x3a95b6['tlng']=_0x50604e,_0x3a95b6['tlat']=_0x3092b7,_0x3a95b6['speed']=_0x37208c[0x2],_0x3a95b6['age']--):_0x3a95b6['age']=0x0;}}return this[_0x1c4697(0x4e4,0x49f)];}[_0x420c92(-0x1a0,-0x22c)](_0x3224e9){let _0x168c8d,_0x5b990a;for(let _0x49eaa6=0x0;_0x49eaa6<0x1e;_0x49eaa6++){_0x168c8d=this[_0x41b894(-0x232,-0x261)](),_0x5b990a=this['getUVByPoint'](_0x168c8d[_0x41b894(-0x17f,-0x125)],_0x168c8d['lat']);if(_0x5b990a&&_0x5b990a[0x2]>0x0)break;}if(!_0x5b990a)return _0x3224e9;function _0x53737a(_0x580b35,_0x5ac2d6){return _0x420c92(_0x580b35,_0x5ac2d6-0x75c);}function _0x41b894(_0x35879c,_0x15e460){return _0x420c92(_0x15e460,_0x35879c-0xff);}const _0x1cc7b4=_0x168c8d[_0x41b894(-0x17f,-0x158)]+this['_calc_speedRate'][0x0]*_0x5b990a[0x0],_0x9ab30e=_0x168c8d['lat']+this['_calc_speedRate'][0x1]*_0x5b990a[0x1];return _0x3224e9[_0x53737a(0x544,0x4de)]=_0x168c8d['lng'],_0x3224e9['lat']=_0x168c8d['lat'],_0x3224e9['tlng']=_0x1cc7b4,_0x3224e9['tlat']=_0x9ab30e,_0x3224e9[_0x53737a(0x455,0x44a)]=Math['round'](Math['random']()*this[_0x41b894(-0x185,-0x1ca)]),_0x3224e9[_0x41b894(-0x188,-0xfe)]=_0x5b990a[0x2],_0x3224e9;}['destroy'](){for(const _0x14daaf in this){delete this[_0x14daaf];}}}function fRandomByfloat(_0x23dd11,_0x3f8a88){return _0x23dd11+Math['random']()*(_0x3f8a88-_0x23dd11);}const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace[_0x420c92(-0x2f2,-0x2b0)]['BaseLayer'];class CanvasWindLayer extends BaseLayer{constructor(_0x23f53e={}){super(_0x23f53e),this['_setOptionsHook'](_0x23f53e),this['canvas']=null,_0x23f53e['colors']&&_0x23f53e['steps']&&(this['_colorRamp']=new mars3d__namespace['ColorRamp'](_0x23f53e));}['_setOptionsHook'](_0x1c3416,_0x5172d0){this[_0x96c3fc(-0x2b,0x46)]=0x3e8/(_0x1c3416[_0x23831e(0x36b,0x377)]||0xa),this['_pointerEvents']=this['options'][_0x96c3fc(0x65,0xf)]??![],this['color']=_0x1c3416[_0x23831e(0x317,0x2bd)]||'#ffffff',this['lineWidth']=_0x1c3416[_0x96c3fc(0x83,-0x9)]||0x1,this['fixedHeight']=_0x1c3416[_0x23831e(0x359,0x3df)]??0x0;function _0x96c3fc(_0x564f15,_0x260ed2){return _0x420c92(_0x260ed2,_0x564f15-0x2d5);}function _0x23831e(_0x42102d,_0x547db6){return _0x420c92(_0x547db6,_0x42102d-0x62e);}this['reverseY']=_0x1c3416['reverseY']??![],this[_0x23831e(0x3b1,0x37e)]&&this[_0x96c3fc(0x58,-0x2a)]['setOptions'](_0x1c3416);}get['layer'](){function _0x3cfe55(_0x2be6cb,_0x1bd3f9){return _0x2d2794(_0x1bd3f9,_0x2be6cb-0x104);}return this[_0x3cfe55(0x116,0xfd)];}get['canvasWidth'](){function _0xbebbc5(_0x3bc79e,_0x48ea85){return _0x2d2794(_0x3bc79e,_0x48ea85-0x3d7);}return this[_0xbebbc5(0x474,0x45a)]['scene']['canvas']['clientWidth'];}get['canvasHeight'](){function _0xc39951(_0x2894af,_0x4db0bf){return _0x2d2794(_0x2894af,_0x4db0bf-0xe3);}function _0x273fe4(_0x329008,_0x5f4932){return _0x420c92(_0x5f4932,_0x329008-0x663);}return this[_0xc39951(0x10e,0x166)][_0xc39951(0xcf,0x105)]['canvas']['clientHeight'];}get[_0x420c92(-0x26f,-0x270)](){function _0x56d7d2(_0xa8ecda,_0x317ce8){return _0x420c92(_0xa8ecda,_0x317ce8-0x416);}return this[_0x56d7d2(0x16c,0x12a)];}set['pointerEvents'](_0x59efd6){function _0x1a27a3(_0x2bb56f,_0x388deb){return _0x420c92(_0x388deb,_0x2bb56f-0x239);}this['_pointerEvents']=_0x59efd6;if(!this['canvas'])return;function _0x14e32(_0x21db0e,_0x2fd69f){return _0x420c92(_0x2fd69f,_0x21db0e-0x7);}_0x59efd6?this['canvas'][_0x1a27a3(-0xac,-0xe5)]['pointer-events']='all':this['canvas']['style'][_0x14e32(-0x282,-0x2bd)]='none';}get[_0x420c92(-0x29d,-0x291)](){function _0xb8d744(_0x14fe82,_0x4e8fb4){return _0x2d2794(_0x4e8fb4,_0x14fe82-0x371);}function _0x574f8a(_0x51ce70,_0x1f82b4){return _0x420c92(_0x51ce70,_0x1f82b4-0x1f4);}return this[_0xb8d744(0x478,0x47a)][_0xb8d744(0x42c,0x3b5)];}set['particlesNumber'](_0x2cb3ee){function _0x39570a(_0x3ac0e6,_0x392002){return _0x420c92(_0x392002,_0x3ac0e6-0x613);}function _0x1589d6(_0x4eb61d,_0x355d07){return _0x420c92(_0x355d07,_0x4eb61d-0x43a);}this['options'][_0x39570a(0x382,0x32a)]=_0x2cb3ee,clearTimeout(this[_0x39570a(0x2e5,0x277)]),this[_0x39570a(0x2e5,0x287)]=setTimeout(()=>{function _0x5e6c5e(_0x102871,_0x3012dd){return _0x39570a(_0x3012dd- -0x4bd,_0x102871);}this[_0x5e6c5e(-0xa5,-0x115)]();},0x1f4);}get['speedRate'](){function _0x52de4f(_0x274db1,_0x5dcd3b){return _0x2d2794(_0x5dcd3b,_0x274db1-0x30a);}return this[_0x52de4f(0x411,0x4a2)]['speedRate'];}set['speedRate'](_0x579637){this['options'][_0x4284db(0x1c9,0x17c)]=_0x579637;function _0x580822(_0x454651,_0x384de9){return _0x420c92(_0x454651,_0x384de9-0x248);}function _0x4284db(_0x4a5125,_0x33a2b3){return _0x2d2794(_0x4a5125,_0x33a2b3-0x59);}this[_0x4284db(0xf4,0x128)]&&(this[_0x580822(-0x1a,-0x35)][_0x580822(-0x72,0x1f)]=_0x579637);}get['maxAge'](){function _0x52a5cc(_0x7629e1,_0xba98bb){return _0x2d2794(_0x7629e1,_0xba98bb-0x96);}return this['options'][_0x52a5cc(0x1e9,0x15e)];}set[_0x2d2794(0xbd,0xc8)](_0x28bf7c){function _0x225414(_0x3590cb,_0x5ef4b6){return _0x2d2794(_0x3590cb,_0x5ef4b6-0xc0);}function _0x1317c5(_0x11ce33,_0x365a42){return _0x2d2794(_0x11ce33,_0x365a42-0x119);}this[_0x1317c5(0x1ec,0x220)][_0x225414(0x1f4,0x188)]=_0x28bf7c,this['windField']&&(this['windField']['maxAge']=_0x28bf7c);}get[_0x420c92(-0x2ab,-0x2ba)](){return this['windData'];}set['data'](_0x2d0722){this['setData'](_0x2d0722);}[_0x420c92(-0x3b4,-0x326)](_0x5ac3a3){function _0x363c9b(_0x7ab505,_0x26ad2c){return _0x420c92(_0x7ab505,_0x26ad2c-0x454);}function _0x16ba41(_0x5edcc9,_0x3f0d84){return _0x2d2794(_0x5edcc9,_0x3f0d84-0x186);}_0x5ac3a3?this[_0x363c9b(0x230,0x1d8)]():(this['windData']&&(this['options']['data']=this['windData']),this[_0x363c9b(0xcf,0x12b)]());}[_0x420c92(-0x2e2,-0x2b7)](){function _0x262b2a(_0x1c118d,_0x289983){return _0x420c92(_0x289983,_0x1c118d-0x3f6);}this['options']['worker']?this['initWorker']():this['windField']=new CanvasWindField(this[_0x262b2a(0x1b1,0x23b)]);}[_0x420c92(-0x251,-0x27c)](){function _0x32e6a8(_0x5c1f5c,_0x5a07fe){return _0x2d2794(_0x5c1f5c,_0x5a07fe- -0x195);}this['canvas']=this[_0x32e6a8(-0x10e,-0xae)]();function _0x9f48bc(_0x416a19,_0x302535){return _0x2d2794(_0x302535,_0x416a19-0x475);}const _0x28cc08={};_0x28cc08['willReadFrequently']=!![],this[_0x32e6a8(-0x9c,-0xc3)]=this['canvas']['getContext']('2d',_0x28cc08),this[_0x9f48bc(0x575,0x4e1)](),this[_0x32e6a8(-0x4e,-0x8e)]['data']&&this['setData'](this['options'][_0x32e6a8(-0x97,-0x103)]);}['_removedHook'](){function _0x206d7d(_0x2ea3ff,_0xdc3634){return _0x2d2794(_0x2ea3ff,_0xdc3634-0x1ff);}this[_0x956609(-0x151,-0x18c)](),this[_0x956609(-0x265,-0x225)]();function _0x956609(_0x3e4c32,_0x2df60b){return _0x420c92(_0x2df60b,_0x3e4c32-0xd6);}this[_0x206d7d(0x237,0x211)]&&(this['_map']['container'][_0x206d7d(0x1c8,0x22e)](this[_0x206d7d(0x192,0x211)]),delete this[_0x206d7d(0x1a0,0x211)]);}[_0x2d2794(0x67,0xe7)](){const _0x5301ea=mars3d__namespace['DomUtil']['create']('canvas','mars3d-canvasWind',this['_map'][_0x322a5a(-0x254,-0x2dd)]);_0x5301ea['style'][_0x322a5a(-0x1fa,-0x21f)]=_0x31c7e5(0x3c6,0x380);function _0x31c7e5(_0x275396,_0xa45696){return _0x2d2794(_0x275396,_0xa45696-0x28e);}_0x5301ea['style']['top']=_0x322a5a(-0x22c,-0x1f4),_0x5301ea['style'][_0x322a5a(-0x25d,-0x242)]='0px';function _0x322a5a(_0x4f574d,_0x3d8bd5){return _0x2d2794(_0x3d8bd5,_0x4f574d- -0x29d);}return _0x5301ea[_0x31c7e5(0x276,0x2f5)]['width']=this['_map']['scene']['canvas'][_0x322a5a(-0x1f1,-0x1b1)]+'px',_0x5301ea['style']['height']=this['_map']['scene']['canvas']['clientHeight']+'px',_0x5301ea['style']['pointerEvents']=this['_pointerEvents']?_0x322a5a(-0x246,-0x1cb):'none',_0x5301ea['style'][_0x322a5a(-0x232,-0x286)]=this[_0x322a5a(-0x196,-0x1a6)][_0x31c7e5(0x346,0x2f9)]??0x9,_0x5301ea[_0x322a5a(-0x1e0,-0x23c)]=this['_map'][_0x31c7e5(0x23d,0x2b0)][_0x31c7e5(0x2cf,0x2a0)]['clientWidth'],_0x5301ea['height']=this[_0x31c7e5(0x315,0x311)]['scene']['canvas'][_0x31c7e5(0x317,0x387)],_0x5301ea;}['resize'](){function _0x1c6bcc(_0x14cf66,_0xde5037){return _0x420c92(_0x14cf66,_0xde5037-0x236);}function _0x24b19f(_0xa1418,_0xfb0271){return _0x2d2794(_0xa1418,_0xfb0271-0x52);}this[_0x1c6bcc(-0xb3,-0x104)]&&(this['canvas']['style'][_0x24b19f(0x18a,0x10f)]=this['_map'][_0x24b19f(0x6d,0x74)]['canvas']['clientWidth']+'px',this['canvas']['style'][_0x1c6bcc(0x5d,-0xd)]=this['_map'][_0x24b19f(0x48,0x74)][_0x1c6bcc(-0x174,-0x104)][_0x24b19f(0x172,0x14b)]+'px',this['canvas']['width']=this['_map']['scene']['canvas']['clientWidth'],this[_0x1c6bcc(-0xb6,-0x104)][_0x1c6bcc(-0x53,-0xd)]=this['_map'][_0x1c6bcc(-0xd2,-0xf4)]['canvas']['clientHeight']);}['bindEvent'](){const _0x4d08c2=this;let _0x49d885=Date[_0x3310db(0x267,0x2de)]();(function _0x513ac9(){if(_0x4d08c2[_0x5504a4(0x377,0x332)])return;function _0x5504a4(_0x1624e3,_0x1c53f9){return _0x3310db(_0x1624e3,_0x1c53f9-0x12f);}function _0x339e0e(_0x5106ee,_0x4ffef5){return _0x3310db(_0x5106ee,_0x4ffef5- -0x3d8);}_0x4d08c2['_animateFrame']=window[_0x5504a4(0x39e,0x3a1)](_0x513ac9);if(_0x4d08c2['show']&&_0x4d08c2['windField']){const _0x545da2=Date[_0x5504a4(0x490,0x40d)](),_0x4f74a9=_0x545da2-_0x49d885;_0x4f74a9>_0x4d08c2[_0x5504a4(0x344,0x32e)]&&(_0x49d885=_0x545da2-_0x4f74a9%_0x4d08c2['frameTime'],_0x4d08c2[_0x339e0e(-0x1b5,-0x196)]());}}(),window[_0x230538(-0x1cb,-0x154)](_0x3310db(0x2ea,0x288),this['resize']['bind'](this),![]),this['mouse_down']=![],this[_0x230538(-0x242,-0x2bd)]=![]);function _0x3310db(_0x29c340,_0x2cc486){return _0x2d2794(_0x29c340,_0x2cc486-0x1b3);}function _0x230538(_0x34a04b,_0x359675){return _0x420c92(_0x359675,_0x34a04b-0x72);}this['options']['mouseHidden']&&(this['_map']['on'](mars3d__namespace[_0x3310db(0x33a,0x2e3)]['wheel'],this[_0x3310db(0x2a1,0x284)],this),this['_map']['on'](mars3d__namespace['EventType']['mouseDown'],this[_0x3310db(0x27d,0x2dc)],this),this['_map']['on'](mars3d__namespace[_0x3310db(0x2cb,0x2e3)]['mouseUp'],this['_onMouseUpEvent'],this));}['unbindEvent'](){window['cancelAnimationFrame'](this[_0x29bf65(0x16,-0xb)]),delete this[_0x5e28b3(-0x135,-0x148)],window[_0x5e28b3(-0x145,-0x1b6)]('resize',this[_0x29bf65(0x30,-0x3e)]);function _0x5e28b3(_0x248042,_0x54c9fa){return _0x420c92(_0x248042,_0x54c9fa-0xfc);}function _0x29bf65(_0xf27d9f,_0x54a8f8){return _0x2d2794(_0xf27d9f,_0x54a8f8- -0x113);}this[_0x5e28b3(-0x168,-0x149)][_0x29bf65(0x51,-0x14)]&&(this[_0x5e28b3(-0x18c,-0x1cd)]['off'](mars3d__namespace['EventType']['wheel'],this['_onMapWhellEvent'],this),this['_map'][_0x5e28b3(-0x202,-0x1ce)](mars3d__namespace['EventType']['mouseDown'],this[_0x29bf65(-0x1a,0x16)],this),this['_map'][_0x5e28b3(-0x24c,-0x1ce)](mars3d__namespace['EventType'][_0x5e28b3(-0x16d,-0x16d)],this['_onMouseUpEvent'],this),this['_map']['off'](mars3d__namespace['EventType'][_0x29bf65(-0x124,-0xa3)],this['_onMouseMoveEvent'],this));}[_0x2d2794(0xa9,0xd1)](_0x58c2e4){clearTimeout(this[_0x140cf9(0x369,0x30e)]);if(!this[_0x1753e8(-0x297,-0x208)]||!this['canvas'])return;function _0x1753e8(_0x283266,_0x30d344){return _0x420c92(_0x283266,_0x30d344-0xdc);}function _0x140cf9(_0x246aa2,_0x1e6312){return _0x420c92(_0x1e6312,_0x246aa2-0x603);}this['canvas']['style']['visibility']='hidden',this[_0x140cf9(0x369,0x3f7)]=setTimeout(()=>{function _0x3de48c(_0x536e6a,_0x1f4ee1){return _0x140cf9(_0x1f4ee1- -0x9,_0x536e6a);}if(!this[_0x580f83(0x95,0x78)])return;this[_0x3de48c(0x344,0x38f)]();function _0x580f83(_0x2deba5,_0x32a98e){return _0x140cf9(_0x2deba5- -0x28a,_0x32a98e);}this['canvas'][_0x580f83(0x94,0x62)]['visibility']='visible';},0xc8);}[_0x420c92(-0x21c,-0x223)](_0x458991){this[_0x24972d(0x416,0x3aa)]=!![];function _0x38f7bc(_0xbcc052,_0x1344af){return _0x420c92(_0xbcc052,_0x1344af-0x9a);}this['_map']['off'](mars3d__namespace['EventType'][_0x38f7bc(-0x2c2,-0x242)],this[_0x38f7bc(-0x119,-0x1ac)],this);function _0x24972d(_0x167d24,_0x194ac2){return _0x2d2794(_0x167d24,_0x194ac2-0x367);}this['_map']['on'](mars3d__namespace[_0x24972d(0x4a2,0x497)][_0x24972d(0x3e7,0x3d7)],this[_0x38f7bc(-0x228,-0x1ac)],this);}['_onMouseMoveEvent'](_0x81394c){if(!this[_0x4c94d1(0x103,0x101)]||!this['canvas'])return;function _0x2183e0(_0x391c29,_0x163aee){return _0x420c92(_0x163aee,_0x391c29-0x700);}function _0x4c94d1(_0x1e08ed,_0x43ea42){return _0x2d2794(_0x43ea42,_0x1e08ed-0x9b);}this[_0x4c94d1(0xde,0x131)]&&(this[_0x2183e0(0x3c6,0x34a)]['style']['visibility']='hidden',this['mouse_move']=!![]);}[_0x420c92(-0x21d,-0x281)](_0x470984){if(!this['show']||!this[_0x4c3cd3(0x466,0x4bd)])return;this['_map']['off'](mars3d__namespace[_0xf9bae6(0x2e8,0x292)][_0xf9bae6(0x1aa,0x1d2)],this['_onMouseMoveEvent'],this);this[_0x4c3cd3(0x497,0x41e)]&&this[_0x4c3cd3(0x4ec,0x4a1)]&&this[_0x4c3cd3(0x535,0x4ae)]();function _0xf9bae6(_0x40219,_0x405085){return _0x2d2794(_0x40219,_0x405085-0x162);}this['canvas']['style'][_0xf9bae6(0x1c4,0x1f9)]='visible';function _0x4c3cd3(_0x312a58,_0x532b50){return _0x420c92(_0x532b50,_0x312a58-0x7a0);}this['mouse_down']=![],this['mouse_move']=![];}['setData'](_0x150c9e){this['clear'](),this['windData']=_0x150c9e;function _0x119d49(_0x487141,_0x3ebe24){return _0x2d2794(_0x3ebe24,_0x487141-0x428);}this[_0x4e0c53(0x51,-0x3b)]['setDate'](_0x150c9e);function _0x4e0c53(_0x1622ff,_0x2ec002){return _0x2d2794(_0x2ec002,_0x1622ff- -0x7e);}this[_0x4e0c53(0x63,0x2d)]();}[_0x420c92(-0x2b8,-0x26b)](){if(!this[_0x3e0cdf(-0x1d8,-0x1b5)])return;this[_0x3e0cdf(-0x19d,-0x14e)]['setOptions'](this[_0x215b7b(-0x14a,-0x1a4)]);function _0x215b7b(_0x5635ae,_0x327542){return _0x2d2794(_0x327542,_0x5635ae- -0x251);}function _0x3e0cdf(_0x249d8e,_0x1f0b0d){return _0x420c92(_0x249d8e,_0x1f0b0d-0x12f);}this[_0x215b7b(-0x1c2,-0x14b)]();}[_0x2d2794(0x68,0x8f)](){function _0x56092e(_0x5994b0,_0x12410a){return _0x420c92(_0x5994b0,_0x12410a-0x69b);}if(this[_0x56092e(0x489,0x479)])return;this['_updateIng']=!![];function _0x5dd1b2(_0x551a2a,_0x25b328){return _0x420c92(_0x25b328,_0x551a2a-0x3bb);}if(this[_0x56092e(0x322,0x374)])this['windField']['update']();else{const _0xc9643b=this['windField']['getParticles']();this['_drawLines'](_0xc9643b);}this['_updateIng']=![];}['_drawLines'](_0x332461){this['canvasContext']['globalCompositeOperation']=_0x19ae16(-0x2ee,-0x273),this['canvasContext'][_0x19ae16(-0x114,-0x19a)](0x0,0x0,this[_0x19ae16(-0x1f5,-0x18b)],this[_0x19ae16(-0x1e9,-0x1ed)]);function _0x4e9d8d(_0x59f7f4,_0x277287){return _0x420c92(_0x59f7f4,_0x277287-0x18);}this[_0x19ae16(-0x130,-0x1c5)]['globalCompositeOperation']='lighter',this[_0x4e9d8d(-0x291,-0x262)][_0x4e9d8d(-0x2c8,-0x320)]=0.9;function _0x19ae16(_0x1c83a7,_0x3278ba){return _0x2d2794(_0x1c83a7,_0x3278ba- -0x297);}const _0x1c8932=this[_0x4e9d8d(-0x344,-0x2b1)]['scene']['mode']!==Cesium[_0x19ae16(-0x2b9,-0x23e)][_0x19ae16(-0x22e,-0x210)],_0x8d30fc=this['canvasWidth']*0.25;if(this[_0x19ae16(-0x1f8,-0x228)])for(let _0x3c16b5=0x0,_0x1ea5aa=_0x332461['length'];_0x3c16b5<_0x1ea5aa;_0x3c16b5++){const _0x18dd6c=_0x332461[_0x3c16b5],_0x23d228=this['_tomap'](_0x18dd6c['lng'],_0x18dd6c['lat'],_0x18dd6c),_0x1c025d=this['_tomap'](_0x18dd6c[_0x4e9d8d(-0x289,-0x218)],_0x18dd6c['tlat'],_0x18dd6c);if(!_0x23d228||!_0x1c025d)continue;if(_0x1c8932&&Math[_0x19ae16(-0x295,-0x217)](_0x23d228[0x0]-_0x1c025d[0x0])>=_0x8d30fc)continue;this['canvasContext']['beginPath'](),this['canvasContext']['lineWidth']=this['lineWidth'],this[_0x4e9d8d(-0x22e,-0x262)]['strokeStyle']=this[_0x4e9d8d(-0x2a8,-0x2c5)]['getColor'](_0x18dd6c['speed']),this['canvasContext']['moveTo'](_0x23d228[0x0],_0x23d228[0x1]),this['canvasContext']['lineTo'](_0x1c025d[0x0],_0x1c025d[0x1]),this['canvasContext']['stroke']();}else{this['canvasContext']['beginPath'](),this['canvasContext'][_0x19ae16(-0x13f,-0x19d)]=this[_0x4e9d8d(-0x2ce,-0x23a)],this[_0x19ae16(-0x209,-0x1c5)][_0x4e9d8d(-0x1f9,-0x203)]=this['color'];for(let _0x553a72=0x0,_0x5c6092=_0x332461['length'];_0x553a72<_0x5c6092;_0x553a72++){const _0x46faff=_0x332461[_0x553a72],_0x2cc857=this[_0x19ae16(-0x1d6,-0x20d)](_0x46faff[_0x4e9d8d(-0x27e,-0x266)],_0x46faff['lat'],_0x46faff),_0x3b9e52=this[_0x4e9d8d(-0x308,-0x2aa)](_0x46faff['tlng'],_0x46faff[_0x4e9d8d(-0x2c4,-0x256)],_0x46faff);if(!_0x2cc857||!_0x3b9e52)continue;if(_0x1c8932&&Math[_0x19ae16(-0x274,-0x217)](_0x2cc857[0x0]-_0x3b9e52[0x0])>=_0x8d30fc)continue;this['canvasContext']['moveTo'](_0x2cc857[0x0],_0x2cc857[0x1]),this[_0x19ae16(-0x165,-0x1c5)]['lineTo'](_0x3b9e52[0x0],_0x3b9e52[0x1]);}this['canvasContext'][_0x19ae16(-0x25f,-0x1ce)]();}}[_0x420c92(-0x2a6,-0x2c2)](_0x17625d,_0x12d25d,_0x41c8b3){const _0x2ed0dc=Cesium[_0x1431a5(-0x1a2,-0x1e7)][_0x1431a5(-0x236,-0x25d)](_0x17625d,_0x12d25d,this['fixedHeight']);function _0x192697(_0x114b5e,_0x3c0e09){return _0x2d2794(_0x3c0e09,_0x114b5e-0x26);}const _0x3ddb0f=this['_map']['scene'];function _0x1431a5(_0x114952,_0x1e4090){return _0x420c92(_0x114952,_0x1e4090-0x7a);}if(_0x3ddb0f[_0x1431a5(-0x21f,-0x1f8)]===Cesium[_0x192697(0x7f,0xbf)]['SCENE3D']){const _0x534cbf=new Cesium['EllipsoidalOccluder'](_0x3ddb0f[_0x1431a5(-0x19b,-0x1f3)][_0x192697(0xde,0x173)],_0x3ddb0f['camera'][_0x192697(0xb4,0x89)]),_0x2123d6=_0x534cbf['isPointVisible'](_0x2ed0dc);if(!_0x2123d6)return _0x41c8b3['age']=0x0,null;}const _0x200cf7=Cesium['SceneTransforms'][_0x192697(0xd5,0xef)](this['_map']['scene'],_0x2ed0dc);return _0x200cf7?[_0x200cf7['x'],_0x200cf7['y']]:null;}[_0x420c92(-0x245,-0x227)](){this[_0x26ede8(0x2bf,0x2ba)]['clear']();function _0x26ede8(_0x3b7d8c,_0x4e0d8b){return _0x420c92(_0x3b7d8c,_0x4e0d8b-0x537);}delete this['windData'];}[_0x2d2794(0x81,0x10a)](){this[_0x5766ab(0x39,0xc2)]=new Worker(this['options']['worker']),this[_0x4c596a(-0x32c,-0x29d)]['onmessage']=_0x273013=>{function _0x1c46b2(_0x5ebb41,_0x45baff){return _0x4c596a(_0x5ebb41-0x452,_0x45baff);}function _0x16ff27(_0xeb4967,_0x5a7a73){return _0x4c596a(_0xeb4967-0xcf,_0x5a7a73);}this['_drawLines'](_0x273013[_0x16ff27(-0x1f0,-0x173)][_0x16ff27(-0x19c,-0x1c9)]),this['_updateIng2']=![];},this[_0x4c596a(-0x282,-0x2fd)]={'init':_0x147578=>{const _0x1acb7a={};_0x1acb7a['type']='init',_0x1acb7a['options']=_0x147578;function _0x2ece9c(_0x15ae44,_0x2a2bda){return _0x5766ab(_0x15ae44,_0x2a2bda- -0x40);}this['worker'][_0x2ece9c(0x119,0x16a)](_0x1acb7a);},'setOptions':_0x30e251=>{const _0x1bf4a0={};function _0x571616(_0x333d87,_0x5767c3){return _0x5766ab(_0x333d87,_0x5767c3-0x7);}function _0x2b1c21(_0x1c198b,_0x6e4d9f){return _0x4c596a(_0x6e4d9f-0x46b,_0x1c198b);}_0x1bf4a0[_0x2b1c21(0x18e,0x141)]=_0x571616(0x15b,0xf8),_0x1bf4a0[_0x571616(0x15e,0x1ab)]=_0x30e251,this['worker']['postMessage'](_0x1bf4a0);},'setDate':_0x508b55=>{const _0x11a156={};_0x11a156[_0x17bb3a(0x232,0x281)]='setDate',_0x11a156['data']=_0x508b55;function _0x17bb3a(_0x471393,_0x503e5b){return _0x5766ab(_0x503e5b,_0x471393-0x16e);}this['worker']['postMessage'](_0x11a156);},'update':()=>{function _0x28f13c(_0x2c4c1d,_0x31a45c){return _0x4c596a(_0x2c4c1d-0x2b8,_0x31a45c);}if(this['_updateIng2'])return;this[_0x28f13c(-0x71,-0xa8)]=!![];const _0x70666f={};_0x70666f['type']=_0x59ead0(0x3f0,0x457);function _0x59ead0(_0x33cbfa,_0x114cf9){return _0x4c596a(_0x114cf9-0x719,_0x33cbfa);}this['worker'][_0x59ead0(0x4e4,0x4d5)](_0x70666f);},'clear':()=>{const _0x4ba5e1={};function _0x37dc63(_0x306afc,_0x2e196a){return _0x5766ab(_0x2e196a,_0x306afc-0x274);}function _0x8dede7(_0x2f5eb7,_0xd649cd){return _0x5766ab(_0xd649cd,_0x2f5eb7-0x7f);}_0x4ba5e1['type']='clear',this[_0x8dede7(0x141,0x1b4)][_0x8dede7(0x229,0x26a)](_0x4ba5e1);}};function _0x4c596a(_0x328e07,_0x2e1292){return _0x2d2794(_0x2e1292,_0x328e07- -0x351);}function _0x5766ab(_0x2888c8,_0x338ff9){return _0x420c92(_0x2888c8,_0x338ff9-0x3e9);}this['windField'][_0x5766ab(0x1fa,0x17d)](this[_0x5766ab(0x11e,0x1a4)]);}}mars3d__namespace[_0x420c92(-0x344,-0x337)][_0x420c92(-0x1de,-0x241)]('canvasWind',CanvasWindLayer),mars3d__namespace['layer'][_0x420c92(-0x2e6,-0x2f2)]=CanvasWindLayer,mars3d__namespace['CanvasWindField']=CanvasWindField,mars3d__namespace['WindUtil']=WindUtil,exports['CanvasWindField']=CanvasWindField,exports[_0x420c92(-0x312,-0x2f2)]=CanvasWindLayer,exports['WindLayer']=WindLayer,exports[_0x420c92(-0x36d,-0x308)]=WindUtil;const _0x36bd8a={};function _0x9c0b(){const _0x17060c=['ceil','RGBA','default','min','uniform\x20sampler2D\x20currentParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0auniform\x20sampler2D\x20particlesWind;\x0a\x0a//\x20used\x20to\x20calculate\x20the\x20wind\x20norm\x0auniform\x20vec2\x20uSpeedRange;\x20//\x20(min,\x20max);\x0auniform\x20vec2\x20vSpeedRange;\x0auniform\x20float\x20pixelSize;\x0auniform\x20float\x20speedFactor;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0afloat\x20calculateWindNorm(vec3\x20speed)\x20{\x0a\x20\x20\x20\x20vec3\x20percent\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20percent.x\x20=\x20(speed.x\x20-\x20uSpeedRange.x)\x20/\x20(uSpeedRange.y\x20-\x20uSpeedRange.x);\x0a\x20\x20\x20\x20percent.y\x20=\x20(speed.y\x20-\x20vSpeedRange.x)\x20/\x20(vSpeedRange.y\x20-\x20vSpeedRange.x);\x0a\x20\x20\x20\x20float\x20normalization\x20=\x20length(percent);\x0a\x0a\x20\x20\x20\x20return\x20normalization;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20//\x20vec3\x20currentSpeed\x20=\x20texture(currentParticlesSpeed,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20windVector\x20=\x20texture(particlesWind,\x20v_textureCoordinates).rgb;\x0a\x0a\x20\x20\x20\x20vec4\x20nextSpeed\x20=\x20vec4(speedFactor\x20*\x20pixelSize\x20*\x20windVector,\x20calculateWindNorm(windVector));\x0a\x20\x20\x20\x20out_FragColor\x20=\x20nextSpeed;\x0a}\x0a','791003Lfunyv','particlesComputing','vertexShaderSource','abs','viewport','off','_map','STATIC_DRAW','sqrt','depthMask','SCENE3D','primitiveType','frameRate','_tomap','_speedRate','__proto__','currentParticlesPosition','positionWC','update','colorTable','drawingBufferHeight','data','Draw','Pass','_mountedHook','updateViewerParameters','visibility','mouse_move','IDENTITY','removeEventListener','createParticlesTextures','layer','green','PixelDatatype','clampToLatitudeRange','dropRate','rawRenderState','Math','position','rows','uniform\x20sampler2D\x20colorTable;\x0a\x0ain\x20float\x20speedNormalization;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20out_FragColor\x20=\x20texture(colorTable,\x20vec2(speedNormalization,\x200.0));\x0a}\x0a','ZERO','lonRange','Compute','defines','canvasHeight','enabled','clientWidth','NEAREST','mouseDown','worldToWindowCoordinates','Sampler','_bilinearInterpolation','refreshTimer','dimensions','rgb(206,255,255)','xmin','nextParticlesSpeed','TextureMagnificationFilter','ellipsoid','vmin','pixelSize','particlesNumber','clearCommand','width','LINEAR','requestAnimationFrame','createTexture','preExecute','OPAQUE','pointer-events','_onMap_preRenderEvent','speed','pixelFormat','outputTexture','maxAge','stroke','in\x20vec3\x20position;\x0ain\x20vec2\x20st;\x0a\x0aout\x20vec2\x20textureCoordinate;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20textureCoordinate\x20=\x20st;\x0a\x20\x20\x20\x20gl_Position\x20=\x20vec4(position,\x201.0);\x0a}\x0a','_onMouseUpEvent','geometry','textures','lng','windField','_addedHook','_onMapWhellEvent','canvasContext','lon','dropRateBump','resize','particlesTextureSize','destroy','TextureMinificationFilter','currentTrails','mode','clearFramebuffers','pointerEvents','204ytolAA','tlat','globe','init','redraw','43932NaMSAp','mouseUp','blending','canvasResize','particles','_createCanvas','ymax','vertexArray','16221vREKrp','Cartesian3','uniformMap','TWO_PI','fromCache','interval','__esModule','speedFactor','absolute','4970644amXHnk','push','screen','particleSystem','sin','shaderProgram','clientHeight','lineWidth','segments','DISABLE_GL_POSITION_LOG_DEPTH','fillRect','particlesTextures','mouseHidden','bindEvent','BufferUsage','VertexArray','sources','minimum','lev','_onMouseMoveEvent','options','_animateFrame','height','initWorker','register','canvasWidth','postMessage','depthTest','addEventListener','postProcessingPosition','RenderState','PI_OVER_TWO','xmax','vdata','7540040BgQyVm','createRenderingTextures','getParticles','createSegmentsGeometry','_calc_speedRate','wheel','max','tlng','attributeLocations','primitives','uniform\x20sampler2D\x20trailsColorTexture;\x0auniform\x20sampler2D\x20trailsDepthTexture;\x0a\x0ain\x20vec2\x20textureCoordinate;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20trailsColor\x20=\x20texture(trailsColorTexture,\x20textureCoordinate);\x0a\x20\x20\x20\x20float\x20trailsDepth\x20=\x20texture(trailsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20\x20\x20float\x20globeDepth\x20=\x20czm_unpackDepth(texture(czm_globeDepthTexture,\x20textureCoordinate));\x0a\x0a\x20\x20\x20\x20if\x20(trailsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20trailsColor;\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20}\x0a}\x0a','_randomParticle','pixelDatatype','setDate','speedRate','PixelFormat','clear','setGeometry','createCommand','viewRectangleToLonLatRange','_onMouseDownEvent','_updateIng','now','477828CejNyA','fromGeometry','maxParticles','destroyParticlesTextures','EventType','strokeStyle','getFullscreenQuad','getUVByPoint','16BTvkJV','ShaderSource','red','getUVByXY','Color','createRenderingPrimitives','nextTrails','grid','unbindEvent','canvas','randomBetween','globalAlpha','LayerUtil','autoClear','_calcUV','computeViewRectangle','_data','particlesWind','getRandomLatLng','length','Geometry','_canrefresh','DEPTH_COMPONENT','forEach','DepthFunction','scene','_removedHook','destination-in','worker','_showHook','type','_updateIng2','lat','47377eSwJmC','defined','toDegrees','preRender','arrayBufferView','removeChild','add','levmax','blue','vmax','22wyXPnj','color','updatePosition','WindLayer','ShaderProgram','ymin','age','pow','addPrimitives','toGridXY','north','getOptions','left','camera','mod','mouse_down','WindUtil','randomizeParticles','ComponentDatatype','uniform\x20sampler2D\x20segmentsColorTexture;\x0auniform\x20sampler2D\x20segmentsDepthTexture;\x0a\x0auniform\x20sampler2D\x20currentTrailsColor;\x0auniform\x20sampler2D\x20trailsDepthTexture;\x0a\x0auniform\x20float\x20fadeOpacity;\x0a\x0ain\x20vec2\x20textureCoordinate;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20pointsColor\x20=\x20texture(segmentsColorTexture,\x20textureCoordinate);\x0a\x20\x20\x20\x20vec4\x20trailsColor\x20=\x20texture(currentTrailsColor,\x20textureCoordinate);\x0a\x0a\x20\x20\x20\x20trailsColor\x20=\x20floor(fadeOpacity\x20*\x20255.0\x20*\x20trailsColor)\x20/\x20255.0;\x20//\x20make\x20sure\x20the\x20trailsColor\x20will\x20be\x20strictly\x20decreased\x0a\x0a\x20\x20\x20\x20float\x20pointsDepth\x20=\x20texture(segmentsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20\x20\x20float\x20trailsDepth\x20=\x20texture(trailsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20\x20\x20float\x20globeDepth\x20=\x20czm_unpackDepth(texture(czm_globeDepthTexture,\x20textureCoordinate));\x0a\x0a\x20\x20\x20\x20out_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20if\x20(pointsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20out_FragColor\x20+\x20pointsColor;\x0a\x20\x20\x20\x20}\x0a\x20\x20\x20\x20if\x20(trailsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20out_FragColor\x20=\x20out_FragColor\x20+\x20trailsColor;\x0a\x20\x20\x20\x20}\x0a\x20\x20\x20\x20gl_FragDepth\x20=\x20min(pointsDepth,\x20trailsDepth);\x0a}\x0a','Cesium','container','commandToExecute','in\x20vec2\x20st;\x0a//\x20it\x20is\x20not\x20normal\x20itself,\x20but\x20used\x20to\x20control\x20normal\x0ain\x20vec3\x20normal;\x20//\x20(point\x20to\x20use,\x20offset\x20sign,\x20not\x20used\x20component)\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x0auniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20postProcessingSpeed;\x0a\x0auniform\x20float\x20particleHeight;\x0a\x0auniform\x20float\x20aspect;\x0auniform\x20float\x20pixelSize;\x0auniform\x20float\x20lineWidth;\x0a\x0aout\x20float\x20speedNormalization;\x0a\x0avec3\x20convertCoordinate(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20WGS84\x20(lon,\x20lat,\x20lev)\x20->\x20ECEF\x20(x,\x20y,\x20z)\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_conversion#From_geodetic_to_ECEF_coordinates\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20WGS\x2084\x20geometric\x20constants\x0a\x20\x20\x20\x20float\x20a\x20=\x206378137.0;\x20//\x20Semi-major\x20axis\x0a\x20\x20\x20\x20float\x20b\x20=\x206356752.3142;\x20//\x20Semi-minor\x20axis\x0a\x20\x20\x20\x20float\x20e2\x20=\x206.69437999014e-3;\x20//\x20First\x20eccentricity\x20squared\x0a\x0a\x20\x20\x20\x20float\x20latitude\x20=\x20radians(lonLatLev.y);\x0a\x20\x20\x20\x20float\x20longitude\x20=\x20radians(lonLatLev.x);\x0a\x0a\x20\x20\x20\x20float\x20cosLat\x20=\x20cos(latitude);\x0a\x20\x20\x20\x20float\x20sinLat\x20=\x20sin(latitude);\x0a\x20\x20\x20\x20float\x20cosLon\x20=\x20cos(longitude);\x0a\x20\x20\x20\x20float\x20sinLon\x20=\x20sin(longitude);\x0a\x0a\x20\x20\x20\x20float\x20N_Phi\x20=\x20a\x20/\x20sqrt(1.0\x20-\x20e2\x20*\x20sinLat\x20*\x20sinLat);\x0a\x20\x20\x20\x20float\x20h\x20=\x20particleHeight;\x20//\x20it\x20should\x20be\x20high\x20enough\x20otherwise\x20the\x20particle\x20may\x20not\x20pass\x20the\x20terrain\x20depth\x20test\x0a\x0a\x20\x20\x20\x20vec3\x20cartesian\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20cartesian.x\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20cosLon;\x0a\x20\x20\x20\x20cartesian.y\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20sinLon;\x0a\x20\x20\x20\x20cartesian.z\x20=\x20((b\x20*\x20b)\x20/\x20(a\x20*\x20a)\x20*\x20N_Phi\x20+\x20h)\x20*\x20sinLat;\x0a\x20\x20\x20\x20return\x20cartesian;\x0a}\x0a\x0avec4\x20calcProjectedCoordinate(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20the\x20range\x20of\x20longitude\x20in\x20Cesium\x20is\x20[-180,\x20180]\x20but\x20the\x20range\x20of\x20longitude\x20in\x20the\x20NetCDF\x20file\x20is\x20[0,\x20360]\x0a\x20\x20\x20\x20//\x20[0,\x20180]\x20is\x20corresponding\x20to\x20[0,\x20180]\x20and\x20[180,\x20360]\x20is\x20corresponding\x20to\x20[-180,\x200]\x0a\x20\x20\x20\x20lonLatLev.x\x20=\x20mod(lonLatLev.x\x20+\x20180.0,\x20360.0)\x20-\x20180.0;\x0a\x20\x20\x20\x20vec3\x20particlePosition\x20=\x20convertCoordinate(lonLatLev);\x0a\x20\x20\x20\x20vec4\x20projectedCoordinate\x20=\x20czm_modelViewProjection\x20*\x20vec4(particlePosition,\x201.0);\x0a\x20\x20\x20\x20return\x20projectedCoordinate;\x0a}\x0a\x0avec4\x20calcOffset(vec4\x20currentProjectedCoordinate,\x20vec4\x20nextProjectedCoordinate,\x20float\x20offsetSign)\x20{\x0a\x20\x20\x20\x20vec2\x20aspectVec2\x20=\x20vec2(aspect,\x201.0);\x0a\x20\x20\x20\x20vec2\x20currentXY\x20=\x20(currentProjectedCoordinate.xy\x20/\x20currentProjectedCoordinate.w)\x20*\x20aspectVec2;\x0a\x20\x20\x20\x20vec2\x20nextXY\x20=\x20(nextProjectedCoordinate.xy\x20/\x20nextProjectedCoordinate.w)\x20*\x20aspectVec2;\x0a\x0a\x20\x20\x20\x20float\x20offsetLength\x20=\x20lineWidth\x20/\x202.0;\x0a\x20\x20\x20\x20vec2\x20direction\x20=\x20normalize(nextXY\x20-\x20currentXY);\x0a\x20\x20\x20\x20vec2\x20normalVector\x20=\x20vec2(-direction.y,\x20direction.x);\x0a\x20\x20\x20\x20normalVector.x\x20=\x20normalVector.x\x20/\x20aspect;\x0a\x20\x20\x20\x20normalVector\x20=\x20offsetLength\x20*\x20normalVector;\x0a\x0a\x20\x20\x20\x20vec4\x20offset\x20=\x20vec4(offsetSign\x20*\x20normalVector,\x200.0,\x200.0);\x0a\x20\x20\x20\x20return\x20offset;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec2\x20particleIndex\x20=\x20st;\x0a\x0a\x20\x20\x20\x20vec3\x20currentPosition\x20=\x20texture(currentParticlesPosition,\x20particleIndex).rgb;\x0a\x20\x20\x20\x20vec4\x20nextPosition\x20=\x20texture(postProcessingPosition,\x20particleIndex);\x0a\x0a\x20\x20\x20\x20vec4\x20currentProjectedCoordinate\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20vec4\x20nextProjectedCoordinate\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20if\x20(nextPosition.w\x20>\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20currentProjectedCoordinate\x20=\x20calcProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20\x20\x20\x20\x20nextProjectedCoordinate\x20=\x20calcProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20currentProjectedCoordinate\x20=\x20calcProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20\x20\x20\x20\x20nextProjectedCoordinate\x20=\x20calcProjectedCoordinate(nextPosition.xyz);\x0a\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20float\x20pointToUse\x20=\x20normal.x;\x20//\x20-1\x20is\x20currentProjectedCoordinate\x20and\x20+1\x20is\x20nextProjectedCoordinate\x0a\x20\x20\x20\x20float\x20offsetSign\x20=\x20normal.y;\x0a\x0a\x20\x20\x20\x20vec4\x20offset\x20=\x20pixelSize\x20*\x20calcOffset(currentProjectedCoordinate,\x20nextProjectedCoordinate,\x20offsetSign);\x0a\x20\x20\x20\x20if\x20(pointToUse\x20<\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20gl_Position\x20=\x20currentProjectedCoordinate\x20+\x20offset;\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20gl_Position\x20=\x20nextProjectedCoordinate\x20+\x20offset;\x0a\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20speedNormalization\x20=\x20texture(postProcessingSpeed,\x20particleIndex).a;\x0a}\x0a','frameTime','globeBoundingSphere','Appearance','framebuffers','isDestroy','9950BeQnqS','PrimitiveType','FLOAT','setOptions','createRawRenderState','context','auto','remove','SceneMode','CanvasWindLayer','particlesRendering','east','bind','latRange','isInExtent','_pointerEvents','applyViewerParameters','framebuffer','windTextures','udata','updateSpeed','keys','style','show','getSpeed','fragmentShaderSource','zIndex','toRadians','cols','colors','_colorRamp','mouseMove','0px','drawingBufferWidth','UNSIGNED_BYTE','nextParticlesPosition','fromDegrees','viewerParameters','fixedHeight'];_0x9c0b=function(){return _0x17060c;};return _0x9c0b();}_0x36bd8a['value']=!![],Object['defineProperty'](exports,_0x2d2794(0x13f,0xf0),_0x36bd8a);
}));
