import request from '@/utils/request'

export const personStream = (data) =>
	request({
		url: '/traffic/person/currentMonth',
		method: 'get',
		params:data
	})	

export const vehicleStream = (data) =>
	request({
		url: 'traffic/vehicle/currentMonth',
		method: 'get',
		params:data
	})
export const deviceType = (data) =>
	request({
		url: 'device/Statistics/webAnFang',
		method: 'get',
		params:data
	})	
export const event = (data) =>
	request({
		url: 'warn/Statistics/webHappenTimeByMonth',
		method: 'get',
		params:data
	})	
	
