<template>
	<role-scope :scopeList="scopeList"></role-scope>
	<role-edit :statusList="statusList" :scopeList="scopeList" @search="search"></role-edit>
	<system-user-del :userGroupList="userGroupList" :statusList="statusList" :scopeList="scopeList" @search="search"></system-user-del>
	<w-chat-user-del :userGroupList="userGroupList" :statusList="statusList" :scopeList="scopeList" @search="search"></w-chat-user-del>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.roleName" @keydown.enter="search" placeholder="角色名" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="12">
			<el-button style="float: right;" type="primary" @click="add">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col>
			<el-table stripe :data="roleList" border style="width: 100%">
				<el-table-column prop="roleName" align="center" label="角色名称"  />
				<el-table-column prop="roleCode" align="center" label="角色代码"  />
				<!-- <el-table-column prop="status" align="center" :formatter="formatStatus" label="状态" /> -->
				
				<el-table-column prop="status" align="center" label="状态" width="88">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList,scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="updateTime" align="center" label="更新时间" width="168" />
				<el-table-column align="center" width="200" label="操作">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('sys:role:save')">编辑</el-button>
						<el-button type="text" size="default" @click="copy(scope.row.id)" v-if="hasPerm('sys:role:save')">复制</el-button>
						<el-button style="margin-right: 10px;" type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('sys:role:delete')">删除</el-button>
						<el-dropdown>
							<el-button type="text" size="default">更多</el-button>
						    <template #dropdown>
						      <el-dropdown-menu>
						        <el-dropdown-item>
									<el-button type="text" size="default" @click="systemUser(scope.row.id)">系统用户</el-button>
									<el-button type="text" size="default" @click="wChatUser(scope.row.id)">微信用户</el-button>
									<el-button type="text" size="default" @click="dataScope(scope.row.id)" v-if="hasPerm('sys:role:socpe:query')">数据范围</el-button>
								</el-dropdown-item>
						      </el-dropdown-menu>
						    </template>
						</el-dropdown>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
	
</template>

<script>
import { listUserGroup } from "@/api/admin/userGroup"
import { listUser } from "@/api/admin/user"
import { weChatList } from "@/api/weChat/weChatUser"
import { pagingRole,deleteRole,getRole,scopeList } from "@/api/admin/role"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import roleEdit from "@/componts/admin/role/roleEdit.vue"
import roleScope from "@/componts/admin/role/roleScope.vue"
import systemUserDel from "@/componts/admin/role/systemUserDel.vue"
import wChatUserDel from "@/componts/admin/role/wChatUserDel.vue"
export default {
	components:{ roleEdit,roleScope,systemUserDel,wChatUserDel },
	data() {
		return {
			searchModel: {},
			roleList: [],
			userGroupList:[],
			statusList:[],
			scopeList:[],
			total:0,
			pageSize:10
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			pagingRole(this.searchModel)
			.then(res => {
				this.roleList = res.data.result.list
				this.total = res.data.result.total
			})
		},
		systemUser(id){
			listUser({roleId:id}).then(res => {
				const data = {
					id: id,
					result: res.data.result
				}
				mitt.emit('openSystemUserDelList',data)
			})
		},
		wChatUser(id){
			weChatList({roleId:id}).then(res => {
				const data = {
					id: id,
					result: res.data.result
				}
				mitt.emit('openwChatUserDelList',data)
			})
		},
		dataScope(id){
			scopeList({roleId:id})
			.then(res =>{
				mitt.emit('openRoleScopeEdit',res.data.result)
			})
		},
		edit(id){
			getRole(id)
			.then(res =>{
				mitt.emit('openRoleEdit',res.data.result)
			})
		},
		copy(id){
			getRole(id)
			.then(res =>{
				mitt.emit('openRoleCopy',res.data.result)
			})
		},
		add(){
			mitt.emit('openRoleAdd')
		},
		deleted(id){
			this.$confirm('删除角色, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					deleteRole(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},
		async init(){
			mitt.off('openRoleEdit')
			mitt.off('openRoleAdd')
			try{
				let res = await pagingRole(this.searchModel)
				let resStatus = await listDictByNameEn('role_status')
				let resScope = await listDictByNameEn('role_scope')
				this.statusList = resStatus.data.result
				this.scopeList = resScope.data.result
				this.roleList = res.data.result.list
				this.total = res.data.result.total

				let resGroup = await listUserGroup({totalize:99999})
				var groupStr = JSON.stringify(resGroup.data.result.list).replaceAll('groupName','label').replaceAll('id','value')
				this.userGroupList = JSON.parse(groupStr)
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>
