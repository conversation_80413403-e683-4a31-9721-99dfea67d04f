<template>
	<warn-event-type-edit :statusList="statusList" :tagList="tagList" @search="search"></warn-event-type-edit>
	<el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.name" placeholder="类型名称" clearable />
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="12">
			<el-button style="float: right;" type="primary" @click="add" v-if="hasPerm('warn:warnEventType:add')">添 加</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table :data="personList" border height="calc(100vh - 300px)" style="width: 100%">
				<el-table-column prop="name" width="200" label="类型名称" />
				<el-table-column prop="note" header-align="center" label="类型描述" />
				<el-table-column prop="tags" align="center" label="标签">
					<template #default="scope">
						<span v-for="item in getTags(scope.row)" :key="item" class="tag-item"
							:style="'background-color:' + item.cssClass">{{ item.nameCn }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="isRuleRecognize" width="120" align="center" label="是否规则识别">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.isRuleRecognize)">{{
							formatDict(statusList,
								scope.row.isRuleRecognize)
						}}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="isPerson" width="120" align="center" label="是否人员">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList, scope.row.isPerson)">{{
							formatDict(statusList,
								scope.row.isPerson)
						}}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="sort" width="60" align="center" label="排序" />
				<el-table-column align="center" width="100" label="操作" v-if="hasPerm('warn:warnEventType:delete') || hasPerm('warn:warnEventType:edit')">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('warn:warnEventType:edit')">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('warn:warnEventType:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
				layout="total, sizes, prev, pager, next, jumper" @current-change="currentChange"
				@size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>

<script>
import { warnEventTypeList, warnEventTypeListDelete, getWarnEventType } from "@/api/warn/warn"
import { listDictByNameEn } from "@/api/admin/dict"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import warnEventTypeEdit from "@/componts/warn/warnEventTypeEdit.vue"
export default {
	components: { warnEventTypeEdit },
	data() {
		return {
			searchModel: {},
			personList: [],
			communityId: localStorage.getItem("communityId"),
			statusList: [],
			tagList: [],
			total: 0,
			pageSize: 10
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		},
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		search() {
			this.searchModel.communityId = this.communityId
			warnEventTypeList(this.searchModel)
				.then(res => {
					this.personList = res.data.result.list
					this.total = res.data.result.total
				})
		},
		edit(id) {
			getWarnEventType(id)
				.then(res => {
					console.log(mitt)
					mitt.emit('openWarnEventTypeEdit', res.data.result)
				})
		},
		add() {
			mitt.emit('openWarnEventTypeAdd')
		},
		deleted(id) {
			this.$confirm('删除事件感知类型, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				warnEventTypeListDelete(id)
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num) {
			this.searchModel.pageSize = num
			this.search()
		},
		getTags(row) {
			let list = []
			if (row.tags) {
				let tagArrary = JSON.parse(row.tags)
				list = this.tagList.filter(item => tagArrary.some(tag => tag == item.nameEn))
			}
			return list
		},
		async getPersonTag() {
			let tagRes = await listDictByNameEn('person_tag')
			this.tagList = tagRes.data.result
		},
		async init() {
			mitt.off('openWarnEventTypeEdit')
			mitt.off('openWarnEventTypeAdd')
			try {
				this.searchModel.communityId = this.communityId
				let res = await warnEventTypeList(this.searchModel)
				this.personList = res.data.result.list
				this.total = res.data.result.total
				let isStatus = await listDictByNameEn('is_or_not')
				this.statusList = isStatus.data.result
				this.getPersonTag()
			} catch (err) {
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
.el-row {
	margin-bottom: 20px;
	background-color: #fff;
	padding: 20px 10px;
	border-radius: 5px;
}

.tag-item {
	display: inline-block;
	margin-bottom: 5px;
	margin-right: 5px;
	width: 59px;
	border-radius: 7px;
	color: #FFF;
	font-size: 12px;
	line-height: 14px;
	font-family: Microsoft YaHei;
	padding: 5px 5px 5px 5px;

}
</style>
