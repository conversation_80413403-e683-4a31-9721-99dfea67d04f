import request from '@/utils/request'

// 查询用户方案分页
export function pagingUserSolution(query) {
  return request({
    url: '/mapi/v3_8_0/assy/userSolution',
    method: 'get',
    params: query
  })
}

// 查询用户方案列表
export function listUserSolution(query) {
  return request({
    url: '/mapi/v3_8_0/assy/userSolution/list',
    method: 'get',
    params: query
  })
}

// 查询用户方案详细
export function getUserSolution(id) {
  return request({
    url: '/mapi/v3_8_0/assy/userSolution/' + id,
    method: 'get'
  })
}

// 新增用户方案
export function addUserSolution(data) {
  return request({
    url: '/mapi/v3_8_0/assy/userSolution',
    method: 'post',
    data: data
  })
}

// 修改用户方案
export function updateUserSolution(data) {
  return request({
    url: '/mapi/v3_8_0/assy/userSolution',
    method: 'put',
    data: data
  })
}

// 删除用户方案
export function delUserSolution(id) {
  return request({
    url: '/mapi/v3_8_0/assy/userSolution/' + id,
    method: 'delete'
  })
}
