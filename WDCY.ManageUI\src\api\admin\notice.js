import request from '@/utils/request'

export const listNotice = (data) =>
	request({
		url: '/notice/page',
		method: 'get',
		params: data
	})
export const addNotice = (data) =>
	request({
		url: '/notice',
		method: 'post',
		data: data
	})
export const getNotice = (id) =>
	request({
		url: '/notice/'+id,
		method: 'get',
	})
export const deleteNotice = (id) =>
	request({
		url: '/notice/'+id,
		method: 'delete',
		// params: {
		// 	noticeId: id
		// }
	})
export const editNotice = (data) =>
	request({
		url: '/notice',
		method: 'put',
		data: data
	})