<template>
  <building-edit ref="buildingEditRef" @search="search"></building-edit>
  <building-detail></building-detail>
  <open-id-edit @search="search"></open-id-edit>
  <map-select-point @point="point"></map-select-point>
  <mars-map @point="point"></mars-map>
  <geofencing @point="point"></geofencing>
  <el-row :gutter="20">
    <el-col :span="4">
      <el-input
        v-model="searchModel.buildingName"
        @keydown.enter="search"
        placeholder="楼栋名称"
        clearable
      />
    </el-col>
    <el-col :span="4">
      <el-button type="primary" @click="search">搜 索</el-button>
    </el-col>
    <el-col :span="4" :push="12">
      <el-button
        style="float: right"
        type="primary"
        @click="add"
        v-if="hasPerm('base:building:add')"
        >添 加</el-button
      >
      <el-button
        style="float: right; margin-right: 15px"
        type="primary"
        @click="calibrateOpen"
        v-if="hasPerm('base:building:calibrate')"
        >一键校准</el-button
      >
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-table stripe :data="buildingList" border style="width: 100%">
        <el-table-column
          prop="buildingNumber"
          align="center"
          label="楼栋名称"
        />
        <el-table-column prop="sort" align="center" label="楼栋编号" />
        <el-table-column align="center" label="面积">
          <template #default="scope">
            <span v-if="scope.row.acreage">{{ scope.row.acreage }} M²</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="位置">
          <template #default="scope">
            <el-col
              :span="24"
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <span>{{ scope.row.lng }} - {{ scope.row.lat }}</span>
              <el-button
                v-show="isShow(scope.row)"
                type="text"
                style="font-size: 30px; padding: 0"
                @click="selectPoint(scope.row)"
              >
                <el-icon :size="30">
                  <location-filled></location-filled>
                </el-icon>
              </el-button>
            </el-col>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          align="center"
          label="备注"
        ></el-table-column>
        <el-table-column align="center" width="210" label="操作">
          <template #default="scope">
            <el-button
              type="text"
              size="default"
              @click="detail(scope.row.id)"
              v-if="hasPerm('base:building:detail')"
              >详情</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="selectArea(scope.row)"
              v-if="hasPerm('base:building:edit')"
              >围栏
            </el-button>
            <el-button
              type="text"
              size="default"
              @click="edit(scope.row.id)"
              v-if="hasPerm('base:building:edit')"
              >编辑
            </el-button>
            <el-button
              type="text"
              size="default"
              @click="deleted(scope.row.id)"
              v-if="hasPerm('base:building:delete')"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination
        background
        v-model:page-size="searchModel.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :total="Number(total)"
      ></el-pagination>
    </el-col>
  </el-row>
  <!-- 校准弹窗 -->
  <el-dialog
    draggable
    width="20%"
    v-loading="loading"
    v-model="dialog.show"
    destroy-on-close
    :title="dialog.title"
    append-to-body
  >
    <el-form
      :rules="rules"
      ref="form"
      :model="calibrateModel"
      label-width="90px"
    >
      <el-row style="margin-bottom: 0px">
        <el-col :span="24">
          <el-form-item prop="isGroupOrientedLeasing">
            <template v-slot:label>
              <span>
                校准类型
                <el-tooltip>
                  <template #content>
                    【校准规则项】
                    <br />1、正向同步：商户、出租、群租状态不变，空置、自住或无状态下有租客则改为租赁状态。
                    <br />2、反向同步：租赁状态的房子中没有租客时，将之同步为非租赁状态（自住或空置）。
                  </template>
                  <el-icon><info-filled /></el-icon>
                </el-tooltip>
              </span>
            </template>

            <el-radio-group v-model="calibrateModel.isBackwardSyn" class="ml-4">
              <el-radio :label="0">正向同步</el-radio>
              <el-radio :label="1">反向同步</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center" style="margin-bottom: 0px">
      <el-button
        type="primary"
        style="width: 100px; height: 30px"
        @click="calibrate"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>


<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>
<script>
import {
  listBuilding,
  deleteBuilding,
  getBuilding,
  detailBuilding,
  calibrateRoom,
  updateByPolyCoords
} from "@/api/base/building";
import mitt from "@/utils/mitt";
import buildingEdit from "@/componts/base/buildingEdit.vue";
import buildingDetail from "@/componts/base/buildingDetail.vue";
import mapSelectPoint from "@/componts/map/mapSelectPoint.vue";
import geofencing from "@/componts/map/geofencing.vue";
import openIdEdit from "@/componts/base/openIdEdit.vue";
import marsMap from "@/componts/map/marsMap.vue";
import { getCommunity } from "@/api/base/community";
import { openGeofencingMap } from "@/utils/myUtils";
export default {
  components: {
    buildingEdit,
    buildingDetail,
    mapSelectPoint,
    openIdEdit,
    marsMap,
    geofencing
  },
  data() {
    return {
      searchModel: {
        communityId: localStorage.getItem("communityId"),
      },
      calibrateModel: {
        communityId: localStorage.getItem("communityId"),
      },
      buildingList: [],
      statusList: [],
      total: 0,
      pageSize: 10,
      dialog: {
        show: false,
        title: "一键校准",
      },
    };
  },
  mounted() {
    mitt.on("updateGeoPolyCoords", (data) => {
      if (data) {
        console.log("提交保存地理围栏信息", data);
        updateByPolyCoords(data).then((res) => {
          if (res.data.code == 0) {
            this.$message.success(res.data.msg);
          } else {
            this.$message.error(res.data.msg);
          }
        });
      }
    });
  },
  methods: {
    point(e) {
      mitt.emit("setPointValue", e);
    },
    isShow(row) {
      if (
        row.lng != undefined &&
        row.lng != null &&
        row.lng != "" &&
        row.lat != undefined &&
        row.lat != null &&
        row.lat != ""
      ) {
        return true;
      }
      return false;
    },
    selectPoint(row) {
      var communityId = localStorage.getItem("communityId");
      getCommunity(communityId)
        .then((res) => {
          // var result = res.data.result;
          // var config = JSON.parse(result.expandParams);
          // var mode = config.map.mode;
          // if (mode == "amap" || !result.enabled3d) {
          //   openMap(mitt, false, [row.lng, row.lat], row.buildingName);
          // } else if (mode == "mars3d") {
          //   var center = {
          //     lng: config.map.sdgis.position.lng,
          //     lat: config.map.sdgis.position.lat,
          //     alt: config.map.sdgis.position.alt,
          //     heading: config.map.sdgis.view.heading,
          //     pitch: config.map.sdgis.view.pitch,
          //   };

          //   var rotationSet = { x: 0, y: 0, z: 0 };

          //   try {
          //     rotationSet = config.map.sdgis.rotation;
          //   } catch (error) {}

          //   var scaleSet = 1;
          //   try {
          //     scaleSet = config.map.sdgis.scale;
          //   } catch (error) {}

          //   var showBaseMap = false;
          //   try {
          //     showBaseMap = config.map.sdgis.showBaseMap;
          //   } catch (error) {}
          //    
          //   var data = {
          //     edit: false,
          //     title:'查看点位',
          //     point: [row.lng, row.lat, row.alt],
          //     position: config.map.sdgis.position,
          //     center: center,
          //     modeUrl: config.map.sdgis.tdtile,
          //     rotationSet,
          //     scaleSet,
          //     showBaseMap: showBaseMap,
          //   };
          //   console.log(data);
          //   mitt.emit("openMarsMap", data);
          // } else if (mode == "treejs") {
          // }

          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map.mode;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          if (result.enabled3d && mode == "mars3d") {
          
            position = config.map.sdgis.position;

            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          } 
          
          if (result.lng && result.lat) {
            center = {
              lng: result.lng,
              lat: result.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }
         
           ;
          var data = {
            enabled3d: result.enabled3d,
            edit: false,
            point: [    row.lng, row.lat, row.alt],
            position: position,
            center: center,
            modeUrl: modeUrl,
            title: "查看点位",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);

     
          mitt.emit("openMarsMap", data);






        })
        .catch((err) => {});
    },
    // 地理围栏编辑
    selectArea(row) {
      getCommunity(row.communityId)
        .then((res) => {
          var community = res.data.result;
          if (res.data.code == 0 && community) {
            getBuilding(row.id).then((res1) => {
              var info = res1.data.result;
              if (res1.data.code == 0 && info) {
                let position = {
                  lng: info.lng,
                  lat: info.lat,
                  alt: info.alt
                };
                openGeofencingMap(mitt, {
                  id: row.id,
                  title: info.buildingNumber,
                  position: info.lng && info.lat ? position : null,
                  polyCoords: info.polyCoords,
                  enabled3d: community.enabled3d,
                  mapConfig: community.expandParams
                });
              }
            });
          }
        })
        .catch((err) => { });
    },
    search() {
      listBuilding(this.searchModel).then((res) => {
        this.buildingList = res.data.result.list;
        this.total = res.data.result.total;
      });
    },
    // 一件校准
    calibrateOpen() {
      this.dialog.show = true;
      this.calibrateModel = {
        communityId: localStorage.getItem("communityId"),
        isBackwardSyn: 0,
      };
    },
    // 校准提交
    calibrate() {
      // const params = {communityId:localStorage.getItem("communityId")}
      calibrateRoom(this.calibrateModel).then((res) => {
        this.search();
        this.$message.success(res.data.msg);
        this.dialog.show = false;
      });
    },
    detail(id) {
      detailBuilding({ id: id, pageNum: 1 }).then((res) => {
        mitt.emit("openBuildingDetail", res.data.result);
      });
    },
    openId(row) {
      const data = {
        id: row.id,
        openId: row.openId,
        type: "building",
      };
      mitt.emit("openOpenIdEdit", data);
    },
    edit(id) {
      getBuilding(id).then((res) => {
        mitt.emit("openBuildingEdit", res.data.result);
      });
    },
    add() {
      mitt.emit("openBuildingAdd", this.total);
    },
    deleted(id) {
      this.$confirm("删除楼栋, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteBuilding(id).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
          });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      console.log(num);
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      console.log(num);
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      console.log(num);
      this.search();
    },
    handleSizeChange(num) {
      this.searchModel.pageSize = num;
      console.log(num);
      this.search();
    },
    async init() {
      mitt.off("openBuildingEdit");
      mitt.off("openBuildingAdd");
      mitt.off("openBuildingDetail");
      mitt.off("setPointValue");
      mitt.off("openMarsMap");
      try {
        let res = await listBuilding(this.searchModel);
        this.buildingList = res.data.result.list;
        this.total = res.data.result.total;
      } catch (err) {}
    },
  },
  created() {
    this.init();
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
div /deep/ .cell {
  display: flex;
  justify-content: center;
}
</style>
