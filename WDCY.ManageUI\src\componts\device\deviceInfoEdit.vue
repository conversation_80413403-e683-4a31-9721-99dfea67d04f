<template>
  <map-select-point @point="point" @addressCall="setAddress"></map-select-point>
  <mars-map @point="point"></mars-map>
  <el-dialog
    draggable
    width="42%"
    top="3vh"
    v-loading="loading"
    destroy-on-close
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-scrollbar height="542px;padding-top:60px">
      <el-form
        ref="formEl"
        :rules="rules"
        :model="dataModel"
        label-width="120px"
        style="padding-right: 25px"
      >
        <el-form-item label="设施标识" prop="devNo">
          <el-input v-model="dataModel.devNo" placeholder="设施标识"></el-input>
        </el-form-item>
        <el-form-item label="国标编码" prop="gbCode">
          <el-input
            v-model="dataModel.gbCode"
            placeholder="国标编码"
          ></el-input>
        </el-form-item>
        <el-form-item label="设施名称" prop="name">
          <el-input v-model="dataModel.name" placeholder="设施名称"></el-input>
        </el-form-item>

        <el-form-item label="设施类型" prop="codeId">
          <el-cascader
            v-model="dataModel.codeId"
            :props="{ checkStrictly: true }"
            :options="typeList"
            @change="handleChange"
            clearable
            placeholder="选择类型"
          />
        </el-form-item>

        <el-form-item label="设施IP" prop="devIp">
          <el-input v-model="dataModel.devIp" placeholder="设施IP"></el-input>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <!-- <el-input onkeyup = "value=value.replace(/[^\d]/g,'')" v-model="dataModel.sort" placeholder="排序"></el-input> -->
          <el-input-number
            v-model="dataModel.sort"
            :min="1"
            :max="1000"
            :tep="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input
            v-model="dataModel.note"
            maxlength="200"
            placeholder="请填写维护描述"
            show-word-limit
            type="textarea"
          />
        </el-form-item>

        <el-form-item label="地址" prop="address">
          <el-input v-model="dataModel.address" placeholder="地址"></el-input>
        </el-form-item>

        <el-form-item label="经纬度" prop="lng">
          <el-col :span="10" class="text-center">
            <el-input v-model="dataModel.lng" placeholder="经度"></el-input>
          </el-col>
          <el-col :span="2" style="text-align: center">
            <span>-</span>
          </el-col>
          <el-col :span="10">
            <el-input v-model="dataModel.lat" placeholder="纬度"></el-input>
          </el-col>
          <el-col :span="2" style="text-align: center">
            <el-button
              type="text"
              style="font-size: 30px; padding: 0"
              @click="selectPoint"
            >
              <el-icon :size="30">
                <location-filled></location-filled>
              </el-icon>
            </el-button>
          </el-col>
        </el-form-item>
        <el-form-item label="海拔" prop="alt">
          <el-input-number
            controls-position="right"
            v-model="dataModel.alt"
            placeholder="海拔"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="设施安装类型" prop="installType">
          <el-radio v-model="dataModel.installType" :label="0">地面</el-radio>
          <el-radio v-model="dataModel.installType" :label="1">高空</el-radio>
          <el-radio v-model="dataModel.installType" :label="2">地下室</el-radio>
          <el-radio v-model="dataModel.installType" :label="3">水中</el-radio>
          <el-radio v-model="dataModel.installType" :label="4">土中</el-radio>
          <el-radio v-model="dataModel.installType" :label="5">建筑中</el-radio>
        </el-form-item>

        <el-form-item label="设施状态" prop="status">
          <el-radio v-model="dataModel.status" :label="0">正常</el-radio>
          <el-radio v-model="dataModel.status" :label="1">离线</el-radio>
          <el-radio v-model="dataModel.status" :label="2">异常</el-radio>
        </el-form-item>

        <el-form-item v-if="show" label="大屏展示" prop="enable">
          <el-radio v-model="dataModel.enable" :label="true">启用</el-radio>
          <el-radio v-model="dataModel.enable" :label="false">禁用</el-radio>
        </el-form-item>

        <el-form-item label="图片">
          <el-card class="box-card">
            <template #header>
              <div style="display: flex; justify-content: space-between">
                <el-button @click="uploadImg" class="button" type="text">
                  <input
                    style="position: fixed; left: -9999px; display: none"
                    type="file"
                    accept="image/*"
                    id="imgReader"
                    ref="upload"
                    @change="loadingImg"
                    :value="upload_input"
                  />裁剪上传
                </el-button>
                <el-button v-if="!picture" class="button" type="text">
                  <el-upload
                    :show-file-list="false"
                    :http-request="imgUpload"
                    accept="image/jpeg,image/jpg,image/png"
                    >上传</el-upload
                  >
                </el-button>
                <el-button
                  v-else
                  class="button"
                  type="text"
                  @click="deletePhoto"
                  >删除</el-button
                >
              </div>
            </template>
            <el-image
              fit="contain"
              style="height: 100px; width: 200px"
              :preview-src-list="[imgServer + picture]"
              :src="imgServer + picture"
            ></el-image>
          </el-card>
        </el-form-item>

        <el-form-item label="扩展参数" prop="expandParams">
          <!-- <JsonEditorVue class="editor" language="cn" v-model="jsonVal" /> -->
          <JsonEditorVue
            class="editor"
            language="cn"
            :modelValue="jsonVal"
            @update:modelValue="changeJson"
          />
        </el-form-item>

        <el-dialog draggable v-model="viewPic" title="裁剪头像">
          <div style="display: flex">
            <img id="cropImg" style="width: 300px; height: 300px" />
          </div>
          <div>
            <div style="display: flex; margin-top: 10px">
              <el-button type="primary" @click="GetData">确认</el-button>
            </div>
          </div>
        </el-dialog>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer" style="padding-right: 25px">
        <el-button
          type="primary"
          style="width: 100px; height: 30px; margin-top: 20px"
          @click="onSubmit"
          >提 交
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>

<script>
import CropperObj from "../../utils/myUtils.js";
import Cropper from "cropperjs";
import "cropperjs/dist/cropper.css";
import { truncate } from "lodash";
import { fileUpload } from "@/api/admin/file";
import JsonEditorVue from "json-editor-vue3";
import { deviceInfoListAdd, deviceInfoListEdit } from "@/api/device/device";
import { getCommunity } from "@/api/base/community";
import mitt from "@/utils/mitt";

import mapSelectPoint from "@/componts/map/mapSelectPoint.vue";
import marsMap from "@/componts/map/marsMap.vue";
export default {
  props: ["statusList", "typeList", "oriTypeList"],
  components: {
    mapSelectPoint,
    JsonEditorVue,
    marsMap,
  },
  data() {
    return {
      loading: false,
      dataModel: {},
      show: false,
      jsonVal: {},
      dialog: {},
      picture: false,
      upload_input: "",
      imgServer: import.meta.env.VITE_BASE_API,
      communityId: localStorage.getItem("communityId"),
      viewPic: false,
      CROPPER: CropperObj,
      rules: {
        devNo: [
          {
            required: true,
            message: "请输入设施标识",
            trigger: "blur",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入设施名称",
            trigger: "blur",
          },
        ],
        installType: [
          {
            required: true,
            message: "请选择设施安装类型",
            trigger: "blur",
          },
        ],
        devTypeId: [
          {
            required: true,
            message: "请选择设施类型",
            trigger: "change",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择设状态",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    changeJson(json) {
      console.info("changeJson: ", json);
      this.jsonVal = json;
    },
    deletePhoto() {
      this.picture = false;
    },
    imgUpload(files) {
      let form = new FormData();
      form.append("file", files.file);
      // form.append("modulesName", "device/" + localStorage.getItem("communityId") + "/device")
      form.append("modulesName", "device");
      form.append("functionName", "deviceInfo");
      form.append("communityId", localStorage.getItem("communityId"));
      fileUpload(form).then((res) => {
        this.picture = res.data.result.url;
        if (res.data.code == 0) {
          this.$message.success("上传成功");
        }
      });
    },
    loadingImg(eve) {
      this.upload_input = eve.target.files[0];
      this.viewPic = true;
      //读取上传文件
      let reader = new FileReader();
      if (event.target.files[0]) {
        //readAsDataURL方法可以将File对象转化为data:URL格式的字符串（base64编码）
        reader.readAsDataURL(eve.target.files[0]);
        reader.onload = (e) => {
          let dataURL = reader.result;
          //将img的src改为刚上传的文件的转换格式
          document.querySelector("#cropImg").src = dataURL;

          const image = document.getElementById("cropImg");
          //创建cropper实例-----------------------------------------
          let CROPPER = new Cropper(image, {
            // aspectRatio: 16 / 16,
            initialAspectRatio: 2 / 3,
            viewMode: 1,
            autoCropArea: 0.95,
            minCanvasWidth: 100,
            minCanvasHeight: 100,
            // minContainerWidth:500,
            // minContainerHeight:500,
            dragMode: "move",
          });
          this.CROPPER = CROPPER;
        };
      }
      this.upload_input = "";
    },
    uploadImg() {
      document.querySelector("#imgReader").click();
      if (this.CROPPER) {
        this.CROPPER.destroy();
        this.CROPPER = undefined;
      }
    },
    GetData() {
      this.viewPic = false;
      //getCroppedCanvas方法可以将裁剪区域的数据转换成canvas数据
      this.CROPPER.getCroppedCanvas({
        maxWidth: 480,
        maxHeight: 480,
        fillColor: "#fff",
        imageSmoothingEnabled: true,
        imageSmoothingQuality: "high",
      }).toBlob(
        (blob) => {
          //然后调用浏览器原生的toBlob方法将canvas数据转换成blob数据

          //之后就可以愉快的将blob数据发送至后端啦，可根据自己情况进行发送，我这里用的是axios
          const form = new FormData();
          // 第三个参数为文件名，可选填.
          form.append("file", blob, "example.jpg");
          form.append("modulesName", "device");
          form.append("functionName", "deviceInfo");
          form.append("communityId", localStorage.getItem("communityId"));
          fileUpload(form).then((res) => {
            this.picture = res.data.result.url;
            if (res.data.code == 0) {
              this.$message.success("上传成功");
            }
          });
        },
        "image/jpeg",
        0.95
      );
    },
    handleChange(e) {
      if (e == null) {
        this.dataModel.devTypeId = null;
        return;
      } else {
        this.dataModel.devTypeId = e[e.length - 1];
        let flag = false;
        for (let item of this.oriTypeList) {
          console.log(item, e);
          if (item.id == e[e.length - 1] && item.code == "video_surveillance") {
            flag = true;
          }
        }
        if (flag) {
          this.show = true;
        } else {
          this.show = false;
        }
      }
    },
    // 地图选点
    selectPoint() {
      var communityId = localStorage.getItem("communityId");
      getCommunity(communityId)
        .then((res) => {
        
          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map.mode;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          if (result.enabled3d && mode == "mars3d") {
            position = config.map.sdgis.position;

            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (result.lng && result.lat) {
            center = {
              lng: result.lng,
              lat: result.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }

          var data = {
            enabled3d: result.enabled3d,
            edit: true,
            point: [this.dataModel.lng, this.dataModel.lat, this.dataModel.alt],
            position: position,
            center: center,
            modeUrl: modeUrl,
            title: "地图选点",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);

          mitt.emit("openMarsMap", data);
        })
        .catch((err) => {});
    },
    setAddress(e) {
      this.dataModel.address = e.regeocode.formattedAddress;
    },
    point(e) {
      this.dataModel.lng = e[0];
      this.dataModel.lat = e[1];
      if (e.length == 3) {
        this.dataModel.alt = e[2];
      }
    },
    onSubmit() {
      this.$refs.formEl.validate((valid, fields) => {
        if (valid) {
          console.log("device submit!", valid);

          this.dataModel.expandParams = JSON.stringify(this.jsonVal);
          this.dataModel.type = 5;
          this.dataModel.communityId = this.communityId;
          if (this.dataModel.expandParams) {
            let obj = JSON.parse(this.dataModel.expandParams);
            obj.enableVideo = this.dataModel.enable;
            obj.picture = this.picture;
            this.dataModel.expandParams = JSON.stringify(obj);
          } else {
            this.dataModel.expandParams = JSON.stringify({
              enableVideo: this.dataModel.enable,
              picture: this.picture,
            });
          }
          if (this.dataModel.id == 0) {
            deviceInfoListAdd(this.dataModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          } else {
            deviceInfoListEdit(this.dataModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          }
        } else {
          console.log("error submit!", fields);
        }
      });
    },
  },
  mounted() {
    mitt.on("openDeviceInfoEdit", (menu) => {
      this.jsonVal = {};
      this.show = false;
      if (menu.topCode == "video_surveillance") {
        this.show = true;
      }
      menu.enable = false;
      this.picture = false;
      // 初始化扩展参数
      if (menu.expandParams) {
        let obj = JSON.parse(menu.expandParams);
        this.jsonVal = obj;
        menu.enable = obj.enableVideo;
        this.picture = obj.picture;
      }
      this.dataModel = menu;
      this.dialog.show = true;
      this.dialog.title = "修改";
    });
    mitt.on("openDeviceInfoAdd", (id) => {
      this.picture = false;
      this.jsonVal = {};
      this.dataModel = {
        id: 0,
        parentId: id,
        enable: false,
      };
      this.dialog.show = true;
      this.dialog.title = "添加";
    });
  },
  created() {

    mitt.off("openMarsMap");
  },
};
</script>

<style scoped>
.editor {
  width: 805px;
}

#cropImg {
  height: 300px;
  width: 300px;
  display: block;
  overflow: hidden;
  box-shadow: 0 0 5px #adadad;
}

.previewText {
  margin-top: 10px;
}

.box-card {
  width: 100%;
}
</style>
