<template>
  <el-dialog
    draggable
    width="60%"
    destroy-on-close
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
    :close-on-click-modal="false"
    top="5vh"
  >
    <el-scrollbar height="542px">
      <el-form
        :rules="rules"
        ref="form"
        :model="governanceModel.governanceEvent"
        label-width="100px"
        style="width: calc(100% - 10px)"
      >
        <el-row>
          <el-col :span="4">
            <el-form-item label="治理类型:" prop="governanceType">
              <div :style="{color:getDictCss(typeList, governanceModel.governanceEvent.governanceType)}">
                {{
                  formatDict(
                    typeList,
                    governanceModel.governanceEvent.governanceType
                  )
                }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="事件状态:" prop="status">
              <el-tag :type="getDictCss(statusList, governanceModel.governanceEvent.status)">
                {{
                  formatDict(
                    statusList,
                    governanceModel.governanceEvent.status
                  )
                }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发生时间:" prop="eventTime">
              {{ governanceModel.governanceEvent.eventTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="事件等级:" prop="eventLevelValue">
              <div :style="{background:getDictCss(levelList, governanceModel.governanceEvent.eventLevelValue)}" style="width:50px;text-align:center;">
                {{
                  formatDict(
                    levelList,
                    governanceModel.governanceEvent.eventLevelValue
                  )
                }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="事件位置:" prop="eventLocation">
              {{ governanceModel.governanceEvent.eventLocation }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="事件说明:" prop="eventNote">
              {{ governanceModel.governanceEvent.eventNote }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="事件对象:" prop="governanceObjectList">
              <el-table
                stripe
                :data="governanceModel.governanceEvent.governanceObjectList"
                border
                style="width: 100%"
              >
                <el-table-column
                  prop="communityName"
                  show-overflow-tooltip
                  align="center"
                  label="小区"
                />
                <!-- <el-table-column
                  prop="buildingNumber"
                  show-overflow-tooltip
                  align="center"
                  label="楼栋"
                />
                <el-table-column
                  prop="unitNumber"
                  show-overflow-tooltip
                  align="center"
                  label="单元"
                />
                <el-table-column
                  prop="roomNumber"
                  show-overflow-tooltip
                  align="center"
                  label="房间"
                /> -->
                <el-table-column
                  prop="address"
                  show-overflow-tooltip
                  align="center"
                  label="地址"
                />
                <el-table-column
                  prop="name"
                  show-overflow-tooltip
                  align="center"
                  width="150"
                  label="姓名"
                />
                <el-table-column
                  prop="idCard"
                  show-overflow-tooltip
                  align="center"
                  label="证件号"
                />
                <el-table-column
                  prop="phone"
                  show-overflow-tooltip
                  align="center"
                  label="电话"
                />
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="经办人员:" prop="governanceResponsibilityList">
              <el-table
                stripe
                :data="governanceModel.governanceEvent.governanceResponsibilityList"
                border
                style="width: 100%"
              >
                <el-table-column
                  prop="name"
                  show-overflow-tooltip
                  align="center"
                  label="姓名"
                >
                
              <template #default="scope">
                <div style="flex:1;border-radius:5px;padding:0 15px;display:flex;justify-content:center;">
                  <el-tooltip placement="top" :key="index">
                    <template #content>
                      <div style="display:flex;flex-direction:column">
                        <span v-if="scope.row.phone">电 话：{{scope.row.phone}}</span>
                        <span v-if="scope.row.dutyNote">职 责：{{scope.row.dutyNote}}</span>
                        <span v-if="scope.row.evaluation">评 语：{{scope.row.evaluation}}</span>
                      </div>
                    </template>
                    <div style="position: relative;margin-right:5px;">
                      <div v-if="scope.row.evaluation" style="width:5px;height:5px;background:red;position: absolute;border-radius:5px;top: 5px;right: 5px;"></div>
                      <el-button @click="editEvaluationFunc(scope.row)" v-if="hasPerm('base:governanceEvent:comment')">{{scope.row.name}}</el-button>
                      <el-button  v-else>{{scope.row.name}}</el-button>
                    </div>
                  </el-tooltip>
                </div>
              </template>
              </el-table-column>
                <el-table-column
                  prop="certificateType"
                  align="center"
                  :formatter="formatCertificateType"
                  label="证件类型"
                />
                <el-table-column
                  prop="idCard"
                  show-overflow-tooltip
                  align="center"
                  label="证件号"
                />
                <el-table-column
                  prop="phone"
                  show-overflow-tooltip
                  align="center"
                  label="电话"
                />
                <el-table-column
                  prop="dutyNote"
                  show-overflow-tooltip
                  align="center"
                  label="职责描述"
                />
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="fileList.length>0">
          <el-col :span="24">
            <el-form-item label="相关图片:">
              <el-scrollbar
                class="pic--video"
                style="height: 198px; width: 100%"
              >
                <div style="display: flex">
                  <div
                    v-for="(item, idx) in fileList"
                    :key="idx"
                    style="
                      width: 146px;
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                      margin-right: 10px;
                    "
                  >
                    <div
                      class="mask"
                      style="
                        height: 146px;
                        width: 146px;
                        display: flex;
                        align-items: center;
                        justify-content:center;
                        border: 1px dashed #ccc;
                        border-radius: 5px;
                      "
                    >
                      <el-image
                      style="max-height:146px;max-width:146px"
                        :initial-index="idx"
                        :preview-src-list="previewList"
                        :src="item.url"
                      ></el-image>
                    </div>
                    <div style="width: 100%; overflow: hidden; height: 32px">
                      {{ item.name }}
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="fileList1.length>0">
          <el-col :span="24">
            <el-form-item label="相关视频:">
              <el-scrollbar
                class="pic--video"
                style="height: 198px; width: 100%"
              >
                <div style="display: flex; position: relative; top: -10px">
                  <div
                    v-for="(item, idx) in fileList1"
                    :key="idx"
                    style="
                      width: 146px;
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                      margin-right: 10px;
                    "
                  >
                    <div
                      class="mask"
                      style="
                        height: 146px;
                        display: flex;
                        align-items: center;
                        border-radius: 5px;
                        cursor: pointer;
                      "
                    >
                      <el-image
                        :src="item.url"
                        @click="videoView(item)"
                      ></el-image>
                    </div>
                    <div style="width: 100%; overflow: hidden; height: 32px">
                      {{ item.name }}
                    </div>
                  </div>
                </div>
              </el-scrollbar>
              <el-dialog
                v-model="dialogVisibleVideo"
                :title="dialogVisibleVideoText"
                class="picView"
              >
                <video
                  :src="videoUrl"
                  style="width: 100%; max-height: 520px"
                  controls
                ></video>
              </el-dialog>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="governanceModel.governanceEvent.otherList.length>0">
          <el-col :span="24">
            <el-form-item label="相关文件:">
              <el-scrollbar style="height: 198px">
                <ul>
                  <li
                    v-for="item in governanceModel.governanceEvent.otherList"
                    :key="item"
                    @click="handleOtherCardPreview(item)"
                  >
                    <el-icon><Document /></el-icon> {{ item.name }}
                  </li>
                </ul>
              </el-scrollbar>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
<el-row style = "margin-top:10px;width:100%" v-if="dataList.length>0">
  <!-- <el-scrollbar style="height: 500px;width:99%"> -->
  <div style="margin:0 20px;"><span style="height:15px;width:5px;background:#4b96ff;display:inline-block;margin-right:5px"></span>记录 </div>
  <el-col style="margin-top:10px;height:calc(100% );overflow:auto">
      <div v-for="(activity, index) in dataList" style="padding-left:30px;" :key="index">
        <div style="line-height:32px;border-left:2px solid #e0e4ea;padding:0 15px">
          <div style="height:10px;width:10px;background: #568efe;border-radius:5px;position:relative;right:21px;top:21px">
            <div style="height:12px;width:12px;border:1px solid #568efe;border-radius:6px;position:relative;left:-2px;top:-2px"></div>
          </div>
          <div style="display:flex;justify-content:space-between;align-items:center; line-height:32px;background:#e6edff;padding:0 15px">
            <div>{{activity.governanceTime}}</div>
          </div>
          <div style="background:#f5f5f5;padding:15px 15px 10px">
            <div style="display:flex;">
              <div style="width:70px">经办人员:</div>
              
              <div style="flex:1;background:#fff;border-radius:5px;padding:0 15px;display:flex">
                <el-tooltip placement="top" v-for="(item, index) in activity.governanceResponsibilityVoList" :key="index">
                  <template #content>
                    <div style="display:flex;flex-direction:column">
                      <span v-if="item.phone">电 话：{{item.phone}}</span>
                      <span v-if="item.dutyNote">职 责：{{item.dutyNote}}</span>
                      <span v-if="item.evaluation">评 语：{{item.evaluation}}</span>
                    </div>
                  </template>
                  <div style="position: relative;margin-right:5px;">
                    <div v-if="item.evaluation" style="width:5px;height:5px;background:red;position: absolute;border-radius:5px;top: 5px;right: 5px;"></div>
                    <el-button @click="editEvaluationFunc(item)" v-if="hasPerm('base:governanceEvent:comment')">{{item.name}}</el-button>
                    <el-button  v-else>{{item.name}}</el-button>
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
          <div style="background:#f5f5f5;padding:0 15px 10px">
            <div style="display:flex;">
              <div style="width:70px">相关描述:</div>
              <div style="flex:1;background:#fff;border-radius:5px;padding:0 15px">{{activity.note}}</div>
            </div>
          </div>
          <div style="display:flex;max-height:300px">
            <div style="background:#f5f5f5;padding:0 15px 10px;width:60%">
              <div style="display:flex;" v-if="JSON.parse(activity.extraData).photoList.length">
                <div style="width:70px">相关图片:</div>
                <!-- <div style="flex:1;background:#fff;border-radius:5px;padding:0 15px">{{JSON.parse(activity.extraData).photoList.url}}</div> -->
                <div style="display:flex;flex-wrap:wrap;overflow:auto; width:100%;max-height:300px;padding-left:15px">
                  <div class="img-box" v-for="(item, index) in JSON.parse(activity.extraData).photoList" :key="index" style="width:32%;">
                    <el-image :preview-src-list="formatPreviewList(JSON.parse(activity.extraData).photoList)" :initial-index="index" :src="imgServer + item.url" style="height:120px;"></el-image>
                    <div style="position:relative ;bottom:13px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{item.name}}</div>
                  </div>
                </div>
              </div>
            </div>
            <div style="width:40%;max-height:300px">
              <div style="background:#f5f5f5;padding-right:15px;height:100%;display:flex;flex-direction:column;">
                <div style="padding-bottom:5px;" v-if="JSON.parse(activity.extraData).videoList.length">
                  <div style="width:70px">相关视频:</div>
                  <div style="flex:1;background:#fff;border-radius:5px;max-height:118px;min-height:50px;overflow:auto;line-height:28px;padding-left:10px;">
                    <div v-for="(item, index) in JSON.parse(activity.extraData).videoList" @click="openVideo(item)" style="width:75%;cursor:pointer" :key="index">
                      <el-icon size="18" style="position:relative;top:4px"><VideoCamera /></el-icon>{{item.name}}
                    </div>
                  </div>
                </div>
                <div style="padding-bottom:5px;" v-if="JSON.parse(activity.extraData).otherList.length">
                  <div style="width:70px">相关文件:</div>
                  <div style="flex:1;background:#fff;border-radius:5px;max-height:118px;min-height:50px;overflow:auto;line-height:28px;padding-left:10px">
                    <div v-for="(item, index) in JSON.parse(activity.extraData).otherList" @click="openFile(item)" style="width:75%;cursor:pointer" :key="index">
                      <el-icon size="18" style="position:relative;top:4px"><Document /></el-icon>{{item.name}}
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
  </el-col>
<!-- </el-scrollbar> -->
</el-row>
    </el-scrollbar>
    <el-dialog
      draggable
      class="picView"
      top="5vh"
      width="1000px"
      v-loading="loading"
      v-model="videoShow"
      title="查看视频">
      <div class="view_contoiner">
          <video
            :src="videoUrl"
            style="max-width:100%;height:100%"
            controls>
          </video>
      </div>
  </el-dialog>
    <el-dialog width="30%" draggable v-model="evaluationShow" title="领导点评">
      <el-row style="padding:0">
          <el-col :span="24">
              <el-input
              v-model="evaluationModel.evaluation"
              maxlength="200"
              rows="5"
              placeholder="请简单描述职责"
              show-word-limit
              type="textarea"
            />
          </el-col>
        </el-row>
        <el-row style="padding:0" justify="center">
          <el-button
            type="primary"
            style="width: 100px; height: 30px; margin-top: 20px"
            @click="submit"
            >提 交</el-button
          >
        </el-row>
    </el-dialog>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="close"
        >关 闭</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>

<script>
import {governanceEventNotRequireMask } from "@/api/governance/governanceEvent"
import {governanceLogList } from "@/api/governance/governanceLog"
import { listGovernanceObject } from "@/api/governance/governanceObject";
import { listGovernanceResponsibility, editEvaluation } from "@/api/governance/governanceResponsibility";
import mitt from "@/utils/mitt";
import { listDictByNameEn } from "@/api/admin/dict";
import { getDictCss, formatDict } from "@/utils/dict";

export default {
  props: ["statusList", "typeList", "levelList"],
  data() {
    return {
      imgServer: import.meta.env.VITE_BASE_API,
      searchObjectModel: {
        governanceEventId: localStorage.getItem("id"),
      },
      searchResponsibilityModel: {
        governanceEventId: localStorage.getItem("id"),
      },
      governanceModel: {},
      evaluationModel: {},
      governanceEvent: {},
      certificateTypeList: [],
      dialog: {},
      evaluationShow: false,
      fileList: [],
      fileList1: [],
      fileList2: [],
      dataList: [],
      dialogVisibleVideo: false,
      dialogVisibleVideoText: "",
      videoUrl: "",
      governanceObjectList: [],
      governanceResponsibilityList: [],
      viewStatus: true,
      videoShow: false,
      previewList: []
    };
  },
  methods: {
    formatPreviewList(data){
      const list = []
      if (data.length) {
        for (const key in data) {
          list.push(this.imgServer+''+data[key].url)
        }
      }
      return list
    },
    formatName(row){
      console.log(row.governanceResponsibilityVoList);
      var list = ""
      for (const item of row.governanceResponsibilityVoList) {
        console.log(item.name);
        list+=item.name+"、"
      }
      console.log(list);
      return list
    },
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    // 相关文件
    handleOtherCardPreview(uploadFile) {
      var uploadFile1 = {};
      this.governanceModel.governanceEvent.otherList.some((item) => {
        if (item.name == uploadFile.name) {
          uploadFile1 = item;
        }
      });
      window.open(this.imgServer + uploadFile1.url);
    },
    // 添加评语
    editEvaluationFunc(row){
      console.log(row);
      this.evaluationShow = true
      this.evaluationModel.evaluation = row.evaluation
      this.evaluationModel.id = row.id
    },
    openVideo(row){
      this.videoShow = true
      this.videoUrl = this.imgServer + row.url
    },
    openFile(row){
      window.open(this.imgServer + row.url)
    },
    videoView(uploadFile) {
      this.dialogVisibleVideoText =
        "相关视频--" + uploadFile.name.split(".")[0];
      this.videoUrl = [];
      this.videoUrl = uploadFile.videoUrl;
      this.dialogVisibleVideo = true;
    },
    submit(){
      this.evaluationModel.evaluation = this.evaluationModel.evaluation.trim()

      editEvaluation(this.evaluationModel).then(res => {
        this.$message.success(res.data.msg);
        this.evaluationShow = false
        governanceLogList({governanceEventId: this.searchObjectModel.governanceEventId}).then(res => {
         this.dataList =  res.data.result
        })
        governanceEventNotRequireMask(this.searchObjectModel.governanceEventId).then(res => {
          this.governanceModel.governanceEvent = res.data.result
          console.log(res.data.result);
        })
        // listGovernanceResponsibility(
        //   this.searchResponsibilityModel
        // ).then(res => {
        //   this.governanceModel.governanceResponsibilityList = res.data.result.list;
        // })
      })
    },
    getVideoCover(file) {
      const video = document.createElement("video"); // 也可以自己创建video
      video.src = file.url; // url地址 url跟 视频流是一样的

      var canvas = document.createElement("canvas"); // 获取 canvas 对象
      const ctx = canvas.getContext("2d"); // 绘制2d
      video.crossOrigin = "anonymous"; // 解决跨域问题，也就是提示污染资源无法转换视频
      video.currentTime = 1; // 第一帧

      video.oncanplay = () => {
        canvas.width = video.clientWidth ? video.clientWidth : 320; // 获取视频宽度
        canvas.height = video.clientHeight ? video.clientHeight : 240; //获取视频高度
        // 利用canvas对象方法绘图
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        // 转换成base64形式
        let videoFirstimgsrc = canvas.toDataURL("image/png"); // 截取后的视频封面
        let videoUrl = file.url;
        file.url = videoFirstimgsrc; // file的url储存封面图片
        file.videoUrl = videoUrl; // file的videoUrl储存视频

        video.remove();
        canvas.remove();
      };
      return file;
    },
    setFileList(_fileList) {
      for (let obj of _fileList) {
        //视频附件，获取第一帧画面作为 封面展示
        this.getVideoCover(obj);
      }
      this.fileList1 = _fileList; //fileList 为 Element file-list 参数值
    },
    close() {
      this.dialog.show = false;
    },
    // 证件类型格式化
    formatCertificateType(row, column, cellValue, index) {
      let result = "";
      for (let item of this.certificateTypeList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
        }
      }
      return result;
    },
    async init() {
      try {
        let certificate_res = await listDictByNameEn("certificate_type");
        this.certificateTypeList = certificate_res.data.result;
      } catch (err) {}
    },
  },

  mounted() {
    this.jsonVal = {};
    this.$nextTick(function () {
      mitt.on("openGovernanceView", (governanceEvent) => {
        this.searchObjectModel.governanceEventId = governanceEvent.id;
        this.searchResponsibilityModel.governanceEventId = governanceEvent.id;
        this.init();
        this.governanceModel.governanceEvent = governanceEvent;
        this.dialog.show = true;
        governanceLogList({governanceEventId: governanceEvent.id}).then(res => {
         this.dataList =  res.data.result
        })
        this.governanceModel.governanceEvent.videoList = [];
        this.governanceModel.governanceEvent.photoList = [];
        this.governanceModel.governanceEvent.otherList = [];
        this.dialog.title = "查看事件详情";

        let photoList = JSON.parse(
          this.governanceModel.governanceEvent.extraData
        ).photoList;
        let videoList = JSON.parse(
          this.governanceModel.governanceEvent.extraData
        ).videoList;
        let otherList = JSON.parse(
          this.governanceModel.governanceEvent.extraData
        ).otherList;
        //图片
        if (!photoList) {
          photoList = [];
        } else {
          this.fileList = [];
          this.previewList = [];
          for (const item of photoList) {
            if (item.url.includes(this.imgServer)) {
              this.fileList.push({ name: item.name, url: item.url });
              this.previewList.push(this.imgServer + "" + item.url)
            } else {
              this.fileList.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
              this.previewList.push(this.imgServer + "" + item.url)
            }
          }
        }

        //视频
        if (!videoList) {
          videoList = [];
        } else {
          this.fileList1 = [];
          for (const item of videoList) {
            if (item.url.includes(this.imgServer)) {
              this.fileList1.push({ name: item.name, url: item.url });
            } else {
              this.fileList1.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
            }
          }
        }

        //文件
        if (!otherList) {
          otherList = [];
        } else {
          this.fileList2 = [];
          for (const item of otherList) {
            if (item.url.includes(this.imgServer)) {
              this.fileList2.push({ name: item.name, url: item.url });
            } else {
              this.fileList2.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
            }
          }
        }

        this.governanceModel.governanceEvent.videoList = videoList;
        this.governanceModel.governanceEvent.photoList = photoList;
        this.governanceModel.governanceEvent.otherList = otherList;
        this.setFileList(this.fileList1);
      });
    });
  },
  created() {
    mitt.off("openGovernanceView");
  },
};
</script>

<style scoped lang="less">
// div /deep/.picView .el-dialog__header {
//   background-color: #fff;
//   box-shadow: none;
// }
// div /deep/ .picView .el-dialog__close {
//   color: #ccc;
// }

.pic--video div /deep/ .el-upload-list {
  display: flex;
  width: 100%;
  flex-wrap: nowrap;
  > li,
  div {
    display: flex;
    flex-shrink: 0;
  }
}
ul{
  li{
    list-style: none;
    cursor: pointer;
  }
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.img-box:not(:nth-child(3n)){
  margin-right: calc(6% / 3);
}
// .mask{
//   position: relative;
//   display: inline-block;
//   image{
//     display:block;
//   }
// }
// .mask:hover {
//   // content: '';
//   // position: absolute;
//   // top: 0;
//   // left: 0;
//   // right: 0;
//   // bottom: 0;
//   background: rgba(0, 0, 0, 0.5); /* 黑色半透明蒙版 */
// }
.view_contoiner{
  height:570px;
  width: 100%;
  display:flex;
  justify-content:center;
}
</style>
