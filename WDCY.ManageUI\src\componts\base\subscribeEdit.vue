<template>
	<el-dialog draggable width="40%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="healthExaminaModel" label-width="100px">
            <el-row>
                <el-col :span="24">
                <!-- <el-form-item label="" prop="expandParams"> -->
                    <JsonEditorVue
                    language="cn"
                    class="editor"
                    :modelValue="jsonVal"
                    @update:modelValue="changeJson"
                    />
                <!-- </el-form-item> -->
                </el-col>
            </el-row>
		</el-form>
		<!-- <el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="submit">生 成</el-button>
		</el-row> -->
	</el-dialog>
</template>

<script>
import { getProductionAssert } from "@/api/healthCheck/healthExamination"
import mitt from "@/utils/mitt";
import JsonEditorVue from "json-editor-vue3";
export default {
    components: { JsonEditorVue },
	data() {
		return {
			loading: false,
			healthExaminaModel: {},
			dialog: {},
      jsonVal: {},
      jsonValed: {},
			personId: "",
			startToEndTime: [],
			imgServer: import.meta.env.VITE_BASE_API,
		}
	},
	methods: {
        changeJson(json) {
        	this.jsonVal = json;
        },
        submit(){
            this.jsonVal.funcId = this.healthExaminaModel.funcId
            getProductionAssert(this.jsonVal).then(res => {
                this.jsonValed = JSON.parse(JSON.stringify(res.data));
                console.log(res.data);
            }).catch(()=>{
                this.$message.error('生成失败')
            })
        },
        init(){
        }
	},
	mounted() {
      this.jsonVal = {};
		  this.$nextTick(function () {
			mitt.on('openSubscribeEdit', (res) => {
        this.jsonVal = JSON.parse(JSON.stringify(res))
				this.dialog.show = true
				this.dialog.title = "查看订阅"
			})
		})
	},
    created(){
        this.init()
    }
}
</script>
<style scoped>

.editor {
  width: 100%;
  height: 500px;
}
.avatar-uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}
</style>