<template>
  <person-edit
    :sexList="sexList"
    :statusList="statusList"
    :tagList="tagList"
    :certificateTypeTagList="certificateTypeTagList"
    @search="search"
  ></person-edit>
  <person-belong-detail
    :sexList="sexList"
    @search="search"
  ></person-belong-detail>
  <open-id-edit @search="search"></open-id-edit>
  <person-view
    :sexList="sexList"
    @search="search"
    :tagList="tagList"
  ></person-view>
  <input type="file" id="btn_file" @change="filechange" style="display: none" />
  <input
    multiple
    type="file"
    id="btn_pic_file"
    @change="loadingImg"
    style="display: none"
  />

  <el-row :gutter="20">
    <el-col :span="4">
      <!--  :options="buildingNodeList"
				@change="handleChange"  -->
      <el-cascader
        style="width: 100%"
        @change="handleChange"
        :props="{
          expandTrigger: 'hover',
          checkStrictly: true,
          lazy: true,
          lazyLoad: this.loadTreeNode,
        }"
        filterable
        clearable
        placeholder="选择楼房"
      />
    </el-col>
    <el-col :span="3">
      <el-input
        v-model="searchModel.idCard"
        @keydown.enter="search"
        placeholder="证件号码"
        clearable
      />
    </el-col>
    <el-col :span="2">
      <el-input
        v-model="searchModel.name"
        @keydown.enter="search"
        placeholder="姓名"
        clearable
      />
    </el-col>
    <el-col :span="2">
      <el-input
        v-model="searchModel.phone"
        @keydown.enter="search"
        placeholder="手机号"
        clearable
      />
    </el-col>
    <el-col :span="2">
      <el-select
        style="width: 100%"
        v-model="searchModel.status"
        placeholder="人员状态"
        clearable
      >
        <el-option
          v-for="item in statusList"
          :key="item.nameEn"
          :label="item.nameCn"
          :value="parseInt(item.nameEn)"
        ></el-option>
      </el-select>
    </el-col>
    <el-col :span="2">
      <el-select
        style="width: 100%"
        v-model="searchModel.houseStatus"
        placeholder="房屋状态"
        clearable
      >
        <el-option
          v-for="item in statusList"
          :key="item.nameEn"
          :label="item.nameCn"
          :value="parseInt(item.nameEn)"
        ></el-option>
      </el-select>
    </el-col>
    <el-col :span="2">
      <el-select
        style="width: 100%"
        v-model="searchModel.tags"
        placeholder="标签"
        clearable
      >
        <el-option
          v-for="item in tagList"
          :key="item.nameEn"
          :label="item.nameCn"
          :value="item.nameEn"
        ></el-option>
      </el-select>
    </el-col>
    <el-col :span="4">
      <el-button type="primary" @click="search">搜 索</el-button>
      <el-button type="primary" @click="refresh">刷 新</el-button>
      <el-button type="primary" @click="importPic">导入图片</el-button>
    </el-col>
    <el-col :span="3">
      <el-button
        style="float: right"
        type="primary"
        @click="add"
        v-if="hasPerm('base:person:add')"
        >添 加</el-button
      >
      <!-- <el-button style="float: right; margin-right: 20px;" type="primary" @click="importExcel">导 入</el-button> -->
      <el-dropdown
        style="float: right; margin-right: 20px"
        split-button
        type="primary"
        @click="importExcel"
      >
        导 入
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              ><a
                :href="
                  imgServer +
                  '/assets/import/小区基础信息-楼栋房间人员导入模板.xlsx'
                "
                >导入模板下载</a
              >
            </el-dropdown-item>
            <!-- <el-dropdown-item command="b">Action 2</el-dropdown-item> -->
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-col>
  </el-row>

  <el-row :gutter="20">
    <el-col :span="24">
      <el-table
        stripe
        :data="personList"
        border
        height="calc(100vh - 300px)"
        style="width: 100%"
      >
        <el-table-column prop="photo" width="80px" align="center" label="照片">
          <template #default="scope">
            <el-image
              preview-teleported
              fit="contain"
              style="width: 50px; height: 50px"
              :src="imgServer + scope.row.photo"
              :preview-src-list="[imgServer + scope.row.photo]"
            >
              <template #error>
                <el-image
                  preview-teleported
                  fit="contain"
                  style="width: 50px; height: 50px"
                  :src="errorHeadImg"
                  :preview-src-list="[errorHeadImg]"
                >
                </el-image>
              </template>
            </el-image>
          </template>
        </el-table-column>

        <el-table-column
          prop="name"
          width="125px"
          align="center"
          label="姓名"
        />
        <el-table-column prop="age" width="54px" align="center" label="年龄" />
        <el-table-column
          prop="sex"
          width="54px"
          align="center"
          :formatter="formatSex"
          label="性别"
        />
        <el-table-column prop="nativePlace" align="center" label="籍贯" />
        <!-- <el-table-column prop="status" width="90px" align="center" :formatter="formatStatus" label="状态" /> -->
        <el-table-column prop="status" align="center" label="状态" width="95">
          <template #default="scope">
            <el-tag :type="getDictCss(statusList, scope.row.status)">{{
              formatDict(statusList, scope.row.status)
            }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="address"
          show-overflow-tooltip
          align="center"
          label="住址"
        />
        <el-table-column prop="tags" align="center" label="标签">
          <template #default="scope">
            <span
              v-for="item in getTags(scope.row)"
              :key="item"
              class="tag-item"
              :style="'background-color:' + item.cssClass"
            >
              {{ item.nameCn }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="createTime"
          align="center"
          width="165"
          label="创建时间"
        />
        <el-table-column align="center" width="245" label="操作">
          <template #default="scope">
            <el-button
              type="text"
              size="default"
              @click="findMe(scope.row.id)"
              v-if="hasPerm('base:person:idCard:query')"
              >查看本户</el-button
            >
            <el-button type="text" size="default" @click="view(scope.row.id)"
              >查看</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="viewLog(scope.row.name)"
              >查看日志</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="myRoom(scope.row.id)"
              v-if="hasPerm('base:person:belong:query')"
              >房产</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="editCardNum(scope.row.id)"
              v-if="hasPerm('base:person:update-card:edit')"
              >换卡</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="openId(scope.row)"
              v-if="hasPerm('base:person:update-openid:edit')"
              >OpenId</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="edit(scope.row.id)"
              v-if="hasPerm('base:person:edit')"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="deleted(scope.row.id)"
              v-if="hasPerm('base:person:delete')"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination
        background
        v-model:page-size="searchModel.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :total="Number(total)"
      >
      </el-pagination>
      <!--  @prev-click="prevClick"
				@next-click="nextClick"  -->
    </el-col>

    <el-dialog
      draggable
      v-model="errMsgStatus"
      :title="communityName"
      custom-class="diaLogClass"
    >
      <div class="importBox">
        <span class="importText"
          >已成功导入<strong style="color: #4b96ff">{{ successNum }}</strong
          >条</span
        >
        <span class="importText"
          >导入失败<strong style="color: red">{{ failNum }}</strong
          >条</span
        >
      </div>
      <div class="errMsg">
        <div class="err_msg_title">
          <span class="blue_icon"></span>
          <span style="margin-left: 8px; display: inline-block"
            >导入失败明细</span
          >
        </div>
        <div class="errMsgMain" v-if="failNum">
          <el-tooltip v-for="(item, index) in errMsgList" :key="index">
            <template #content>
              <div
                v-for="(item, index) in popUpMsg"
                :key="index"
                style="line-height: 26px"
              >
                {{ item }}
              </div>
            </template>
            <div @mouseover="hover(item)" class="everyErrMsg">
              {{ item }}
            </div>
          </el-tooltip>
        </div>
        <img v-else class="nothing" src="../../assets/img/nothing.png" alt="" />
      </div>
      <div class="button_box">
        <el-button @click="copyToClipboard($event)">复 制</el-button>
        <el-button @click="close">确 定</el-button>
      </div>
    </el-dialog>
  </el-row>
</template>

<script>
import { ElLoading } from "element-plus";
import { listVehicle } from "@/api/base/vehicle";
import {
  listPerson,
  deletePerson,
  getPerson,
  getIdCard,
  personExamine,
  importPersonImg,
} from "@/api/base/person";
import { getPersonBelong } from "@/api/base/personBelong";
import { queryChildrenNodeBuilding, importData } from "@/api/base/building";
import { listPersonBelong } from "@/api/base/personBelong";
import { listDictByNameEn } from "@/api/admin/dict";
import { accountCommunity } from "@/api/base/community";
import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict";
import personView from "@/componts/base/personView.vue";
import personEdit from "@/componts/base/personEdit.vue";
import openIdEdit from "@/componts/base/openIdEdit.vue";
import personBelongDetail from "@/componts/base/personBelongDetail.vue";
import errImg from "../../assets/img/defaultHead.png";
import router from "../../router/router";
import { listBuilding, getUnitList, getRoomList } from "@/api/base/building";

export default {
  components: { personEdit, personView, personBelongDetail, openIdEdit },
  data() {
    return {
      searchModel: {
        communityId: localStorage.getItem("communityId"),
      },
      buildingNodeList: [],
      personList: [],
      statusList: [],
      tagList: [],
      certificateTypeTagList: [],
      sexList: [],
      total: 0,
      errMsgList: [],
      errMsgStatus: false,
      popUpMsg: [],
      successNum: "",
      failNum: "",
      communityName: "",
      imgServer: import.meta.env.VITE_BASE_API,
      pageSize: 10,
      errorHeadImg: errImg,
      fullLoading: null,
      buildingList: [],
      ret1: {},
    };
  },
  methods: {
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    formatSex(row, column, cellValue, index) {
      return formatDict(this.sexList, cellValue);
    },
    // 人员导入
    filechange() {
      this.fullLoading = ElLoading.service({
        fullscreen: true,
        background: "rgba(0, 0, 0, 0.7)",
        text: "正在执行导入，稍候将返回结果，请不要刷新！！！",
      });
      var file = document.getElementById("btn_file").files[0];
      var formdata = new FormData();
      formdata.append("file", file);
      formdata.append("communityId", localStorage.getItem("communityId"));
      formdata.append(
        "modulesName",
        "person/" + localStorage.getItem("communityId")
      );
      importData(formdata).then((res) => {
        this.fullLoading.close();
        if (res.data.result) {
          this.errMsgStatus = true;
          this.errMsgList = res.data.result.errList;
          this.successNum = res.data.result.success;
          this.failNum = res.data.result.fail;
          this.search();
        }
        // mitt.emit('openPersonAdd')
      });
    },
    close() {
      this.errMsgStatus = false;
    },
    // 导入成功弹窗里hover
    hover(item) {
      if (item) {
        if (item.includes("；")) {
          item = item.substring(item.indexOf(" ") + 1);
          this.popUpMsg = item.split("；");
        } else {
          this.popUpMsg = [item];
        }
      }
    },
    copyToClipboard() {
      // 复制
      if (!this.failNum) {
        return;
      }
      // navigator clipboard 需要https等安全上下文
      const that = this;
      if (navigator.clipboard && window.isSecureContext) {
        // navigator clipboard 向剪贴板写文本
        that.$message.success("复制成功!");
        return navigator.clipboard.writeText(this.errMsgList.join("\r\n"));
      } else {
        // 创建text area
        let textArea = document.createElement("textarea");
        textArea.value = this.errMsgList.join("\r\n");
        // 使text area不在viewport，同时设置不可见
        textArea.style.position = "absolute";
        textArea.style.opacity = 0;
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        that.$message.success("复制成功!");
        return new Promise((res, rej) => {
          // 执行复制命令并移除文本框
          document.execCommand("copy") ? res() : rej();
          textArea.remove();
        });
      }
    },
    // 导入图片
    importPic() {
      document.getElementById("btn_pic_file").value = null;
      document.getElementById("btn_pic_file").click();
    },

    loadingImg(e) {
      this.fullLoading = ElLoading.service({
        fullscreen: true,
        background: "rgba(0, 0, 0, 0.7)",
        text: "正在执行导入，稍候将返回结果，请不要刷新！！！",
      });
      let file = e.target.files;
      var formdata = new FormData();
      for (let i = 0; i < file.length; i++) {
        formdata.append("images", file[i]);
      }
      formdata.append("communityId", localStorage.getItem("communityId"));
      // var file = document.getElementById("btn_file").files[0]
      importPersonImg(formdata)
        .then((res) => {
          this.fullLoading.close();
          if (res.data.result) {
            this.errMsgStatus = true;
            this.errMsgList = res.data.result.errList;
            this.successNum = res.data.result.success;
            this.failNum = res.data.result.fail;
            this.search();
          }
          // mitt.emit('openPersonAdd')
        })
        .catch(() => {
          this.fullLoading.close();
        });
    },
    // 上传前的钩子
    beforeUploadImg(file) {
      const type = file.name.substring(file.name.lastIndexOf(".")); // 获取文件后缀，取文件格式
      const name = file.name.split(".")[0];
      console.log(name, "name``````");
      const isLt10M = file.size / 1024 / 1024 < 25;
      if (!this.uploadImgType.includes(type)) {
        this.$message({
          type: "error",
          message: "只支持jpg,jpeg,png,gif,JPG,JPEG,PNG,GIF文件格式！",
        });
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: "上传文件大小不能超过 25MB!",
          type: "warning",
        });
        return false;
      }
    },
    importExcel() {
      document.getElementById("btn_file").value = null;
      document.getElementById("btn_file").click();
    },
    handleCommand(command) {
      this.$message.info(`click on item ${command}`);
    },

    //加载树节点  首次加载页面时就会执行一次，之后每选中一个节点也会调用，来渲染下一层
    async loadTreeNode(node, resolve) {
      console.log(node);
      // 首次加载时 node为{root:true,level:0}
      // node 节点数据  获取node的level字段的值
      const { level } = node;
      //下一层节点
      const nodes = [];
      //如果有子节点 或者 为根节点（即首次进入level为0）
      //也有人写成 node.level == 0 作用是一样的
      // if (node.hasChildren || node.root) {
      if (node.level < 3) {
        // 0 代表第一次请求
        let nodeId = level == 0 ? null : node.value;
        //这里setTimeout的目的是 显示加载动画
        // setTimeout(() => {
        //调用后端接口 获得返回数据
        console.log(nodeId);
        if (nodeId == null) {
          let result = await listBuilding({
            communityId: localStorage.getItem("communityId"),
            pageSize: 999999,
          });
          this.ret1 = JSON.parse(
            JSON.stringify(result.data.result.list)
              .replaceAll("buildingNumber", "label")
              .replaceAll("id", "value")
          );
        } else if (level == 1) {
          console.log(node.value);
          let result = await getUnitList({
            buildingId: node.value,
            communityId: localStorage.getItem("communityId"),
            pageSize: 99999999,
          });
          this.ret1 = JSON.parse(
            JSON.stringify(result.data.result.list)
              .replaceAll("unitNumber", "label")
              .replaceAll("id", "value")
          );
        } else if (level == 2) {
          let result = await getRoomList({
            unitId: node.value,
            communityId: localStorage.getItem("communityId"),
            pageSize: 99999999,
          });
          this.ret1 = JSON.parse(
            JSON.stringify(result.data.result.list)
              .replaceAll("roomNumber", "label")
              .replaceAll("id", "value")
          );
        }
        // queryChildrenNodeBuilding
        if (this.ret1) {
          //ret.reulst为后端返回的数据
          console.log(this.ret1, 12312312312);
          let nodes = this.ret1;
          // 回调渲染下一层
          resolve(nodes);
        } else {
          //后端报错 弹窗提示失败
          this.$message({
            type: "error",
            message: "部门层级加载失败，请联系管理员！",
          });
        }
        // }, 1);
      } else {
        //如果没有子节点就不发起请求，直接渲染，也避免了点击叶子节点仍然有加载动画的问题
        resolve(nodes);
      }
    },
    async loadTreeNode1(node, resolve) {
      console.log(node);
      // 首次加载时 node为{root:true,level:0}
      // node 节点数据  获取node的level字段的值
      const { level } = node;
      //下一层节点
      const nodes = [];
      //如果有子节点 或者 为根节点（即首次进入level为0）
      //也有人写成 node.level == 0 作用是一样的
      if (node.level < 2) {
        // 0 代表第一次请求
        let nodeId =
          level == 0
            ? { communityId: localStorage.getItem("communityId") }
            : node.value;
        //这里setTimeout的目的是 显示加载动画
        // setTimeout(() => {
        //调用后端接口 获得返回数据
        let ret = await queryChildrenNodeBuilding(nodeId);
        console.log(ret.data);
        if (ret && ret.data.code == 0) {
          console.log(ret.data.result);
          //ret.reulst为后端返回的数据
          console.log(level);
          let nodes = JSON.parse(
            JSON.stringify(ret.data.result)
              .replaceAll("buildingNumber", "label")
              .replaceAll("unitNumber", "label")
              .replaceAll("roomNumber", "label")
              .replaceAll("id", "value")
          );
          // let nodes = JSON.parse(JSON.stringify(ret.data.result).replaceAll("unitNumber","label").replaceAll("id","value"));

          // 回调渲染下一层
          resolve(nodes);
        } else {
          //后端报错 弹窗提示失败
          this.$message({
            type: "error",
            message: "部门层级加载失败，请联系管理员！",
          });
        }
        // }, 1);
      } else {
        //如果没有子节点就不发起请求，直接渲染，也避免了点击叶子节点仍然有加载动画的问题
        resolve(nodes);
      }
    },
    handleChange(e) {
      console.log(e);
      if (e == null) {
        this.searchModel.buildingId = null;
        this.searchModel.unitId = null;
        this.searchModel.roomId = null;
        return;
      }
      if (e.length == 1) {
        this.searchModel.buildingId = e[0];
        this.searchModel.unitId = null;
        this.searchModel.roomId = null;
      } else if (e.length == 2) {
        this.searchModel.buildingId = e[0];
        this.searchModel.unitId = e[1];
        this.searchModel.roomId = null;
      } else if (e.length == 3) {
        this.searchModel.buildingId = e[0];
        this.searchModel.unitId = e[1];
        this.searchModel.roomId = e[2];
      }
    },
    search() {
      listPerson(this.searchModel).then((res) => {
        this.personList = res.data.result.list;
        this.total = res.data.result.total;
      });
    },
    refresh() {
      this.searchModel = { communityId: localStorage.getItem("communityId") };
      listPerson(this.searchModel).then((res) => {
        this.personList = res.data.result.list;
        this.total = res.data.result.total;
      });
    },
    // 查看
    view(id) {
      listPersonBelong({
        personId: id,
        communityId: localStorage.getItem("communityId"),
      }).then((res) => {
        getPerson("particular/" + id).then((pres) => {
          listVehicle({
            personId: id,
            communityId: localStorage.getItem("communityId"),
          }).then((vres) => {
            mitt.emit("openPersonView", {
              data: res.data.result,
              person: pres.data.result,
              vehicle: vres.data.result,
            });
          });
        });
      });
    },
    // 查看日志
    viewLog(name) {
      console.log(name);
      this.$router.push({ path: "/log", query: { body: name } });
    },
    // 查看本户
    async findMe(id) {
     
      await listPersonBelong({
        personId: id,
        communityId: localStorage.getItem("communityId"),
        liveType: 0,
      }).then((res) => {
        console.log(res);

        if (res.data.result && res.data.result.length > 0) {
          getPersonBelong(res.data.result[0].id).then((res) => {
            const eList = [
              res.data.result.buildingId,
              res.data.result.unitId,
              res.data.result.roomId,
            ];
            this.handleChange(eList);
            this.searchModel.name = null;
            this.search();
          });
        } else {
          this.$message({
            message: "暂无房产信息",
            type: "warning",
          });
        }
      });
    },
    openId(row) {
      const data = {
        id: row.id,
        openId: row.openId,
        type: "person",
      };
      mitt.emit("openOpenIdEdit", data);
    },
    edit(id) {
      getPerson(id).then((res) => {
        mitt.emit("openPersonEdit", { person: res.data.result, id: id });
      });
    },
    editCardNum(id) {
      getPerson(id).then((res) => {
        mitt.emit("openCardEdit", {
          cardNumber: res.data.result.cardNumber,
          id: id,
        });
      });
    },
    // 房产
    myRoom(id) {
      listPersonBelong({
        personId: id,
        communityId: localStorage.getItem("communityId"),
      }).then((res) => {
        getPerson(id).then((pres) => {
          mitt.emit("openPersonBelongDetail", {
            data: res.data.result,
            person: pres.data.result,
          });
        });
      });
    },
    // 审核功能
    examine(id, status) {
      this.$confirm("审核, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          personExamine({ id: id, status: status }).then((res) => {
            this.$message.success(res.data.msg);
            this.search();
          });
        })
        .catch(() => {});
    },
    add() {
      mitt.emit("openPersonAdd");
    },
    deleted(id) {
      this.$confirm("删除人员, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deletePerson({
            id: id,
            communityId: localStorage.getItem("communityId"),
          }).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
          });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    // prevClick(num) {
    // 	this.searchModel.pageNum = num
    // 	this.search()
    // },
    // nextClick(num) {
    // 	this.searchModel.pageNum = num
    // 	this.search()
    // },
    handleSizeChange(num) {
      this.searchModel.pageSize = num;
      this.search();
    },
    // 格式化标签
    getTags(row) {
      let list = [];
      if (row.tags) {
        let tagArrary = JSON.parse(row.tags);
      
        if (typeof tagArrary === "string") {
          tagArrary = eval(tagArrary);
        }
        //console.log('JSON.parse(row.tags)', tagArrary, typeof tagArrary)
        if (tagArrary instanceof Array) {
          list = this.tagList.filter((item) =>
            tagArrary.some((tag) => tag == item.nameEn)
          );
        }
      }
      return list;
    },
    async getPersonTag() {
      let tagRes = await listDictByNameEn("person_tag");
      this.tagList = tagRes.data.result;
    },
    async init() {
      mitt.off("openPersonEdit");
      mitt.off("openPersonView");
      mitt.off("openPersonAdd");
      mitt.off("openPersonBelongDetail");

      try {
        let res = await listPerson(this.searchModel);
        this.personList = res.data.result.list;
        this.total = res.data.result.total;

        this.getPersonTag();

        let certificateType_res = await listDictByNameEn("certificate_type");
        this.certificateTypeTagList = certificateType_res.data.result;

        let sex_res = await listDictByNameEn("sex");
        this.sexList = sex_res.data.result;

        let status_res = await listDictByNameEn("person_status");
        this.statusList = status_res.data.result;

        // 格式化级联楼栋列表
        let childrenNodeBuilding_res = await queryChildrenNodeBuilding(
          this.searchModel
        );
        for (let i = 0; i < childrenNodeBuilding_res.data.result.length; i++) {
          for (
            let j = 0;
            j < childrenNodeBuilding_res.data.result[i].children.length;
            j++
          ) {
            for (
              let k = 0;
              k <
              childrenNodeBuilding_res.data.result[i].children[j].children
                .length;
              k++
            ) {
              delete childrenNodeBuilding_res.data.result[i].children[j]
                .children[k].buildingNumber;
              delete childrenNodeBuilding_res.data.result[i].children[j]
                .children[k].unitNumber;
            }
          }
        }
        var buildingStr = JSON.stringify(childrenNodeBuilding_res.data.result)
          .replaceAll("buildingNumber", "label")
          .replaceAll("unitNumber", "label")
          .replaceAll("roomNumber", "label")
          .replaceAll("id", "value");
        this.buildingNodeList = JSON.parse(buildingStr);

        listBuilding({
          communityId: localStorage.getItem("communityId"),
          pageSize: 999999,
        }).then((res) => {
          this.buildingList = JSON.parse(
            JSON.stringify(res.data.result.list)
              .replaceAll("buildingNumber", "label")
              .replaceAll("id", "value")
          );
        });
      } catch (err) {}
    },
  },
  created() {
    this.init();
    accountCommunity().then((res) => {
      const communityId = localStorage.getItem("communityId");
      const communityName = res.data.result.filter((item) => {
        return communityId == item.id;
      });
      this.communityName = `导入${communityName[0].communityName}小区基础数据`;
    });
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}

.errMsg {
  width: 1056px;
  height: 534px;
  /* overflow-x: auto; */
  background-color: #fff;
  /* position: relative; */
  color: #666666;
  font-size: 14px;
  box-sizing: border-box;
}

.err_msg_title {
  padding-top: 18px;
  padding-bottom: 24px;
  padding-left: 19px;
}

.errMsgMain {
  height: 456px;
  overflow-x: auto;
}

.blue_icon {
  height: 16px;
  width: 4px;
  background-color: #4b96ff;
  display: inline-block;
  border-radius: 1px;
}

.everyErrMsg {
  height: 38px;
  width: 1019px;
  line-height: 38px;
  /* margin-bottom: 10px; */
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 0 19px;
  cursor: pointer;
  user-select: none;
}

.everyErrMsg:nth-of-type(2n + 1) {
  background-color: rgba(80, 158, 255, 0.1);
}

.nothing {
  text-align: center;
  display: block;
  margin: auto;
  /* margin-bottom: 140px; */
}

::v-deep .diaLogClass {
  height: 780px;
  width: 1124px;
  background-color: rgb(241, 245, 255);
  overflow-x: auto;
  position: relative;
  border-radius: 16px;
}

::v-deep .el-dialog__body {
  padding: 0 34px;
}

.importBox {
  background-color: #fff;
  width: 1056px;
  line-height: 48px;
  border-radius: 6px;
  margin: 27px auto 5px;
  font-size: 14px;
}

.importText {
  padding-left: 32px;
}

.button_box {
  margin: 32px auto 36px;
  width: 237px;
}

.button_box > button {
  width: 112px;
  height: 40px;
}

.button_box > button:nth-child(1) {
  background-color: #3694ff;
  color: #fff;
}

.tag-item {
  display: inline-block;
  margin-bottom: 5px;
  margin-right: 5px;
  width: 59px;
  border-radius: 7px;
  color: #fff;
  font-size: 12px;
  line-height: 14px;
  font-family: Microsoft YaHei;
  padding: 5px 5px 5px 5px;
}

.button_box > button:nth-child(2) {
  color: #3694ff;
  border: 1px solid#3694FF;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

a {
  color: #77bd5b;
}
</style>
