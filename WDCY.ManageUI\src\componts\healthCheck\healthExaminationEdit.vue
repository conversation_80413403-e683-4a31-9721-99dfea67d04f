<template>
	<production-assert-create></production-assert-create>
	<examine-one-edit :statusList="statusList" :categoryList="categoryList" :methodList="methodList" :hasRightList="hasRightList" @search="search" ></examine-one-edit>
	<el-dialog draggable width="40%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="healthExaminaModel" label-width="100px">
			<el-row>
				<el-col :span="24">
					<el-form-item label="功能名称" prop="funcName">
						<el-input maxlength="50" show-word-limit v-model="healthExaminaModel.funcName" placeholder="功能名称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="接口路径" prop="apiPath">
						<el-input maxlength="200" show-word-limit v-model="healthExaminaModel.apiPath" placeholder="接口路径"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="功能分类" prop="funcCategory">
						<!-- <el-input v-model="healthExaminaModel.funcCategory" placeholder="功能分类"></el-input> -->
                        <el-select style="width: 100%" v-model="healthExaminaModel.funcCategory" clearable placeholder="功能分类">
							<el-option v-for="item in categoryList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="功能版本" prop="funcVer">
						<el-input v-model="healthExaminaModel.funcVer" placeholder="功能版本"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="请求方式" prop="method">
                        <el-select style="width: 100%" v-model="healthExaminaModel.method" clearable placeholder="请求方式">
							<el-option v-for="item in methodList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<!-- <el-col :span="12">
					<el-form-item label="前置操作" prop="hasRight">
                        <el-tree-select style="width: 100%;" v-model="menuModel.parentId" :data="newMenuList"
							check-strictly :render-after-expand="false" placeholder="选择父级" />
					</el-form-item>
				</el-col> -->
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="权限控制" prop="hasRight">
                        <el-select style="width: 100%" v-model="healthExaminaModel.hasRight" clearable placeholder="权限控制">
							<el-option v-for="item in hasRightList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
                    <el-form-item label="状态" prop="thirdSystemId">
                        <el-radio-group v-model="healthExaminaModel.status">
                            <el-radio-button  v-for="item in statusList" :key="item.nameEn"
                                :label="parseInt(item.nameEn)">{{ item.nameCn }}</el-radio-button >
                        </el-radio-group>
                    </el-form-item>
            	</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
				<el-form-item label="备注" prop="note">
					<el-input
					v-model="healthExaminaModel.note"
					maxlength="200"
					placeholder="请简单说明区域系统情况"
					show-word-limit
					type="textarea"
					/>
				</el-form-item>
				</el-col>
			</el-row>
            <el-row>
                <el-col :span="24">
                <el-form-item label="扩展参数" prop="expandParams">
                    <JsonEditorVue
                    language="cn"
                    class="editor"
                    :modelValue="jsonVal"
                    @update:modelValue="changeJson"
                    />
                </el-form-item>
                </el-col>
            </el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="examineOne">检 查</el-button>
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="productionAssert">生成断言</el-button>
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">保 存
			</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import productionAssertCreate from "@/componts/healthCheck/productionAssertCreate.vue"
import examineOneEdit from "@/componts/healthCheck/examineOneEdit.vue"
import { addHealthExamina, editHealthExamina, toExaminaOne } from "@/api/healthCheck/healthExamination"
import mitt from "@/utils/mitt";
import JsonEditorVue from "json-editor-vue3";
export default {
	props: ['statusList', 'categoryList', 'hasRightList', 'methodList'],
    components: { JsonEditorVue, examineOneEdit },
	data() {
		return {
			loading: false,
			healthExaminaModel: {},
			dialog: {},
            jsonVal: {},
			personId: "",
			startToEndTime: [],
			imgServer: import.meta.env.VITE_BASE_API,
		}
	},
	methods: {
        changeJson(json) {
        	this.jsonVal = json;
        },

		//生成断言
		productionAssert(){
			mitt.emit('openProductionAssert',this.healthExaminaModel.id)
		},
		
		//检查
		examineOne(){
			const id = this.healthExaminaModel.id
			this.$confirm('正在执行检查, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					toExaminaOne(id)
					.then(res =>{
						mitt.emit('openExamineOneEdit',res.data.result)
					})
				}).catch(()=>{})
		},
		onSubmit() {
			// this.dialog.show = false
			this.$refs['form'].validate((valid) => {
				if (valid) {
                    this.healthExaminaModel.expandParams = JSON.stringify(this.jsonVal);
					if (this.healthExaminaModel.id == 0) {
						// this.healthExaminaModel.delete(id)
						addHealthExamina(this.healthExaminaModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								// this.dialog.show = false
							})
					} else {
						editHealthExamina(this.healthExaminaModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								// this.dialog.show = false
							})
					}
				}
			})
		},
        init(){
        }
	},
	mounted() {
        this.jsonVal = {};
		this.$nextTick(function () {
			mitt.on('openRegionalSystemEdit', (data) => {
				this.healthExaminaModel = data
				this.dialog.show = true
				this.dialog.title = "修改信息"
                this.jsonVal = JSON.parse(this.healthExaminaModel.expandParams);
			})
			mitt.on('openRegionalSystemAdd', () => {
				//新增默认扩展参数
                this.jsonVal = {
					"requestParam": {
						"pathVariable": {
							"id": 1,
							"typ": 0
						},
						"queryParam": {
							"id": 1,
							"typ": 0
						},
						"bodyData": [{
							"id": 1,
							"typ": 0
						}]
					},
					"resultAssert": {
						"dataSource": {
							"url": "",
							"queryParam": {
								"id": 1,
								"typ": 0
							}
						},
						"fields": [{
							"name": "field1",
							"type": "number"
						},
						{
							"name": "field2",
							"type": "number"
						},
						{
							"name": "field3",
							"type": "number"
						},
						{
							"name": "field4",
							"type": "number"
						}
						],
						"customRules": [{
							"expression": "field1 + field2 === field3 + field4",
							"message": "field1 + field2 不等于 field3 + field4"
						},
						{
							"expression": "field1 + field2 >= 1000",
							"message": "field1 + field2 小于 1000"
						}
						]
					}
				}
				this.healthExaminaModel = {
					id: 0,
					status: 1,
					method: 'post',
					hasRight: true,
					funcCategory: 'admin'
				}
				this.dialog.show = true
				this.dialog.title = "添加健康检查"
			})
		})
	},
    created(){
        this.init()
    }
}
</script>
<style scoped>

.editor {
  width: 805px;
}
.avatar-uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}
</style>
