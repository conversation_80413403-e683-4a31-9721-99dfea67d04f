import request from '@/utils/request'

// 查询方案版面列表
export function listLayout(query) {
  return request({
    url: '/sapi/v3_8_0/assy/layout/list',
    method: 'get',
    params: query
  })
}

// 查询方案版面详细
export function getLayout(id) {
  return request({
    url: '/sapi/v3_8_0/assy/layout/' + id,
    method: 'get'
  })
}

// 新增方案版面
export function addLayout(data) {
  return request({
    url: '/sapi/v3_8_0/assy/layout',
    method: 'post',
    data: data
  })
}

// 修改方案版面
export function updateLayout(data) {
  return request({
    url: '/sapi/v3_8_0/assy/layout',
    method: 'put',
    data: data
  })
}

// 删除方案版面
export function delLayout(id) {
  return request({
    url: '/sapi/v3_8_0/assy/layout/' + id,
    method: 'delete'
  })
}
