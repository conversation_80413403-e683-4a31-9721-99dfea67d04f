import request from '@/utils/request'

export const listThirdSystem = (data) =>
	request({
		url: '/third-system/page',
		method: 'get',
		params: data
	})
export const getThirdSystem = (id) =>
	request({
		url: '/third-system/'+id,
		method: 'get'
	})

export const addThirdSystem = (data) =>
	request({
		url: '/third-system',
		method: 'post',
		data: data
	})
export const editThirdSystem = (data) =>
	request({
		url: '/third-system',
		method: 'put',
		data: data
	})
export const deleteThirdSystem = (id) =>
	request({
		url: '/third-system/'+id,
		method: 'delete'
	})


export const listThirdSysConf = (data) =>
	request({
		url: '/third-sys-conf/list',
		method: 'get',
		params: data
	})
export const getThirdSysConf = (id) =>
	request({
		url: '/third-sys-conf/'+id,
		method: 'get'
	})
export const addThirdSysConf = (data) =>
	request({
		url: '/third-sys-conf',
		method: 'post',
		data: data
	})
export const editThirdSysConf = (data) =>
	request({
		url: '/third-sys-conf',
		method: 'put',
		data: data
	})
export const deleteThirdSysConf = (id) =>
	request({
		url: '/third-sys-conf/'+id,
		method: 'delete'
	})

export const listThirdSysAccount = (data) =>
	request({
		url: '/third-sys-account/list',
		method: 'get',
		params: data
	})
export const getThirdSysAccount = (id) =>
	request({
		url: '/third-sys-account/'+id,
		method: 'get'
	})
export const addThirdSysAccount = (data) =>
	request({
		url: '/third-sys-account',
		method: 'post',
		data: data
	})
export const editThirdSysAccount = (data) =>
	request({
		url: '/third-sys-account',
		method: 'put',
		data: data
	})
export const deleteThirdSysAccount = (id) =>
	request({
		url: '/third-sys-account/'+id,
		method: 'delete'
	})