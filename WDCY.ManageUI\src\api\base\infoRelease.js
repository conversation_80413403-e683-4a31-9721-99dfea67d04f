import request from '@/utils/request'

export const listInfoRelease = (data) =>
	request({
		url: '/infoRelease',
		method: 'get',
		params: data
	})
export const getInfoRelease = (id) =>
	request({
		url: '/infoRelease/'+id,
		method: 'get',
	})
export const addInfoRelease = (data) =>
	request({
		url: '/infoRelease',
		method: 'post',
		data: data
	})
export const editInfoRelease = (data) =>
	request({
		url: '/infoRelease',
		method: 'put',
		data: data
	})
export const deleteInfoRelease = (id) =>
	request({
		url: '/infoRelease',
		method: 'delete',
		params: {
			id: id
		}
	})
