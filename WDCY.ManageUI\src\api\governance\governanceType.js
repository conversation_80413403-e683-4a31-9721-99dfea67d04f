import request from '@/utils/request'

export const listAdministerTypeList = (data) =>
	request({
		url: '/mapi/v3.7.3/administerTypeList/'+data.nameEn,
		method: 'get',
		params: data.searchModel
	})

export const addAdministerTypeDict = (data) =>
	request({
		url: '/mapi/v3.7.3/administerType',
		method: 'post',
		data: data
	})
export const editAdministerTypeDict = (data) =>
	request({
		url: '/mapi/v3.7.3/administerType',
		method: 'put',
		data: data
	})
export const deleteAdministerTypeDict = (id) =>
	request({
		url: '/mapi/v3.7.3/administerType',
		method: 'delete',
		params: {
			id: id
		}
	})
export const listDictByNameEn = (nameEn) =>
	request({
		url: '/dict/listDictByNameEn/'+nameEn,
		method: 'get'
	})