<template>
	<el-dialog draggable width="50%" top='5vh' destroy-on-close v-loading="loading" v-model="dialog.show" :title="logModel.type + dialog.title">
		<el-descriptions class="margin-top" :title="'日志明细'" :column="2" border>
			<!-- <template #extra>
				<el-button type="primary">Operation</el-button>
			</template> -->
			<el-descriptions-item label="日志类型" label-class-name="el-descriptions-item-label">
				{{ formatDict(typeList, logModel.type) }}
			</el-descriptions-item>
			<el-descriptions-item label="类型简述" label-class-name="el-descriptions-item-label">
				{{ logModel.typeName }}
			</el-descriptions-item>
			<el-descriptions-item label="操作人">{{ logModel.userName || logModel.userId }}</el-descriptions-item>
			<el-descriptions-item label="操作人类型">{{ logModel.userType }}</el-descriptions-item>
			<el-descriptions-item label="请求时间">{{ logModel.createTime }}</el-descriptions-item>
			<el-descriptions-item label="操作IP地址">{{ logModel.ipAddress }}</el-descriptions-item>
			<el-descriptions-item label="请求参数" :span="2">
				{{ logModel.request }}
			</el-descriptions-item>
		</el-descriptions>
		<el-form-item label="响应结果" prop="response" style="margin:10px 0 0 18px">
			<JsonEditorVue language="cn" class="editor" :modelValue="jsonVal" @update:modelValue="changeJson" />
		</el-form-item>

		<el-form v-if="false" :rules="rules" ref="form" :model="logModel" label-width="120px">

			<el-form-item label="类型" prop="type">
				<el-select disabled style="width: 100%;" v-model="logModel.type" placeholder="日志类型">
					<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn"
						:value="item.nameEn"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="类型简述" prop="typeName">
				<el-input readonly v-model="logModel.typeName" placeholder="类型简述"></el-input>
			</el-form-item>

			<el-form-item label="操作人" prop="userName">
				<el-input readonly v-model="logModel.userName" placeholder="操作人"></el-input>
			</el-form-item>

			<el-form-item label="操作IP地址" prop="ipAddress">
				<el-input readonly v-model="logModel.ipAddress" placeholder="IP"></el-input>
			</el-form-item>

			<el-form-item label="请求参数" prop="request">
				<el-input :rows="6" type="textarea" readonly v-model="logModel.request" placeholder="请求参数"></el-input>
			</el-form-item>

			<el-form-item label="响应结果" prop="response">
				<el-input :rows="6" type="textarea" readonly v-model="logModel.response" placeholder="响应结果"></el-input>
			</el-form-item>

		</el-form>
	</el-dialog>
</template>

<script>
import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict"
import JsonEditorVue from "json-editor-vue3";
export default {
    components: { JsonEditorVue },
	props: ['typeList'],
	data() {
		return {
			loading: false,
			logModel: {},
            jsonVal: {},
			dialog: {}
		}
	},
	methods: {
        changeJson(json) {
            // console.info("changeJson: ", json);
            this.jsonVal = json;
        },
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		},
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		}
	},
	mounted() {
        this.jsonVal = {};
		mitt.on('openLogDetail', (log) => {
			this.logModel = log
			this.jsonVal = JSON.parse(this.logModel.response);
			this.dialog.show = true
			this.dialog.title = "日志详情"
		})
	}
}
</script>

<style scoped lang="less">
// .margin-top {
//   width: 100%;
//   height: 500px;
// }
.el-descriptions-item-label{
	width: 100px;
}
/deep/ .el-descriptions__content {
    max-width: 800px;
	word-break: break-all; // 让内容超出列宽时自动换行显示
  	word-wrap: break-word;
  }
  
.editor {
    width: 100%;
	height: 400px;
}
</style>