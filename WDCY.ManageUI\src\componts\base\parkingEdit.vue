<template>
  <el-dialog
    draggable
    width="32%"
    top="5vh"
    destroy-on-close
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form
      :rules="rules"
      ref="form"
      :model="parkingModel"
      label-width="100px"
    >
      <el-row>
        <el-col :span="22">
          <el-form-item label="停车场名称" prop="name">
            <el-input
              v-model="parkingModel.name"
              placeholder="停车场名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="标识" prop="code">
            <el-input v-model="parkingModel.code" placeholder="标识"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="地址" prop="address">
            <el-input
              v-model="parkingModel.address"
              placeholder="地址"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="经纬度" prop="lng">
            <el-input
              style="width: 45%"
              v-model="parkingModel.lng"
              placeholder="经度"
            ></el-input
            >&nbsp;-&nbsp;<el-input
              style="width: 45%"
              v-model="parkingModel.lat"
              placeholder="纬度"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label-width="0">
            <el-button
              type="text"
              style="font-size: 30px; padding: 0"
              @click="selectPoint"
            >
              <el-icon :size="30">
                <location-filled></location-filled>
              </el-icon>
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="总停车位" label-width="" prop="totalCount">
            <el-input-number
              style="width: 100%"
              v-model="parkingModel.totalCount"
              placeholder="总停车位"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row> </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="开放式ID" prop="openId">
            <el-input
              v-model="parkingModel.openId"
              placeholder="小区开放式ID"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="状态" prop="status">
            <el-select
              style="width: 100%"
              v-model="parkingModel.status"
              placeholder="状态"
            >
              <el-option
                v-for="item in statusList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="parseInt(item.nameEn)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="22">
          <el-form-item label="备注" prop="note">
            <el-input
              v-model="parkingModel.note"
              maxlength="200"
              placeholder="请简单说明小区及其硬件配置情况"
              show-word-limit
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="扩展参数" prop="expandParams">
            <!-- <json-editor-vue class="editor" language="cn" v-model="jsonVal" /> -->
            <JsonEditorVue
              language="cn"
              class="editor"
              :modelValue="jsonVal"
              @update:modelValue="changeJson"
            />
            <!-- <JsonEditorVue class="editor" modelValue="data" @update:modelValue="changeJson" @blur="validate" /> -->
            <!-- <json-editor-vue class="editor" v-model="jsonVal" /> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>
  
<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>
  
<script>
import { accountCommunity } from "@/api/base/community";
import { editParking, addParking } from "@/api/base/parking";
import mitt from "@/utils/mitt";
// import { openMap, openMarsMap } from "@/utils/myUtils";
import JsonEditorVue from "json-editor-vue3";
import { getCommunity } from "@/api/base/community";

export default {
  components: { JsonEditorVue },
  props: ["statusList", "groupList"],
  data() {
    return {
      loading: false,
      jsonVal: {},
      parkingModel: {},
      communityList: [],
      dialog: {},
      rules: {
        communityId: [
          {
            required: true,
            message: "请输入小区名",
            trigger: "change",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入停车场名称",
            trigger: "blur",
          },
        ],
        code: [
          {
            required: true,
            message: "请输入标识",
            trigger: "blur",
          },
        ],
        totalCount: [
          {
            required: true,
            message: "请输入总停车位",
            trigger: "blur",
          },
        ],
        groupId: [
          {
            required: true,
            message: "请选择所属组",
            trigger: "change",
          },
        ],
      },
    };
  },
  methods: {
    changeJson(json) {
      // console.info("changeJson: ", json);
      this.jsonVal = json;
    },
    selectPoint() {
      var communityId = localStorage.getItem("communityId");
      getCommunity(communityId)
        .then((res) => {
          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map.mode;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          if (result.enabled3d && mode == "mars3d") {
            position = config.map.sdgis.position;

            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (result.lng && result.lat) {
            center = {
              lng: result.lng,
              lat: result.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }

          var data = {
            enabled3d: result.enabled3d,
            edit: true,
            point: [this.parkingModel.lng, this.parkingModel.lat, 0],
            position: position,
            center: center,
            modeUrl: modeUrl,
            title: "地图选点",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);
          mitt.emit("openMarsMap", data);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.parkingModel.expandParams = JSON.stringify(this.jsonVal);
          console.log(this.parkingModel);
          if (this.dialog.title == "添加停车场") {
            addParking(this.parkingModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          } else {
            editParking(this.parkingModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          }
        }
      });
    },
    async init() {
      await accountCommunity().then((res) => {
        this.communityList = res.data.result;
      });
    },
  },
  mounted() {
    this.jsonVal = {};
    this.$nextTick(function () {
      mitt.on("openParkingEdit", (parking) => {
        this.parkingModel = parking;
        this.dialog.show = true;
        this.dialog.title = "修改信息";
        this.jsonVal = JSON.parse(this.parkingModel.expandParams);
      });
      mitt.on("openParkingAdd", () => {
        this.parkingModel = {
          status: 1,
          communityId: localStorage.getItem("communityId"),
        };
        this.dialog.show = true;
        this.dialog.title = "添加停车场";
      });
      mitt.on("setPointValue", (e) => {
        console.log(e, 123123);
        this.parkingModel.lng = e[0];
        this.parkingModel.lat = e[1];
      });
      mitt.on("setAddress", (e) => {
        this.parkingModel.address = e.regeocode.formattedAddress;
      });
    });
  },
  created() {
    mitt.off("openMarsMap");
    this.init();
  },
};
</script>
  
<style scoped>
.editor {
  width: 805px;
}
</style>
  