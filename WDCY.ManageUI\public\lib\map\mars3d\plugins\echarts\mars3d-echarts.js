/**
 * Mars3D平台插件,结合echarts可视化功能插件  mars3d-echarts
 *
 * 版本信息：v3.7.22
 * 编译日期：2024-07-15 21:21:06
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2024-01-15
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.echarts || require('echarts')), (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'echarts', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-echarts"] = {}, global.echarts, global.mars3d));
})(this, (function (exports, echarts, mars3d) { 
'use strict';(function(_0x60f1ad,_0x189d53){const _0x2d821b={_0x291780:0x81,_0x2f9417:0x6d,_0x4bb07c:0x9e,_0x42fbcb:0x62,_0x307f5f:0x33d,_0x4a370d:0xba,_0x35bee3:0xb0},_0x45f3b4={_0x3e1699:0x1dc},_0x25f499=_0x60f1ad();function _0x183978(_0x18f6d6,_0x2a779d){return _0x1029(_0x18f6d6-0x1e4,_0x2a779d);}function _0x17acda(_0x2973ba,_0x5f4782){return _0x1029(_0x2973ba- -_0x45f3b4._0x3e1699,_0x5f4782);}while(!![]){try{const _0x585ed1=-parseInt(_0x17acda(-_0x2d821b._0x291780,-_0x2d821b._0x2f9417))/0x1*(parseInt(_0x17acda(-_0x2d821b._0x4bb07c,-0x73))/0x2)+-parseInt(_0x17acda(-0x75,-_0x2d821b._0x42fbcb))/0x3*(-parseInt(_0x17acda(-0xc3,-0xa4))/0x4)+parseInt(_0x183978(_0x2d821b._0x307f5f,0x35c))/0x5+parseInt(_0x17acda(-0x85,-0x8e))/0x6+parseInt(_0x17acda(-0x82,-0x7f))/0x7*(parseInt(_0x183978(0x309,0x2dd))/0x8)+parseInt(_0x183978(0x34a,0x355))/0x9*(parseInt(_0x17acda(-_0x2d821b._0x4a370d,-_0x2d821b._0x35bee3))/0xa)+-parseInt(_0x183978(0x33a,0x349))/0xb;if(_0x585ed1===_0x189d53)break;else _0x25f499['push'](_0x25f499['shift']());}catch(_0x5c8b94){_0x25f499['push'](_0x25f499['shift']());}}}(_0x93aa,0x88765));function _interopNamespace(_0x5441c9){const _0x1f0dea={_0x4a2aa2:0x5c,_0x4e4936:0x3b};function _0x24d3a5(_0x1acb45,_0x3bf17c){return _0x1029(_0x1acb45- -0x342,_0x3bf17c);}if(_0x5441c9&&_0x5441c9['__esModule'])return _0x5441c9;var _0x352932=Object['create'](null);return _0x5441c9&&Object[_0x24d3a5(-0x21c,-0x215)](_0x5441c9)['forEach'](function(_0x667d7a){function _0xe8cdcb(_0x2b7968,_0x2d19a7){return _0x24d3a5(_0x2b7968-0x263,_0x2d19a7);}function _0x3aa720(_0x546c2a,_0x4d7246){return _0x24d3a5(_0x4d7246- -0xa6,_0x546c2a);}if(_0x667d7a!=='default'){var _0x249451=Object['getOwnPropertyDescriptor'](_0x5441c9,_0x667d7a);Object[_0xe8cdcb(0x40,0x16)](_0x352932,_0x667d7a,_0x249451[_0xe8cdcb(_0x1f0dea._0x4a2aa2,_0x1f0dea._0x4e4936)]?_0x249451:{'enumerable':!![],'get':function(){return _0x5441c9[_0x667d7a];}});}}),_0x352932['default']=_0x5441c9,_0x352932;}function _0x2c1487(_0x4a2942,_0x482030){return _0x1029(_0x482030- -0x191,_0x4a2942);}var echarts__namespace=_interopNamespace(echarts),mars3d__namespace=_interopNamespace(mars3d);const Cesium$1=mars3d__namespace['Cesium'];class CompositeCoordinateSystem{constructor(_0x2141ef,_0x1a11d2){const _0x21f7da={_0x1e4446:0x150};function _0x5a274d(_0x527dbf,_0x4376bc){return _0x1029(_0x527dbf-_0x21f7da._0x1e4446,_0x4376bc);}function _0x4efd22(_0x370127,_0x477900){return _0x1029(_0x370127-0x69,_0x477900);}this['_mars3d_scene']=_0x2141ef,this[_0x4efd22(0x17d,0x194)]=['lng',_0x5a274d(0x27a,0x25d)],this['_mapOffset']=[0x0,0x0],this['_api']=_0x1a11d2;}['setMapOffset'](_0x26661b){this['_mapOffset']=_0x26661b;}['getBMap'](){return this['_mars3d_scene'];}['dataToPoint'](_0x9813d1){const _0x59352b={_0x448d1c:0x332,_0x4a25ff:0x356,_0x46c589:0x317,_0x2d0a80:0x344,_0x40bb4b:0x321,_0x11e1e4:0x32b},_0x3cf9af=this[_0x49eb6c(-0x19b,-0x196)],_0x57f70a=[NaN,NaN];function _0x49eb6c(_0x26b875,_0x3890a5){return _0x1029(_0x26b875- -0x2c4,_0x3890a5);}let _0x1fb180=_0x3cf9af['echartsFixedHeight'];_0x3cf9af['echartsAutoHeight']&&(_0x1fb180=_0x3cf9af[_0x455dde(_0x59352b._0x448d1c,_0x59352b._0x4a25ff)](Cesium$1['Cartographic'][_0x455dde(0x35f,0x364)](_0x9813d1[0x0],_0x9813d1[0x1])));const _0x194a87=Cesium$1[_0x49eb6c(-0x178,-0x17b)]['fromDegrees'](_0x9813d1[0x0],_0x9813d1[0x1],_0x1fb180);if(!_0x194a87)return _0x57f70a;const _0x39ca62=Cesium$1[_0x455dde(_0x59352b._0x46c589,_0x59352b._0x2d0a80)]['worldToWindowCoordinates'](_0x3cf9af,_0x194a87);function _0x455dde(_0x25ad89,_0x3e2e87){return _0x1029(_0x25ad89-0x207,_0x3e2e87);}if(!_0x39ca62)return _0x57f70a;if(_0x3cf9af[_0x455dde(0x336,0x35b)]&&_0x3cf9af[_0x49eb6c(-0x165,-0x175)]===Cesium$1[_0x455dde(_0x59352b._0x40bb4b,0x32a)]['SCENE3D']){const _0x3643ee=new Cesium$1['EllipsoidalOccluder'](_0x3cf9af['globe']['ellipsoid'],_0x3cf9af[_0x455dde(0x325,0x31c)]['positionWC']),_0x41f2d5=_0x3643ee['isPointVisible'](_0x194a87);if(!_0x41f2d5)return _0x57f70a;}return[_0x39ca62['x']-this[_0x455dde(0x322,_0x59352b._0x11e1e4)][0x0],_0x39ca62['y']-this['_mapOffset'][0x1]];}[_0x2ec481(0x355,0x350)](){const _0x3923e1={_0x5e3f5c:0x226},_0x4462da=this[_0x5d5d46(-0x20d,-0x236)];function _0x5d5d46(_0x1933a4,_0x32d4f8){return _0x2ec481(_0x32d4f8- -0x552,_0x1933a4);}function _0x4fea14(_0x541d55,_0x267b66){return _0x2ec481(_0x541d55-0x11b,_0x267b66);}return new echarts__namespace['graphic'][(_0x5d5d46(-0x246,-_0x3923e1._0x5e3f5c))](0x0,0x0,_0x4462da[_0x5d5d46(-0x241,-0x214)](),_0x4462da[_0x5d5d46(-0x23e,-0x23b)]());}[_0x2c1487(-0x60,-0x42)](){return echarts__namespace['matrix']['create']();}}CompositeCoordinateSystem[_0x2ec481(0x300,0x2f9)]=['lng','lat'],CompositeCoordinateSystem['create']=function(_0x46b05a,_0x3aa345){const _0x1634d5={_0x2dd56c:0x14c,_0x2b33f0:0x167},_0x545d37={_0x151044:0x1ad,_0x3ad75a:0xec},_0x1dbbc5={_0x20bf85:0x254},_0x3a6a31={_0x45726f:0x3d3},_0x547873={_0x5561aa:0x1dc},_0x445728={_0x1d1bf8:0x20a};function _0x54328f(_0x4f43dd,_0x283cb8){return _0x2ec481(_0x283cb8- -_0x445728._0x1d1bf8,_0x4f43dd);}let _0x586956;function _0x444236(_0x38fe87,_0xc1cf95){return _0x2ec481(_0xc1cf95- -_0x547873._0x5561aa,_0x38fe87);}const _0x9c44ee=_0x46b05a['scheduler'][_0x54328f(_0x1634d5._0x2dd56c,0x144)]['_mars3d_scene'];_0x46b05a[_0x444236(0x162,0x14d)](_0x444236(_0x1634d5._0x2b33f0,0x13c),function(_0x37107f){const _0x167aeb=_0x3aa345[_0x354986(0x4b8,0x4d5)]()['painter'];function _0x354986(_0x417cc6,_0xb474d3){return _0x444236(_0xb474d3,_0x417cc6-0x361);}if(!_0x167aeb)return;function _0x2f1a9c(_0x372379,_0x562807){return _0x444236(_0x562807,_0x372379-_0x3a6a31._0x45726f);}!_0x586956&&(_0x586956=new CompositeCoordinateSystem(_0x9c44ee,_0x3aa345)),_0x37107f['coordinateSystem']=_0x586956,_0x586956['setMapOffset'](_0x37107f[_0x2f1a9c(0x52d,0x526)]||[0x0,0x0]);}),_0x46b05a['eachSeries'](function(_0xc240d7){function _0x575041(_0x1c6ef5,_0x2fc6aa){return _0x444236(_0x1c6ef5,_0x2fc6aa- -_0x1dbbc5._0x20bf85);}function _0x3d89df(_0x1e2733,_0x505231){return _0x444236(_0x1e2733,_0x505231- -0x30e);}_0xc240d7[_0x3d89df(-_0x545d37._0x151044,-0x1c3)](_0x575041(-_0x545d37._0x3ad75a,-0xe8))==='mars3dMap'&&(!_0x586956&&(_0x586956=new CompositeCoordinateSystem(_0x9c44ee,_0x3aa345)),_0xc240d7['coordinateSystem']=_0x586956);});};if(echarts__namespace!==null&&echarts__namespace!==void 0x0&&echarts__namespace['init']){echarts__namespace[_0x2c1487(-0x88,-0x71)](_0x2ec481(0x318,0x328),CompositeCoordinateSystem);const _0x25f776={};_0x25f776['type']='mars3dMapRoam',_0x25f776['event']=_0x2c1487(-0x5e,-0x59),_0x25f776['update']='updateLayout',echarts__namespace['registerAction'](_0x25f776,function(_0x3a7baa,_0x47e462){});const _0x3390e1={};_0x3390e1[_0x2c1487(-0x80,-0x74)]=![];const _0x431130={};_0x431130['type']=_0x2c1487(-0x48,-0x65),_0x431130['getBMap']=function(){return this['_mars3d_scene'];},_0x431130['defaultOption']=_0x3390e1,echarts__namespace['extendComponentModel'](_0x431130),echarts__namespace[_0x2ec481(0x31d,0x324)]({'type':_0x2ec481(0x318,0x31b),'init':function(_0xb676a5,_0x5013b3){const _0xa841da={_0x21004b:0x420,_0x934baa:0x422};this['api']=_0x5013b3;function _0x5026cc(_0x288bfe,_0x31d1f8){return _0x2ec481(_0x31d1f8-0xd7,_0x288bfe);}this[_0x5026cc(0x437,_0xa841da._0x21004b)]=_0xb676a5[_0x116f3e(-0xf6,-0xd0)][_0x5026cc(0x452,0x425)][_0x116f3e(-0xd5,-0xfb)];function _0x116f3e(_0x2aea1b,_0x334c9f){return _0x2ec481(_0x334c9f- -0x410,_0x2aea1b);}this['scene']['postRender'][_0x5026cc(_0xa841da._0x934baa,0x409)](this['moveHandler'],this);},'moveHandler':function(_0x4c9fa0,_0x4a7151){const _0x28d83c={};_0x28d83c['type']='mars3dMapRoam',this['api']['dispatchAction'](_0x28d83c);},'render':function(_0x5855bf,_0x47beb4,_0x41c0bf){},'dispose':function(_0x4e6890){const _0x12965={_0x41f12c:0x199,_0x3e20f4:0x179},_0x49365b={_0x21cebe:0x216};function _0x452baf(_0x3610f4,_0x489e51){return _0x2ec481(_0x489e51- -_0x49365b._0x21cebe,_0x3610f4);}function _0x583202(_0x3e296c,_0x204733){return _0x2ec481(_0x3e296c- -0x4b2,_0x204733);}this['scene'][_0x583202(-_0x12965._0x41f12c,-0x1b8)]['removeEventListener'](this[_0x583202(-_0x12965._0x3e20f4,-0x16b)],this);}});}else throw new Error(_0x2c1487(-0x4c,-0x5a));const Cesium=mars3d__namespace[_0x2c1487(-0x77,-0x57)],BaseLayer=mars3d__namespace[_0x2c1487(-0x8d,-0x70)]['BaseLayer'];class EchartsLayer extends BaseLayer{constructor(_0x34b178={}){const _0x2f5015={_0x1ce0ef:0x162};function _0x32d837(_0x2b2496,_0x3d090f){return _0x2ec481(_0x2b2496- -0x472,_0x3d090f);}super(_0x34b178),this['_pointerEvents']=this['options'][_0x32d837(-_0x2f5015._0x1ce0ef,-0x13d)];}get['layer'](){return this['_echartsInstance'];}get[_0x2c1487(-0x7f,-0x6d)](){return this['_pointerEvents'];}set[_0x2ec481(0x310,0x2ff)](_0x1bae96){const _0x48cef7={_0x285fe1:0x2cc},_0x9ecd1={_0x3acc52:0x1e3};function _0x9007e8(_0x5cb36e,_0x2557e2){return _0x2ec481(_0x5cb36e-_0x9ecd1._0x3acc52,_0x2557e2);}this['_pointerEvents']=_0x1bae96;function _0x3cc53f(_0x143319,_0x4806a9){return _0x2c1487(_0x4806a9,_0x143319-0x339);}this['_echartsContainer']&&(_0x1bae96?this['_echartsContainer']['style'][_0x3cc53f(0x2cc,0x2df)]=_0x3cc53f(0x2dd,0x2d4):this['_echartsContainer']['style'][_0x3cc53f(_0x48cef7._0x285fe1,0x2ea)]='none');}[_0x2ec481(0x303,0x318)](_0x5dff16,_0x4e2518){const _0x52a4cb={_0x604066:0x101,_0x1b40d2:0x105};function _0x5c9219(_0x2181a8,_0x16c4ec){return _0x2ec481(_0x16c4ec- -0x404,_0x2181a8);}this[_0x5c9219(-_0x52a4cb._0x604066,-_0x52a4cb._0x1b40d2)](_0x5dff16);}['_showHook'](_0x37a83a){const _0x3a7c72={_0x202be8:0x115};function _0x207861(_0xbe1991,_0x43cbeb){return _0x2c1487(_0x43cbeb,_0xbe1991-_0x3a7c72._0x202be8);}_0x37a83a?this['_echartsContainer']['style']['visibility']='visible':this['_echartsContainer']['style']['visibility']=_0x207861(0xe5,0x101);}['_mountedHook'](){const _0x4baab5={_0x44590a:0x499},_0x4ab2cf={_0x4b310e:0x18d};function _0x1d14ad(_0x5c8f42,_0x315336){return _0x2ec481(_0x315336-_0x4ab2cf._0x4b310e,_0x5c8f42);}function _0x27d717(_0x3a45d2,_0x1abf54){return _0x2c1487(_0x1abf54,_0x3a45d2-0x4f6);}this[_0x27d717(_0x4baab5._0x44590a,0x49f)]['scene']['echartsDepthTest']=this['options']['depthTest']??!![],this[_0x1d14ad(0x4bf,0x4ad)]['scene']['echartsAutoHeight']=this['options']['clampToGround']??![],this['_map'][_0x27d717(0x4c2,0x4cb)]['echartsFixedHeight']=this['options']['fixedHeight']??0x0;}[_0x2ec481(0x354,0x35e)](){const _0x80bc65={_0x57ad0d:0x216,_0x2b9bf5:0x19a,_0x15dbab:0x190,_0x484161:0x241},_0x1f9d04={_0x1dfa21:0xe7};function _0x4e17dd(_0x5d981a,_0x52ab5b){return _0x2ec481(_0x52ab5b- -_0x1f9d04._0x1dfa21,_0x5d981a);}this[_0x4e17dd(0x209,_0x80bc65._0x57ad0d)]=this['_createChartOverlay'](),this[_0x28d820(-_0x80bc65._0x2b9bf5,-0x174)]=echarts__namespace['init'](this['_echartsContainer']),this['_echartsInstance'][_0x28d820(-0x1aa,-_0x80bc65._0x15dbab)]=this['_map']['scene'];function _0x28d820(_0x22932c,_0x144b49){return _0x2c1487(_0x144b49,_0x22932c- -0x142);}this[_0x4e17dd(0x23b,0x218)](this[_0x4e17dd(0x21e,_0x80bc65._0x484161)]);}['_removedHook'](){const _0xb9c7c6={_0x4a2370:0x106,_0x4bdba7:0x116,_0x47fd99:0x117,_0x4d0009:0x13f,_0x5c3c2a:0xef,_0x3d7e9b:0x2b7,_0x278452:0xcd,_0x3f39a9:0x25a,_0x334ab4:0x286},_0x259811={_0x18bc82:0x77};function _0x173411(_0x528f47,_0x16e92e){return _0x2ec481(_0x16e92e- -_0x259811._0x18bc82,_0x528f47);}this[_0x15535a(0x117,_0xb9c7c6._0x4a2370)]&&(this[_0x173411(0x2bf,0x2ae)]['clear'](),this[_0x15535a(0x117,_0xb9c7c6._0x4bdba7)]['dispose'](),delete this[_0x15535a(_0xb9c7c6._0x47fd99,_0xb9c7c6._0x4d0009)]);function _0x15535a(_0x1fbd69,_0x2d57f7){return _0x2ec481(_0x1fbd69- -0x20e,_0x2d57f7);}this[_0x15535a(_0xb9c7c6._0x5c3c2a,0x111)]&&(this['_map'][_0x173411(0x2bc,_0xb9c7c6._0x3d7e9b)][_0x15535a(0xed,_0xb9c7c6._0x278452)](this[_0x173411(_0xb9c7c6._0x3f39a9,_0xb9c7c6._0x334ab4)]),delete this['_echartsContainer']);}['_createChartOverlay'](){const _0x45b023={_0x519464:0x491,_0x4d395f:0x1ac,_0x9320ec:0x1a3,_0x3f2939:0x474,_0x1c1096:0x483,_0x46162a:0x47b,_0x2db5e1:0x48a,_0x553d94:0x473,_0x29a441:0x450},_0x254a21=mars3d__namespace[_0x3708ce(_0x45b023._0x519464,0x48a)]['create']('div',_0x3708ce(0x488,0x49a),this['_map'][_0xad12ba(-_0x45b023._0x4d395f,-_0x45b023._0x9320ec)]);_0x254a21['id']=this['id'];function _0x3708ce(_0x3b4f24,_0x238c54){return _0x2ec481(_0x238c54-0x14e,_0x3b4f24);}_0x254a21['style'][_0x3708ce(_0x45b023._0x3f2939,0x48f)]='absolute',_0x254a21['style'][_0x3708ce(0x489,_0x45b023._0x1c1096)]=_0x3708ce(0x4a0,_0x45b023._0x46162a),_0x254a21[_0xad12ba(-0x17a,-0x180)][_0xad12ba(-0x1e8,-0x1be)]=_0xad12ba(-0x1cb,-0x1a4);function _0xad12ba(_0x168c24,_0x21ad9b){return _0x2c1487(_0x168c24,_0x21ad9b- -0x154);}return _0x254a21['style'][_0x3708ce(0x452,0x44c)]=this[_0x3708ce(_0x45b023._0x2db5e1,0x46e)][_0xad12ba(-0x195,-0x188)][_0xad12ba(-0x1ab,-0x19a)]['clientWidth']+'px',_0x254a21['style'][_0x3708ce(_0x45b023._0x553d94,0x49e)]=this[_0x3708ce(0x468,0x46e)]['scene']['canvas']['clientHeight']+'px',_0x254a21['style']['pointerEvents']=this['_pointerEvents']?'all':_0x3708ce(0x463,_0x45b023._0x29a441),_0x254a21['style']['zIndex']=this['options']['zIndex']??0x9,_0x254a21;}['resize'](){const _0x35872e={_0x2d5324:0x1ed,_0x3f9d90:0x1ee,_0x304387:0xa2,_0x4cdec3:0x8a,_0x3a1c15:0x4d},_0x547e8c={_0x5bbc75:0xaa};if(!this[_0x11e222(0x235,0x216)])return;function _0xf4de9b(_0x160fd5,_0x3d526e){return _0x2c1487(_0x160fd5,_0x3d526e-_0x547e8c._0x5bbc75);}this['_echartsContainer'][_0xf4de9b(0xa4,0x7e)]['width']=this['_map']['scene']['canvas']['clientWidth']+'px';function _0x11e222(_0x5e1f09,_0x50f87e){return _0x2c1487(_0x5e1f09,_0x50f87e-0x26e);}this[_0x11e222(_0x35872e._0x2d5324,_0x35872e._0x3f9d90)][_0xf4de9b(_0x35872e._0x304387,0x7e)]['height']=this[_0xf4de9b(0x77,0x4d)][_0xf4de9b(_0x35872e._0x4cdec3,0x76)]['canvas']['clientHeight']+'px',this['_echartsInstance'][_0xf4de9b(_0x35872e._0x3a1c15,0x61)]();}['setEchartsOption'](_0x4bbcb0,_0x3207b1,_0x639ac8){const _0x5d4f28={_0x530de8:0x381};function _0xb19690(_0x30d9ef,_0x59ffa2){return _0x2c1487(_0x30d9ef,_0x59ffa2-_0x5d4f28._0x530de8);}function _0x361692(_0x117807,_0x1646a5){return _0x2ec481(_0x1646a5- -0x46b,_0x117807);}if(this['_echartsInstance']){const _0x59dab6={};_0x59dab6[_0xb19690(0x30f,0x31e)]=!![],_0x4bbcb0={'mars3dMap':{},...mars3d__namespace['Util']['getAttrVal'](_0x4bbcb0,_0x59dab6)},delete _0x4bbcb0['eventParent'],this[_0xb19690(0x329,0x329)]['setOption'](_0x4bbcb0,_0x3207b1,_0x639ac8);}}['getRectangle'](_0x103aa7){const _0x523b8a={_0x5b3f76:0x13,_0x48acdf:0xc,_0x157162:0x1da,_0x2b8918:0x11,_0x46aa40:0x42,_0x4aaa2c:0x1},_0x4c72a4={_0x2ee8d3:0x12a},_0x510da7={_0xf9bcbb:0x9d,_0x102997:0x9c,_0x481156:0x1e3,_0x33e6bc:0x1fa};let _0x471560,_0x330ed5,_0x349299,_0x2f15cf;function _0x44d760(_0x12b559){function _0x1cc72e(_0x37c5c0,_0x3c0565){return _0x1029(_0x37c5c0- -0x1e2,_0x3c0565);}if(!Array[_0x1cc72e(-_0x510da7._0xf9bcbb,-_0x510da7._0x102997)](_0x12b559))return;function _0x4a6703(_0x13132d,_0x59dc8b){return _0x1029(_0x59dc8b- -0x316,_0x13132d);}const _0x356463=_0x12b559[0x0]||0x0,_0x5ddd6c=_0x12b559[0x1]||0x0;_0x356463!==0x0&&_0x5ddd6c!==0x0&&(_0x471560===undefined?(_0x471560=_0x356463,_0x330ed5=_0x356463,_0x349299=_0x5ddd6c,_0x2f15cf=_0x5ddd6c):(_0x471560=Math[_0x1cc72e(-0x91,-0x88)](_0x471560,_0x356463),_0x330ed5=Math[_0x4a6703(-_0x510da7._0x481156,-_0x510da7._0x33e6bc)](_0x330ed5,_0x356463),_0x349299=Math['min'](_0x349299,_0x5ddd6c),_0x2f15cf=Math['max'](_0x2f15cf,_0x5ddd6c)));}const _0x4c76fa=this['options']['series'];function _0x5267ea(_0x2786d7,_0x413fec){return _0x2ec481(_0x2786d7- -0x343,_0x413fec);}function _0x3bba89(_0x5a9c5a,_0x378143){return _0x2ec481(_0x5a9c5a- -_0x4c72a4._0x2ee8d3,_0x378143);}_0x4c76fa&&_0x4c76fa[_0x5267ea(-_0x523b8a._0x5b3f76,_0x523b8a._0x48acdf)](_0x4a6619=>{const _0x140bfc={_0x55ffad:0x42d,_0x256d3e:0x449,_0x3978f9:0x41c,_0x4fed23:0x449},_0x33b410={_0x256684:0x305};_0x4a6619['data']&&_0x4a6619['data']['forEach'](_0x45592b=>{const _0x43410d={_0xc4e4e0:0x1a4};function _0x1d22bc(_0x2bc99d,_0x886f83){return _0x1029(_0x886f83-_0x33b410._0x256684,_0x2bc99d);}function _0x5c3085(_0xa9c6ee,_0x390cab){return _0x1029(_0x390cab- -_0x43410d._0xc4e4e0,_0xa9c6ee);}if(_0x45592b['value'])_0x44d760(_0x45592b[_0x1d22bc(0x411,_0x140bfc._0x55ffad)]);else _0x45592b[_0x1d22bc(_0x140bfc._0x256d3e,0x453)]&&_0x45592b['coords'][_0x1d22bc(_0x140bfc._0x3978f9,_0x140bfc._0x4fed23)](_0x14fca9=>{_0x44d760(_0x14fca9);});});});if(_0x471560===0x0&&_0x349299===0x0&&_0x330ed5===0x0&&_0x2f15cf===0x0)return null;if(_0x103aa7!==null&&_0x103aa7!==void 0x0&&_0x103aa7[_0x5267ea(-0x34,-0x57)]){const _0x352cc9={};return _0x352cc9[_0x3bba89(_0x523b8a._0x157162,0x1de)]=_0x471560,_0x352cc9[_0x5267ea(0x7,0x3)]=_0x330ed5,_0x352cc9['ymin']=_0x349299,_0x352cc9[_0x5267ea(-0x14,-_0x523b8a._0x2b8918)]=_0x2f15cf,_0x352cc9;}else return Cesium[_0x5267ea(-_0x523b8a._0x46aa40,-0x58)][_0x5267ea(_0x523b8a._0x4aaa2c,0x2)](_0x471560,_0x349299,_0x330ed5,_0x2f15cf);}['on'](_0x3f981f,_0x417c9c,_0x4b4a5f){return this['_echartsInstance']['on'](_0x3f981f,_0x417c9c,_0x4b4a5f||this),this;}['onByQuery'](_0x1f3224,_0x4331cb,_0x8663f9,_0x9e09aa){const _0x36d8d2={_0x5a50d8:0x50,_0x1b71fa:0x45};this[_0x425a70(-_0x36d8d2._0x5a50d8,-_0x36d8d2._0x1b71fa)]['on'](_0x1f3224,_0x4331cb,_0x8663f9,_0x9e09aa||this);function _0x425a70(_0x43b484,_0x523df8){return _0x2c1487(_0x43b484,_0x523df8-0x13);}return this;}['off'](_0x4fd040,_0x327ca0,_0x12f6a8){const _0x28ed0f={_0x53d36b:0x36e},_0xc4a71a={_0x5bda34:0x4f};function _0x1a47f2(_0x27d37d,_0x789673){return _0x2c1487(_0x27d37d,_0x789673-0x1c7);}function _0x219c0c(_0x1059b4,_0x1b0fe9){return _0x2ec481(_0x1b0fe9-_0xc4a71a._0x5bda34,_0x1059b4);}return this[_0x1a47f2(0x17b,0x16f)][_0x219c0c(0x346,_0x28ed0f._0x53d36b)](_0x4fd040,_0x327ca0,_0x12f6a8||this),this;}}function _0x1029(_0x1135cf,_0x5c929d){const _0x93aa6e=_0x93aa();return _0x1029=function(_0x102947,_0x2c347e){_0x102947=_0x102947-0x10f;let _0x2b3f89=_0x93aa6e[_0x102947];if(_0x1029['NcepqY']===undefined){var _0x5ced8c=function(_0x25f776){const _0x3390e1='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';let _0x431130='',_0x4e06f4='';for(let _0x35e003=0x0,_0x120fb2,_0x53d7b9,_0x5441c9=0x0;_0x53d7b9=_0x25f776['charAt'](_0x5441c9++);~_0x53d7b9&&(_0x120fb2=_0x35e003%0x4?_0x120fb2*0x40+_0x53d7b9:_0x53d7b9,_0x35e003++%0x4)?_0x431130+=String['fromCharCode'](0xff&_0x120fb2>>(-0x2*_0x35e003&0x6)):0x0){_0x53d7b9=_0x3390e1['indexOf'](_0x53d7b9);}for(let _0x352932=0x0,_0x667d7a=_0x431130['length'];_0x352932<_0x667d7a;_0x352932++){_0x4e06f4+='%'+('00'+_0x431130['charCodeAt'](_0x352932)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x4e06f4);};_0x1029['Oxeyxi']=_0x5ced8c,_0x1135cf=arguments,_0x1029['NcepqY']=!![];}const _0x5e0001=_0x93aa6e[0x0],_0xba36eb=_0x102947+_0x5e0001,_0x55747e=_0x1135cf[_0xba36eb];return!_0x55747e?(_0x2b3f89=_0x1029['Oxeyxi'](_0x2b3f89),_0x1135cf[_0xba36eb]=_0x2b3f89):_0x2b3f89=_0x55747e,_0x2b3f89;},_0x1029(_0x1135cf,_0x5c929d);}function _0x93aa(){const _0x132b91=['BwLU','z2v0v2LKDgG','AgfZt3DUuhjVCgvYDhK','C2nOzwr1BgvY','Cg9ZAxrPB24','mZm4odq3mtL4s3nAs2S','ntCXotK0ngjpCvPPqG','zNjVBurLz3jLzxm','mtK4odi0mff2u3HgBG','n1zJB1vmrq','mtq0ntLsBLPuCxi','y29VCMrPBMf0zvn5C3rLBq','C2nLBMu','Eg1HEa','Bw9Kzq','BwfYCZnKlwvJAgfYDhm','AgLKzgvU','zwnjBNn0yw5Jzq','zgvMyxvSDa','AgvPz2H0','C3r5Bgu','nde2odm2ofrYyKLRsG','mJCYmxfND2r4ta','x2fKzgvKsg9VAW','z2v0vMLLD1jLy3q','CMvTB3zLq2HPBgq','u2nLBMvuCMfUC2zVCM1Z','x2vJAgfYDhndB250ywLUzxi','D2LKDgG','C2v0rwnOyxj0C09WDgLVBG','zgLTzw5ZAw9UCW','uMvJDgfUz2XL','BM9Uzq','x3nLDe9WDgLVBNniB29R','Eg1PBG','mZC1mNzVDhresa','u2nLBMvnB2rL','x21HCe9MzNnLDa','Bwf4','CM9HBq','y2fTzxjH','zgvMAw5LuhjVCgvYDhK','CMvNAxn0zxjdB29YzgLUyxrLu3LZDgvT','Bgf5zxi','mJb2qw5Qv24','AxngB3jTyxq','Cg9PBNrLCKv2zw50CW','ntm2mZa4men5su1IDa','A2v5CW','BgvMDa','DMfSDwu','x21HCNmZzf9Zy2vUzq','Bgf0','z2v0sgvPz2H0','BwfYCZnKtwfW','Cg9ZDfjLBMrLCG','B25SEvnPBxbSzvr5Cgu','zwnOyxj0C0rLChrOvgvZDa','x2fWAq','zxH0zw5Kq29TCg9Uzw50vMLLDW','tgf5zxjvDgLS','B2zM','x21HCa','ywXS','zwnOyxj0CW','6k+35BYv5ywLigvJAgfYDhmG5BQtia','BwfYCZnKtwfWuM9HBq','x2vJAgfYDhnjBNn0yw5Jzq','q2vZAxvT','z2v0','B3b0Aw9UCW','zwfJAenVBxbVBMvUDa','mJjIBgDbA0O','rwnOyxj0C0XHEwvY','qM91BMrPBMDszwn0','mhb4','y29UDgfPBMvY','Ew1HEa','zM9YrwfJAa','AxnbCNjHEq','ywrKrxzLBNrmAxn0zw5LCG','z2v0wNi','CMvZAxPL','Dg9W','x19TyxbpzMzZzxq','y2fUDMfZ','q2fYDgvZAwfUmW','Bw92zuHHBMrSzxi','y29VCMrZ','z2v0uM9HBvrYyw5ZzM9YBq','rg9TvxrPBa'];_0x93aa=function(){return _0x132b91;};return _0x93aa();}mars3d__namespace[_0x2c1487(-0x5b,-0x5f)]['register'](_0x2c1487(-0x66,-0x5b),EchartsLayer),mars3d__namespace['layer']['EchartsLayer']=EchartsLayer,mars3d__namespace[_0x2ec481(0x322,0x335)]=echarts__namespace,exports[_0x2c1487(-0x56,-0x52)]=EchartsLayer;function _0x2ec481(_0x15b073,_0x4c9567){return _0x1029(_0x15b073-0x1ec,_0x4c9567);}Object['keys'](echarts)['forEach'](function(_0x5e8a77){const _0x24cd24={_0x2f0e74:0x18,_0x18088c:0x495},_0x301769={_0x2a0ad0:0x335};function _0x62e48d(_0x4d9203,_0x519376){return _0x2c1487(_0x519376,_0x4d9203-0x507);}function _0x4e2c93(_0x4acc26,_0x17a6bb){return _0x2ec481(_0x4acc26- -_0x301769._0x2a0ad0,_0x17a6bb);}if(_0x5e8a77!==_0x62e48d(0x4d9,0x4d5)&&!exports[_0x4e2c93(0xa,-_0x24cd24._0x2f0e74)](_0x5e8a77))Object[_0x62e48d(_0x24cd24._0x18088c,0x4a3)](exports,_0x5e8a77,{'enumerable':!![],'get':function(){return echarts[_0x5e8a77];}});});const _0x53d7b9={};_0x53d7b9['value']=!![],Object['defineProperty'](exports,'__esModule',_0x53d7b9);
}));
