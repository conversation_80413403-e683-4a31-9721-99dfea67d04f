import axios from 'axios'
import mitt from "@/utils/mitt";
var timeout = null
var tt = null
var lockReconnect = false;//避免重复连接

var isLogin = false
var indexC = 0
var userInfo
var subscribeList = []
var reConnect = 0

class SocketUtil {
    constructor() {

    }

    login() {

        let that = this;
        //否则重新登录获取新token


        const token = localStorage.getItem("token")
        const headers = { 'content-type': 'application/json', Authorization: token }
        // console.log(headers);
        axios({
            method: 'POST',
            url: import.meta.env.VITE_BASE_SOCKET_LOGIN,
            timeout: 12000,
            headers: headers
        }).then(function (res) {
            // console.log(res)

            if (res.data.code == 0) {
                reConnect = 0
                let expires_time = new Date().getTime() + parseInt(res.data.result.expires_in * 0.8) * 1000;
                let token = JSON.parse(that.getToken()) == null ? {} : JSON.parse(that.getToken());
                token.expires_time = expires_time;
                token.access_token = res.data.result.access_token;
                token.refresh_token = res.data.result.refresh_token;

                sessionStorage.setItem("wdcy_socketToken", JSON.stringify(token));

                loginStatus = 0

                that.createSocket();
            } else {
                lockReconnect = false;
                that.reconnectSocket()
            }
        }).catch(function (err) {
            // console.log('socket login error')

            lockReconnect = false;
            that.reconnectSocket()

        });

    }

    connect(info) {
        // console.log(info);
        let that = this;

        userInfo = info

        if (!userInfo) return

        if (!that.isTokenExpired(that.getToken())) {
            //token没过期的话 直接用token链接socket
            reConnect = 1
            that.createSocket();
            return
        } else {
            // console.log('login1');
            that.login()
        }
    }

    //判断token是否过期
    isTokenExpired(token) {

        if (!token) return true
        let expires_time = JSON.parse(token).expires_time;
        let curentTime = new Date().getTime();
        if (curentTime >= expires_time) {

            return true;
        } else {

            return false;
        }
    }

    //获取Token对象
    getToken() {
        return sessionStorage.getItem("wdcy_socketToken") ? sessionStorage.getItem("wdcy_socketToken") : null;
    }

    createSocket() {
        // reConnect 1重连 0新链接

        if (this.socket) {
            return
        }
        let token = JSON.parse(this.getToken());
        let url = import.meta.env.VITE_BASE_SOCKET_WS + '?client=manager&Authorization=' + token.access_token + '&uid=' + userInfo.id +
            '&reConnect=' + reConnect;
        this.socket = new WebSocket(url);
        this.socket.onmessage = (res) => {

            let dict = JSON.parse(res.data);
            if (dict.dataContent == 'pong') {
                this.startHeartBeat()
                return
            }

            if ((dict.topic == 'touristFlow' ||  //人流
                dict.topic == 'vehicleFlow' ||   //车流
                dict.topic == 'warnEvent' ||     //事件预警
                dict.topic == 'deviceEvent' ||   //设备预计
                dict.topic == 'online-status' || //在线人数 对象
                dict.topic == 'online-status-list' ||  //在线人数列表
                dict.topic == 'user-offline' ||  //在线人数列表 list

                dict.topic == 'deviceStatus') &&  //设备状态
                (dict.dataContent != null && dict.dataContent != 'null')) {
                indexC++
                console.log('indexC', indexC)
                // console.log(dict)

                mitt.emit(dict.topic, dict);

            }
        }
        this.socket.onopen = (res) => {

            // console.log('socket onopen')

            isLogin = true

            lockReconnect = false;

            this.startHeartBeat()

            if (subscribeList.length > 0) {

                subscribeList.forEach(element => {

                    let para = JSON.stringify({
                        'topic': 'subscribe',
                        'dataContent': element
                    })

                    this.socket.send(para)

                });

            }

        }
        this.socket.onerror = (res) => {

            lockReconnect = false;
            // console.log('socket onerror')
            if (isLogin)
                this.reconnectSocket()
        }
        this.socket.onclose = (res) => {

            lockReconnect = false;

            // console.log('socket onclose')
            if (isLogin)
                this.reconnectSocket()
        }
    }

    subscribe(subscribeArr) {
        // if (!isLogin || !this.socket) return
        // //订阅数据类型（人流数量  车流数量  预警事件 设备事件）
        // let para = JSON.stringify({
        //     'topic': 'subscribe',
        //     'dataContent': subscribeArr
        // })
        // this.socket.send(para)
        // subscribeList.push(subscribeArr)
        //  
        var data = {
            client: "manager",
            subscribes: subscribeArr
        }
        const token = localStorage.getItem("token")
        // console.log(token,data);
        const headers = { 'content-type': 'application/json', Authorization: token }
        axios({
            method: 'POST',
            data: data,
            url: import.meta.env.VITE_BASE_API + '/push-server/subscribe',
            timeout: 12000,
            headers: headers
        }).then(function (res) {
            // console.log(res)
            let code = res.data.code

            //  
            // console.log('code', code)
            switch (code) {

                case -1:
                    if (subscribeArr[0].topic = 'online-status') {
                        mitt.emit('online-status-failed');
                    }
                    break;

                case 0:
                    subscribeList.push(subscribeArr)
                    break;
                case 200:
                    // other handlers
                    subscribeList.push(subscribeArr)
                    break
                case 503 || 500:

                    break

                case 401:

                    // 用户未认证
                    break

                case 402:
                    //推送未认证

                    break
                default:


            }

        }).catch(function (err) {

            console.log('socket subscribe error2')
        });


    }

    unsubscribe(subscribeArr) {
        if (!isLogin || !this.socket) return
        //订阅数据类型（人流数量  车流数量   预警事件（火警 高空）  设备事件  设备状态）
        // let para = JSON.stringify({
        //     'dataType': 'unsubscribe',
        //     'topic': dataType,
        //     'sendCode': 'wdcy',
        //     'tag': tag,
        //     'keyId': communityId
        // })

        let para = JSON.stringify({
            'topic': 'unsubscribe',
            'dataContent': subscribeArr
        })


        subscribeArr.forEach(element => {

            for (let index = 0; index < subscribeList.length; index++) {

                if (subscribeList[index] == element)

                    subscribeList.splice(index, 1)
            }
        });

        // console.log('subscribeList', subscribeList)

        this.socket.send(para)

    }

    sendMessage(msg) {
        this.socket.send(msg);
    }

    startHeartBeat() {
        let that = this
        timeout = setTimeout(function () {
            let para = JSON.stringify({ 'topic': 'ping' })
            that.socket.send(para)
        }, 30000)
    }

    reconnectSocket() {
        // console.log('reconnectSocket')

        var that = this
        if (lockReconnect) {
            return;
        };
        lockReconnect = true;

        clearTimeout(timeout)
        if (this.socket)
            this.socket.close()
        this.socket = null

        //没连接上会一直重连，设置延迟避免请求过多
        tt && clearTimeout(tt);
        tt = setTimeout(function () {

            that.connect(userInfo)

        }, 1000 * 10);

    }

    closeSocket() {
        //  
        isLogin = false
        if (this.socket) {
            // this.unsubscribe('touristFlow')
            // this.unsubscribe('vehicleFlow')
            // this.unsubscribe('warnEvent')
            // this.unsubscribe('deviceEvent')
            // this.unsubscribe('deviceStatus')
            clearTimeout(timeout)
            this.socket.close()
            this.socket = null
            tt && clearTimeout(tt);
        }
    }

}



const socketUtil = new SocketUtil()

export default socketUtil