<template>
  <map-select-point @point="point" @addressCall="setAddress"></map-select-point>
  <mars-map @point="point"></mars-map>
  <el-dialog
    draggable
    width="50%"
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form :rules="rules" :model="dataModel" label-width="120px">
      <el-row>
        <el-col :span="18">
          <el-form-item label="车流信息名称" prop="name">
            <el-input
              v-model="dataModel.name"
              placeholder="车流信息名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="车流信息描述" prop="note">
            <el-input
              type="textarea"
              v-model="dataModel.note"
              placeholder="车流信息描述"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="经纬度" prop="lng">
            <el-input
              style="width: 45%"
              v-model="dataModel.lng"
              placeholder="经度"
            ></el-input
            >&nbsp;-&nbsp;
            <el-input
              style="width: 45%"
              v-model="dataModel.lat"
              placeholder="纬度"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label-width="0">
            <el-button
              type="text"
              style="font-size: 30px; padding: 0"
              @click="selectPoint"
            >
              <el-icon :size="30">
                <location-filled></location-filled>
              </el-icon>
            </el-button>
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="进出类型" prop="flowType">
            <el-select
              style="width: 100%"
              v-model="dataModel.flowType"
              placeholder="进出类型"
            >
              <el-option
                v-for="item in flowTypeList"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="parseInt(item.nameEn)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="end">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>

<script>
import {
  vehiclePointListAdd,
  vehiclePointListEdit,
} from "@/api/situation/situation";
import mitt from "@/utils/mitt";
import { openMap } from "@/utils/myUtils";
import mapSelectPoint from "@/componts/map/mapSelectPoint.vue";
import { openMarsMap } from "@/utils/myUtils";
import marsMap from "@/componts/map/marsMap.vue";
import { getCommunity } from "@/api/base/community";
export default {
  props: ["statusList", "typeList", "flowTypeList"],
  components: { mapSelectPoint, marsMap },
  data() {
    return {
      loading: false,
      dataModel: {},
      dialog: {},
      communityId: localStorage.getItem("communityId"),
      rules: {
        name: [
          {
            required: true,
            message: "请输入车流信息名称",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    selectPoint() {
      // openMap(mitt, true, [this.dataModel.lng, this.dataModel.lat], this.dataModel.note); //this.dataModel.address

      var communityId = localStorage.getItem("communityId");
      getCommunity(communityId)
        .then((res) => {
          var result = res.data.result;
          var config = JSON.parse(result.expandParams);
          var mode = config.map.mode;

          var center;
          var rotationSet = { x: 0, y: 0, z: 0 };
          var scaleSet = 1;
          var showBaseMap = false;
          var position;
          var modeUrl;

          if (result.enabled3d && mode == "mars3d") {
            position = config.map.sdgis.position;

            modeUrl = config.map.sdgis.tdtile;

            try {
              rotationSet = config.map.sdgis.rotation;
            } catch (error) {}

            try {
              scaleSet = config.map.sdgis.scale;
            } catch (error) {}

            try {
              showBaseMap = config.map.sdgis.showBaseMap;
            } catch (error) {}
          }

          if (result.lng && result.lat) {
            center = {
              lng: result.lng,
              lat: result.lat,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          } else {
            center = {
              lng: 120.645728,
              lat: 31.138525,
              alt: 500,
              heading: 0,
              pitch: -90,
            };
          }

          var data = {
            enabled3d: result.enabled3d,
            edit: true,
            point: [this.dataModel.lng, this.dataModel.lat, this.dataModel.alt],
            position: position,
            center: center,
            modeUrl: modeUrl,
            title: "地图选点",
            rotationSet,
            scaleSet,
            showBaseMap: showBaseMap,
          };
          console.log(data);

          openMarsMap(mitt, data);
        })
        .catch((err) => {});
    },
    point(e) {
      this.dataModel.lng = e[0];
      this.dataModel.lat = e[1];
    },
    setAddress(e) {
      this.dataModel.note = e.regeocode.formattedAddress;
    },
    onSubmit() {
      this.dataModel.communityId = this.communityId;
      if (this.dataModel.id == 0) {
        vehiclePointListAdd(this.dataModel).then((res) => {
          this.$message.success(res.data.msg);
          this.$emit("search");
          this.dialog.show = false;
        });
      } else {
        vehiclePointListEdit(this.dataModel).then((res) => {
          this.$message.success(res.data.msg);
          this.$emit("search");
          this.dialog.show = false;
        });
      }
    },
  },
  mounted() {
    mitt.on("openVehiclePointEdit", (menu) => {
      this.dataModel = menu;
      this.dialog.show = true;
      this.dialog.title = "修改";
    });
    mitt.on("openVehiclePointAdd", (id) => {
      this.dataModel = {
        id: 0,
      };
      this.dialog.show = true;
      this.dialog.title = "添加";
    });
  },
  created() {
    mitt.off("openMarsMap");
  },
};
</script>
