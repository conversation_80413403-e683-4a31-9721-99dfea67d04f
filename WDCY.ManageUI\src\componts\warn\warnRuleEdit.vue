<template>
  <action-rule-edit :icoList="icoList" @search="search"></action-rule-edit>
  <condition-rule-edit @search="search"></condition-rule-edit>
  <el-dialog draggable
    width="50%"
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-row :gutter="20">
      <el-icon style="margin: 3px 5px 0 0">
        <!-- <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-ba633cb8=""><path fill="currentColor" d="M389.44 768a96.064 96.064 0 0 1 181.12 0H896v64H570.56a96.064 96.064 0 0 1-181.12 0H128v-64h261.44zm192-288a96.064 96.064 0 0 1 181.12 0H896v64H762.56a96.064 96.064 0 0 1-181.12 0H128v-64h453.44zm-320-288a96.064 96.064 0 0 1 181.12 0H896v64H442.56a96.064 96.064 0 0 1-181.12 0H128v-64h133.44z"></path></svg> -->
        <svg
          viewBox="0 0 1024 1024"
          xmlns="http://www.w3.org/2000/svg"
          data-v-ba633cb8=""
        >
          <path
            fill="currentColor"
            d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704z"
          ></path>
        </svg>
      </el-icon>
      <div style="margin-bottom: 20px" v-if="this.conditionType === 'AND'">
        <b>如果所有条件满足</b>
      </div>
      <div style="margin-bottom: 20px" v-if="this.conditionType === 'OR'">
        <b>如果任一条件满足</b>
      </div>
      <el-table :data="eventDetectRule" border style="width: 100%">
        <el-table-column prop="name" width="180" align="left" label="事件名称" />
        <!-- <el-table-column prop="note" align="center" label="检测规则描述" /> -->
        <el-table-column prop="note" align="left" label="检测规则描述">
          <template #default="scope">
            <span>{{ getDetectRuleNote(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="repeatFilterDuration" align="center" label="重复过滤时长" width="118" >
          <template #default="scope">
            <div style="display: flex; align-items: center">
              <el-icon><timer /></el-icon>
              <span style="margin-left: 10px">{{ scope.row.repeatFilterDuration }}秒</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" width="100" label="操作" v-if="hasPerm('warn:warnRule:update') || hasPerm('warn:warnRule:delete')">
          <template #default="scope">
            <el-button
              v-if="hasPerm('warn:warnRule:update')"
              type="text"
              size="default"
              @click="editCondition(scope.row.id, scope.row)"
              >编辑</el-button
            >
            <el-button
            v-if="hasPerm('warn:warnRule:delete')"
              type="text"
              size="default"
              @click="deletedCondition(scope.row.conditionId)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div>
        <el-button type="text" size="default" @click="addCondition()"
        v-if="hasPerm('warn:warnRule:add')"
          >继续添加条件</el-button
        >
      </div>
    </el-row>
    <el-row><br /></el-row>
    <el-row :gutter="20">
      <el-icon style="margin: 3px 5px 0 0">
        <svg
          viewBox="0 0 1024 1024"
          xmlns="http://www.w3.org/2000/svg"
          data-v-ba633cb8=""
        >
          <path
            fill="currentColor"
            d="M389.44 768a96.064 96.064 0 0 1 181.12 0H896v64H570.56a96.064 96.064 0 0 1-181.12 0H128v-64h261.44zm192-288a96.064 96.064 0 0 1 181.12 0H896v64H762.56a96.064 96.064 0 0 1-181.12 0H128v-64h453.44zm-320-288a96.064 96.064 0 0 1 181.12 0H896v64H442.56a96.064 96.064 0 0 1-181.12 0H128v-64h133.44z"
          ></path>
        </svg>
        <!-- <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-ba633cb8=""><path fill="currentColor" d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704z"></path></svg> -->
      </el-icon>
      <div style="margin-bottom: 20px"><b>就执行</b></div>
      <el-table :data="strategyExecute" border style="width: 100%">
        <el-table-column label="图标" align="center" prop="icoUrl" width="75">
            <template #default="scope">
                <el-image style="width:45px;height:45px;background: #aaa;" :src="imgServer+formatImg(scope.row.icoInfoId)" fit="contain"></el-image>
            </template>
        </el-table-column>
        <el-table-column
          prop="actionType"
          width="100"
          align="center"
          label="动作类型"
        >
          <template #default="scope">
            <div v-if="scope.row.actionType === 0">建议提醒</div>
            <div v-if="scope.row.actionType === 1">设施动作</div>
          </template>
        </el-table-column>
        <el-table-column width="86" align="center" label="延迟时间">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              <el-icon><timer /></el-icon>
              <span style="margin-left: 10px">{{ scope.row.delay }}秒</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="86" align="center" label="弹窗时间">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              <el-icon><timer /></el-icon>
              <span style="margin-left: 10px">{{ scope.row.popTime }}秒</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sequence"
          align="center"
          width="86"
          label="执行顺序"
        />
        <el-table-column prop="warnMsg" align="center" label="建议提醒" />
        <el-table-column
          prop="deviceName"
          align="center"
          label="动作设施名称"
        />
        <el-table-column prop="name" align="center" label="动作名称" />
        <el-table-column align="center" width="100" label="操作" v-if="hasPerm('warn:warnAction:update') || hasPerm('warn:warnAction:add')">
          <template #default="scope">
            <el-button type="text" size="default" @click="editAction(scope.row.id)"
            v-if="hasPerm('warn:warnAction:update')"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="deletedAction(scope.row.id)"
              v-if="hasPerm('warn:warnAction:delete')"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div>
        <el-button type="text" size="default" @click="addAction()"
        v-if="hasPerm('warn:warnAction:add')"
          >继续添加动作</el-button
        >
      </div>
    </el-row>
  </el-dialog>
</template>

<script>
import {
  warnRuleList,
  warnStrategyConditionDelete,
  getWarnStrategyExecute,
  warnStrategyExecuteDelete,
} from "@/api/warn/warnStrategy";
import { getWarnEventSourceInfo } from "@/api/warn/warn";
import { listIcon } from "@/api/base/icon"
import actionRuleEdit from "@/componts/warn/actionRuleEdit.vue";
import conditionRuleEdit from "@/componts/warn/conditionRuleEdit.vue";
import mitt from "@/utils/mitt";
import { getWarnDetectRuleNote } from "@/utils/myUtils";

export default {
  props: ["statusList", "tagList"],
  components: { actionRuleEdit, conditionRuleEdit },
  data() {
    return {
      loading: false,
      imgServer: import.meta.env.VITE_BASE_API,
      communityId: localStorage.getItem("communityId"),
      personModel: {},
      deviceTypeList: [],
      eventTypeList: [],
      deviceList: [],
      actionList: [],
      conditionType: "",
      actionVisible: false,
      conditionVisible: false,
      strategyExecuteList: [],
      dialog: {},
      strategyId: null,
      eventDetectRule: {},
      strategyExecute: {},
    };
  },
  methods: {
    formatImg(icoId){
        let result = "";
        this.icoList.forEach(element => {
            if (element.id == icoId) {
                result = element.icoUrl
            }
        })
        return result;
    },
    search() {
      warnRuleList(this.strategyId).then((res) => {
        this.eventDetectRule = res.data.result.warnEventSourceInfoVOList;
        this.strategyExecute = res.data.result.warnStrategyExecuteList;
      });
    },
    // 添加条件
    addCondition() {
      mitt.emit("openConditionRuleAdd", { id: this.personModel.id });
    },
    editCondition(id, row) {
      getWarnEventSourceInfo(id).then((res) => {
        mitt.emit("openConditionRuleEdit", {
          data: res.data.result,
          id: this.personModel.id,
          conditionId: row.conditionId,
          repeatFilterDuration: row.repeatFilterDuration
        });
      });
    },
    deletedCondition(id) {
      this.$confirm("删除条件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          warnStrategyConditionDelete(id)
            .then((res) => {
              this.search();
              this.$message.success(res.data.msg);
            })
        })
        .catch(() => {});
    },
    // 添加动作
    addAction() {
      mitt.emit("openActionRuleAdd", { strategyId: this.strategyId });
    },
    // 动作修改
    editAction(id) {
      getWarnStrategyExecute(id).then((res) => {
        mitt.emit("openActionRuleEdit", {
          data: res.data.result,
          strategyId: this.strategyId,
        });
      });
    },
    deletedAction(id) {
      this.$confirm("删除动作, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          warnStrategyExecuteDelete(id)
            .then((res) => {
              this.search();
              this.$message.success(res.data.msg);
            })
        })
        .catch(() => {});
    },
    // 检测规则描述
    getDetectRuleNote(row) {
      return getWarnDetectRuleNote(row.note, row.warnEventDetectRules);
    },
    async init(){
      let icoList = await listIcon({pageSize: 9999,icoCategory:"ico_warn"})
      this.icoList = icoList.data.result.list
    }
  },
  created(){
    this.init()
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openWarnRuleEdit", (person) => {
        this.personModel = person.data;
        this.conditionType = person.conditionType;
        this.eventDetectRule = person.data.warnEventSourceInfoVOList;
        this.strategyExecute = person.data.warnStrategyExecuteList;
        this.strategyId = person.data.id;
        this.dialog.show = true;
        this.dialog.title = "修改";
      });
    });
  },
};
</script>
