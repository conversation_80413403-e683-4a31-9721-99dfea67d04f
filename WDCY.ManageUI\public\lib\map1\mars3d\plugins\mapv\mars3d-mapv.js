/**
 * Mars3D平台插件,结合mapv可视化功能插件  mars3d-mapv
 *
 * 版本信息：v3.8.13
 * 编译日期：2025-01-09 16:08
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：火星科技免费公开版 ，2024-08-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mapv || require('mapv')), (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mapv', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-mapv"] = {}, global.mapv, global.mars3d));
})(this, (function (exports, mapv, mars3d) { 
'use strict';(function(_0x59dd76,_0x3dc8dd){const _0x25108d={_0x58b037:0x468,_0x117ce6:0x46e,_0x58cf2a:0x4ac,_0x9e36d:0x214,_0x212312:0x49e,_0x5f1beb:0x200,_0x47c5c2:0x16c,_0x16b91e:0x1ac,_0x449113:0x19e},_0x1b32d9={_0x3d33b3:0x2b8};function _0x42e089(_0x16ff4c,_0x43950a){return _0x49da(_0x43950a- -0x38c,_0x16ff4c);}function _0x5e76c9(_0x28128f,_0x22bd78){return _0x49da(_0x22bd78-_0x1b32d9._0x3d33b3,_0x28128f);}const _0x511cc1=_0x59dd76();while(!![]){try{const _0x2db932=parseInt(_0x5e76c9(0x459,0x42e))/0x1+-parseInt(_0x5e76c9(_0x25108d._0x58b037,_0x25108d._0x117ce6))/0x2+parseInt(_0x5e76c9(_0x25108d._0x58cf2a,0x496))/0x3+-parseInt(_0x42e089(-_0x25108d._0x9e36d,-0x205))/0x4*(parseInt(_0x5e76c9(0x478,0x47b))/0x5)+parseInt(_0x5e76c9(0x49f,_0x25108d._0x212312))/0x6+parseInt(_0x42e089(-_0x25108d._0x5f1beb,-0x20e))/0x7+-parseInt(_0x42e089(-_0x25108d._0x47c5c2,-_0x25108d._0x16b91e))/0x8*(-parseInt(_0x42e089(-0x1af,-_0x25108d._0x449113))/0x9);if(_0x2db932===_0x3dc8dd)break;else _0x511cc1['push'](_0x511cc1['shift']());}catch(_0x42cb32){_0x511cc1['push'](_0x511cc1['shift']());}}}(_0x5f77,0xded45));function _interopNamespace(_0x3dcff0){const _0x21ed79={_0x38c104:0x10,_0x5ed8cc:0x36,_0xbe55b9:0xb,_0x3e4d53:0x7},_0x3103d9={_0x2d49f0:0x1dd},_0x558fed={_0x335161:0x33};function _0x237ed9(_0x54b4e4,_0x20525f){return _0x49da(_0x54b4e4-0x26a,_0x20525f);}if(_0x3dcff0&&_0x3dcff0[_0x443e2c(_0x21ed79._0x38c104,-_0x21ed79._0x5ed8cc)])return _0x3dcff0;var _0x2f09bf=Object[_0x237ed9(0x409,0x3f4)](null);_0x3dcff0&&Object['keys'](_0x3dcff0)['forEach'](function(_0x53aaa1){function _0x81c4eb(_0x219ea6,_0x318477){return _0x443e2c(_0x219ea6-0x34,_0x318477);}function _0x7ddf74(_0x4f8855,_0x438f95){return _0x237ed9(_0x438f95- -0x161,_0x4f8855);}if(_0x53aaa1!=='default'){var _0x155060=Object['getOwnPropertyDescriptor'](_0x3dcff0,_0x53aaa1);Object[_0x81c4eb(0xe,_0x558fed._0x335161)](_0x2f09bf,_0x53aaa1,_0x155060[_0x81c4eb(-0x35,-0x55)]?_0x155060:{'enumerable':!![],'get':function(){return _0x3dcff0[_0x53aaa1];}});}});function _0x443e2c(_0x480a4a,_0x581298){return _0x49da(_0x480a4a- -_0x3103d9._0x2d49f0,_0x581298);}return _0x2f09bf[_0x443e2c(_0x21ed79._0xbe55b9,_0x21ed79._0x3e4d53)]=_0x3dcff0,_0x2f09bf;}var mapv__namespace=_interopNamespace(mapv),mars3d__namespace=_interopNamespace(mars3d);function _0x4b25e5(_0x4abfba,_0x46393a){return _0x49da(_0x4abfba- -0x346,_0x46393a);}const Cesium$1=mars3d__namespace[_0x17944e(0x26a,0x261)],baiduMapLayer=mapv__namespace?mapv__namespace['baiduMapLayer']:null,BaseLayer$1=baiduMapLayer?baiduMapLayer['__proto__']:Function;class MapVRenderer extends BaseLayer$1{constructor(_0x3a025c,_0x2255e3,_0x10e82f,_0x1b2fd6){const _0x316cc4={_0x1644db:0x23d,_0x4a326b:0x247},_0x480860={_0x25b13a:0x40};super(_0x3a025c,_0x2255e3,_0x10e82f);if(!BaseLayer$1)return;function _0x4b09d1(_0x334f01,_0xe89067){return _0x17944e(_0x334f01-0x129,_0xe89067);}function _0x5d507a(_0x421ae6,_0x4578a3){return _0x17944e(_0x421ae6- -_0x480860._0x25b13a,_0x4578a3);}this[_0x5d507a(0x242,_0x316cc4._0x1644db)]=_0x3a025c,this['scene']=_0x3a025c['scene'],this[_0x5d507a(0x232,0x247)]=_0x2255e3,_0x10e82f=_0x10e82f||{},this['init'](_0x10e82f),this['argCheck'](_0x10e82f),this['initDevicePixelRatio'](),this[_0x4b09d1(0x3da,0x3ef)]=_0x1b2fd6,this[_0x5d507a(_0x316cc4._0x4a326b,0x25d)]=!0x1,this['animation']=_0x10e82f['animation'];}['initDevicePixelRatio'](){const _0x52ded8={_0x1f64d9:0x9b,_0x3bdff4:0x402},_0x3f0c48={_0x1befe9:0x1a2};function _0x100976(_0x498fa2,_0x5e7649){return _0x17944e(_0x5e7649- -0x2ef,_0x498fa2);}function _0xe34697(_0x2d33ae,_0x3e0e74){return _0x17944e(_0x2d33ae-_0x3f0c48._0x1befe9,_0x3e0e74);}this[_0x100976(-_0x52ded8._0x1f64d9,-0x8f)]=window[_0xe34697(_0x52ded8._0x3bdff4,0x415)]||0x1;}['addAnimatorEvent'](){}['animatorMovestartEvent'](){const _0x1202e9={_0x19a006:0x60,_0x48f496:0xb5,_0x5802cb:0xa1,_0x517a80:0x5d},_0x1f099b={_0x400816:0x238},_0x512a77=this[_0x521e8a(0x85,_0x1202e9._0x19a006)][_0x521e8a(0xb2,0xaa)];function _0x1ba425(_0x283b81,_0x2df967){return _0x17944e(_0x2df967- -_0x1f099b._0x400816,_0x283b81);}function _0x521e8a(_0x221a8c,_0x3321a9){return _0x17944e(_0x3321a9- -0x201,_0x221a8c);}this[_0x521e8a(_0x1202e9._0x48f496,_0x1202e9._0x5802cb)]()&&this['animator']&&(this[_0x521e8a(0x7a,0xa5)][_0x1ba425(_0x1202e9._0x517a80,0x41)]=_0x512a77['stepsRange']['start']);}[_0x17944e(0x24e,0x22a)](){const _0x388337={_0x5aa4d6:0x575};function _0x5dee9d(_0x30ef53,_0x5d89cf){return _0x17944e(_0x30ef53-0x2d3,_0x5d89cf);}this[_0x5dee9d(_0x388337._0x5aa4d6,0x5bb)]()&&this['animator'];}[_0x17944e(0x28b,0x299)](){const _0x3939a3={_0x34e565:0x403,_0x539146:0x4c1},_0xe09aab={_0x4eff61:0x1b5};function _0x49a5fb(_0x15d27c,_0x97de54){return _0x17944e(_0x15d27c- -0x100,_0x97de54);}function _0x2bf462(_0x2bb6b7,_0x2c641e){return _0x17944e(_0x2c641e-_0xe09aab._0x4eff61,_0x2bb6b7);}return this['canvasLayer']['canvas'][_0x2bf462(_0x3939a3._0x34e565,0x440)](this[_0x2bf462(_0x3939a3._0x539146,0x47b)]);}[_0x4b25e5(-0x1ac,-0x17a)](_0x75eb3b){const _0x155cc9={_0x2177ce:0x17b,_0x494205:0x18f,_0x488a69:0x17b,_0x577a7c:0xbf},_0x153e45={_0x33a705:0xb7},_0x4b64e5={_0x1dd568:0x3dd};function _0x53aeab(_0x265ca2,_0x385660){return _0x17944e(_0x265ca2- -_0x4b64e5._0x1dd568,_0x385660);}this[_0x53aeab(-0x17c,-_0x155cc9._0x2177ce)]=_0x75eb3b,this[_0x53aeab(-0x14f,-_0x155cc9._0x494205)](_0x75eb3b),this['context']=this['options']['context']||'2d';function _0x231793(_0x53aeb9,_0x2de6dc){return _0x4b25e5(_0x53aeb9-_0x153e45._0x33a705,_0x2de6dc);}Cesium$1[_0x231793(-0x121,-0x15a)](this['options'][_0x53aeab(-_0x155cc9._0x488a69,-0x13f)])&&this[_0x231793(-_0x155cc9._0x577a7c,-0xb1)]&&this['canvasLayer']['setZIndex']&&this['canvasLayer']['setZIndex'](this['options']['zIndex']),this['initAnimator']();}['_canvasUpdate'](_0x3a1e84){const _0x17cd20={_0x47cbcc:0x439,_0x5ef9db:0x42a,_0x5e61fc:0x462,_0x59f678:0x441,_0x6ad79c:0x1d7,_0x581e82:0x191,_0x21f055:0x41f,_0x1fe0d3:0x46d,_0x5fb151:0x18d,_0x23515c:0x150,_0x11e257:0x435,_0x1fa391:0x194,_0x329210:0x4a2,_0x4d8513:0x478,_0x2e7cfc:0x128,_0x1232ff:0x136,_0x4dc741:0x192,_0x342aeb:0x457,_0x495ba8:0x47a,_0x5799c0:0x49e,_0x4cfdb5:0x156,_0x2190ad:0x15c,_0x19f43d:0x13b,_0x496627:0x110,_0x441f04:0x15e,_0x313582:0x19d,_0x2368a5:0x451,_0x5c22fc:0x445,_0x2c3599:0x183,_0x465261:0x157,_0x8a4b03:0x482,_0x381d4f:0x151,_0x496821:0x15d},_0x3ed86d={_0x16e6d2:0x439,_0x1cd725:0x416,_0x7a3d7f:0x487,_0x482935:0x4b6,_0x934c32:0x456,_0xe55e0a:0x41e,_0x1f1634:0x3e0,_0x2d5665:0x7c},_0x2119a7={_0x5656fe:0x116},_0x1b5d1a={_0x4ce315:0x60b},_0xe720e4={_0xb2174b:0x35};if(!this['canvasLayer']||this['stopAniamation'])return;const _0x5aa80c=this['scene'];function _0x1934f0(_0x2f0260,_0x1c8693){return _0x4b25e5(_0x2f0260-_0xe720e4._0xb2174b,_0x1c8693);}const _0x982433=this['options']['animation'];function _0x1e0166(_0x544b06,_0x415190){return _0x4b25e5(_0x415190-_0x1b5d1a._0x4ce315,_0x544b06);}const _0x4c934e=this['getContext']();if(this['isEnabledTime']()){if(void 0x0===_0x3a1e84)return void this[_0x1934f0(-0x198,-0x186)](_0x4c934e);this['context']==='2d'&&(_0x4c934e['save'](),_0x4c934e['globalCompositeOperation']='destination-out',_0x4c934e[_0x1e0166(_0x17cd20._0x47cbcc,_0x17cd20._0x5ef9db)]='rgba(0,\x200,\x200,\x20.1)',_0x4c934e[_0x1e0166(_0x17cd20._0x5e61fc,_0x17cd20._0x59f678)](0x0,0x0,_0x4c934e[_0x1934f0(-0x158,-0x11d)][_0x1934f0(-0x1af,-_0x17cd20._0x6ad79c)],_0x4c934e['canvas']['height']),_0x4c934e[_0x1e0166(0x479,0x434)]());}else this[_0x1934f0(-0x198,-0x164)](_0x4c934e);if(this['context']==='2d')for(const _0x1cea76 in this['options']){_0x4c934e[_0x1cea76]=this['options'][_0x1cea76];}else _0x4c934e['clear'](_0x4c934e['COLOR_BUFFER_BIT']);const _0x59c41b={'transferCoordinate':function(_0x4acc61){const _0x4c3b45={_0x5c9216:0x5b7},_0x26dcb5=null;function _0x193387(_0x1d548d,_0x22669c){return _0x1934f0(_0x1d548d-_0x4c3b45._0x5c9216,_0x22669c);}let _0x46560c=_0x5aa80c['mapvFixedHeight'];_0x5aa80c[_0x277f59(-0xb,-0x52)]&&(_0x46560c=_0x5aa80c[_0x193387(0x44b,_0x3ed86d._0x16e6d2)](Cesium$1['Cartographic']['fromDegrees'](_0x4acc61[0x0],_0x4acc61[0x1])));const _0x40c39f=Cesium$1[_0x277f59(-0x47,-0x6d)][_0x277f59(-0x58,-0x60)](_0x4acc61[0x0],_0x4acc61[0x1],_0x46560c);function _0x277f59(_0x2a76e9,_0x2bfc7f){return _0x1934f0(_0x2bfc7f-_0x2119a7._0x5656fe,_0x2a76e9);}if(!_0x40c39f)return _0x26dcb5;const _0x574766=mars3d__namespace[_0x193387(_0x3ed86d._0x1cd725,0x458)][_0x193387(_0x3ed86d._0x7a3d7f,_0x3ed86d._0x482935)](_0x5aa80c,_0x40c39f);if(!_0x574766)return _0x26dcb5;if(_0x5aa80c[_0x193387(_0x3ed86d._0x934c32,0x48a)]&&_0x5aa80c['mode']===Cesium$1[_0x193387(_0x3ed86d._0xe55e0a,_0x3ed86d._0x1f1634)][_0x277f59(-0x78,-0x3d)]){const _0x57ddf0=new Cesium$1['EllipsoidalOccluder'](_0x5aa80c['globe']['ellipsoid'],_0x5aa80c['camera'][_0x277f59(-_0x3ed86d._0x2d5665,-0x40)]),_0x21c939=_0x57ddf0['isPointVisible'](_0x40c39f);if(!_0x21c939)return _0x26dcb5;}return[_0x574766['x'],_0x574766['y']];}};void 0x0!==_0x3a1e84&&(_0x59c41b[_0x1e0166(0x40b,0x429)]=function(_0x595e96){function _0x41647b(_0x4cfd93,_0x163d44){return _0x1e0166(_0x163d44,_0x4cfd93- -0x2f2);}const _0x138f0c=_0x982433[_0x41647b(0x17a,0x19b)]||0xa;return!!(_0x3a1e84&&_0x595e96['time']>_0x3a1e84-_0x138f0c&&_0x595e96['time']<_0x3a1e84);});let _0x5dea9c;if(this[_0x1934f0(-_0x17cd20._0x581e82,-0x1be)]['draw']==='cluster'&&(!this[_0x1e0166(_0x17cd20._0x21f055,0x445)][_0x1934f0(-0x140,-0x162)]||this['options'][_0x1934f0(-0x140,-0x124)]>=this['getZoom']())){this['map']['getExtent']();const _0x192f6c=this['getZoom'](),_0x444019=this[_0x1e0166(0x436,0x449)][_0x1e0166(0x464,_0x17cd20._0x1fe0d3)]([-0xb4,-0x5a,0xb4,0x5a],_0x192f6c);this['pointCountMax']=this[_0x1934f0(-_0x17cd20._0x5fb151,-_0x17cd20._0x23515c)]['trees'][_0x192f6c]['max'],this[_0x1e0166(_0x17cd20._0x11e257,0x470)]=this['supercluster'][_0x1934f0(-_0x17cd20._0x1fa391,-0x154)][_0x192f6c]['min'];let _0x5aa912={},_0x5aba0a=null,_0x1b7c54=null;if(this[_0x1e0166(0x491,0x459)]===this['pointCountMin'])_0x5aba0a=this['options']['fillStyle'],_0x1b7c54=this['options'][_0x1e0166(_0x17cd20._0x329210,_0x17cd20._0x4d8513)]||0x8;else{const _0x5657ef={};_0x5657ef['min']=this['pointCountMin'],_0x5657ef['max']=this['pointCountMax'],_0x5657ef['minSize']=this['options']['minSize']||0x8,_0x5657ef['maxSize']=this[_0x1934f0(-0x191,-0x174)]['maxSize']||0x1e,_0x5657ef['gradient']=this['options'][_0x1934f0(-_0x17cd20._0x2e7cfc,-_0x17cd20._0x1232ff)],_0x5aa912=new mapv__namespace['utilDataRangeIntensity'](_0x5657ef);}for(let _0x1a8b8d=0x0;_0x1a8b8d<_0x444019['length'];_0x1a8b8d++){const _0x128150=_0x444019[_0x1a8b8d];_0x128150[_0x1934f0(-0x16f,-_0x17cd20._0x4dc741)]&&_0x128150['properties'][_0x1e0166(0x458,_0x17cd20._0x342aeb)]?(_0x444019[_0x1a8b8d][_0x1e0166(0x46f,_0x17cd20._0x495ba8)]=_0x1b7c54||_0x5aa912[_0x1934f0(-0x19c,-0x187)](_0x128150['properties'][_0x1e0166(0x41c,0x448)]),_0x444019[_0x1a8b8d]['fillStyle']=_0x5aba0a||_0x5aa912[_0x1e0166(0x493,_0x17cd20._0x5799c0)](_0x128150['properties'][_0x1934f0(-0x18e,-_0x17cd20._0x4cfdb5)])):_0x444019[_0x1a8b8d][_0x1934f0(-_0x17cd20._0x2190ad,-0x162)]=this['options']['size'];}this['clusterDataSet']['set'](_0x444019),_0x5dea9c=this[_0x1934f0(-_0x17cd20._0x19f43d,-_0x17cd20._0x496627)][_0x1934f0(-0x19d,-_0x17cd20._0x441f04)](_0x59c41b);}else _0x5dea9c=this['dataSet'][_0x1934f0(-_0x17cd20._0x313582,-0x1c8)](_0x59c41b);this['processData'](_0x5dea9c);this[_0x1e0166(_0x17cd20._0x2368a5,_0x17cd20._0x5c22fc)]['unit']==='m'&&this['options']['size']&&(this['options']['_size']=this['options']['size']);const _0x29d26e=mars3d__namespace['PointTrans']['toWindowCoordinates'](_0x5aa80c,Cesium$1[_0x1934f0(-_0x17cd20._0x2c3599,-_0x17cd20._0x465261)]['fromDegrees'](0x0,0x0));if(!_0x29d26e)return;this[_0x1e0166(0x42c,0x45c)](_0x4c934e,new mapv__namespace[(_0x1e0166(0x492,_0x17cd20._0x8a4b03))](_0x5dea9c),this['options'],_0x29d26e),this['options'][_0x1934f0(-_0x17cd20._0x381d4f,-_0x17cd20._0x496821)]&&this[_0x1934f0(-0x191,-0x159)]['updateCallback'](_0x3a1e84);}[_0x17944e(0x2b3,0x2e1)](_0x1170df,_0x1f3b52){const _0xbb2efc={_0x520088:0x157,_0x1e7c21:0x13f,_0x40250c:0x139},_0x4b022d={_0x35fc4e:0x39a};let _0xb8a23f=_0x1170df;_0xb8a23f&&_0xb8a23f[_0x115a17(-_0xbb2efc._0x520088,-0x145)]&&(_0xb8a23f=_0xb8a23f['get']()),void 0x0!==_0xb8a23f&&this['dataSet'][_0x115a17(-_0xbb2efc._0x1e7c21,-0x151)](_0xb8a23f);function _0x4c2faf(_0x3a1837,_0x4d92ba){return _0x4b25e5(_0x3a1837-0x130,_0x4d92ba);}const _0x56329b={};function _0x115a17(_0x24dc3c,_0x46d8dd){return _0x17944e(_0x46d8dd- -_0x4b022d._0x35fc4e,_0x24dc3c);}_0x56329b[_0x115a17(-0x160,-_0xbb2efc._0x40250c)]=_0x1f3b52,super['update'](_0x56329b);}['addData'](_0x48a2a2,_0x61b7d){const _0x54e4ff={_0x12affc:0x4f9},_0x288b2e={_0x337121:0x285};let _0x866596=_0x48a2a2;const _0x3126b8={};function _0x479cff(_0x5004e6,_0x5af2c1){return _0x17944e(_0x5004e6-0x25f,_0x5af2c1);}_0x3126b8[_0x479cff(0x4c0,0x4c3)]=_0x61b7d;function _0x2b4c3a(_0x4b77c9,_0x124f1a){return _0x4b25e5(_0x4b77c9-_0x288b2e._0x337121,_0x124f1a);}_0x48a2a2&&_0x48a2a2[_0x479cff(0x4b4,_0x54e4ff._0x12affc)]&&(_0x866596=_0x48a2a2['get']()),this['dataSet'][_0x479cff(0x52b,0x507)](_0x866596),this[_0x479cff(0x51b,0x532)](_0x3126b8);}[_0x17944e(0x253,0x288)](){return this['dataSet'];}[_0x17944e(0x254,0x224)](_0x1cdec6){const _0x425ef9={_0x2df099:0x33c,_0x4d0621:0x37b,_0x2231e7:0x35a},_0x2bf0a0={_0x4581fe:0xa3};function _0x117924(_0x254a5f,_0x21f3f1){return _0x17944e(_0x254a5f- -0x2e4,_0x21f3f1);}function _0x42a455(_0x3c5bc5,_0x49a402){return _0x4b25e5(_0x49a402-0x538,_0x3c5bc5);}if(this['dataSet']){const _0x4a377b=this['dataSet'][_0x42a455(_0x425ef9._0x2df099,0x366)]({'filter':function(_0x40258c){function _0x3d83ef(_0x1ad5f1,_0x316970){return _0x42a455(_0x1ad5f1,_0x316970- -0x303);}return _0x1cdec6==null||typeof _0x1cdec6!==_0x3d83ef(0xc0,_0x2bf0a0._0x4581fe)||!_0x1cdec6(_0x40258c);}});this['dataSet'][_0x42a455(_0x425ef9._0x4d0621,_0x425ef9._0x2231e7)](_0x4a377b);const _0x226e71={};_0x226e71['options']=null,this['update'](_0x226e71);}}[_0x17944e(0x29b,0x29b)](){const _0x4ef3c5={_0xb58ef3:0x3d3,_0x1b6dd3:0x3a5};this[_0x545654(_0x4ef3c5._0xb58ef3,_0x4ef3c5._0x1b6dd3)]&&this['dataSet'][_0x233dc5(0xc,0x19)]();const _0x2b7767={};_0x2b7767['options']=null;function _0x545654(_0x5c38c4,_0x2fa93b){return _0x17944e(_0x5c38c4-0x161,_0x2fa93b);}function _0x233dc5(_0x21f53d,_0x16cf71){return _0x17944e(_0x16cf71- -0x241,_0x21f53d);}this['update'](_0x2b7767);}['draw'](){const _0x5086a5={_0xd13a32:0x3d9},_0xadd413={_0x52fcc8:0x158};function _0x5a15fe(_0x213729,_0x161a5c){return _0x17944e(_0x161a5c-_0xadd413._0x52fcc8,_0x213729);}this['canvasLayer'][_0x5a15fe(0x416,_0x5086a5._0xd13a32)]();}['clear'](_0x434427){const _0x418b06={_0x3e0f64:0x3b3,_0x5a63c7:0xa5,_0x3636ce:0xde,_0xf9df82:0x102},_0xd85d04={_0x3cde5b:0x26b};function _0x2fbb62(_0x5a59d5,_0x158222){return _0x4b25e5(_0x158222-_0xd85d04._0x3cde5b,_0x5a59d5);}function _0x36d378(_0x3ec438,_0x596d44){return _0x17944e(_0x3ec438-0x16c,_0x596d44);}_0x434427&&_0x434427['clearRect']&&_0x434427[_0x36d378(_0x418b06._0x3e0f64,0x397)](0x0,0x0,_0x434427[_0x2fbb62(_0x418b06._0x5a63c7,_0x418b06._0x3636ce)][_0x2fbb62(0xbf,0x87)],_0x434427[_0x2fbb62(0xad,_0x418b06._0x3636ce)][_0x2fbb62(0xdc,_0x418b06._0xf9df82)]);}[_0x17944e(0x27f,0x293)](){const _0xcf1608={_0x24c834:0x3f};function _0x3c65d8(_0x2ba5f3,_0x4abc4c){return _0x17944e(_0x2ba5f3-_0xcf1608._0x24c834,_0x4abc4c);}return this[_0x3c65d8(0x2c1,0x2ef)]['level'];}[_0x17944e(0x274,0x264)](){const _0x51ded4={_0x28e21f:0x1fa},_0x575b0b={_0xe32298:0x485},_0x265ee6={_0x50007c:0x36b};function _0xfc1a44(_0x2d34b0,_0x367b5f){return _0x4b25e5(_0x2d34b0-_0x265ee6._0x50007c,_0x367b5f);}this['clear'](this[_0x279353(-_0x51ded4._0x28e21f,-0x1ea)]()),this[_0xfc1a44(0x1df,0x1b0)]();function _0x279353(_0xde8171,_0x5683b3){return _0x17944e(_0xde8171- -_0x575b0b._0xe32298,_0x5683b3);}this['animator']&&this['animator']['stop'](),this['animator']=null,this['canvasLayer']=null;}}if(mapv__namespace!==null&&mapv__namespace!==void 0x0&&mapv__namespace['DataSet'])mapv__namespace['DataSet']['prototype']['transferCoordinate']=function(_0x53856f,_0x462a4d,_0x4fb23f,_0x4946d0){const _0x54adff={_0x5bf7e4:0x1e5,_0x50f8e9:0x21a,_0x5fc4ec:0xb8,_0x196cce:0x207,_0x38c972:0x1ee,_0x10fded:0x1ed,_0x287876:0x1c1},_0x342bd2={_0x58d763:0x37,_0x5e3b12:0x28},_0x1638ce={_0x49436b:0xad},_0x505272={_0x63c85a:0xa6},_0x451caf={_0x5d18f0:0x1af};function _0x5cdcf4(_0x5bd1a4,_0x43d00c){return _0x17944e(_0x43d00c- -_0x451caf._0x5d18f0,_0x5bd1a4);}_0x4946d0=_0x4946d0||_0x464744(0x1c3,0x186),_0x4fb23f=_0x4fb23f||_0x5cdcf4(0xe8,0xe3);function _0x464744(_0x5943b0,_0x4b367b){return _0x17944e(_0x5943b0- -_0x505272._0x63c85a,_0x4b367b);}for(let _0x3494da=0x0;_0x3494da<_0x53856f[_0x464744(0x1ed,_0x54adff._0x5bf7e4)];_0x3494da++){const _0x342e32=_0x53856f[_0x3494da][_0x464744(0x227,_0x54adff._0x50f8e9)],_0x2606ca=_0x342e32[_0x4fb23f];switch(_0x342e32['type']){case'Point':{const _0x37c57e=_0x462a4d(_0x2606ca);_0x37c57e?_0x342e32[_0x4946d0]=_0x37c57e:_0x342e32[_0x4946d0]=[-0x3e7,-0x3e7];}break;case'LineString':{const _0x127dcf=[];for(let _0x459722=0x0;_0x459722<_0x2606ca['length'];_0x459722++){const _0x3eba93=_0x462a4d(_0x2606ca[_0x459722]);_0x3eba93&&_0x127dcf[_0x5cdcf4(0x96,_0x54adff._0x5fc4ec)](_0x3eba93);}_0x342e32[_0x4946d0]=_0x127dcf;}break;case _0x464744(_0x54adff._0x196cce,0x244):case'Polygon':{const _0x331519=_0x44c685(_0x2606ca);_0x342e32[_0x4946d0]=_0x331519;}break;case _0x464744(0x1c6,_0x54adff._0x38c972):{const _0x508a69=[];for(let _0x4318ad=0x0;_0x4318ad<_0x2606ca[_0x464744(0x1ed,0x234)];_0x4318ad++){const _0x21f99f=_0x44c685(_0x2606ca[_0x4318ad]);_0x21f99f[_0x464744(_0x54adff._0x10fded,0x1f3)]>0x0&&_0x508a69[_0x464744(_0x54adff._0x287876,0x1a8)](_0x21f99f);}_0x342e32[_0x4946d0]=_0x508a69;}break;}}function _0x44c685(_0x10c04e){const _0xa41708=[];function _0x366757(_0x3f8664,_0xe95eb8){return _0x5cdcf4(_0x3f8664,_0xe95eb8-0x207);}function _0x398a63(_0xdc6c7,_0x7c09cc){return _0x5cdcf4(_0x7c09cc,_0xdc6c7- -_0x1638ce._0x49436b);}for(let _0x11f2f4=0x0;_0x11f2f4<_0x10c04e['length'];_0x11f2f4++){const _0x10dc89=_0x10c04e[_0x11f2f4],_0x30d717=[];for(let _0x4b18fb=0x0;_0x4b18fb<_0x10dc89[_0x398a63(_0x342bd2._0x58d763,-0x3)];_0x4b18fb++){const _0x5bb4dd=_0x462a4d(_0x10dc89[_0x4b18fb]);_0x5bb4dd&&_0x30d717['push'](_0x5bb4dd);}_0x30d717[_0x398a63(0x37,_0x342bd2._0x5e3b12)]>0x0&&_0xa41708[_0x366757(0x289,0x2bf)](_0x30d717);}return _0xa41708;}return _0x53856f;};else throw new Error(_0x4b25e5(-0x198,-0x15c));const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer']['BaseLayer'];function _0x5f77(){const _0x5bff53=['rxzLBNruExbL','y2fUDMfZ','y2XLyxjeyxrH','Cg9ZAxrPB25xqW','Bwv0Ag9KCW','rgf0yvnLDa','u0nftKuZra','yw5PBwf0B3jnB3zLC3rHCNrfDMvUDa','DxbKyxrLq2fSBgjHy2S','AxnfBMfIBgvKvgLTzq','x21HCa','mti2nJK4nvHVsfHmwq','Cg9PBNrLCKv2zw50CW','C3rLChm','x29Utw92zuvUzev2zw50','BM9Uzq','yMLUzev2zw50','twfWvKXHEwvY','yw5PBwf0Aw9U','BgvMDa','txvSDgLmAw5Lu3rYAw5N','Aw5PDerLDMLJzvbPEgvSuMf0Aw8','AxngB3jTyxq','Bw91C2vnB3zL','y2fUDMfZtgf5zxi','Bwf4q2X1C3rLCLPVB20','DxbKyxrLrgf0yq','Bgf5zxi','Cg9ZDfjLBMrLCG','x21HCfzszw5KzxjLCG','y2X1C3rLCKrHDgftzxq','x29Utw92zvn0yxj0rxzLBNq','x2rHDgfdywnOzq','z2v0q29SB3i','ywjZB2X1Dgu','DxbKyxrL','x3jLC2v0','AgvPz2H0','mta2nZC2mgzXwe94Aq','DMfSDwu','mti4uefiyM9Z','Dg9xAw5KB3DdB29YzgLUyxrLCW','y2fTzxjHtw92zvn0yxj0','uMvJDgfUz2XL','A2v5CW','y29UDgv4Da','mtaWmtq1odjPChHjBvu','mhb4','zgvMyxvSDa','z3jHzgLLBNq','Bw91C2vTB3zL','ywrK','z2vVBwv0CNK','x19LC01VzhvSzq','mtK5nZe5A1rLC0fz','D2LKDgG','Eg1PBG','zMLSDgvY','zMLSBfn0EwXL','y2XLyxjszwn0','vxrPBa','C2v0','zMvHDhvYzxm','yMXVy2S','x2fKzgvKsg9VAW','ywrKrgf0yq','yw5PBwf0B3jnB3zLzw5KrxzLBNq','zgvMAw5Lza','CMvZDg9Yzq','ug9PBNruCMfUCW','x3bVAw50zxjfDMvUDhm','z2v0rgf0yq','CMvTB3zLrgf0yq','z2v0','z2v0u2L6zq','mta0ndm4mvver0vAyq','x29UtwfWq2XPy2S','u2nLBMvnB2rL','y2XLyxi','x2nYzwf0zunHBNzHCW','Cg9ZAxrPB24','zMLSBfjLy3q','DhjLzxm','mteXmJCWnK9mAvPODa','zgv2AwnLugL4zwXsyxrPBW','B3b0Aw9UCW','EKLUzgv4','y2XPy2S','Cg9PBNrFy291BNq','C3vWzxjJBhvZDgvY','C3r5Bgu','ChvZAa','mJrhsxjgC04','x2nVB3jKAw5HDgvZ','q2vZAxvT','x29UtwfWtw91C2vnB3zL','txvSDgLqB2X5z29U','Ew1HEa','z2v0uMvJDgfUz2XL','q2fYDgvZAwfUmW','tgf5zxjvDgLS','Bw91C2veB3DU','zgf0yvnLDa','y2X1C3rLCL9Pza','zgvZDhjVEq','Cg9PBNrdB3vUDe1HEa','BwfWDG','CMvUzgvY','zhjHD0nVBNrLEhq','C3rLCa','zgvWDgHuzxn0','Aw5PDa','zNjVBurLz3jLzxm','x2rHDge','Dw5IAw5KrxzLBNq','z2v0wM9VBq','y3jLyxrL','zhjHDW','BwfW','ChjVCgvYDgLLCW','CMvTB3zLq2HPBgq','CMvZAxPL','z2v0sgvPz2H0','C3rVCefUAwfTyxrPB24','DhjHAwXZ','z2v0q2X1C3rLCNm','BwfWDKf1Dg9izwLNAhq','z2v0q29UDgv4Da','Cg9PBNrdB3vUDe1PBG','D2LUzg93ug9ZAxrPB24','Aw5PDerHDgfsyw5Nzq','6k+35BYv5ywLig1HChyG5BQtia','B2zM','BwfWDKrLChrOvgvZDa','y29VCMrPBMf0zxm','BgvUz3rO','BwLUu2L6zq','zNvUy3rPB24','C2L6zq','mJmWmdy0mMXxENvIua','zgvMAw5LuhjVCgvYDhK'];_0x5f77=function(){return _0x5bff53;};return _0x5f77();}class MapVLayer extends BaseLayer{constructor(_0x59d9d6,_0x259e79){const _0x2dc5b2={_0x1f463b:0x25,_0x48a625:0x40f},_0x5d5a4b={_0x22e525:0x5c4};super(_0x59d9d6),this[_0x5e0fd9(0x4e,_0x2dc5b2._0x1f463b)]=this[_0x31c2de(0x3fe,0x405)]['pointerEvents'];function _0x31c2de(_0x217c9b,_0x3d9dac){return _0x4b25e5(_0x217c9b-_0x5d5a4b._0x22e525,_0x3d9dac);}function _0x5e0fd9(_0x595864,_0x3a1782){return _0x17944e(_0x595864- -0x204,_0x3a1782);}this[_0x31c2de(_0x2dc5b2._0x48a625,0x41c)]=_0x259e79||new mapv__namespace['DataSet'](_0x59d9d6['data']),this['canvas']=null;}get['pointerEvents'](){return this['_pointerEvents'];}set['pointerEvents'](_0x3d359c){const _0x306895={_0x4be295:0x1d3,_0x55c870:0x207};function _0x9d4c63(_0x524a69,_0x5babe6){return _0x17944e(_0x524a69- -0xc7,_0x5babe6);}function _0xfdc00f(_0x17bc22,_0x5e05b3){return _0x17944e(_0x17bc22-0x107,_0x5e05b3);}this['_pointerEvents']=_0x3d359c,this[_0x9d4c63(0x1d3,0x1be)]&&(_0x3d359c?this[_0x9d4c63(_0x306895._0x4be295,_0x306895._0x55c870)]['style']['pointerEvents']='all':this['canvas']['style']['pointerEvents']='none');}['_showHook'](_0x381727){const _0x49977b={_0x598f1c:0x537,_0x9a8823:0x53f,_0x20107c:0x586,_0x3b5329:0x590,_0x37e5dd:0xb2,_0x34cf4e:0x33};function _0x2453be(_0x1127e6,_0x19e6d2){return _0x4b25e5(_0x1127e6-0x108,_0x19e6d2);}function _0x3d3966(_0x245fd9,_0x3115a8){return _0x17944e(_0x245fd9-0x2ec,_0x3115a8);}_0x381727?this[_0x3d3966(0x586,0x571)]['style']['display']=_0x3d3966(_0x49977b._0x598f1c,_0x49977b._0x9a8823):this[_0x3d3966(_0x49977b._0x20107c,_0x49977b._0x3b5329)][_0x2453be(-0xb9,-_0x49977b._0x37e5dd)]['display']=_0x2453be(-0x77,-_0x49977b._0x34cf4e);}['_mountedHook'](){const _0x1f4091={_0x1bf6e7:0x4ca,_0x57639d:0x4c3,_0xbd5ff7:0x4ec,_0xc31af9:0x4b9,_0x195118:0x4ee,_0xddd008:0x4aa};function _0x1d27b0(_0x2d926b,_0x3331e9){return _0x17944e(_0x3331e9-0x249,_0x2d926b);}this['_map']['scene'][_0x3525d7(0x26c,0x29c)]=this[_0x3525d7(0x239,0x26c)][_0x1d27b0(_0x1f4091._0x1bf6e7,_0x1f4091._0x57639d)]??!![];function _0x3525d7(_0x2a9561,_0x81dd32){return _0x4b25e5(_0x81dd32-0x432,_0x2a9561);}this[_0x1d27b0(0x4d3,_0x1f4091._0xbd5ff7)]['scene']['mapvAutoHeight']=this['options']['clampToGround']??![],this[_0x1d27b0(_0x1f4091._0xc31af9,0x4ec)]['scene']['mapvFixedHeight']=this[_0x1d27b0(_0x1f4091._0x195118,_0x1f4091._0xddd008)]['fixedHeight']??0x0;}[_0x4b25e5(-0x1db,-0x1ab)](){const _0x1b9157={_0x4306ba:0xd4,_0xe5f6e7:0xe0,_0x3853f1:0x11a,_0x4fc3a4:0xd5,_0x49df47:0xbf,_0x54741b:0x42d,_0x1dd5b7:0x48e,_0x2bbac0:0x45a,_0x2ecbb1:0x41f,_0x20f538:0x464,_0xb88821:0x478,_0x24530a:0x460},_0x15f717={_0x2c9e98:0x5e2};this['dataSet']&&(!this[_0x5c0593(-_0x1b9157._0x4306ba,-0xe0)]['_data']||this[_0x5c0593(-0xe3,-_0x1b9157._0xe5f6e7)][_0x5c0593(-_0x1b9157._0x3853f1,-_0x1b9157._0x4fc3a4)][_0x5c0593(-0xad,-_0x1b9157._0x49df47)]===0x0)&&(this['dataSet']['_data']=[]['concat'](this[_0x429f63(_0x1b9157._0x54741b,0x470)][_0x429f63(0x474,_0x1b9157._0x1dd5b7)]));this['_mapVRenderer']=new MapVRenderer(this[_0x429f63(0x45e,_0x1b9157._0x2bbac0)],this['dataSet'],this['options'],this),this['initDevicePixelRatio']();function _0x429f63(_0x49da76,_0x304e13){return _0x4b25e5(_0x49da76-_0x15f717._0x2c9e98,_0x304e13);}this['canvas']=this[_0x429f63(0x416,_0x1b9157._0x2ecbb1)](),this['render']=this['render']['bind'](this),this[_0x429f63(_0x1b9157._0x20f538,0x466)]();function _0x5c0593(_0x28481d,_0x52342e){return _0x17944e(_0x52342e- -0x352,_0x28481d);}this[_0x429f63(_0x1b9157._0xb88821,_0x1b9157._0x24530a)]();}['_removedHook'](){const _0x19dde2={_0x19bd1b:0x3a1,_0x545129:0x396,_0x576e12:0x12e},_0x1ebc47={_0x192662:0x170};function _0x5cbf35(_0xf5fc,_0x43c7c1){return _0x17944e(_0xf5fc-0x123,_0x43c7c1);}this[_0x5cbf35(_0x19dde2._0x19bd1b,_0x19dde2._0x545129)]();this[_0x547897(_0x19dde2._0x576e12,0x146)]&&(this['_mapVRenderer']['destroy'](),this[_0x5cbf35(0x3d9,0x413)]=null);function _0x547897(_0x5f28c4,_0x804926){return _0x17944e(_0x804926- -_0x1ebc47._0x192662,_0x5f28c4);}this['canvas']['parentElement']['removeChild'](this['canvas']);}[_0x17944e(0x2ae,0x272)](){const _0x3c83c3={_0x1ebcfd:0x3f0};function _0x5f3607(_0x56177e,_0x237592){return _0x17944e(_0x237592- -_0x3c83c3._0x1ebcfd,_0x56177e);}this[_0x5f3607(-0x172,-0x190)]=window['devicePixelRatio']||0x1;}['bindEvent'](){const _0x1ef400={_0x21c579:0x185,_0x24d7f7:0x1af,_0x2d3f14:0x1f5,_0x41a0fc:0x1d7,_0x44a896:0x177,_0x1993ab:0x181},_0x546ca3={_0x7f97f2:0x9};var _0x38ad70,_0x1ae750;function _0x47a689(_0x589cdc,_0xf2934f){return _0x4b25e5(_0xf2934f-0x365,_0x589cdc);}this['_map']['on'](mars3d__namespace[_0x1698fb(-_0x1ef400._0x21c579,-0x191)][_0x47a689(0x1d4,_0x1ef400._0x24d7f7)],this['_onMoveStartEvent'],this),this[_0x1698fb(-0x17b,-0x138)]['on'](mars3d__namespace[_0x47a689(_0x1ef400._0x2d3f14,_0x1ef400._0x41a0fc)]['cameraMoveStart'],this['_onMoveStartEvent'],this),this[_0x47a689(0x1b1,0x1e1)]['on'](mars3d__namespace['EventType']['cameraMoveEnd'],this[_0x1698fb(-_0x1ef400._0x44a896,-0x163)],this);function _0x1698fb(_0x483da5,_0x256f70){return _0x4b25e5(_0x483da5-_0x546ca3._0x7f97f2,_0x256f70);}(_0x38ad70=this[_0x1698fb(-0x1bd,-0x182)])!==null&&_0x38ad70!==void 0x0&&(_0x38ad70=_0x38ad70[_0x1698fb(-_0x1ef400._0x1993ab,-0x156)])!==null&&_0x38ad70!==void 0x0&&_0x38ad70[_0x47a689(0x1e5,0x1a1)]&&this['_map']['on'](mars3d__namespace['EventType']['click'],this['_onMapClick'],this),(_0x1ae750=this['options'])!==null&&_0x1ae750!==void 0x0&&(_0x1ae750=_0x1ae750['methods'])!==null&&_0x1ae750!==void 0x0&&_0x1ae750['mousemove']&&this[_0x1698fb(-0x17b,-0x199)]['on'](mars3d__namespace['EventType']['mouseMove'],this['_onMapMouseMove'],this);}[_0x4b25e5(-0x1a9,-0x1ab)](){const _0x5c0c8e={_0x2f72da:0xb,_0x18a8d4:0x9,_0x418c76:0x123,_0x3676bd:0x1a1,_0x506296:0x0,_0x17f773:0x19a,_0x123c55:0x175,_0x21b16c:0x59,_0x36e996:0x2a,_0x5cd3e3:0x1d2,_0x5e2fe9:0x15f,_0x339589:0xb,_0x154561:0x6,_0x2e7614:0x204},_0x402599={_0x1edbb7:0x18c},_0x1a9cbb={_0xc9a8a9:0x3};var _0x451636,_0x18ffcd;this['_map']['off'](mars3d__namespace['EventType']['mouseDown'],this['_onMoveStartEvent'],this),this['_map'][_0x3fb3ff(-_0x5c0c8e._0x2f72da,-_0x5c0c8e._0x18a8d4)](mars3d__namespace['EventType'][_0x1e637a(-_0x5c0c8e._0x418c76,-0x167)],this['_onMoveStartEvent'],this),this[_0x1e637a(-_0x5c0c8e._0x3676bd,-0x187)][_0x3fb3ff(-0xb,_0x5c0c8e._0x506296)](mars3d__namespace['EventType']['cameraMoveEnd'],this['_onMoveEndEvent'],this);function _0x1e637a(_0x374308,_0x167370){return _0x4b25e5(_0x167370- -_0x1a9cbb._0xc9a8a9,_0x374308);}function _0x3fb3ff(_0x3fa58d,_0x203cba){return _0x4b25e5(_0x3fa58d-_0x402599._0x1edbb7,_0x203cba);}this['_map'][_0x1e637a(-0x16d,-_0x5c0c8e._0x17f773)](mars3d__namespace['EventType'][_0x1e637a(-0x180,-_0x5c0c8e._0x123c55)],this[_0x3fb3ff(0x22,0x13)],this),(_0x451636=this['options'])!==null&&_0x451636!==void 0x0&&(_0x451636=_0x451636['methods'])!==null&&_0x451636!==void 0x0&&_0x451636[_0x3fb3ff(-0x38,-_0x5c0c8e._0x21b16c)]&&this['_map']['off'](mars3d__namespace[_0x3fb3ff(-0x2,-_0x5c0c8e._0x36e996)]['click'],this[_0x1e637a(-0x1dd,-_0x5c0c8e._0x5cd3e3)],this),(_0x18ffcd=this['options'])!==null&&_0x18ffcd!==void 0x0&&(_0x18ffcd=_0x18ffcd[_0x1e637a(-0x178,-0x18d)])!==null&&_0x18ffcd!==void 0x0&&_0x18ffcd[_0x1e637a(-0x171,-_0x5c0c8e._0x5e2fe9)]&&this['_map'][_0x3fb3ff(-_0x5c0c8e._0x339589,_0x5c0c8e._0x154561)](mars3d__namespace['EventType']['mouseMove'],this[_0x1e637a(-_0x5c0c8e._0x2e7614,-0x1bf)],this);}[_0x17944e(0x2b8,0x2d9)](){const _0x2acb6a={_0x2cde0c:0xc7,_0x963027:0xa9,_0x33e037:0xf1,_0x5b2359:0xb9,_0x3a47d7:0x8c,_0x4accd1:0x519},_0x43a93b={_0x36732d:0x674};function _0x4a66f8(_0x6b2eee,_0x3b7d82){return _0x4b25e5(_0x3b7d82-_0x43a93b._0x36732d,_0x6b2eee);}function _0x26c3ea(_0x55d2cc,_0x4cf9c5){return _0x4b25e5(_0x4cf9c5-0xde,_0x55d2cc);}this['_mapVRenderer']&&(this['_mapVRenderer'][_0x26c3ea(-_0x2acb6a._0x2cde0c,-_0x2acb6a._0x963027)](),this['_map'][_0x26c3ea(-_0x2acb6a._0x33e037,-_0x2acb6a._0x5b2359)](mars3d__namespace['EventType']['postRender'],this[_0x26c3ea(-0x9b,-_0x2acb6a._0x3a47d7)],this),this[_0x4a66f8(_0x2acb6a._0x4accd1,0x4f0)]['on'](mars3d__namespace['EventType']['postRender'],this[_0x26c3ea(-0x9b,-_0x2acb6a._0x3a47d7)],this));}[_0x17944e(0x2a7,0x2dc)](){const _0x1b8f4f={_0x15ba90:0xb7,_0x24ab6c:0xd3},_0x33d480={_0x244821:0x376};function _0x29f5b1(_0x34c41d,_0x4c7983){return _0x17944e(_0x34c41d- -_0x33d480._0x244821,_0x4c7983);}function _0x11f5d0(_0x2e849f,_0x5558e0){return _0x4b25e5(_0x2e849f-0x64e,_0x5558e0);}this[_0x29f5b1(-0xc0,-_0x1b8f4f._0x15ba90)]&&(this[_0x29f5b1(-_0x1b8f4f._0x24ab6c,-0xd2)]['off'](mars3d__namespace['EventType'][_0x29f5b1(-0xc1,-0xff)],this['_reset'],this),this['_mapVRenderer']['animatorMoveendEvent'](),this['_reset']());}['_setOptionsHook'](_0x598319,_0x552eba){this['_removedHook'](),this['_addedHook']();}[_0x17944e(0x24d,0x27c)](_0x28a702){const _0x90711a={_0x81d58d:0x5e};function _0x37de21(_0x114ac0,_0x58c795){return _0x17944e(_0x114ac0- -0x2dd,_0x58c795);}function _0x1c4641(_0x172bba,_0x3cbca7){return _0x17944e(_0x172bba- -0x311,_0x3cbca7);}this[_0x1c4641(-0x5b,-0x84)]&&this['_mapVRenderer']['addData'](_0x28a702,this[_0x37de21(-0x7c,-_0x90711a._0x81d58d)]);}[_0x17944e(0x2b3,0x288)](_0x5db67d){const _0x2d989a={_0x3de681:0x312};function _0x1fb73a(_0x578565,_0x24da5e){return _0x4b25e5(_0x24da5e-0x457,_0x578565);}function _0x281157(_0x949d77,_0x22784f){return _0x4b25e5(_0x22784f-0xbc,_0x949d77);}this[_0x1fb73a(_0x2d989a._0x3de681,0x2e6)]&&this['_mapVRenderer'][_0x281157(-0xc6,-0xb8)](_0x5db67d,this['options']);}['getData'](){const _0x106aa3={_0xfa1072:0x16},_0x2f26c0={_0x469295:0x2a0};this[_0x234b91(-0x25,_0x106aa3._0xfa1072)]&&(this[_0x4bd681(0x1d,-0x20)]=this['_mapVRenderer']['getData']());function _0x4bd681(_0x34426c,_0x1ec7a2){return _0x4b25e5(_0x1ec7a2-0x195,_0x34426c);}function _0x234b91(_0x48d2ce,_0x4ff602){return _0x17944e(_0x4ff602- -_0x2f26c0._0x469295,_0x48d2ce);}return this['dataSet'];}[_0x4b25e5(-0x1d3,-0x1ce)](_0x242e6a){const _0x1dda28={_0x5eb413:0x19c};function _0x5b979b(_0x29f519,_0x394e69){return _0x17944e(_0x394e69- -0x11a,_0x29f519);}this['_mapVRenderer']&&this[_0x5b979b(0x16a,_0x1dda28._0x5eb413)]['removeData'](_0x242e6a);}['removeAllData'](){const _0x365ac9={_0x437aa8:0x2fb,_0x190b7e:0x2db},_0x43d5d7={_0x59589f:0x467};function _0x2bda34(_0x1600a3,_0x3f73bd){return _0x4b25e5(_0x3f73bd-_0x43d5d7._0x59589f,_0x1600a3);}function _0x2a2896(_0x2f68fc,_0x2af291){return _0x4b25e5(_0x2f68fc-0x13a,_0x2af291);}this[_0x2bda34(0x33c,0x2f6)]&&this['_mapVRenderer'][_0x2bda34(_0x365ac9._0x437aa8,_0x365ac9._0x190b7e)]();}[_0x17944e(0x25b,0x229)](){const _0x4d613d={_0x384d3b:0x2dd,_0x37a70d:0x2ee,_0x4072c9:0x31b,_0x18fe2c:0x337,_0x2f760c:0x33f,_0x1721ce:0x1a3,_0x132c17:0x17a,_0x3cb397:0x122,_0x1f2151:0x33b,_0xe01a47:0x320,_0x3abcf2:0x314,_0x1b2469:0x1c6},_0xc13ddd=mars3d__namespace['DomUtil'][_0x4b3e2b(0x180,0x1bd)](_0x4b48d8(0x31b,0x326),'mars3d-mapv',this['_map']['container']);_0xc13ddd['id']=this['id'];function _0x4b3e2b(_0x1eafca,_0x1ca188){return _0x17944e(_0x1eafca- -0x100,_0x1ca188);}_0xc13ddd['style'][_0x4b48d8(_0x4d613d._0x384d3b,_0x4d613d._0x37a70d)]='absolute',_0xc13ddd['style']['top']='0px',_0xc13ddd['style'][_0x4b48d8(0x32d,0x342)]='0px',_0xc13ddd['width']=parseInt(this['_map'][_0x4b48d8(_0x4d613d._0x4072c9,_0x4d613d._0x18fe2c)]['width']),_0xc13ddd[_0x4b48d8(_0x4d613d._0x2f760c,0x307)]=parseInt(this[_0x4b3e2b(_0x4d613d._0x1721ce,_0x4d613d._0x132c17)]['canvas'][_0x4b3e2b(0x1be,0x1a4)]),_0xc13ddd['style']['width']=this[_0x4b3e2b(0x1a3,0x1ea)][_0x4b48d8(_0x4d613d._0x4072c9,0x358)][_0x4b3e2b(0x166,_0x4d613d._0x3cb397)]['width'],_0xc13ddd['style']['height']=this[_0x4b48d8(0x324,_0x4d613d._0x1f2151)][_0x4b3e2b(0x19a,0x163)]['style']['height'],_0xc13ddd['style'][_0x4b48d8(0x326,_0x4d613d._0xe01a47)]=this['_pointerEvents']?'auto':_0x4b48d8(0x329,_0x4d613d._0x3abcf2),_0xc13ddd[_0x4b48d8(0x2e7,0x2be)]['zIndex']=this[_0x4b3e2b(0x161,0x192)]['zIndex']??0x9;function _0x4b48d8(_0x2575c4,_0x153f29){return _0x4b25e5(_0x2575c4-0x4a8,_0x153f29);}if(this['options'][_0x4b3e2b(_0x4d613d._0x1b2469,0x193)]==='2d'){const _0x553b96=this['devicePixelRatio'];_0xc13ddd[_0x4b48d8(0x30c,0x2cf)](this[_0x4b3e2b(0x161,0x182)]['context'])['scale'](_0x553b96,_0x553b96);}return _0xc13ddd;}[_0x4b25e5(-0x16a,-0x134)](){const _0x2e3ad1={_0x513886:0x1c5,_0x2408af:0x1d1};this[_0x564b86(-0x1b7,-0x1f7)]();function _0x55d27b(_0x157973,_0x2e37ef){return _0x17944e(_0x157973- -0x12e,_0x2e37ef);}function _0x564b86(_0xb322b4,_0x480241){return _0x17944e(_0xb322b4- -0x43c,_0x480241);}this[_0x564b86(-_0x2e3ad1._0x513886,-_0x2e3ad1._0x2408af)]();}[_0x4b25e5(-0x1a6,-0x1ba)](){const _0x1ae76f={_0x4a7998:0x29,_0x4ae5d6:0x53};function _0x205005(_0x543064,_0x168870){return _0x4b25e5(_0x168870-0x117,_0x543064);}this[_0x205005(-_0x1ae76f._0x4a7998,-_0x1ae76f._0x4ae5d6)]();}['remove'](){function _0x16c4cc(_0x4a0ace,_0x4d8e3){return _0x4b25e5(_0x4d8e3-0x7b,_0x4a0ace);}function _0x3688d6(_0x2cd393,_0x34bcb3){return _0x17944e(_0x34bcb3-0x19d,_0x2cd393);}this['_mapVRenderer']&&(this['_mapVRenderer']['destroy'](),this[_0x16c4cc(-0x103,-0xf6)]=null),this['canvas']['parentElement'][_0x3688d6(0x3f1,0x421)](this['canvas']);}['render'](){this['_mapVRenderer']['_canvasUpdate']();}['resize'](){const _0x22ab92={_0x51e00a:0x3af,_0x1394c4:0x3a3,_0x3f29ac:0x553,_0x243656:0x3bf,_0x2f67cf:0x58d,_0xc0e9ec:0x57b,_0x550ce6:0x3c6,_0x48ede9:0x5e8},_0x2dd0ab={_0xfdff5a:0x160};function _0x3b79d1(_0x535eb7,_0x2e4a53){return _0x17944e(_0x535eb7-0x2f3,_0x2e4a53);}function _0x174a25(_0x5d48d4,_0x160fa9){return _0x17944e(_0x5d48d4-_0x2dd0ab._0xfdff5a,_0x160fa9);}if(this['canvas']){const _0x4f8309=this['canvas'];_0x4f8309[_0x174a25(0x3c6,_0x22ab92._0x51e00a)][_0x174a25(0x3bc,0x3dd)]=_0x174a25(0x41b,0x426),_0x4f8309['style']['top']='0px',_0x4f8309['style']['left']=_0x174a25(0x428,0x425),_0x4f8309[_0x174a25(_0x22ab92._0x1394c4,0x394)]=parseInt(this['_map'][_0x3b79d1(0x58d,0x590)]['width']),_0x4f8309['height']=parseInt(this['_map'][_0x3b79d1(0x58d,_0x22ab92._0x3f29ac)]['height']),_0x4f8309['style'][_0x174a25(0x3a3,0x39d)]=this['_map']['canvas'][_0x174a25(0x3c6,0x389)][_0x174a25(0x3a3,_0x22ab92._0x243656)],_0x4f8309['style'][_0x174a25(0x41e,0x43d)]=this['_map'][_0x3b79d1(_0x22ab92._0x2f67cf,_0x22ab92._0xc0e9ec)][_0x174a25(_0x22ab92._0x550ce6,0x399)][_0x3b79d1(0x5b1,_0x22ab92._0x48ede9)];}}[_0x17944e(0x26e,0x264)](_0x581b62){const _0x188694={_0x130ef7:0x429,_0x3948ae:0x9a,_0x2ca93a:0x9c,_0x2531ff:0x6e},_0x4d6f1c={_0x201df7:0x1e0},_0x5bc282={_0x39dfda:0x10a};function _0x12706d(_0x414569,_0x7392a8){return _0x4b25e5(_0x414569-_0x5bc282._0x39dfda,_0x7392a8);}if(!this[_0x1289f1(0x452,_0x188694._0x130ef7)]||!this['dataSet'][_0x1289f1(0x45d,0x48f)])return;function _0x1289f1(_0x48e9b5,_0x294bd3){return _0x17944e(_0x48e9b5-_0x4d6f1c._0x201df7,_0x294bd3);}const _0x4a3001={};_0x4a3001['type']='FeatureCollection',_0x4a3001[_0x12706d(-0xd3,-_0x188694._0x3948ae)]=this[_0x12706d(-0xab,-0x6e)]['_data'];const _0x289f7f=mars3d__namespace[_0x12706d(-0xd5,-_0x188694._0x2ca93a)]['getExtentByGeoJSON'](_0x4a3001);if(!_0x289f7f)return;return _0x581b62!==null&&_0x581b62!==void 0x0&&_0x581b62[_0x12706d(-_0x188694._0x2531ff,-0x30)]?_0x289f7f:Cesium[_0x12706d(-0x59,-0x62)]['fromDegrees'](_0x289f7f[_0x12706d(-0xd9,-0xcf)],_0x289f7f['ymin'],_0x289f7f['xmax'],_0x289f7f[_0x12706d(-0xb0,-0xed)]);}[_0x4b25e5(-0x1cf,-0x1c1)](_0x12d65c){function _0xb783a1(_0x1e54dd,_0x1b3e9b){return _0x17944e(_0x1b3e9b-0x170,_0x1e54dd);}this['_cache_event']=_0x12d65c;function _0x38ee08(_0x2c498d,_0x22eb74){return _0x17944e(_0x2c498d- -0x45c,_0x22eb74);}this[_0x38ee08(-0x1a6,-0x18f)]&&this['_mapVRenderer']['clickEvent'](_0x12d65c[_0xb783a1(0x416,0x3fd)],_0x12d65c);}[_0x4b25e5(-0x1bc,-0x1d8)](_0x12a722){this['_cache_event']=_0x12a722;function _0x2f306e(_0x11b482,_0x363735){return _0x4b25e5(_0x11b482-0x442,_0x363735);}this['_mapVRenderer']&&this['_mapVRenderer']['mousemoveEvent'](_0x12a722[_0x2f306e(0x2a8,0x2e5)],_0x12a722);}['on'](_0x4a8c70,_0x4c3362,_0x25e34f){const _0x48b56d={_0x294553:0x2f4,_0x173dc7:0x30d,_0x5a6800:0x313,_0x1f2d0c:0x309,_0x2e69b8:0x33a},_0x5cf5ac={_0x2d8a7f:0x4f8},_0x338605={_0x23eef6:0x276};this['options'][_0x40eb09(0x30d,_0x48b56d._0x294553)]=this['options'][_0x40eb09(_0x48b56d._0x173dc7,0x340)]||{};if(_0x4a8c70===mars3d__namespace['EventType'][_0x40eb09(0x2d3,0x2b4)])this['options'][_0x40eb09(0x30d,0x352)]['click']=_0x2ef4a4=>{function _0x457b4a(_0x183d8c,_0x59d0db){return _0x49c340(_0x183d8c,_0x59d0db- -0xf3);}if(_0x2ef4a4){const _0x53b9c4={...this['_cache_event']};_0x53b9c4[_0x457b4a(_0x338605._0x23eef6,0x292)]=this,_0x53b9c4['data']=_0x2ef4a4,_0x4c3362['bind'](_0x25e34f)(_0x53b9c4);}},this[_0x40eb09(_0x48b56d._0x5a6800,0x2d2)]['on'](mars3d__namespace[_0x40eb09(_0x48b56d._0x1f2d0c,0x33c)]['click'],this['_onMapClick'],this);else _0x4a8c70===mars3d__namespace['EventType'][_0x40eb09(0x320,_0x48b56d._0x2e69b8)]&&(this['options']['methods']['mousemove']=_0x3ec70e=>{if(_0x3ec70e){const _0x5c8f23={...this['_cache_event']};_0x5c8f23['layer']=this,_0x5c8f23['data']=_0x3ec70e,_0x4c3362['bind'](_0x25e34f)(_0x5c8f23);}},this['_map']['on'](mars3d__namespace['EventType']['mouseMove'],this['_onMapMouseMove'],this));function _0x40eb09(_0x39dfed,_0x3b5ee4){return _0x17944e(_0x39dfed-0x70,_0x3b5ee4);}function _0x49c340(_0xdd0cdf,_0x2e519a){return _0x4b25e5(_0x2e519a-_0x5cf5ac._0x2d8a7f,_0xdd0cdf);}return this;}['off'](_0x51df38,_0x891c91){const _0xf50c01={_0x4b686d:0xd2,_0x5d13f4:0x90,_0x184c64:0x1b,_0xd95a2e:0x4a,_0x1b5255:0x9,_0x45f28a:0x5,_0x5f51d7:0x9f,_0x4121ea:0x63};if(_0x51df38===_0x28521c(-_0xf50c01._0x4b686d,-_0xf50c01._0x5d13f4)){var _0x35e085;this[_0x28521c(-0x92,-0xae)][_0x3d4127(-_0xf50c01._0x184c64,-0x4b)](_0x51df38,this['_onMapClick'],this),(_0x35e085=this[_0x3d4127(-_0xf50c01._0xd95a2e,-0x40)]['methods'])!==null&&_0x35e085!==void 0x0&&_0x35e085[_0x3d4127(0x20,-_0xf50c01._0x1b5255)]&&delete this['options']['methods'][_0x28521c(-0xd2,-0x8e)];}else{if(_0x51df38===_0x3d4127(_0xf50c01._0x45f28a,0x2)){var _0x20c9b6;this['_map']['off'](_0x51df38,this['_onMapMouseMove'],this),(_0x20c9b6=this['options'][_0x3d4127(-0xe,-0xd)])!==null&&_0x20c9b6!==void 0x0&&_0x20c9b6['mousemove']&&delete this[_0x28521c(-0xd4,-_0xf50c01._0x5f51d7)][_0x28521c(-0x98,-_0xf50c01._0x4121ea)]['mousemove'];}}function _0x28521c(_0x4ba643,_0x18f89f){return _0x17944e(_0x4ba643- -0x335,_0x18f89f);}function _0x3d4127(_0x10b3b7,_0x1e9bff){return _0x17944e(_0x10b3b7- -0x2ab,_0x1e9bff);}return this;}}function _0x49da(_0x4405be,_0x3a1be9){const _0x5f7752=_0x5f77();return _0x49da=function(_0x49da6e,_0x5871be){_0x49da6e=_0x49da6e-0x162;let _0x210428=_0x5f7752[_0x49da6e];if(_0x49da['rWkqGo']===undefined){var _0x4186f9=function(_0x2eca3d){const _0x244773='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';let _0x4551c4='',_0x2d6549='';for(let _0x2d374e=0x0,_0x13f4de,_0x4cb417,_0x5a0be5=0x0;_0x4cb417=_0x2eca3d['charAt'](_0x5a0be5++);~_0x4cb417&&(_0x13f4de=_0x2d374e%0x4?_0x13f4de*0x40+_0x4cb417:_0x4cb417,_0x2d374e++%0x4)?_0x4551c4+=String['fromCharCode'](0xff&_0x13f4de>>(-0x2*_0x2d374e&0x6)):0x0){_0x4cb417=_0x244773['indexOf'](_0x4cb417);}for(let _0x3af4ed=0x0,_0x3dcff0=_0x4551c4['length'];_0x3af4ed<_0x3dcff0;_0x3af4ed++){_0x2d6549+='%'+('00'+_0x4551c4['charCodeAt'](_0x3af4ed)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x2d6549);};_0x49da['KcvCVL']=_0x4186f9,_0x4405be=arguments,_0x49da['rWkqGo']=!![];}const _0x3a12ec=_0x5f7752[0x0],_0x49c447=_0x49da6e+_0x3a12ec,_0x1834e1=_0x4405be[_0x49c447];return!_0x1834e1?(_0x210428=_0x49da['KcvCVL'](_0x210428),_0x4405be[_0x49c447]=_0x210428):_0x210428=_0x1834e1,_0x210428;},_0x49da(_0x4405be,_0x3a1be9);}function _0x17944e(_0x166bdc,_0x3d778f){const _0x32cdd5={_0x4dd50b:0xe1};return _0x49da(_0x166bdc-_0x32cdd5._0x4dd50b,_0x3d778f);}mars3d__namespace[_0x17944e(0x270,0x23e)]['register']('mapv',MapVLayer),mars3d__namespace['layer'][_0x4b25e5(-0x17d,-0x1af)]=MapVLayer,mars3d__namespace[_0x4b25e5(-0x1b1,-0x1c6)]=mapv__namespace,exports['MapVLayer']=MapVLayer,Object[_0x17944e(0x2c5,0x2ad)](mapv)['forEach'](function(_0x18ab0f){if(_0x18ab0f!=='default'&&!exports['hasOwnProperty'](_0x18ab0f))Object['defineProperty'](exports,_0x18ab0f,{'enumerable':!![],'get':function(){return mapv[_0x18ab0f];}});});const _0x3af4ed={};_0x3af4ed[_0x17944e(0x2c0,0x286)]=!![],Object['defineProperty'](exports,'__esModule',_0x3af4ed);
}));
