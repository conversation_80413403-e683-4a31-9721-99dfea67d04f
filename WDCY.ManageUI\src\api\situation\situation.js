import request from '@/utils/request'

export const personList = (data) =>
	request({
		url: '/personInAndOut',
		method: 'get',
		params: data
	})

export const personListDelete = (id) =>
	request({
		url: '/personInAndOut',
		method: 'delete',
		params: {
			id: id
		}
	})

export const vehicleList = (data) =>
	request({
		url: '/vehicleInAndOut',
		method: 'get',
		params: data
	})

export const vehicleListDelete = (id) =>
	request({
		url: '/vehicleInAndOut',
		method: 'delete',
		params: {
			id: id
		}
	}) 

	export const vehiclePointList = (data) =>
	request({
		url: '/vehicleFlowInfo',
		method: 'get',
		params: data
	})

export const vehiclePointListDelete = (id) =>
	request({
		url: '/vehicleFlowInfo',
		method: 'delete',
		params: {
			id: id
		}
	}) 

	export const getVehiclePoint = (id) =>
	request({
		url: '/vehicleFlowInfo/'+id,
		method: 'get',
	})

	export const vehiclePointListAdd = (data) =>
	request({
		url: '/vehicleFlowInfo',
		method: 'post',
		data: data
	})
export const vehiclePointListEdit = (data) =>
	request({
		url: '/vehicleFlowInfo',
		method: 'put',
		data: data
	})
	

	export const touristPointList = (data) =>
	request({
		url: '/touristFlowInfo',
		method: 'get',
		params: data
	})

export const touristPointListDelete = (id) =>
	request({
		url: '/touristFlowInfo',
		method: 'delete',
		params: {
			id: id
		}
	}) 
	export const getTouristPoint = (id) =>
	request({
		url: '/touristFlowInfo/'+id,
		method: 'get',
	})

	export const touristPointListAdd = (data) =>
	request({
		url: '/touristFlowInfo',
		method: 'post',
		data: data
	})
export const touristPointListEdit = (data) =>
	request({
		url: '/touristFlowInfo',
		method: 'put',
		data: data
	})
	export const touristFlowDeviceList = (data) =>
	request({
		url: '/touristFlowDevice',
		method: 'get',
		data: data
	}) 

	export const vehicleFlowDeviceList = (id) =>
	request({
		url: '/vehicleFlowDevice',
		method: 'get',
		params: {
			id: id
		}
	}) 
	export const vehicleFlowDeviceListAdd = (data) =>
	request({
		url: '/vehicleFlowDevice',
		method: 'post',
		data: data
	})
	export const vehicleFlowDeviceListDelete = (data) =>
	request({
		url: '/vehicleFlowDevice',
		method: 'delete',
		data: data
	}) 

	export const touristFlowDeviceListAdd = (data) =>
	request({
		url: '/touristFlowDevice',
		method: 'post',
		data: data
	})
	export const touristFlowDeviceListDelete = (data) =>
	request({
		url: '/touristFlowDevice',
		method: 'delete',
		data: data
	})
