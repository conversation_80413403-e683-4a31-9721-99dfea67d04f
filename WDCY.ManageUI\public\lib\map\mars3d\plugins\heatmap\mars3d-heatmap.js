/**
 * Mars3D平台插件,结合heatmap可视化功能插件  mars3d-heatmap
 *
 * 版本信息：v3.7.22
 * 编译日期：2024-07-15 21:21:06
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2024-01-15
 */
(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
	typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-heatmap"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';(function(_0xa9f641,_0x40c2ee){var _0x812369={_0x288654:0x12f,_0x5babed:0x1d7,_0x5187f2:0x187,_0x129f7c:0x19f,_0xef965c:0x126,_0x569fac:0x1a3},_0xf9dce8={_0x579e19:0x6d},_0x14df7d={_0x5227cb:0xce};function _0x4b66fe(_0xf66055,_0x46b04f){return _0x2b6e(_0x46b04f-_0x14df7d._0x5227cb,_0xf66055);}function _0xae4d54(_0x66bca4,_0x46e2c7){return _0x2b6e(_0x46e2c7-_0xf9dce8._0x579e19,_0x66bca4);}var _0x32ca67=_0xa9f641();while(!![]){try{var _0x59433d=-parseInt(_0xae4d54(0x114,_0x812369._0x288654))/0x1+-parseInt(_0xae4d54(0x100,0x12b))/0x2*(-parseInt(_0x4b66fe(0x226,_0x812369._0x5babed))/0x3)+-parseInt(_0xae4d54(0xbf,0xe5))/0x4+parseInt(_0xae4d54(_0x812369._0x5187f2,0x162))/0x5+-parseInt(_0xae4d54(0xee,0x118))/0x6*(-parseInt(_0xae4d54(_0x812369._0x129f7c,0x14b))/0x7)+-parseInt(_0xae4d54(0x154,0x15b))/0x8*(parseInt(_0xae4d54(0x178,_0x812369._0xef965c))/0x9)+parseInt(_0x4b66fe(0x15e,_0x812369._0x569fac))/0xa;if(_0x59433d===_0x40c2ee)break;else _0x32ca67['push'](_0x32ca67['shift']());}catch(_0x76e9ef){_0x32ca67['push'](_0x32ca67['shift']());}}}(_0x15e2,0x452b5));function _interopNamespace(_0x24e20b){var _0x289cb7={_0x1fd163:0x4,_0x1e976a:0x10,_0x3bdd59:0x321},_0x7d7588={_0x229324:0xe7};function _0x2f88f1(_0x36a924,_0x20f43a){return _0x2b6e(_0x20f43a-0x3c,_0x36a924);}if(_0x24e20b&&_0x24e20b['__esModule'])return _0x24e20b;var _0x4c4fc7=Object['create'](null);return _0x24e20b&&Object['keys'](_0x24e20b)[_0x2f88f1(0xc3,0xb2)](function(_0xa372ab){function _0x4822a4(_0x1d3a4f,_0x121a70){return _0x2f88f1(_0x121a70,_0x1d3a4f-0x237);}function _0x36595a(_0x2ba260,_0x5bb428){return _0x2f88f1(_0x2ba260,_0x5bb428- -_0x7d7588._0x229324);}if(_0xa372ab!=='default'){var _0x100776=Object[_0x36595a(_0x289cb7._0x1fd163,-_0x289cb7._0x1e976a)](_0x24e20b,_0xa372ab);Object[_0x4822a4(_0x289cb7._0x3bdd59,0x350)](_0x4c4fc7,_0xa372ab,_0x100776['get']?_0x100776:{'enumerable':!![],'get':function(){return _0x24e20b[_0xa372ab];}});}}),_0x4c4fc7['default']=_0x24e20b,_0x4c4fc7;}function _0x15e2(){var _0x4c81bb=['u3rLBMnPBez1BMn0Aw9U','x2XHEwvY','Eg1PBG','z2v0rgf0yq','zxHWB3j0CW','mtiWwM1StNr3','x3HgAwvSza','6k+35BYv5ywLigHLyxrTyxaUANmG5BQtia','twf0zxjPywXuExbL','y2XLyxjszwn0','s0vfua','y29UDgfPBMvY','ndi4nZmWDLHbCfDs','z2v0rgf0yvvsta','Bwf4t3bHy2L0Eq','Dw5PzM9YBxm','ug9PBNruCMfUCW','BwvYz2u','yM9KEq','CMvUzgvYywXS','CMvJDgfUz2XL','u3rLBMnPBe9WzxjHDgLVBG','C2v0rgf0yq','x3jLBMrLCKjVDw5KyxjPzxm','BwLUq2fUDMfZu2L6zq','Bwf4','z3jHBNvSyxjPDhK','mc42','x29UrxH0CMvTyunOyw5Nzq','x21VDw50zwriB29R','CgX1z2LUCW','AdmZnW','nda2otjeCxjky0q','Cg9ZAxrPB246ywjZB2X1Dgu7BgvMDdOWo3rVCdOWoW','AxnbCNjHEq','Bwf4q2fUDMfZu2L6zq','yxjJuMfKAxvZu2nHBgu','zM9YBwf0uMvJDgfUz2XL','x2HLyxq','yMX1CG','DMvYDgv4rM9YBwf0','x2rYyxDbBhbOyq','zMLSBfn0EwXL','y2XLyxi','y29VCMrPBMf0zxm','y3nZvgv4Da','zgvMyxvSDa','C3r5Bgu','Dw5KzwzPBMvK','zMLSBfjLy3q','zgvMyxvSDeDYywrPzw50','vxrPBa','x3jLy3rHBMDSzq','yxbWBhK','zxH0CMvTywnOyw5Nzq','x2DLDeLUDgvYBMfSrgf0yq','Bg9UBgf0mM1LCMnHDg9Y','x3rLBxbSyxrLCW','Bgf5zxi','C2HHzg93q3r4','z3jHCgHPyW','x2nVBNrHAw5LCG','yNvTCe1HCa','q2vZAxvT','x2DLDefYy0HLyxrdyw52yxm','CMDIkdi1nsWYntuSmJu1kq','Ew1PBG','zM9YrwfJAa','x2XHC3rFBujVDw5KCW','mtqYnteYogfsvM9jBa','x2nVBMzPzW','y2XHC3noyw1L','x3zHBhvLrMLLBgq','x3bVC2L0Aw9UCW','CMvUzgvYCgfYDgLHBa','z2v0q29UDgv4Da','AgvPz2H0','x2DLDeHLyxrdyw52yxm','z2v0vMfSDwvbDa','y2fUDMfZ','x2nMz1jHzgL1CW','mc44nq','Dg9eyxrHvvjm','x3vUt3jNyw5PEMveyxrH','y29UzMLNDxjL','x19LC01VzhvSzq','x3vWzgf0zvbVC2L0Aw9UC0HVB2S','Cg9ZAxrPB25Z','y3jLyxrL','BgvUz3rO','z3jLzw4','rg9TvxrPBa','uMvJDgfUz2XLuhjPBwL0AxzL','CMfKAq','x21HEe9WywnPDhK','EwvSBg93','y2fSBa','mc44','Aw1Hz2u','zw1PDa','x3DPzhrO','x3nLDfn0EwXLCW','x21cB3vUzhnnyxG','CMvWBgfJzq','z2v0t3DUuhjVCgvYDhLezxnJCMLWDg9Y','CM91BMq','x3jLBMrLCMvY','x2HLAwDODa','C3bSAxroDw0','zgvMyxvSDfjLBMrLCMvY','zgLMzKHLAwDODa','zgvMyxvSDejSDxi','yMfJA2DYB3vUzenVBg9Y','Aw1Hz2uVCg5N','rwXSAxbZB2LKu3vYzMfJzufWCgvHCMfUy2u','x3bHBgv0Dgu','y3jLyxrLtwf0zxjPywW','ChjVDg90ExbL','BwLUt3bHy2L0Eq','DxbKyxrLq29UzMLN','mtmYvK96t1j6','x2nVB3jKAw5HDg9Y','Bg5N','zgvMAw5LuhjVCgvYDhK','ChvZAa','x3vZzuDYywrPzw50t3bHy2L0Eq','Bgf0','CMfKAxvZ','B25fEhrYzw1Hq2HHBMDL','rvbtrZOZodu3','AgvHDa','x3n0B3jL','Ew1HEa','D2LKDgG6','ndi4otrszhLtzeO','yxjJ','BwLU','zgf0yq','sw1Hz2uY','nNzesMTPvq','C2v0q29VCMrPBMf0B3i','AgvHDfn0EwXL','D2LKDgG','ntqWndaXqMj6BLLq','x29Yz2fUAxnLrgf0yq','y3r4','B3b0Aw9UCW','x2nYzwf0zufYy0DYyxbOAwm','z3jHzgLLBNq','x29WywnPDhK','Dw5PzM9YBsbZyw1WBgvYmKqGAw1Hz2u7cGPJEM1FBwf0zxjPywWGy3PTx2DLDe1HDgvYAwfSkgn6Bv9TyxrLCMLHBeLUChv0ig1HDgvYAwfSsw5WDxqPihSkicbJEM1FBwf0zxjPywWGBwf0zxjPywWGpsbJEM1Fz2v0rgvMyxvSDe1HDgvYAwfSkg1HDgvYAwfSsw5WDxqPoWOGihzLyZiGC3qGpsbTyxrLCMLHBeLUChv0lNn0oWOGihzLyZqGy29SB3jjBwfNzsa9ihrLEhr1CMuOAw1Hz2uSihn0ktSkicbPzIHJB2XVCKLTywDLlNjNyIa9psb2zwmZkdeUmcKGFhWGy29SB3jjBwfNzs5Yz2iGpt0GDMvJmYGWlJaPksb7cIaGicbKAxnJyxjKoWOGih0kicbTyxrLCMLHBc5KAwzMDxnLid0Gy29SB3jjBwfNzs5Yz2i7cIaGBwf0zxjPywWUywXWAgeGpsbJB2XVCKLTywDLlMe7cIaGCMv0DxjUig1HDgvYAwfSoWP9cG','x2rHDge','y3jLyxrLrwXLBwvUDa','z2v0sw1Hz2veyxrH','x2DYyxbOAwm','y1n0B3jL','zgvMyxvSDfjHzgL1CW','A2v5CW','x21PBG','ywrKrgf0yq','x3LgAwvSza','x2DYyxbOAwmY','nZqWndi2mffRshf2sq','Eg1HEa','qMfZzuXHEwvY','zgvMyxvSDfzHBhvLrMLLBgq','mc41nq','yMvNAw5qyxrO','uMvUzgvYu3rHDgu','C2HVDW','ywrKq29SB3jtDg9W','mtiYmZGXBxDvrfb4','x21HCa','B3bHy2L0Eq','y29SB3i','CMDIysGWldaSmcWXkq','x3vWzgf0zuDYywrPzw50','C2v0rgf0yu1PBG','x3jHzgK','zNjVBurLz3jLzxm','DMfSDwu','x21HEa'];_0x15e2=function(){return _0x4c81bb;};return _0x15e2();}function _mergeNamespaces(_0x54db60,_0x3ce08c){var _0x4db544={_0xb4d8cd:0x1f4};return _0x3ce08c['forEach'](function(_0x1f99fc){var _0x59ae13={_0x300ad9:0x11c},_0x11920d={_0x23509a:0x3d};function _0x155ca5(_0x1954aa,_0x1dd8ee){return _0x2b6e(_0x1dd8ee- -0x2e2,_0x1954aa);}function _0x4e68b6(_0x1e7dc0,_0x349fbd){return _0x2b6e(_0x1e7dc0- -_0x4db544._0xb4d8cd,_0x349fbd);}_0x1f99fc&&typeof _0x1f99fc!=='string'&&!Array[_0x4e68b6(-0xe9,-0x119)](_0x1f99fc)&&Object['keys'](_0x1f99fc)[_0x155ca5(-0x247,-0x26c)](function(_0xa96456){var _0x16ef0c={_0x158964:0x4b3};function _0x22bc9a(_0x255e40,_0x40a8f5){return _0x4e68b6(_0x255e40-_0x11920d._0x23509a,_0x40a8f5);}function _0x23e3be(_0x2f3c6d,_0x15b11d){return _0x4e68b6(_0x2f3c6d-_0x16ef0c._0x158964,_0x15b11d);}if(_0xa96456!=='default'&&!(_0xa96456 in _0x54db60)){var _0x3f26e9=Object[_0x22bc9a(-_0x59ae13._0x300ad9,-0x138)](_0x1f99fc,_0xa96456);Object[_0x23e3be(0x36d,0x3b9)](_0x54db60,_0xa96456,_0x3f26e9['get']?_0x3f26e9:{'enumerable':!![],'get':function(){return _0x1f99fc[_0xa96456];}});}});}),_0x54db60;}var mars3d__namespace=_interopNamespace(mars3d),commonjsGlobal=typeof globalThis!==_0x106b06(-0xdb,-0x103)?globalThis:typeof window!=='undefined'?window:typeof global!==_0x106b06(-0xdb,-0xa2)?global:typeof self!=='undefined'?self:{},_0x57e799={};_0x57e799['exports']={};var heatmap$1=_0x57e799;function _0x485cd6(_0x180d11,_0x4fb09c){return _0x2b6e(_0x4fb09c- -0x2ff,_0x180d11);}(function(_0x1b308a){var _0x192338={_0x51b5aa:0x1f2,_0x35959f:0x1dc,_0x4478ea:0x1c0,_0x2e7079:0x143,_0x1a937d:0x145,_0x522649:0x188,_0x2645f4:0x19d},_0x207456={_0x4de8dd:0xe1},_0xe772b7={_0x552ea4:0x454},_0x4322b7={_0x517640:0x202},_0x40709e={_0x312504:0x41a,_0x161642:0x3b1},_0x17832a={_0x3ded17:0x180};function _0x2a31a1(_0x165c29,_0x5768a9){return _0x485cd6(_0x5768a9,_0x165c29-_0x17832a._0x3ded17);}(function(_0xef1e7f,_0x11b46c,_0x247456){_0x1b308a['exports']?_0x1b308a['exports']=_0x247456():_0x11b46c[_0xef1e7f]=_0x247456();}(_0x2a31a1(-0x77,-0x89),commonjsGlobal,function(){var _0x3abe41={_0x4f18da:0xf7,_0xc5d976:0xa4},_0xb872={_0x4fc4b3:0x385,_0x289ebf:0x3a0,_0x461b78:0x235,_0x1d20a8:0x228,_0x450d91:0x29d},_0x5b0b9f={_0x2d5e95:0x6d,_0x411be7:0x3e,_0x823fd6:0x79,_0x455a0f:0x8c,_0x455f58:0x4b},_0x47d3e3={_0x7e9cb4:0x16,_0x4759db:0x41,_0x3450ef:0x54,_0x369d54:0xe,_0x5a3a65:0x3dd},_0x29b4d3={_0x1ed527:0x1fc,_0x24353f:0xb7,_0x373cfb:0x16c,_0x2e6209:0xf5,_0x448d77:0xe7},_0x5143b0={_0x370fe0:0xed,_0x568ea2:0x163},_0x29090d={_0x3e5c48:0x162,_0x4e0a79:0x3ac,_0x5e466d:0x39a},_0x361667={_0x294f7c:0x2ce,_0x3629a0:0x2e5,_0x547cd6:0x1b4,_0xef73ca:0x235,_0x4abca9:0x235,_0x2151e1:0x277,_0x5650ed:0x367,_0x7aeadc:0x2cf,_0x42a20e:0x1f4,_0x3c599c:0x1e0,_0x4a7b12:0x2b6},_0x30c2fa={_0x365baf:0xb0,_0xdf1e8c:0x109,_0x5b5e6e:0xfb,_0x9e8c35:0xc2,_0x1aa49f:0x138},_0x581c14={_0x1f2a8f:0x3c6,_0x4d77f4:0x39d,_0x1268f6:0x149},_0x6c23dd={_0x3c5800:0xad,_0xe048e0:0xca,_0x340b88:0xfc},_0x5d1471={_0x2ef9f2:0x45a,_0x3e4a3e:0x425,_0x828e9c:0x4ae,_0x13515d:0x1a4},_0x2d3dd0={_0x1d51d1:0x3c0,_0x1adbe6:0x34f},_0x4d4785={};function _0x47c568(_0x15f90e,_0x2fb26c){return _0x2a31a1(_0x2fb26c-0x298,_0x15f90e);}_0x4d4785['0.25']='rgb(0,0,255)',_0x4d4785[_0x47c568(0x21e,_0x192338._0x51b5aa)]='rgb(0,255,0)',_0x4d4785[_0x4a903a(-0x1b3,-_0x192338._0x35959f)]='yellow',_0x4d4785['1']='rgb(255,0,0)';var _0x1b7ec4={};_0x1b7ec4['defaultRadius']=0x28,_0x1b7ec4[_0x4a903a(-0x18f,-_0x192338._0x4478ea)]='canvas2d',_0x1b7ec4[_0x4a903a(-_0x192338._0x2e7079,-_0x192338._0x1a937d)]=_0x4d4785,_0x1b7ec4['defaultMaxOpacity']=0x1,_0x1b7ec4['defaultMinOpacity']=0x0,_0x1b7ec4[_0x47c568(0x20e,0x1bb)]=0.85,_0x1b7ec4['defaultXField']='x',_0x1b7ec4['defaultYField']='y',_0x1b7ec4[_0x4a903a(-_0x192338._0x2e7079,-_0x192338._0x522649)]='value',_0x1b7ec4['plugins']={};var _0x5c8184=_0x1b7ec4,_0x37a59c=function _0x5d7c4f(){var _0x2c7850={_0x192415:0x266},_0x45d79b={_0x48b556:0xa8,_0x4d5c8f:0x7f,_0x19d4bf:0x484},_0x8a1181={_0x1e7a18:0xd5,_0xfe974a:0x11b,_0x2fe0b6:0xc6,_0x3c8a51:0x1be,_0x42e2d2:0xc4},_0x46e902={_0x3d9f69:0xf5,_0xe39dca:0xe9,_0x1e7954:0x1f},_0x57f0f1={_0x1a2bc5:0x1af,_0x481212:0x161,_0x269475:0x193,_0x3ca42b:0x1a5,_0x511dd6:0x1a4,_0x574e58:0x1a7,_0x2aecdf:0x1de,_0x5dfd0e:0x1c2},_0x3a58ac=function _0x21ab2a(_0x1ba1cf){var _0x5f1556={_0x3f670c:0x240};this['_coordinator']={},this['_data']=[],this[_0x3bca77(-0x192,-0x15b)]=[];function _0x19a420(_0x39a9ab,_0xc530a5){return _0x2b6e(_0x39a9ab-0x2f7,_0xc530a5);}this[_0x19a420(0x3c8,0x417)]=0xa,this['_max']=0x1,this['_xField']=_0x1ba1cf['xField']||_0x1ba1cf['defaultXField'];function _0x3bca77(_0x18e941,_0x46da35){return _0x2b6e(_0x46da35- -_0x5f1556._0x3f670c,_0x18e941);}this['_yField']=_0x1ba1cf['yField']||_0x1ba1cf['defaultYField'],this[_0x19a420(0x372,_0x2d3dd0._0x1d51d1)]=_0x1ba1cf['valueField']||_0x1ba1cf['defaultValueField'],_0x1ba1cf[_0x19a420(0x3a9,_0x2d3dd0._0x1adbe6)]&&(this['_cfgRadius']=_0x1ba1cf[_0x3bca77(-0x1d0,-0x18e)]);},_0xa9138d=_0x5c8184[_0x1749b8(_0x40709e._0x312504,0x3d8)];_0x3a58ac[_0x1749b8(0x3e8,_0x40709e._0x161642)]={'_organiseData':function(_0x66ac4a,_0xa35e9e){var _0x9b8450=_0x66ac4a[this[_0x5d3f34(_0x57f0f1._0x1a2bc5,_0x57f0f1._0x481212)]],_0x393da1=_0x66ac4a[this[_0x5d3f34(_0x57f0f1._0x269475,0x1c8)]];function _0x294527(_0x266f2b,_0x24c9d3){return _0x1749b8(_0x266f2b,_0x24c9d3- -0x4e2);}var _0x392ccb=this[_0x5d3f34(_0x57f0f1._0x3ca42b,0x19e)],_0x15b69d=this['_data'],_0x2d0e78=this['_max'];function _0x5d3f34(_0x36e06d,_0xd3c635){return _0x1749b8(_0xd3c635,_0x36e06d- -0x249);}var _0x23f5ab=this['_min'],_0x828196=_0x66ac4a[this['_valueField']]||0x1,_0x294892=_0x66ac4a[_0x294527(-0x15e,-0x127)]||this[_0x294527(-0x170,-0x156)]||_0xa9138d;!_0x15b69d[_0x9b8450]&&(_0x15b69d[_0x9b8450]=[],_0x392ccb[_0x9b8450]=[]);!_0x15b69d[_0x9b8450][_0x393da1]?(_0x15b69d[_0x9b8450][_0x393da1]=_0x828196,_0x392ccb[_0x9b8450][_0x393da1]=_0x294892):_0x15b69d[_0x9b8450][_0x393da1]+=_0x828196;var _0x5d9120=_0x15b69d[_0x9b8450][_0x393da1];if(_0x5d9120>_0x2d0e78)return!_0xa35e9e?this['_max']=_0x5d9120:this['setDataMax'](_0x5d9120),![];else{if(_0x5d9120<_0x23f5ab)return!_0xa35e9e?this[_0x294527(-0x13e,-0x108)]=_0x5d9120:this[_0x5d3f34(_0x57f0f1._0x511dd6,0x1e1)](_0x5d9120),![];else{var _0x1e9ca8={};return _0x1e9ca8['x']=_0x9b8450,_0x1e9ca8['y']=_0x393da1,_0x1e9ca8[_0x5d3f34(_0x57f0f1._0x574e58,_0x57f0f1._0x2aecdf)]=_0x828196,_0x1e9ca8['radius']=_0x294892,_0x1e9ca8['min']=_0x23f5ab,_0x1e9ca8[_0x5d3f34(_0x57f0f1._0x5dfd0e,0x1b5)]=_0x2d0e78,_0x1e9ca8;}}},'_unOrganizeData':function(){var _0x106571=[],_0x9682df=this['_data'],_0x17df7a=this['_radi'];function _0x43dbb1(_0x1a8725,_0x37c0cc){return _0x1749b8(_0x37c0cc,_0x1a8725- -0x3f2);}for(var _0xacd21d in _0x9682df){for(var _0x4d7c03 in _0x9682df[_0xacd21d]){var _0x26921a={};_0x26921a['x']=_0xacd21d,_0x26921a['y']=_0x4d7c03,_0x26921a['radius']=_0x17df7a[_0xacd21d][_0x4d7c03],_0x26921a['value']=_0x9682df[_0xacd21d][_0x4d7c03],_0x106571[_0x34bd82(-0xa3,-_0x46e902._0x3d9f69)](_0x26921a);}}var _0xc170c6={};_0xc170c6[_0x34bd82(-0xbe,-_0x46e902._0xe39dca)]=this['_min'],_0xc170c6[_0x34bd82(-0x7d,-0xa2)]=this['_max'],_0xc170c6[_0x43dbb1(-0x2d,-_0x46e902._0x1e7954)]=_0x106571;function _0x34bd82(_0x399e80,_0x3c0158){return _0x1bf031(_0x3c0158-0x1d9,_0x399e80);}return _0xc170c6;},'_onExtremaChange':function(){function _0x4a04bd(_0x41abb5,_0xb93fe6){return _0x1bf031(_0x41abb5-0x450,_0xb93fe6);}function _0x378d9b(_0x209f48,_0x95bcd){return _0x1bf031(_0x95bcd-0x70c,_0x209f48);}this['_coordinator'][_0x378d9b(_0x5d1471._0x2ef9f2,_0x5d1471._0x3e4a3e)](_0x378d9b(0x46c,_0x5d1471._0x828e9c),{'min':this[_0x4a04bd(_0x5d1471._0x13515d,0x14e)],'max':this['_max']});},'addData':function(){function _0x32254d(_0x184208,_0x40ddc2){return _0x1bf031(_0x184208-0x3c6,_0x40ddc2);}function _0x3ce7cb(_0x40bf89,_0xdce615){return _0x1bf031(_0x40bf89-0xe7,_0xdce615);}if(arguments[0x0][_0x32254d(_0x8a1181._0x1e7a18,0x11b)]>0x0){var _0xa7f7a8=arguments[0x0],_0x5708ba=_0xa7f7a8['length'];while(_0x5708ba--){this[_0x32254d(_0x8a1181._0xfe974a,_0x8a1181._0x2fe0b6)]['call'](this,_0xa7f7a8[_0x5708ba]);}}else{var _0x27b28a=this['_organiseData'](arguments[0x0],!![]);_0x27b28a&&(this[_0x3ce7cb(-0x1cc,-_0x8a1181._0x3c8a51)][_0x32254d(_0x8a1181._0x1e7a18,_0x8a1181._0x42e2d2)]===0x0&&(this['_min']=this['_max']=_0x27b28a['value']),this['_coordinator']['emit'](_0x32254d(0xc6,0xf9),{'min':this['_min'],'max':this['_max'],'data':[_0x27b28a]}));}return this;},'setData':function(_0x1fd333){var _0x58556b={_0xaa7814:0x73f},_0x3a3677=_0x1fd333['data'],_0x575d4a=_0x3a3677[_0x17b999(_0x45d79b._0x48b556,_0x45d79b._0x4d5c8f)];this['_data']=[],this['_radi']=[];for(var _0x1df04d=0x0;_0x1df04d<_0x575d4a;_0x1df04d++){this[_0x395e0e(0x485,_0x45d79b._0x19d4bf)](_0x3a3677[_0x1df04d],![]);}function _0x395e0e(_0x3ae139,_0x3e7889){return _0x1bf031(_0x3ae139-_0x58556b._0xaa7814,_0x3e7889);}this['_max']=_0x1fd333['max'],this['_min']=_0x1fd333['min']||0x0,this['_onExtremaChange'](),this[_0x17b999(0xc8,0x9e)][_0x395e0e(0x458,0x44f)]('renderall',this['_getInternalData']());function _0x17b999(_0x2779cd,_0xfaed1f){return _0x1bf031(_0x2779cd-0x399,_0xfaed1f);}return this;},'removeData':function(){},'setDataMax':function(_0x2cd202){this[_0x2e2b8d(0x107,_0x6c23dd._0x3c5800)]=_0x2cd202,this[_0x2e2b8d(0x107,_0x6c23dd._0xe048e0)](),this['_coordinator']['emit']('renderall',this[_0x192b8b(0xe0,_0x6c23dd._0x340b88)]());function _0x2e2b8d(_0x309d86,_0x58ced5){return _0x1749b8(_0x309d86,_0x58ced5- -0x344);}function _0x192b8b(_0x2c8218,_0x1c6dfd){return _0x1749b8(_0x1c6dfd,_0x2c8218- -0x349);}return this;},'setDataMin':function(_0x388cac){this['_min']=_0x388cac;function _0xe844f4(_0x129f27,_0x1dd89d){return _0x1bf031(_0x129f27-0x63e,_0x1dd89d);}this[_0xe844f4(_0x581c14._0x1f2a8f,_0x581c14._0x4d77f4)]();function _0x187144(_0x4f663f,_0x18354d){return _0x1bf031(_0x4f663f-0x42d,_0x18354d);}return this['_coordinator'][_0x187144(0x146,_0x581c14._0x1268f6)]('renderall',this['_getInternalData']()),this;},'setCoordinator':function(_0x5540d9){this['_coordinator']=_0x5540d9;},'_getInternalData':function(){var _0x1c7a7d={};_0x1c7a7d[_0x5ead21(0x290,0x280)]=this[_0x5ead21(0x2bc,_0x2c7850._0x192415)],_0x1c7a7d['min']=this['_min'];function _0x37bc9a(_0x19e13e,_0xc9edec){return _0x1bf031(_0x19e13e-0x532,_0xc9edec);}function _0x5ead21(_0x105719,_0x1e52cc){return _0x1749b8(_0x105719,_0x1e52cc- -0x18b);}return _0x1c7a7d['data']=this['_data'],_0x1c7a7d[_0x5ead21(0x24c,0x20e)]=this['_radi'],_0x1c7a7d;},'getData':function(){var _0x30343c={_0x1125b0:0x628};function _0x1d26cc(_0x3e92bb,_0x2de40b){return _0x1749b8(_0x2de40b,_0x3e92bb- -_0x30343c._0x1125b0);}return this[_0x1d26cc(-0x299,-0x2dd)]();}};function _0x1749b8(_0x296a8b,_0x702da8){return _0x47c568(_0x296a8b,_0x702da8-0x1f0);}function _0x1bf031(_0x260fba,_0x4d20ee){return _0x47c568(_0x4d20ee,_0x260fba- -0x496);}return _0x3a58ac;}(),_0x873c18=function _0xab8bef(){var _0x1d95ff={_0x2ad41e:0x31e,_0x11a6e1:0x2f8,_0x403aa9:0xab,_0x1a37f8:0x289,_0x4b3243:0xc0,_0x1be377:0x8b,_0x51c981:0x309},_0x507ea3={_0x501304:0x120,_0x355df6:0x9f,_0x2730e8:0xc5,_0x271475:0x143,_0x1c5204:0x135,_0xa122c9:0x284,_0x119043:0x113,_0x59c631:0xf0},_0x46a213={_0x4e1ee0:0x21a,_0x31560e:0x20c,_0x55e6b4:0x46b},_0x29c134={_0x150889:0x6b},_0x4cdb02={_0xdeabc0:0x26b},_0x3098e9={_0x320786:0x1a3,_0xeb05d6:0x422,_0x15e07a:0x3dc},_0x4fd6dc={_0xf22b6:0x16a,_0xc822bc:0x45f,_0x74fe71:0x467,_0x198d78:0x42b,_0x502619:0x14d,_0x443eb4:0x49f},_0x3fbc7c={_0x46e55c:0x1ad,_0x149e0c:0x1bf,_0x4e0b3f:0x380,_0xec365c:0x3d7},_0x22c776={_0x2e802c:0xd0},_0x500f24=function(_0x3d6b51){var _0x3e5e95=_0x3d6b51['gradient']||_0x3d6b51['defaultGradient'],_0x136076=document['createElement']('canvas'),_0x49dbc8={};function _0x218a9d(_0x235e2b,_0x38502e){return _0x2b6e(_0x38502e-0x2bd,_0x235e2b);}_0x49dbc8['willReadFrequently']=!![];var _0x2637a0=_0x136076['getContext']('2d',_0x49dbc8);_0x136076['width']=0x100,_0x136076['height']=0x1;var _0x14e6d6=_0x2637a0['createLinearGradient'](0x0,0x0,0x100,0x1);for(var _0x491823 in _0x3e5e95){_0x14e6d6[_0xe16eec(_0x3fbc7c._0x46e55c,_0x3fbc7c._0x149e0c)](_0x491823,_0x3e5e95[_0x491823]);}_0x2637a0['fillStyle']=_0x14e6d6;function _0xe16eec(_0xf3560,_0x54ccf5){return _0x2b6e(_0xf3560-_0x22c776._0x2e802c,_0x54ccf5);}return _0x2637a0[_0x218a9d(_0x3fbc7c._0x4e0b3f,_0x3fbc7c._0xec365c)](0x0,0x0,0x100,0x1),_0x2637a0['getImageData'](0x0,0x0,0x100,0x1)['data'];},_0x3a6b4b=function(_0x1f0d36,_0x379179){var _0x90afc0={_0x5a8725:0x385},_0x475d94=document[_0x2b8b07(-0x186,-0x198)]('canvas'),_0x3bcda7={};_0x3bcda7['willReadFrequently']=!![];var _0x12823b=_0x475d94['getContext']('2d',_0x3bcda7);function _0x2b8b07(_0x2668df,_0x47ae6c){return _0x2b6e(_0x47ae6c- -0x263,_0x2668df);}var _0x1b25af=_0x1f0d36,_0x2d5ff8=_0x1f0d36;function _0x52f079(_0x3868df,_0x45b471){return _0x2b6e(_0x3868df-_0x90afc0._0x5a8725,_0x45b471);}_0x475d94[_0x2b8b07(-_0x4fd6dc._0xf22b6,-0x1a2)]=_0x475d94['height']=_0x1f0d36*0x2;if(_0x379179==0x1)_0x12823b[_0x52f079(_0x4fd6dc._0xc822bc,0x469)](),_0x12823b[_0x2b8b07(-0x1bb,-0x1a9)](_0x1b25af,_0x2d5ff8,_0x1f0d36,0x0,0x2*Math['PI'],![]),_0x12823b[_0x52f079(0x498,0x452)]=_0x52f079(_0x4fd6dc._0x74fe71,_0x4fd6dc._0x198d78),_0x12823b['fill']();else{var _0x350552=_0x12823b['createRadialGradient'](_0x1b25af,_0x2d5ff8,_0x1f0d36*_0x379179,_0x1b25af,_0x2d5ff8,_0x1f0d36);_0x350552[_0x2b8b07(-0x13c,-0x186)](0x0,_0x2b8b07(-_0x4fd6dc._0x502619,-0x181)),_0x350552['addColorStop'](0x1,'rgba(0,0,0,0)'),_0x12823b[_0x52f079(0x498,0x4b9)]=_0x350552,_0x12823b[_0x52f079(_0x4fd6dc._0x443eb4,0x4a1)](0x0,0x0,0x2*_0x1f0d36,0x2*_0x1f0d36);}return _0x475d94;},_0x27df67=function(_0x2c588e){function _0xa6aed5(_0xfc68ce,_0x45a0d8){return _0x2b6e(_0x45a0d8- -0x1c4,_0xfc68ce);}var _0x4da387=[],_0xefcea3=_0x2c588e[_0xa6aed5(-_0x30c2fa._0x365baf,-_0x30c2fa._0xdf1e8c)],_0x2b65a5=_0x2c588e[_0xa6aed5(-_0x30c2fa._0x5b5e6e,-_0x30c2fa._0x9e8c35)],_0x2e2978=_0x2c588e['radi'],_0x2c588e=_0x2c588e[_0xa6aed5(-0x11f,-0x108)],_0x1b1432=Object['keys'](_0x2c588e);function _0x449590(_0x40df53,_0x286d73){return _0x2b6e(_0x286d73-0x23f,_0x40df53);}var _0x43392a=_0x1b1432[_0x449590(0x305,0x2cb)];while(_0x43392a--){var _0x2f1506=_0x1b1432[_0x43392a],_0x3c7807=Object[_0x449590(0x368,0x30f)](_0x2c588e[_0x2f1506]),_0x2c57d6=_0x3c7807[_0xa6aed5(-0x128,-_0x30c2fa._0x1aa49f)];while(_0x2c57d6--){var _0x4984a7=_0x3c7807[_0x2c57d6],_0x1a7b6d=_0x2c588e[_0x2f1506][_0x4984a7],_0x549135=_0x2e2978[_0x2f1506][_0x4984a7],_0x5a5f59={};_0x5a5f59['x']=_0x2f1506,_0x5a5f59['y']=_0x4984a7,_0x5a5f59['value']=_0x1a7b6d,_0x5a5f59['radius']=_0x549135,_0x4da387['push'](_0x5a5f59);}}var _0x24792c={};return _0x24792c['min']=_0xefcea3,_0x24792c[_0xa6aed5(-0x10f,-0xc2)]=_0x2b65a5,_0x24792c['data']=_0x4da387,_0x24792c;};function _0x274fc4(_0x4aaca5){var _0x11dff2=_0x4aaca5[_0x4c561f(-0x2de,-0x2f1)],_0x5112b5=this['shadowCanvas']=document['createElement']('canvas');function _0x43388e(_0xa5fb51,_0x5a21de){return _0x2b6e(_0xa5fb51- -0x2f6,_0x5a21de);}function _0x4c561f(_0xa2fb41,_0xcf25fb){return _0x2b6e(_0xcf25fb- -0x3e5,_0xa2fb41);}var _0x26aaf9=this[_0x43388e(-0x274,-0x2c6)]=_0x4aaca5[_0x43388e(-0x274,-_0x361667._0x294f7c)]||document[_0x4c561f(-0x361,-0x31a)]('canvas');this[_0x4c561f(-0x291,-_0x361667._0x3629a0)]=[0x2710,0x2710,0x0,0x0];var _0x99b48=getComputedStyle(_0x4aaca5[_0x43388e(-0x202,-_0x361667._0x547cd6)])||{};_0x26aaf9[_0x43388e(-0x27c,-0x241)]='heatmap-canvas',this['_width']=_0x26aaf9[_0x43388e(-_0x361667._0xef73ca,-0x253)]=_0x5112b5[_0x4c561f(-0x374,-0x324)]=_0x4aaca5[_0x43388e(-_0x361667._0x4abca9,-0x215)]||+_0x99b48['width'][_0x43388e(-0x25c,-0x23d)](/px/,''),this['_height']=_0x26aaf9['height']=_0x5112b5['height']=_0x4aaca5[_0x43388e(-_0x361667._0x2151e1,-0x2a0)]||+_0x99b48[_0x4c561f(-0x337,-0x366)]['replace'](/px/,'');var _0x5230a9={};_0x5230a9['willReadFrequently']=!![],this['shadowCtx']=_0x5112b5['getContext']('2d',_0x5230a9);var _0x1e86c7={};_0x1e86c7['willReadFrequently']=!![],this['ctx']=_0x26aaf9[_0x4c561f(-0x350,-_0x361667._0x5650ed)]('2d',_0x1e86c7),_0x26aaf9['style'][_0x4c561f(-0x291,-_0x361667._0x7aeadc)]=_0x5112b5[_0x43388e(-0x1de,-_0x361667._0x42a20e)][_0x43388e(-_0x361667._0x3c599c,-0x1d9)]=_0x4c561f(-_0x361667._0x4a7b12,-0x2db),_0x11dff2['style']['position']='relative',_0x11dff2['appendChild'](_0x26aaf9),this['_palette']=_0x500f24(_0x4aaca5),this['_templates']={},this[_0x43388e(-0x25e,-0x280)](_0x4aaca5);}_0x274fc4[_0x5025bb(_0x4322b7._0x517640,0x1e1)]={'renderPartial':function(_0x3faa3a){var _0x14210e={_0x3bc115:0x3b9};function _0x2b723e(_0x30cfad,_0x24ac32){return _0x5025bb(_0x30cfad- -_0x14210e._0x3bc115,_0x24ac32);}function _0x276405(_0x3739c4,_0x288394){return _0x5025bb(_0x3739c4-0x1b6,_0x288394);}_0x3faa3a[_0x2b723e(-_0x3098e9._0x320786,-0x15d)]['length']>0x0&&(this[_0x276405(_0x3098e9._0xeb05d6,_0x3098e9._0x15e07a)](_0x3faa3a),this['_colorize']());},'renderAll':function(_0x4115da){this['_clear'](),_0x4115da['data']['length']>0x0&&(this['_drawAlpha'](_0x27df67(_0x4115da)),this['_colorize']());},'_updateGradient':function(_0x580953){function _0x284474(_0x333e61,_0x595bba){return _0x5025bb(_0x595bba- -_0x4cdb02._0xdeabc0,_0x333e61);}this[_0x284474(-0x1b,-_0x29c134._0x150889)]=_0x500f24(_0x580953);},'updateConfig':function(_0x2290e7){var _0x2cfad5={_0x50c260:0x7};_0x2290e7[_0x136668(_0x46a213._0x4e1ee0,_0x46a213._0x31560e)]&&this[_0x534e38(_0x46a213._0x55e6b4,0x410)](_0x2290e7);function _0x136668(_0x3816e8,_0xdcb126){return _0x5025bb(_0x3816e8- -_0x2cfad5._0x50c260,_0xdcb126);}function _0x534e38(_0x5c22be,_0x29e771){return _0x5025bb(_0x5c22be-0x22e,_0x29e771);}this['_setStyles'](_0x2290e7);},'setDimensions':function(_0x377aa3,_0x2b1cf3){this[_0x1571c9(_0x29090d._0x3e5c48,0x18a)]=_0x377aa3;function _0x3b5830(_0x1fc982,_0x5d4e5d){return _0x5025bb(_0x5d4e5d-0x1be,_0x1fc982);}this[_0x3b5830(0x3d8,0x3b6)]=_0x2b1cf3;function _0x1571c9(_0x3edc93,_0x25a1da){return _0x5025bb(_0x3edc93- -0x8f,_0x25a1da);}this[_0x3b5830(_0x29090d._0x4e0a79,0x39a)]['width']=this['shadowCanvas']['width']=_0x377aa3,this[_0x3b5830(0x349,_0x29090d._0x5e466d)]['height']=this['shadowCanvas']['height']=_0x2b1cf3;},'_clear':function(){var _0x1be4a8={_0x27bfad:0x38f};function _0x6ee526(_0x539bcf,_0x522c24){return _0x5025bb(_0x539bcf- -0xda,_0x522c24);}function _0x2d7c4d(_0x2be19a,_0xc8fecc){return _0x5025bb(_0x2be19a- -_0x1be4a8._0x27bfad,_0xc8fecc);}this['shadowCtx'][_0x2d7c4d(-0x143,-_0x5143b0._0x370fe0)](0x0,0x0,this['_width'],this[_0x2d7c4d(-0x197,-_0x5143b0._0x568ea2)]),this['ctx']['clearRect'](0x0,0x0,this['_width'],this['_height']);},'_setStyles':function(_0x1b30f6){this['_blur']=_0x1b30f6['blur']==0x0?0x0:_0x1b30f6[_0x581104(_0x29b4d3._0x1ed527,0x1fb)]||_0x1b30f6['defaultBlur'];_0x1b30f6['backgroundColor']&&(this['canvas'][_0xc1a0eb(0x156,0x13a)][_0xc1a0eb(0xe1,0x122)]=_0x1b30f6['backgroundColor']);function _0xc1a0eb(_0x4b0ec0,_0x1b7e78){return _0x5025bb(_0x4b0ec0- -0x11c,_0x1b7e78);}function _0x581104(_0x41bd86,_0xbedf5b){return _0x5025bb(_0xbedf5b- -0x6f,_0x41bd86);}this['_width']=this[_0xc1a0eb(0xc0,_0x29b4d3._0x24353f)]['width']=this['shadowCanvas'][_0x581104(_0x29b4d3._0x373cfb,0x1ac)]=_0x1b30f6[_0x581104(0x16b,0x1ac)]||this['_width'],this['_height']=this['canvas']['height']=this['shadowCanvas']['height']=_0x1b30f6[_0x581104(0x138,0x16a)]||this[_0x581104(0x185,0x189)],this['_opacity']=(_0x1b30f6['opacity']||0x0)*0xff,this['_maxOpacity']=(_0x1b30f6[_0xc1a0eb(0x135,_0x29b4d3._0x2e6209)]||_0x1b30f6['defaultMaxOpacity'])*0xff,this['_minOpacity']=(_0x1b30f6[_0xc1a0eb(_0x29b4d3._0x448d77,0xde)]||_0x1b30f6['defaultMinOpacity'])*0xff,this[_0x581104(0x1a7,0x19b)]=!!_0x1b30f6['useGradientOpacity'];},'_drawAlpha':function(_0x55232b){var _0x5bbbf1={_0x5e7e9c:0x147},_0x24be51=this['_min']=_0x55232b['min'],_0x202a78=this[_0x58d4c5(-0x280,-0x2c3)]=_0x55232b[_0x4abf1c(_0x507ea3._0x501304,0x115)],_0x55232b=_0x55232b['data']||[],_0x1520e1=_0x55232b[_0x4abf1c(0x63,_0x507ea3._0x355df6)];function _0x4abf1c(_0x49f1b1,_0x849b76){return _0x5025bb(_0x849b76- -_0x5bbbf1._0x5e7e9c,_0x49f1b1);}var _0x2ae736=0x1-this['_blur'];function _0x58d4c5(_0x5b6ea9,_0x3891e4){return _0x5025bb(_0x3891e4- -0x505,_0x5b6ea9);}while(_0x1520e1--){var _0x296034=_0x55232b[_0x1520e1],_0x55273d=_0x296034['x'],_0x662e7=_0x296034['y'],_0x1660c5=_0x296034[_0x4abf1c(0xfd,_0x507ea3._0x2730e8)],_0x13aaf8=Math['min'](_0x296034['value'],_0x202a78),_0x49d623=_0x55273d-_0x1660c5,_0x4fe250=_0x662e7-_0x1660c5,_0x2cf99c=this['shadowCtx'],_0x4299aa;!this[_0x4abf1c(_0x507ea3._0x271475,_0x507ea3._0x1c5204)][_0x1660c5]?this['_templates'][_0x1660c5]=_0x4299aa=_0x3a6b4b(_0x1660c5,_0x2ae736):_0x4299aa=this['_templates'][_0x1660c5];var _0x23f4a8=(_0x13aaf8-_0x24be51)/(_0x202a78-_0x24be51);_0x2cf99c['globalAlpha']=_0x23f4a8<0.01?0.01:_0x23f4a8,_0x2cf99c['drawImage'](_0x4299aa,_0x49d623,_0x4fe250),_0x49d623<this[_0x58d4c5(-_0x507ea3._0xa122c9,-0x2ab)][0x0]&&(this[_0x4abf1c(0xd6,_0x507ea3._0x119043)][0x0]=_0x49d623),_0x4fe250<this['_renderBoundaries'][0x1]&&(this['_renderBoundaries'][0x1]=_0x4fe250),_0x49d623+0x2*_0x1660c5>this['_renderBoundaries'][0x2]&&(this['_renderBoundaries'][0x2]=_0x49d623+0x2*_0x1660c5),_0x4fe250+0x2*_0x1660c5>this[_0x4abf1c(_0x507ea3._0x59c631,0x113)][0x3]&&(this['_renderBoundaries'][0x3]=_0x4fe250+0x2*_0x1660c5);}},'_colorize':function(){var _0x185d42={_0x2186da:0x19b},_0x2fc9ef=this[_0x1a42a8(0x30b,0x2f8)][0x0],_0x27e7cf=this[_0x1a42a8(_0x1d95ff._0x2ad41e,_0x1d95ff._0x11a6e1)][0x1],_0x177855=this['_renderBoundaries'][0x2]-_0x2fc9ef,_0x20c430=this['_renderBoundaries'][0x3]-_0x27e7cf;function _0x1a42a8(_0x5113f0,_0x2a18b7){return _0x5025bb(_0x2a18b7-0x9e,_0x5113f0);}var _0x1b9977=this['_width'],_0x2f1a9f=this[_0x1a42a8(0x2cb,0x296)],_0x3f5b20=this[_0x3832e5(_0x1d95ff._0x403aa9,0x87)],_0x1d0a46=this[_0x1a42a8(0x249,_0x1d95ff._0x1a37f8)],_0x44594b=this['_minOpacity'],_0x4c90ef=this['_useGradientOpacity'];_0x2fc9ef<0x0&&(_0x2fc9ef=0x0);_0x27e7cf<0x0&&(_0x27e7cf=0x0);_0x2fc9ef+_0x177855>_0x1b9977&&(_0x177855=_0x1b9977-_0x2fc9ef);_0x27e7cf+_0x20c430>_0x2f1a9f&&(_0x20c430=_0x2f1a9f-_0x27e7cf);function _0x3832e5(_0x6632b2,_0x47e280){return _0x5025bb(_0x47e280- -_0x185d42._0x2186da,_0x6632b2);}var _0x5d0b32=this[_0x1a42a8(0x2fa,0x31c)][_0x3832e5(_0x1d95ff._0x4b3243,_0x1d95ff._0x1be377)](_0x2fc9ef,_0x27e7cf,_0x177855,_0x20c430),_0x4cef45=_0x5d0b32[_0x1a42a8(_0x1d95ff._0x51c981,0x2b4)],_0x2cc8f7=_0x4cef45['length'],_0x39e251=this['_palette'];for(var _0x4529aa=0x3;_0x4529aa<_0x2cc8f7;_0x4529aa+=0x4){var _0x5b872e=_0x4cef45[_0x4529aa],_0x3f4a34=_0x5b872e*0x4;if(!_0x3f4a34)continue;var _0x4dd70e;_0x3f5b20>0x0?_0x4dd70e=_0x3f5b20:_0x5b872e<_0x1d0a46?_0x5b872e<_0x44594b?_0x4dd70e=_0x44594b:_0x4dd70e=_0x5b872e:_0x4dd70e=_0x1d0a46,_0x4cef45[_0x4529aa-0x3]=_0x39e251[_0x3f4a34],_0x4cef45[_0x4529aa-0x2]=_0x39e251[_0x3f4a34+0x1],_0x4cef45[_0x4529aa-0x1]=_0x39e251[_0x3f4a34+0x2],_0x4cef45[_0x4529aa]=_0x4c90ef?_0x39e251[_0x3f4a34+0x3]:_0x4dd70e;}this['ctx']['putImageData'](_0x5d0b32,_0x2fc9ef,_0x27e7cf),this['_renderBoundaries']=[0x3e8,0x3e8,0x0,0x0];},'getValueAt':function(_0x11533d){var _0x10926e,_0x5eff3c=this[_0x291ff4(0x1e1,0x213)],_0x463942=_0x5eff3c['getImageData'](_0x11533d['x'],_0x11533d['y'],0x1,0x1),_0x4b2aa8=_0x463942['data'][0x3];function _0x291ff4(_0x262b41,_0x5a1276){return _0x5025bb(_0x262b41- -0x9d,_0x5a1276);}var _0x8f31f0=this['_max'],_0x4ffa1a=this['_min'];return _0x10926e=Math['abs'](_0x8f31f0-_0x4ffa1a)*(_0x4b2aa8/0xff)>>0x0,_0x10926e;},'getDataURL':function(){return this['canvas']['toDataURL']();}};function _0x5025bb(_0x57f760,_0x2cd80d){return _0x4a903a(_0x2cd80d,_0x57f760-0x3ba);}return _0x274fc4;}(),_0x1edfd9=function _0xa9a5e5(){var _0x23ec6b=![];return _0x5c8184['defaultRenderer']==='canvas2d'&&(_0x23ec6b=_0x873c18),_0x23ec6b;}(),_0x49c737={};_0x49c737[_0x4a903a(-_0x192338._0x2645f4,-0x166)]=function(){var _0x6b9128={},_0x438e81=arguments['length'];for(var _0x5f1b3d=0x0;_0x5f1b3d<_0x438e81;_0x5f1b3d++){var _0x20a086=arguments[_0x5f1b3d];for(var _0x592b28 in _0x20a086){_0x6b9128[_0x592b28]=_0x20a086[_0x592b28];}}return _0x6b9128;};var _0x55e328=_0x49c737,_0x2fa0df=function _0x4f24f2(){var _0x4cc618={_0x5492e1:0x38e,_0x450f29:0x3ab},_0xca7a81={_0x4af47f:0xec,_0x323e1e:0x12e},_0x4fde92={_0x251439:0x1e2},_0x45e42f={_0x2a7d0d:0x11f},_0x328f45={_0x1c1093:0x78},_0x5e9f48={_0xd150fc:0x221,_0x3176ba:0x1ab},_0xccbd24={_0x30bfa5:0x34e},_0x3f1f6e={_0x17051e:0x1e4},_0x36c3af={_0x1f26a7:0x2e6},_0x4e34cc={_0xf31d18:0x353},_0x47803d={_0x1e6e82:0x286},_0x716172={_0x3f5dd7:0x14f,_0x28d7f6:0x38a,_0x22c3f1:0x36a},_0x1834d0={_0x1f5c5b:0x26d},_0x1febaa={_0x3e507b:0x1f8,_0x2f8de5:0x200,_0x2096d3:0x248,_0xa960ca:0x45a,_0x4ffb86:0x260},_0x1c2130={_0x1e12f3:0x10a},_0x2b2074={_0x3343bd:0x72},_0x2bbaef=function _0x5dd269(){var _0x359c48={_0xad8334:0x2b6};function _0x185789(){this['cStore']={};}_0x185789[_0x161d06(0x13d,0x17e)]={'on':function(_0x522a76,_0xb3148f,_0x47c40e){var _0x43451a=this[_0x428b94(_0x2b2074._0x3343bd,0x1e)];!_0x43451a[_0x522a76]&&(_0x43451a[_0x522a76]=[]);function _0x428b94(_0x324006,_0x4e5337){return _0x161d06(_0x4e5337,_0x324006- -0x132);}_0x43451a[_0x522a76]['push'](function(_0x19700d){function _0xa379ae(_0x302ee5,_0x4b9a07){return _0x428b94(_0x4b9a07-0x27f,_0x302ee5);}return _0xb3148f[_0xa379ae(0x30e,_0x359c48._0xad8334)](_0x47c40e,_0x19700d);});},'emit':function(_0x21fa55,_0x3c336c){var _0x4f2076=this['cStore'];if(_0x4f2076[_0x21fa55]){var _0x4ff52c=_0x4f2076[_0x21fa55]['length'];for(var _0x406cf6=0x0;_0x406cf6<_0x4ff52c;_0x406cf6++){var _0x5d6401=_0x4f2076[_0x21fa55][_0x406cf6];_0x5d6401(_0x3c336c);}}}};function _0x161d06(_0x383b49,_0x8bd3ba){return _0x2b6e(_0x8bd3ba-0xd6,_0x383b49);}return _0x185789;}(),_0x1b44f7=function(_0x2a7c36){var _0x3c163b={_0x26e2e9:0x24f},_0x70c38e={_0x30049a:0x350};function _0x13c78d(_0x1e4f37,_0x373c3d){return _0x2b6e(_0x373c3d- -_0x1c2130._0x1e12f3,_0x1e4f37);}var _0x2c1b93=_0x2a7c36['_renderer'],_0x9368e6=_0x2a7c36[_0x13c78d(-_0x47d3e3._0x7e9cb4,-0x5e)],_0x47c4b4=_0x2a7c36[_0x13c78d(-_0x47d3e3._0x4759db,-_0x47d3e3._0x3450ef)];_0x9368e6['on']('renderpartial',_0x2c1b93['renderPartial'],_0x2c1b93),_0x9368e6['on'](_0x13c78d(0x15,-_0x47d3e3._0x369d54),_0x2c1b93['renderAll'],_0x2c1b93);function _0x50e396(_0x288d4c,_0x4250ac){return _0x2b6e(_0x288d4c-_0x70c38e._0x30049a,_0x4250ac);}_0x9368e6['on']('extremachange',function(_0x395b3b){function _0xe537ef(_0x259bb6,_0x1d548f){return _0x13c78d(_0x259bb6,_0x1d548f-0x4aa);}function _0x2e0145(_0x3594a6,_0x2a041b){return _0x13c78d(_0x2a041b,_0x3594a6-_0x3c163b._0x26e2e9);}_0x2a7c36['_config']['onExtremaChange']&&_0x2a7c36['_config'][_0x2e0145(_0x1febaa._0x3e507b,0x1b0)]({'min':_0x395b3b[_0x2e0145(_0x1febaa._0x2f8de5,_0x1febaa._0x2096d3)],'max':_0x395b3b['max'],'gradient':_0x2a7c36['_config']['gradient']||_0x2a7c36[_0xe537ef(_0x1febaa._0xa960ca,0x419)][_0x2e0145(_0x1febaa._0x4ffb86,0x27a)]});}),_0x47c4b4[_0x50e396(0x40f,_0x47d3e3._0x5a3a65)](_0x9368e6);};function _0x13d29d(){var _0x3a0405=this['_config']=_0x55e328[_0x4fd279(-_0x5b0b9f._0x2d5e95,-_0x5b0b9f._0x411be7)](_0x5c8184,arguments[0x0]||{});function _0x4fd279(_0x179d94,_0x3dd1f1){return _0x2b6e(_0x3dd1f1- -0x138,_0x179d94);}function _0x3eefe9(_0x4deb77,_0x54e9a5){return _0x2b6e(_0x54e9a5-0x1fb,_0x4deb77);}this[_0x4fd279(-_0x5b0b9f._0x823fd6,-_0x5b0b9f._0x455a0f)]=new _0x2bbaef();if(_0x3a0405['plugin']){var _0x280766=_0x3a0405['plugin'];if(!_0x5c8184[_0x4fd279(-_0x5b0b9f._0x455f58,-0x31)][_0x280766])throw new Error('Plugin\x20\x27'+_0x280766+'\x27\x20not\x20found.\x20Maybe\x20it\x20was\x20not\x20registered.');else{var _0xabe10a=_0x5c8184['plugins'][_0x280766];this[_0x3eefe9(0x245,0x298)]=new _0xabe10a['renderer'](_0x3a0405),this['_store']=new _0xabe10a['store'](_0x3a0405);}}else this['_renderer']=new _0x1edfd9(_0x3a0405),this['_store']=new _0x37a59c(_0x3a0405);_0x1b44f7(this);}return _0x13d29d['prototype']={'addData':function(){function _0xd84a62(_0x172ca5,_0x520a33){return _0x2b6e(_0x520a33-0x76,_0x172ca5);}function _0x263781(_0x5e1105,_0x34e2d2){return _0x2b6e(_0x5e1105- -0x14,_0x34e2d2);}return this['_store'][_0x263781(0xbe,0x92)]['apply'](this[_0x263781(0xa2,0x84)],arguments),this;},'removeData':function(){this['_store']['removeData']&&this[_0x598dab(-0x1ee,-0x1b7)]['removeData'][_0x598dab(-0x167,-_0x716172._0x3f5dd7)](this[_0x78bf(_0x716172._0x28d7f6,_0x716172._0x22c3f1)],arguments);function _0x78bf(_0x2f23b4,_0x5d6501){return _0x2b6e(_0x2f23b4-0x2d4,_0x5d6501);}function _0x598dab(_0x4b7537,_0x36339b){return _0x2b6e(_0x36339b- -_0x1834d0._0x1f5c5b,_0x4b7537);}return this;},'setData':function(){this['_store'][_0x397a8e(_0xb872._0x4fc4b3,_0xb872._0x289ebf)][_0x1e2300(-_0xb872._0x461b78,-_0xb872._0x1d20a8)](this[_0x1e2300(-_0xb872._0x450d91,-0x290)],arguments);function _0x397a8e(_0x38f9e1,_0x13c728){return _0x2b6e(_0x38f9e1-_0x47803d._0x1e6e82,_0x13c728);}function _0x1e2300(_0x5738d7,_0x2770dd){return _0x2b6e(_0x5738d7- -_0x4e34cc._0xf31d18,_0x2770dd);}return this;},'setDataMax':function(){function _0x1fc981(_0x1905b6,_0xda4fdd){return _0x2b6e(_0xda4fdd- -0x8d,_0x1905b6);}function _0x5aecb3(_0x1bdcb2,_0x3bc25a){return _0x2b6e(_0x3bc25a-0x106,_0x1bdcb2);}return this[_0x5aecb3(0x1c7,0x1bc)]['setDataMax']['apply'](this[_0x1fc981(0x2e,0x29)],arguments),this;},'setDataMin':function(){var _0x335c7f={_0x39c1e8:0x2c8};function _0x159520(_0x49b6b2,_0x2a55cc){return _0x2b6e(_0x49b6b2- -_0x36c3af._0x1f26a7,_0x2a55cc);}function _0x25806d(_0x10b8af,_0x24683d){return _0x2b6e(_0x10b8af- -_0x335c7f._0x39c1e8,_0x24683d);}return this['_store'][_0x25806d(-_0x3f1f6e._0x17051e,-0x1a5)]['apply'](this[_0x159520(-0x230,-0x1fa)],arguments),this;},'configure':function(_0x349965){var _0x554b74={_0x4e1d3a:0x2cb};function _0x39058b(_0x27c6a9,_0x27101f){return _0x2b6e(_0x27c6a9- -_0xccbd24._0x30bfa5,_0x27101f);}this[_0x39058b(-0x2d5,-0x2cd)]=_0x55e328['merge'](this['_config'],_0x349965),this['_renderer'][_0xcef380(-_0x5e9f48._0xd150fc,-0x1e7)](this['_config']),this[_0x39058b(-0x2a2,-0x285)]['emit']('renderall',this[_0xcef380(-0x215,-0x26c)][_0xcef380(-_0x5e9f48._0x3176ba,-0x1a1)]());function _0xcef380(_0x3da8c5,_0x4ae8e0){return _0x2b6e(_0x3da8c5- -_0x554b74._0x4e1d3a,_0x4ae8e0);}return this;},'repaint':function(){var _0x582d12={_0x11de9c:0x42};function _0x36f22d(_0x31f0cf,_0x472640){return _0x2b6e(_0x31f0cf-_0x328f45._0x1c1093,_0x472640);}this['_coordinator'][_0x36f22d(0x10e,_0x45e42f._0x2a7d0d)](_0x36f22d(0x174,0x17c),this[_0x36f22d(0x12e,0x13c)]['_getInternalData']());function _0x4def4b(_0x34988a,_0x429eba){return _0x2b6e(_0x429eba- -_0x582d12._0x11de9c,_0x34988a);}return this;},'getData':function(){function _0x57691c(_0x426eec,_0x20a752){return _0x2b6e(_0x20a752- -0x190,_0x426eec);}function _0x2a2814(_0x370237,_0x46204d){return _0x2b6e(_0x370237- -0x5a,_0x46204d);}return this[_0x57691c(-_0x3abe41._0x4f18da,-0xda)][_0x57691c(-0x68,-_0x3abe41._0xc5d976)]();},'getDataURL':function(){function _0x75fcc2(_0x37fa97,_0x419840){return _0x2b6e(_0x37fa97- -_0x4fde92._0x251439,_0x419840);}return this['_renderer'][_0x75fcc2(-_0xca7a81._0x4af47f,-_0xca7a81._0x323e1e)]();},'getValueAt':function(_0xaefb7c){function _0x174ed1(_0x9112ca,_0x38cd08){return _0x2b6e(_0x9112ca-0x2f1,_0x38cd08);}function _0x6cc053(_0x49b8fb,_0x1fbb46){return _0x2b6e(_0x49b8fb-0x200,_0x1fbb46);}if(this['_store']['getValueAt'])return this['_store'][_0x174ed1(0x372,0x39a)](_0xaefb7c);else return this[_0x174ed1(_0x4cc618._0x5492e1,_0x4cc618._0x450f29)]['getValueAt']?this['_renderer']['getValueAt'](_0xaefb7c):null;}},_0x13d29d;}(),_0x337d20={'create':function(_0x482171){return new _0x2fa0df(_0x482171);},'register':function(_0x363c44,_0x2c382d){var _0x4b2401={_0x165956:0x23d};function _0x101125(_0x5289d0,_0x2a4074){return _0x47c568(_0x2a4074,_0x5289d0-_0x4b2401._0x165956);}_0x5c8184[_0x101125(0x45d,_0xe772b7._0x552ea4)][_0x363c44]=_0x2c382d;}};function _0x4a903a(_0x12de60,_0x16190b){return _0x2a31a1(_0x16190b- -_0x207456._0x4de8dd,_0x12de60);}return _0x337d20;}));}(heatmap$1));function _0x2b6e(_0x105732,_0x30b0bc){var _0x15e2b3=_0x15e2();return _0x2b6e=function(_0x2b6e07,_0x4552d8){_0x2b6e07=_0x2b6e07-0x76;var _0x1119fe=_0x15e2b3[_0x2b6e07];if(_0x2b6e['KssEoO']===undefined){var _0xbecd1a=function(_0x57e799){var _0x2d47d8='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';var _0x1405ac='',_0x1679f2='';for(var _0x4058d6=0x0,_0x2768fb,_0x356592,_0x4b8733=0x0;_0x356592=_0x57e799['charAt'](_0x4b8733++);~_0x356592&&(_0x2768fb=_0x4058d6%0x4?_0x2768fb*0x40+_0x356592:_0x356592,_0x4058d6++%0x4)?_0x1405ac+=String['fromCharCode'](0xff&_0x2768fb>>(-0x2*_0x4058d6&0x6)):0x0){_0x356592=_0x2d47d8['indexOf'](_0x356592);}for(var _0x55ce39=0x0,_0x2dfba2=_0x1405ac['length'];_0x55ce39<_0x2dfba2;_0x55ce39++){_0x1679f2+='%'+('00'+_0x1405ac['charCodeAt'](_0x55ce39)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x1679f2);};_0x2b6e['fEYLMJ']=_0xbecd1a,_0x105732=arguments,_0x2b6e['KssEoO']=!![];}var _0x42aa66=_0x15e2b3[0x0],_0x28ae7d=_0x2b6e07+_0x42aa66,_0x3153dd=_0x105732[_0x28ae7d];return!_0x3153dd?(_0x1119fe=_0x2b6e['fEYLMJ'](_0x1119fe),_0x105732[_0x28ae7d]=_0x1119fe):_0x1119fe=_0x3153dd,_0x1119fe;},_0x2b6e(_0x105732,_0x30b0bc);}var heatmap=heatmap$1[_0x106b06(-0x107,-0xf6)],_0x5289d9={};_0x5289d9['__proto__']=null,_0x5289d9[_0x106b06(-0xdd,-0xd8)]=heatmap;var h337=_mergeNamespaces(_0x5289d9,[heatmap$1['exports']]),HeatMaterial=_0x485cd6(-0x23e,-0x236);if(!heatmap$1[_0x106b06(-0x107,-0x121)]['create'])throw new Error(_0x106b06(-0x104,-0xeb));const Cesium=mars3d__namespace[_0x106b06(-0xcc,-0xe5)],BaseLayer=mars3d__namespace[_0x485cd6(-0x19b,-0x1dc)][_0x485cd6(-0x1ec,-0x228)];var _0x5acfcc={};_0x5acfcc['0.4']='blue',_0x5acfcc[_0x485cd6(-0x1b2,-0x1fb)]=_0x106b06(-0x167,-0x10d),_0x5acfcc[_0x485cd6(-0x288,-0x26b)]=_0x485cd6(-0x213,-0x26d),_0x5acfcc['0.9']='red';var _0x132832={};_0x132832[_0x485cd6(-0x245,-0x208)]=0.8,_0x132832[_0x485cd6(-0x230,-0x256)]=0.1,_0x132832['blur']=0.85,_0x132832['radius']=0x19,_0x132832['gradient']=_0x5acfcc;const DEF_HEATSTYLE=_0x132832;var _0x3baafa={};_0x3baafa['arcRadiusScale']=1.5,_0x3baafa['arcBlurScale']=1.5,_0x3baafa[_0x485cd6(-0x1b9,-0x1ee)]=Cesium[_0x106b06(-0x14f,-0x15e)]['VERTEX_FORMAT'];const DEF_STYLE=_0x3baafa;class HeatLayer extends BaseLayer{constructor(_0x83845f={}){var _0xe27098={_0x34d44a:0xcc,_0x446a69:0x196,_0x4bd3b6:0x122,_0x5bb9c6:0xa1,_0x3496ee:0x20a,_0x3f013c:0x200,_0x2136f7:0x209,_0x156821:0xd5,_0xa98943:0x1db,_0x32bddc:0x20f,_0x412a6c:0x130,_0x1aa1a3:0x20f,_0x162b5a:0x1bc,_0x2163cf:0x225,_0x5f3403:0x1ce},_0x3a4946={_0x454f9a:0x302};super(_0x83845f),this['options'][_0x342368(_0xe27098._0x34d44a,0x122)]=this[_0x50a7b9(_0xe27098._0x446a69,0x1d3)][_0x342368(0xff,_0xe27098._0x4bd3b6)]??document['body']['clientWidth'],this[_0x342368(_0xe27098._0x5bb9c6,0xdb)][_0x50a7b9(_0xe27098._0x3496ee,0x21a)]=Math['min'](this['options'][_0x50a7b9(_0xe27098._0x3f013c,0x21a)],0x1388),this['options']['minCanvasSize']=this['options']['minCanvasSize']??document[_0x50a7b9(0x208,_0xe27098._0x2136f7)]['clientHeight'],this[_0x342368(_0xe27098._0x156821,0xdb)][_0x50a7b9(_0xe27098._0xa98943,_0xe27098._0x32bddc)]=Math[_0x342368(_0xe27098._0x412a6c,0x118)](this['options'][_0x50a7b9(0x1d3,_0xe27098._0x1aa1a3)],0x2bc);function _0x50a7b9(_0xfb29c4,_0x249ce0){return _0x106b06(_0x249ce0-_0x3a4946._0x454f9a,_0xfb29c4);}function _0x342368(_0x13316d,_0x888566){return _0x485cd6(_0x13316d,_0x888566-0x315);}this['options']['heatStyle']={...DEF_HEATSTYLE,...this[_0x50a7b9(_0xe27098._0x162b5a,0x1d3)][_0x50a7b9(_0xe27098._0x2163cf,_0xe27098._0x5f3403)]},this['options']['style']={...DEF_STYLE,...this['options']['style']};}get['layer'](){return this['_layer'];}get[_0x485cd6(-0x276,-0x23f)](){var _0x3d9823={_0x566ba8:0x28b,_0x195144:0x2de},_0x93ece={_0x472390:0x4c},_0x162332={_0x449130:0x1e};function _0x3095ea(_0x57e902,_0x200ab2){return _0x485cd6(_0x200ab2,_0x57e902-_0x162332._0x449130);}function _0x390875(_0x32c3a7,_0x4b6ee1){return _0x485cd6(_0x4b6ee1,_0x32c3a7- -_0x93ece._0x472390);}return this[_0x390875(-0x286,-0x244)][_0x390875(-_0x3d9823._0x566ba8,-_0x3d9823._0x195144)];}set['heatStyle'](_0x10f4af){var _0x27e6ae={_0x13a40a:0x165,_0x1c4a41:0x1bb,_0x3aa3cd:0x46a,_0x11650e:0x48c,_0x318bc3:0x42e},_0x288d02={_0x3b3fa8:0xa0},_0x2b9ffe={_0x5d7194:0x599};this['options'][_0x165ce2(-0x19f,-0x17b)]=mars3d__namespace['Util'][_0x165ce2(-_0x27e6ae._0x13a40a,-_0x27e6ae._0x1c4a41)](this[_0x233f30(_0x27e6ae._0x3aa3cd,_0x27e6ae._0x11650e)]['heatStyle'],_0x10f4af);function _0x233f30(_0x14bf57,_0x2d792a){return _0x106b06(_0x14bf57-_0x2b9ffe._0x5d7194,_0x2d792a);}function _0x165ce2(_0x264f01,_0x2a8b81){return _0x485cd6(_0x2a8b81,_0x264f01-_0x288d02._0x3b3fa8);}this['_heat']&&(this['_heat']['configure'](this['options']['heatStyle']),this[_0x233f30(_0x27e6ae._0x318bc3,0x3ea)](!![]));}get['style'](){var _0x760f85={_0x1ddb3c:0x9e};function _0x15e3fa(_0x5e32f3,_0x393ef0){return _0x485cd6(_0x5e32f3,_0x393ef0-0x1c6);}return this[_0x15e3fa(-_0x760f85._0x1ddb3c,-0x74)]['style'];}set[_0x106b06(-0xdc,-0x117)](_0x63f32b){var _0x4742ab={_0x3d0975:0x1d7,_0x394566:0x193},_0x1b3f4b={_0x55e0ef:0x50};function _0x1ac4d3(_0x4f9aae,_0x497f44){return _0x485cd6(_0x497f44,_0x4f9aae-0x101);}function _0x3562be(_0x4090f8,_0x2e15da){return _0x485cd6(_0x2e15da,_0x4090f8-_0x1b3f4b._0x55e0ef);}this[_0x3562be(-0x1ea,-_0x4742ab._0x3d0975)]['style']=mars3d__namespace[_0x3562be(-_0x4742ab._0x394566,-0x1c1)]['merge'](this['options']['style'],_0x63f32b);}get['positions'](){var _0x55ab00={_0x20c018:0x180},_0x58805c={_0x1ba362:0xdf};function _0x766993(_0x2464f2,_0x55c15d){return _0x485cd6(_0x55c15d,_0x2464f2-_0x58805c._0x1ba362);}return this[_0x766993(-0x1a4,-_0x55ab00._0x20c018)];}set['positions'](_0x5b09f2){this['setPositions'](_0x5b09f2);}get[_0x106b06(-0xdf,-0xec)](){var _0x15a244={_0x3d2314:0x2a1};const _0x1c2e15=[];return this['points']['forEach'](_0x172fc3=>{function _0x553de5(_0x5284ee,_0x366509){return _0x2b6e(_0x5284ee- -0x377,_0x366509);}_0x1c2e15[_0x553de5(-0x2c8,-_0x15a244._0x3d2314)](_0x172fc3['toArray']());}),_0x1c2e15;}get[_0x485cd6(-0x255,-0x202)](){return this['_rectangle'];}['_setOptionsHook'](_0x4ccf6b,_0x4dc9a9){_0x4ccf6b['positions']&&(this['positions']=_0x4ccf6b['positions']);}[_0x485cd6(-0x1c2,-0x1f9)](){var _0x35f528={_0x5e1951:0x1b4,_0x2f48b3:0x1e7};function _0x11f6a0(_0x2271d4,_0x5893d1){return _0x106b06(_0x5893d1- -0xdd,_0x2271d4);}function _0x19a065(_0x32d142,_0x5630c4){return _0x485cd6(_0x32d142,_0x5630c4-0x7a);}if(this[_0x11f6a0(-0x1b8,-0x1b9)]['type']==='image'){var _0x26e12d={};_0x26e12d['crs']=_0x19a065(-_0x35f528._0x5e1951,-0x1d1),_0x26e12d['private']=!![],this['_layer']=new mars3d__namespace['layer']['ImageLayer'](_0x26e12d);}else{var _0x12a852={};_0x12a852['private']=!![],this[_0x11f6a0(-0x236,-_0x35f528._0x2f48b3)]=new mars3d__namespace['layer']['GraphicLayer'](_0x12a852);}}['_addedHook'](){var _0x574e81={_0x10596f:0xe3,_0xcbe9f5:0x8c,_0x1127b9:0x15c},_0x509aa0={_0xbd98ce:0x3a},_0x1590e5={_0x33c9c7:0x2f8};function _0x3fa6dd(_0x2a6ad9,_0x6c58e3){return _0x485cd6(_0x6c58e3,_0x2a6ad9-_0x1590e5._0x33c9c7);}function _0x21cc24(_0x3be361,_0x2b2e63){return _0x106b06(_0x2b2e63-_0x509aa0._0xbd98ce,_0x3be361);}this['_map']['addLayer'](this[_0x3fa6dd(_0x574e81._0x10596f,0x11f)]),this['_container']=mars3d__namespace['DomUtil']['create']('div','mars3d-heatmap\x20mars3d-hideDiv',this[_0x21cc24(-_0x574e81._0xcbe9f5,-0xdb)]['container']),this['options'][_0x21cc24(-0x138,-0x130)]&&(this['positions']=this[_0x21cc24(-0xa9,-0xf5)][_0x21cc24(-_0x574e81._0x1127b9,-0x130)]),this['options']['flyTo']&&this['flyTo']();}['_removedHook'](){var _0x378445={_0x4c734a:0x30d,_0x4a004c:0x2fb},_0x376a17={_0x3bb601:0x274},_0x487333={_0x30acaf:0x3db};function _0x5f142f(_0x277b1a,_0x1ba1e5){return _0x106b06(_0x1ba1e5-_0x487333._0x30acaf,_0x277b1a);}this[_0x5ec8a0(0xf3,0x9b)]&&(mars3d__namespace[_0x5f142f(0x29d,0x275)]['remove'](this['_container']),delete this[_0x5f142f(0x359,_0x378445._0x4c734a)]);this[_0x5f142f(0x2ec,_0x378445._0x4a004c)]();function _0x5ec8a0(_0x91c787,_0x76ccc5){return _0x485cd6(_0x91c787,_0x76ccc5-_0x376a17._0x3bb601);}this['_map']['removeLayer'](this[_0x5f142f(0x324,0x2d1)]);}['addPosition'](_0x59d5c1,_0x417c2a){var _0x418519={_0x579257:0xe5,_0xfbaa57:0xb2},_0x47356d={_0x15be9b:0x25f};function _0x839be2(_0x5b0cdd,_0x4894ea){return _0x485cd6(_0x5b0cdd,_0x4894ea-0x368);}this[_0x839be2(0x96,_0x418519._0x579257)]=this['_positions']||[],this[_0x42f749(-0x24,-0x64)]['push'](_0x59d5c1);function _0x42f749(_0x3d18e4,_0x329fb1){return _0x485cd6(_0x329fb1,_0x3d18e4-_0x47356d._0x15be9b);}this[_0x839be2(_0x418519._0xfbaa57,0xf2)](_0x417c2a);}['setPositions'](_0xfcadcf,_0x416f15){this['_positions']=_0xfcadcf,this['_updatePositionsHook'](_0x416f15);}['clear'](){var _0x2aa3e2={_0x20f4bb:0x56};function _0x3e69af(_0x591c8c,_0x33a3f7){return _0x106b06(_0x33a3f7-0x11f,_0x591c8c);}this['_graphic']&&(this['_layer']['removeGraphic'](this[_0x276fc2(0x4dc,0x4a6)],!![]),delete this['_graphic']);function _0x276fc2(_0x577087,_0x561ff8){return _0x106b06(_0x561ff8-0x5cd,_0x577087);}this['_graphic2']&&(this['_layer']['removeGraphic'](this['_graphic2'],!![]),delete this[_0x3e69af(_0x2aa3e2._0x20f4bb,-0x1)]);}[_0x485cd6(-0x243,-0x276)](_0x458733){var _0x2fd252={_0x22a17a:0x15a,_0x4f0299:0x16e,_0x364981:0x17b,_0x5b2a5b:0x11c,_0x35cbea:0x132,_0x19d861:0x1ab,_0xa1b3b2:0x160,_0x42d82b:0x9,_0x13b14c:0x3a,_0x282372:0x13a,_0x49d835:0x4e,_0x223cbe:0x152,_0x105908:0x1ae,_0x4cc9b6:0x159,_0x2cd92c:0x121,_0x37676c:0x1b5,_0x179d2c:0x7d,_0x4ed007:0x123,_0x48a108:0x1dd,_0x213d01:0x76,_0x344059:0x164,_0x5b8896:0x17d,_0x548815:0x170,_0x16de69:0x1b5};if(!this[_0x37748e(-_0x2fd252._0x22a17a,-_0x2fd252._0x4f0299)]||!this[_0x368336(0x86,0x88)]||!this['positions']||this['positions'][_0x37748e(-_0x2fd252._0x364981,-0x1be)]===0x0)return this;function _0x368336(_0x378510,_0x438f56){return _0x485cd6(_0x378510,_0x438f56-0x2a8);}const _0x201fb5=this['_getHeatCanvas']();function _0x37748e(_0x54ac38,_0x3e786e){return _0x106b06(_0x3e786e- -0x56,_0x54ac38);}if(this[_0x37748e(-_0x2fd252._0x5b2a5b,-_0x2fd252._0x35cbea)]['type']===_0x37748e(-_0x2fd252._0x19d861,-0x1b5))this[_0x37748e(-0x18d,-_0x2fd252._0xa1b3b2)]['setOptions']({'url':_0x201fb5[_0x37748e(-0x1ff,-0x1c5)](_0x368336(-_0x2fd252._0x42d82b,0x4d),0x1),'rectangle':this['_rectangle'],'opacity':this[_0x37748e(-0x13d,-0x132)][_0x368336(_0x2fd252._0x13b14c,0x89)]});else this[_0x37748e(-_0x2fd252._0x282372,-_0x2fd252._0x35cbea)][_0x368336(_0x2fd252._0x49d835,0x63)]?this[_0x368336(0x3b,0x76)]&&_0x458733?(this[_0x368336(0x58,0x76)][_0x37748e(-0x127,-_0x2fd252._0x223cbe)][_0x37748e(-_0x2fd252._0x105908,-0x1b5)]=_0x201fb5,this['_graphic']['uniforms']['bumpMap']=this[_0x37748e(-_0x2fd252._0x4cc9b6,-_0x2fd252._0x2cd92c)](),this[_0x368336(0xd7,0x7d)]&&(this['_graphic2']['uniforms'][_0x37748e(-0x1b8,-_0x2fd252._0x37676c)]=_0x201fb5,this[_0x368336(0xc0,_0x2fd252._0x179d2c)]['uniforms']['bumpMap']=this[_0x37748e(-0x198,-0x17d)]['uniforms'][_0x37748e(-0x129,-_0x2fd252._0x4ed007)])):this[_0x37748e(-_0x2fd252._0x48a108,-0x184)](_0x201fb5):this[_0x368336(0x49,_0x2fd252._0x213d01)]&&_0x458733?this[_0x37748e(-_0x2fd252._0x344059,-_0x2fd252._0x5b8896)]['uniforms'][_0x37748e(-_0x2fd252._0x548815,-_0x2fd252._0x16de69)]=_0x201fb5:this['_createGraphic'](_0x201fb5);return this;}['_createGraphic'](_0x1bcc23){var _0x1d23ef={_0x3fe7fc:0x2b7,_0xe1b8a7:0x313,_0x3e8c2c:0x306,_0x3f8bd7:0x23d,_0x363435:0x2b3,_0x2dfb97:0x2ae,_0x40a941:0x209,_0x4b19a2:0x2fa},_0x12dd2a={_0x17eed5:0x333};this[_0x5479eb(0x302,_0x1d23ef._0x3fe7fc)]();var _0x40699e={};_0x40699e['image']=_0x1bcc23;function _0xf87a05(_0x57d340,_0xac87e3){return _0x106b06(_0x57d340-_0x12dd2a._0x17eed5,_0xac87e3);}function _0x5479eb(_0x57ebc2,_0x36aed5){return _0x106b06(_0x57ebc2-0x3e2,_0x36aed5);}this['_graphic']=new mars3d__namespace[(_0x5479eb(_0x1d23ef._0xe1b8a7,_0x1d23ef._0x3e8c2c))][(_0x5479eb(0x27d,_0x1d23ef._0x3f8bd7))]({...this[_0x5479eb(_0x1d23ef._0x363435,_0x1d23ef._0x2dfb97)],'rectangle':this[_0x5479eb(0x30b,0x2d2)],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'material':mars3d__namespace['MaterialUtil'][_0xf87a05(0x1e6,0x1ef)](mars3d__namespace[_0xf87a05(0x230,_0x1d23ef._0x40a941)][_0x5479eb(0x2ab,_0x1d23ef._0x4b19a2)],_0x40699e),'flat':!![]})}),this['_layer']['addGraphic'](this['_graphic']);}['_createArcGraphic'](_0x4ba35c){var _0x21fbf2={_0x2dc69e:0x4d2,_0x25ca8c:0x471,_0x10952e:0x505,_0x5e3881:0x4ca,_0x3c4dde:0x4bc,_0x174bc6:0x4fa,_0x4e0113:0x4a7,_0x1c2c7a:0x4f9,_0x4d6c1b:0x4bc,_0x227503:0x47f,_0xaebb75:0x478,_0x52086a:0x45b,_0x4ea7d7:0x4bd,_0x10b44d:0x401,_0x2e45b2:0x456,_0x4a3dc7:0x4ed,_0x157b21:0x4d6,_0x77dd58:0x4e2,_0x42886e:0x4da,_0x2c3c14:0x537,_0xef30aa:0x4b1,_0x22a5ff:0x4c1},_0x4e0415={_0x16362c:0x6bd},_0x1f8999={_0x124540:0x5cb};this[_0x481afe(_0x21fbf2._0x2dc69e,0x4df)]();var _0x4c2660={};_0x4c2660['enabled']=!![];var _0x5d099b={};_0x5d099b['enabled']=!![];const _0x19e157=Cesium[_0x481afe(0x499,_0x21fbf2._0x25ca8c)]['fromCache']({'cull':_0x4c2660,'depthTest':_0x5d099b,'stencilTest':{'enabled':!![],'frontFunction':Cesium['StencilFunction']['ALWAYS'],'frontOperation':{'fail':Cesium['StencilOperation'][_0x13c9ed(_0x21fbf2._0x10952e,_0x21fbf2._0x5e3881)],'zFail':Cesium[_0x481afe(_0x21fbf2._0x3c4dde,_0x21fbf2._0x174bc6)][_0x13c9ed(0x4a1,_0x21fbf2._0x5e3881)],'zPass':Cesium[_0x481afe(0x4bc,0x46b)]['REPLACE']},'backFunction':Cesium[_0x481afe(_0x21fbf2._0x4e0113,_0x21fbf2._0x1c2c7a)]['ALWAYS'],'backOperation':{'fail':Cesium['StencilOperation'][_0x13c9ed(0x47e,_0x21fbf2._0x5e3881)],'zFail':Cesium['StencilOperation']['KEEP'],'zPass':Cesium[_0x481afe(_0x21fbf2._0x4d6c1b,0x490)]['REPLACE']},'reference':0x2,'mask':0x2},'blending':Cesium['BlendingState']['ALPHA_BLEND']}),_0x17a369=Math['floor'](this['style']['diffHeight']??this[_0x481afe(0x457,0x435)]*0.02)+0.1;this['style'][_0x13c9ed(_0x21fbf2._0x227503,_0x21fbf2._0xaebb75)]&&delete this['style'][_0x13c9ed(_0x21fbf2._0x52086a,0x478)];const _0x7f812d=(this['style'][_0x13c9ed(0x4c3,0x476)],0x64);let _0x2159dc=Math['max'](this[_0x481afe(0x4db,_0x21fbf2._0x4ea7d7)][_0x13c9ed(_0x21fbf2._0x10b44d,_0x21fbf2._0x2e45b2)],this['_rectangle'][_0x13c9ed(_0x21fbf2._0x4a3dc7,0x498)]);this[_0x481afe(_0x21fbf2._0x157b21,0x498)][_0x13c9ed(_0x21fbf2._0x77dd58,_0x21fbf2._0x42886e)]=_0x2159dc/=_0x7f812d;const _0x4fc11e=new Cesium['Material']({'fabric':{'uniforms':{'image':_0x4ba35c,'repeat':new Cesium['Cartesian2'](0x1,0x1),'color':new Cesium['Color'](0x1,0x1,0x1,0x0),'bumpMap':this[_0x481afe(0x4e7,_0x21fbf2._0x2c3c14)]()},'source':HeatMaterial},'translucent':!![]}),_0x4a4875=this['style']['arcDirection']||0x1;function _0x13c9ed(_0x5a1e3c,_0x3f1a06){return _0x106b06(_0x3f1a06-_0x1f8999._0x124540,_0x5a1e3c);}this['_graphic']=new mars3d__namespace['graphic']['RectanglePrimitive']({...this['options'],'rectangle':this['_rectangle'],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'flat':!![],'aboveGround':!![],'renderState':_0x19e157,'material':_0x4fc11e,'vertexShaderSource':getVertexShaderSource(_0x17a369*_0x4a4875)})});function _0x481afe(_0x3ded43,_0x41bf70){return _0x485cd6(_0x41bf70,_0x3ded43-_0x4e0415._0x16362c);}this['_layer']['addGraphic'](this['_graphic']),this[_0x13c9ed(0x514,0x4ef)]['arcDirection']===0x0&&(this['_graphic2']=new mars3d__namespace['graphic']['RectanglePrimitive']({...this[_0x481afe(0x483,_0x21fbf2._0xef30aa)],'rectangle':this['_rectangle'],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'flat':!![],'aboveGround':!![],'renderState':_0x19e157,'material':_0x4fc11e,'vertexShaderSource':getVertexShaderSource(-_0x17a369)})}),this[_0x13c9ed(0x4e1,_0x21fbf2._0x22a5ff)]['addGraphic'](this['_graphic2']));}['getRectangle'](_0xe62b27){var _0x2138b6={_0x598b1f:0x12e,_0x33cb64:0x16b},_0x157ca9={_0x20a873:0x94};function _0x2ecbc9(_0xeee4ed,_0x14f6d9){return _0x485cd6(_0x14f6d9,_0xeee4ed-0x55d);}function _0x4b97ac(_0x487fb8,_0x15492e){return _0x106b06(_0x15492e- -_0x157ca9._0x20a873,_0x487fb8);}return _0xe62b27!==null&&_0xe62b27!==void 0x0&&_0xe62b27['isFormat']&&this[_0x2ecbc9(0x37b,0x3d1)]?mars3d__namespace['PolyUtil'][_0x2ecbc9(0x36c,0x35b)](this[_0x4b97ac(-_0x2138b6._0x598b1f,-_0x2138b6._0x33cb64)]):this['_rectangle'];}[_0x106b06(-0x174,-0x16e)](){var _0x1d60d3={_0x5bb5d2:0x194,_0x430460:0x68,_0xf8ca6a:0x1da,_0x4fe995:0x194,_0x895a63:0x1a9,_0x5f5b12:0x189,_0x392ed4:0x1c0,_0x5a935b:0x1c,_0x3ad550:0x183,_0x58c566:0x6f,_0x30e230:0x19e,_0x23134e:0x1ca,_0x334afa:0x17e,_0xd492e8:0x28,_0x195a1d:0x25,_0x27b61d:0x29,_0x34de63:0x1d3,_0xfb3df9:0x55,_0x38786b:0x21,_0x293096:0x57,_0x215beb:0x1e,_0x396688:0x14,_0x223119:0x194,_0x3dfa6a:0x1d,_0x49d47c:0x184,_0x8b253b:0x1e4,_0x3660a6:0x198},_0x20a534={_0x459de8:0x2b2},_0x2eb28c={_0x329816:0x413},_0x31376c={_0x40c142:0x26d},_0x278a6a={_0x4ce01b:0x180,_0x2e52b6:0x1c1,_0x4f04d0:0x157},_0x5de6ef={_0x33b7fc:0xc8};const _0x68c6d2=this['_positions'],_0x195727=[];let _0x8d07b8,_0x1094ee,_0x3376c0,_0x5ec20e;_0x68c6d2['forEach'](_0x1f5de4=>{const _0x2249d3=mars3d__namespace['LngLatPoint']['parse'](_0x1f5de4);function _0xa099ed(_0x24789a,_0x201825){return _0x2b6e(_0x24789a-_0x5de6ef._0x33b7fc,_0x201825);}if(!_0x2249d3)return;_0x2249d3[_0xa099ed(0x1af,0x196)]=_0x1f5de4['value']||0x1;function _0x34c1f8(_0x12b99e,_0x4e51b7){return _0x2b6e(_0x12b99e-0x30f,_0x4e51b7);}!this['options']['rectangle']&&(_0x8d07b8===undefined?(_0x8d07b8=_0x2249d3['lng'],_0x1094ee=_0x2249d3[_0xa099ed(0x175,0x14e)],_0x3376c0=_0x2249d3['lat'],_0x5ec20e=_0x2249d3[_0x34c1f8(0x3c0,0x3ed)]):(_0x8d07b8=Math[_0xa099ed(0x183,_0x278a6a._0x4ce01b)](_0x8d07b8,_0x2249d3[_0xa099ed(0x175,_0x278a6a._0x2e52b6)]),_0x1094ee=Math[_0x34c1f8(0x411,0x3c9)](_0x1094ee,_0x2249d3['lng']),_0x3376c0=Math['min'](_0x3376c0,_0x2249d3[_0xa099ed(0x179,_0x278a6a._0x4f04d0)]),_0x5ec20e=Math[_0x34c1f8(0x411,0x458)](_0x5ec20e,_0x2249d3['lat']))),_0x195727['push'](_0x2249d3);});_0x8d07b8===_0x1094ee&&(_0x8d07b8-=0.000001,_0x1094ee+=0.000001);_0x3376c0===_0x5ec20e&&(_0x3376c0-=0.000001,_0x5ec20e+=0.000001);var _0xe1b9eb={};_0xe1b9eb['xmin']=_0x8d07b8,_0xe1b9eb[_0x45dd9e(0x197,_0x1d60d3._0x5bb5d2)]=_0x1094ee,_0xe1b9eb[_0x1c4ce3(_0x1d60d3._0x430460,0x99)]=_0x3376c0,_0xe1b9eb['ymax']=_0x5ec20e;let _0x2f7831=this[_0x1c4ce3(-0x23,0x33)]['rectangle']||_0xe1b9eb;const _0x232ba7=getMercatorBounds(_0x2f7831),_0xa6fbd2=Math['abs'](_0x232ba7[_0x45dd9e(_0x1d60d3._0xf8ca6a,_0x1d60d3._0x4fe995)]-_0x232ba7[_0x45dd9e(0x16a,_0x1d60d3._0x895a63)]),_0xa179d4=Math['abs'](_0x232ba7[_0x1c4ce3(0x62,0x25)]-_0x232ba7['ymin']),_0x4c8900=Math[_0x45dd9e(_0x1d60d3._0x5f5b12,_0x1d60d3._0x392ed4)](_0xa6fbd2,_0xa179d4),_0x1bbe7a=Math[_0x1c4ce3(0x23,0x29)](_0xa6fbd2,_0xa179d4);this['_mBoundsMax']=_0x4c8900;let _0x35587f=0x1;if(_0x4c8900>this[_0x1c4ce3(_0x1d60d3._0x5a935b,0x33)]['maxCanvasSize'])_0x35587f=_0x4c8900/this[_0x45dd9e(0x1b3,_0x1d60d3._0x3ad550)]['maxCanvasSize'],_0x1bbe7a/_0x35587f<this[_0x1c4ce3(0x13,0x33)]['minCanvasSize']&&(_0x35587f=_0x1bbe7a/this['options'][_0x1c4ce3(0x81,_0x1d60d3._0x58c566)]);else _0x1bbe7a<this['options']['minCanvasSize']&&(_0x35587f=_0x1bbe7a/this[_0x1c4ce3(0x6e,0x33)][_0x1c4ce3(0x52,0x6f)],_0x4c8900/_0x35587f>this['options'][_0x45dd9e(_0x1d60d3._0x30e230,_0x1d60d3._0x23134e)]&&(_0x35587f=_0x4c8900/this['options'][_0x1c4ce3(0x59,0x7a)]));const _0xa4515f=this[_0x45dd9e(0x155,_0x1d60d3._0x334afa)]['radius']*1.5,_0x1e9869=_0xa6fbd2/_0x35587f+_0xa4515f*0x2,_0x18fd09=_0xa179d4/_0x35587f+_0xa4515f*0x2,_0x131658=_0xa4515f*_0x35587f;_0x232ba7[_0x1c4ce3(0x1e,0x59)]-=_0x131658,_0x232ba7['ymin']-=_0x131658,_0x232ba7['xmax']+=_0x131658,_0x232ba7['ymax']+=_0x131658,_0x232ba7['xmin']=Math['max'](_0x232ba7['xmin'],-20037508.34),_0x232ba7['xmax']=Math[_0x1c4ce3(-0x1f,0x29)](_0x232ba7['xmax'],20037508.34),_0x232ba7[_0x45dd9e(0x1a4,0x1e9)]=Math['max'](_0x232ba7['ymin'],-20037508.34),_0x232ba7[_0x1c4ce3(_0x1d60d3._0xd492e8,_0x1d60d3._0x195a1d)]=Math[_0x1c4ce3(0x26,_0x1d60d3._0x27b61d)](_0x232ba7['ymax'],20037508.34),this['_scale']=_0x35587f;function _0x1c4ce3(_0xf643f4,_0x34c6c8){return _0x485cd6(_0xf643f4,_0x34c6c8-_0x31376c._0x40c142);}_0x2f7831=geLatLngBounds(_0x232ba7),this['_rectangle']=Cesium['Rectangle'][_0x45dd9e(_0x1d60d3._0x34de63,0x1a4)](_0x2f7831['xmin'],_0x2f7831['ymin'],_0x2f7831['xmax'],_0x2f7831['ymax']);let _0xdb5d16=_0x195727[0x0][_0x1c4ce3(0xf,_0x1d60d3._0xfb3df9)]??0x1,_0x552bba=_0x195727[0x0]['value']??0x0;const _0x82c048=[];_0x195727[_0x1c4ce3(_0x1d60d3._0x38786b,-_0x1d60d3._0x5a935b)](_0xe07f8b=>{const _0x9b532f=mars3d__namespace[_0x5f26b6(_0x2eb28c._0x329816,0x44c)]['lonlat2mercator']([_0xe07f8b['lng'],_0xe07f8b['lat']]),_0x6d8306=_0xe07f8b['value']||0x1,_0x442251=Math['round']((_0x9b532f[0x0]-_0x232ba7['xmin'])/_0x35587f);function _0x5f26b6(_0x388552,_0x5ca000){return _0x1c4ce3(_0x388552,_0x5ca000-0x3e5);}const _0x1ae8ec=Math['round']((_0x232ba7['ymax']-_0x9b532f[0x1])/_0x35587f);_0xdb5d16=Math['max'](_0xdb5d16,_0x6d8306),_0x552bba=Math['min'](_0x552bba,_0x6d8306);var _0x5d9fb0={};_0x5d9fb0['x']=_0x442251,_0x5d9fb0['y']=_0x1ae8ec,_0x5d9fb0['value']=_0x6d8306,_0x82c048['push'](_0x5d9fb0);});var _0x2bc968={};_0x2bc968['min']=this['options']['min']??_0x552bba,_0x2bc968[_0x1c4ce3(_0x1d60d3._0x293096,0x70)]=this[_0x1c4ce3(_0x1d60d3._0x215beb,0x33)]['max']??_0xdb5d16;function _0x45dd9e(_0x45e677,_0x315557){return _0x106b06(_0x315557-_0x20a534._0x459de8,_0x45e677);}_0x2bc968['data']=_0x82c048;const _0xa72279=_0x2bc968;this['_last_heatData']=_0xa72279;if(!this['_last_mBounds']||_0x232ba7[_0x1c4ce3(0x92,0x59)]!==this['_last_mBounds'][_0x1c4ce3(_0x1d60d3._0x396688,0x59)]||_0x232ba7['ymin']!==this[_0x45dd9e(0x123,0x135)]['ymin']||_0x232ba7[_0x45dd9e(0x146,_0x1d60d3._0x223119)]!==this['_last_mBounds']['xmax']||_0x232ba7['ymax']!==this[_0x1c4ce3(_0x1d60d3._0x3dfa6a,-0x1b)]['ymax']){this['_last_mBounds']=_0x232ba7,this['_container'][_0x45dd9e(_0x1d60d3._0x49d47c,0x1d6)]['cssText']=_0x45dd9e(0x17b,0x176)+_0x1e9869+'px;height:'+_0x18fd09+'px;';var _0x1c3eb0={...this['heatStyle']};_0x1c3eb0['container']=this[_0x45dd9e(0x1ba,_0x1d60d3._0x8b253b)];const _0x640ec8=_0x1c3eb0;!this['_heat']?this['_heat']=heatmap$1[_0x1c4ce3(0x12,0x5b)][_0x1c4ce3(0x8,-0x7)](_0x640ec8):this[_0x45dd9e(_0x1d60d3._0x3660a6,0x1cd)][_0x45dd9e(0x11c,0x145)](_0x640ec8);}this['_heat']['setData'](_0xa72279);const _0x26b5fa=mars3d__namespace['DomUtil']['copyCanvas'](this['_heat']['_renderer']['canvas']);return _0x26b5fa;}[_0x485cd6(-0x1cd,-0x1d6)](){var _0xff0ddb={_0x2f8ffc:0xf1,_0x26f5b3:0x266,_0x421f6e:0x2f6,_0x3dcd2f:0xca,_0x28fd4d:0x16f,_0x2bb3b0:0x280,_0x4d7efd:0x127,_0x586f39:0x10a,_0x47f5dd:0x17f,_0x864935:0x184,_0x3624ae:0x127,_0x33f95c:0x1ed,_0x289377:0x1b8,_0x4b1f38:0x24f},_0x1911ea={_0x46b8e7:0x48e},_0xc08c01={};_0xc08c01['0.25']='rgb(0,0,0)',_0xc08c01[_0x87ca1d(0x140,_0xff0ddb._0x2f8ffc)]='rgb(140,140,140)',_0xc08c01[_0x180dd2(_0xff0ddb._0x26f5b3,0x213)]='rgb(216,216,216)',_0xc08c01['1']=_0x180dd2(_0xff0ddb._0x421f6e,0x2b9),this['_heat'][_0x87ca1d(0xee,_0xff0ddb._0x3dcd2f)]({'radius':this[_0x87ca1d(0x127,0x180)][_0x87ca1d(0x119,_0xff0ddb._0x28fd4d)]*this['style'][_0x180dd2(_0xff0ddb._0x2bb3b0,0x29c)],'blur':this[_0x87ca1d(_0xff0ddb._0x4d7efd,_0xff0ddb._0x586f39)]['blur']*this[_0x87ca1d(_0xff0ddb._0x47f5dd,_0xff0ddb._0x864935)]['arcBlurScale'],'gradient':this[_0x87ca1d(_0xff0ddb._0x3624ae,0xd3)]['gradientArc']||_0xc08c01});const _0x2b25af=mars3d__namespace[_0x180dd2(0x1db,0x21d)]['copyCanvas'](this[_0x87ca1d(0x176,0x18a)][_0x180dd2(0x1f2,0x22c)][_0x180dd2(_0xff0ddb._0x33f95c,0x211)]);function _0x87ca1d(_0x2e4dc6,_0x398f98){return _0x485cd6(_0x398f98,_0x2e4dc6-0x366);}this[_0x87ca1d(0x176,_0xff0ddb._0x289377)]['configure'](this['options'][_0x180dd2(0x299,_0xff0ddb._0x4b1f38)]);function _0x180dd2(_0x10e7fd,_0x18c53f){return _0x485cd6(_0x10e7fd,_0x18c53f-_0x1911ea._0x46b8e7);}return _0x2b25af;}['getPointData'](_0x263383){var _0x5d13d6={_0x28dc7a:0xe,_0x4c0198:0x473,_0x5876f4:0x44d,_0x47698e:0x52,_0x2a6c9c:0x45e,_0x55a9f8:0x27,_0xeb6355:0x4d,_0x3fefa7:0x48,_0x50c469:0x45f,_0x59824d:0x457,_0x23dea9:0x486,_0x2b1fa7:0x493,_0x222fa3:0x4dd,_0xf8d1ab:0x48e},_0x1c6be0={_0x9ad955:0x6c1};const _0x2aa154=mars3d__namespace['LngLatPoint']['parse'](_0x263383);if(!_0x2aa154)return{};const _0x5346d4=mars3d__namespace[_0x180db5(-_0x5d13d6._0x28dc7a,0x30)]['lonlat2mercator']([_0x2aa154['lng'],_0x2aa154[_0x450583(_0x5d13d6._0x4c0198,_0x5d13d6._0x5876f4)]]),_0x121be3=this[_0x180db5(-0x31,-_0x5d13d6._0x47698e)],_0x4b03f0=Math[_0x450583(_0x5d13d6._0x2a6c9c,0x486)]((_0x5346d4[0x0]-_0x121be3[_0x450583(0x4ad,0x4fb)])/this['_scale']),_0x53ecb9=Math[_0x450583(0x45e,0x4b3)]((_0x121be3[_0x180db5(-_0x5d13d6._0x55a9f8,-0x12)]-_0x5346d4[0x1])/this['_scale']);var _0x4dea76={};function _0x450583(_0x15f85b,_0x59aa39){return _0x485cd6(_0x59aa39,_0x15f85b-_0x1c6be0._0x9ad955);}_0x4dea76['x']=_0x4b03f0,_0x4dea76['y']=_0x53ecb9;function _0x180db5(_0x4d1a28,_0x52107a){return _0x106b06(_0x52107a-0x12b,_0x4d1a28);}const _0x157387=this['_heat'][_0x180db5(-_0x5d13d6._0xeb6355,-_0x5d13d6._0x3fefa7)](_0x4dea76),_0xd4400b=this[_0x180db5(0x6d,0x46)][_0x450583(_0x5d13d6._0x50c469,_0x5d13d6._0x59824d)][_0x450583(_0x5d13d6._0x23dea9,_0x5d13d6._0x2b1fa7)][_0x450583(0x48e,_0x5d13d6._0x222fa3)](_0x4b03f0-0x1,_0x53ecb9-0x1,0x1,0x1)['data'];var _0x20eb05={};return _0x20eb05['x']=_0x4b03f0,_0x20eb05['y']=_0x53ecb9,_0x20eb05['value']=_0x157387,_0x20eb05[_0x450583(0x4a3,_0x5d13d6._0xf8d1ab)]='rgba('+_0xd4400b[0x0]+','+_0xd4400b[0x1]+','+_0xd4400b[0x2]+','+_0xd4400b[0x3]+')',_0x20eb05;}}mars3d__namespace['LayerUtil']['register'](_0x106b06(-0x13f,-0x121),HeatLayer),mars3d__namespace['layer']['HeatLayer']=HeatLayer,mars3d__namespace['h337']=h337;function _0x106b06(_0x3ed720,_0xa91770){return _0x2b6e(_0x3ed720- -0x1f4,_0xa91770);}function getMercatorBounds(_0x5c638b){var _0x578b4a={_0x4051a4:0x18b,_0x144069:0x1b3,_0x4b8ee2:0x399,_0x37a59d:0x173,_0x2619fd:0x3ee,_0x359d47:0x16f,_0x506738:0x13f},_0x199850={_0x5cd10c:0x387};const _0x13ebf9=mars3d__namespace['PointTrans']['lonlat2mercator']([_0x5c638b['xmin'],_0x5c638b[_0x1a1d12(_0x578b4a._0x4051a4,_0x578b4a._0x144069)]]),_0x53bf74=mars3d__namespace['PointTrans'][_0x32448e(0x3e4,0x3bc)]([_0x5c638b[_0x32448e(_0x578b4a._0x4b8ee2,0x3ef)],_0x5c638b[_0x32448e(0x37a,0x3c8)]]);var _0x540122={};_0x540122[_0x1a1d12(0x1b3,_0x578b4a._0x37a59d)]=_0x13ebf9[0x0];function _0x32448e(_0x51d93d,_0xa9cf81){return _0x106b06(_0x51d93d-0x4b7,_0xa9cf81);}_0x540122[_0x32448e(_0x578b4a._0x2619fd,0x443)]=_0x13ebf9[0x1],_0x540122['xmax']=_0x53bf74[0x0],_0x540122[_0x1a1d12(_0x578b4a._0x359d47,_0x578b4a._0x506738)]=_0x53bf74[0x1];function _0x1a1d12(_0x521d27,_0x3bfc7c){return _0x485cd6(_0x521d27,_0x3bfc7c-_0x199850._0x5cd10c);}return _0x540122;}function geLatLngBounds(_0x17450c){var _0x4c7df0={_0xeeee1d:0x336,_0x16f6a9:0x2ab,_0x42b701:0x358},_0x2c37c6={_0x5394ab:0x1e2};const _0x114ddf=mars3d__namespace[_0x26f561(-_0x4c7df0._0xeeee1d,-0x2dd)]['mercator2lonlat']([_0x17450c[_0x1f3075(0x3e5,0x38c)],_0x17450c[_0x26f561(-0x2ae,-_0x4c7df0._0x16f6a9)]]),_0x3a5480=mars3d__namespace['PointTrans']['mercator2lonlat']([_0x17450c['xmax'],_0x17450c[_0x1f3075(0x32d,_0x4c7df0._0x42b701)]]);var _0x1a0e68={};_0x1a0e68['xmin']=_0x114ddf[0x0],_0x1a0e68['ymin']=_0x114ddf[0x1];function _0x1f3075(_0x263de0,_0x1986c3){return _0x485cd6(_0x263de0,_0x1986c3-0x5a0);}_0x1a0e68['xmax']=_0x3a5480[0x0],_0x1a0e68['ymax']=_0x3a5480[0x1];function _0x26f561(_0x2e1543,_0xa0a6d2){return _0x106b06(_0xa0a6d2- -_0x2c37c6._0x5394ab,_0x2e1543);}return _0x1a0e68;}function getVertexShaderSource(_0x2b0485){return'in\x20vec3\x20position3DHigh;\x0a\x20\x20in\x20vec3\x20position3DLow;\x0a\x20\x20in\x20vec2\x20st;\x0a\x20\x20in\x20float\x20batchId;\x0a\x20\x20uniform\x20sampler2D\x20bumpMap_3;\x0a\x20\x20out\x20vec3\x20v_positionMC;\x0a\x20\x20out\x20vec3\x20v_positionEC;\x0a\x20\x20out\x20vec2\x20v_st;\x0a\x0a\x20\x20void\x20main()\x0a\x20\x20{\x0a\x20\x20\x20\x20vec4\x20p\x20=\x20czm_computePosition();\x0a\x20\x20\x20\x20v_positionMC\x20=\x20position3DHigh\x20+\x20position3DLow;\x0a\x20\x20\x20\x20v_positionEC\x20=\x20(czm_modelViewRelativeToEye\x20*\x20p).xyz;\x0a\x20\x20\x20\x20v_st\x20=\x20st;\x0a\x20\x20\x20\x20vec4\x20color\x20=\x20texture(bumpMap_3,\x20v_st);\x0a\x20\x20\x20\x20float\x20centerBump\x20=\x20distance(vec3(0.0),color.rgb);\x0a\x20\x20\x20\x20vec3\x20upDir\x20=\x20normalize(v_positionMC.xyz);\x0a\x20\x20\x20\x20vec3\x20disPos\x20=\x20upDir\x20*\x20centerBump\x20*\x20'+_0x2b0485+';\x0a\x20\x20\x20\x20p\x20+=vec4(disPos,0.0);\x0a\x20\x20\x20\x20gl_Position\x20=\x20czm_modelViewProjectionRelativeToEye\x20*\x20p;\x0a\x20\x20}\x0a';}exports['HeatLayer']=HeatLayer;var _0x57995c={};_0x57995c['value']=!![],Object[_0x106b06(-0x146,-0x16d)](exports,_0x106b06(-0x16c,-0x121),_0x57995c);
}));
