<template>
  <div
    class="monitor-box"
    :style="{ width: width + 'px', height: height + 'px' }"
  >
    <div
      class="video-view"
      :style="{ width: width + 'px', height: height + 'px' }"
      id="monitorPlayerPlugin"
      ref="monitorPlayerPlugin"
    ></div>
  </div>
</template>
  
  <script>
import dateUtil from "@/utils/dateUtil.js";
import initPlugin from "@/componts/hkVideo/lib/initPlugin";
import mitt from "@/utils/mitt";
export default {
  computed: {
    mode: function () {
      return 1;
    },
  },

  props: {
    videoConfig: {
      type: Object,
      default: {},
    },
    communityId: {
      type: String,
      default: "",
    },

    width: {
      type: Number,
      default: 850,
    },

    height: {
      type: Number,
      default: 550,
    },

    //模式：0：预览，1回放
    playMode: {
      type: Number,
      default: 0,
    },

    videoMode: {
      type: String,
      default: "PLUGIN",
    },

    devId: {
      type: String,
      default: "",
    },

    devNo: {
      type: String,
      default: "",
    },

    backStartTime: {
      type: String,
      default: "",
    },
    backEndTime: {
      type: String,
      default: "",
    },

    //浏览器窗口改变时候是否动态变化组件宽度，将按照初始化组件占浏览器可视区域比例缩放组件宽度
    autoResize: {
      type: Boolean,
      default: true,
    },
    //未安装插件时候是否显示插件下载对话框提示用户下载功能
    downloadDialog: {
      type: Boolean,
      default: true,
    },
    //海康插件下载url
    downloadUrl: {
      type: String,
      default: "",
    },
    downloadText: {
      type: String,
      default:
        "插件启动失败，请检查插件是否安装,如果未安装请点击下载安装,安装后刷新页面",
    },
  },

  data() {
    return {
      oWebControl: null, //插件实例
      argument: {
        appkey: "", //API网关提供的appkey
        secret: "", //API网关提供的secret
        ip: "", //API网关IP地址
        windId: 1, //当前窗口号
      },
      cameraIndexCode: "",
      curWindIndex: 1, //当前选中窗口号，从1开始
      playback_date: "", // 录像回放时间
      oldplayback_date: "",
      iWidth: 835,
      iHeight: 405,

      options: {
        appkey: "", //API网关提供的appkey
        secret: "", //API网关提供的secret
        ip: "", //API网关IP地址
        playMode: 0, //播放模式（决定显示预览还是回放界面）
        port: 443, //端口
        snapDir: "D:\\SnapDir", //抓图存储路径
        videoDir: "D:\\VideoDir", //紧急录像或录像剪辑存储路径
        layout: "1x1", //布局
        enableHTTPS: 1, //是否启用HTTPS协议
        encryptedFields: "secret", //加密字段
        showToolbar: 1,
        showSmart: 1,
        buttonIDs: "0", //自定义工具条按钮
      },
      rate: 0, //组件宽度与窗口宽度比例
      isInitComolete: false, //第一次初始化是否完成
      dialogVisible: false,
    };
  },

  watch: {
    "$lcStore.windowOffset": {
      handler: function (val, oldVal) {
        if (val.width != oldVal.width || val.height != oldVal.height) {
          if (!this.isInitComolete) return;
          this.getWidthAndHeight();

          this.setResize(this.iWidth, this.iHeight);
        }
      },
      // 深度观察监听
      deep: true,
    },

    videoConfig: {
      handler: function (val, oldVal) {
        if (!val) {
          if (this.oWebControl != null) {
            this.oWebControl.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
            this.oWebControl.JS_Disconnect().then(
              () => {
                // 断开与插件服务连接成功
              },
              () => {
                // 断开与插件服务连接失败
              }
            );
          }

          this.oWebControl = null;
        }
      },
      // 深度观察监听
      deep: true,
    },

    backStartTime: {
      handler: function (val, oldVal) {
        console.log("backStartTime", val);

        if (this.oldStartTime != val) this.initPlayBack(val);
      },
      // 深度观察监听
      deep: true,
    },
  },

  mounted() {
    var that = this;

    mitt.on("needPluginHide", (isHide) => {
      try {
        if (isHide) that.oWebControl.JS_HideWnd();
        else {
          that.oWebControl.JS_ShowWnd();
        }
      } catch (error) {}
    });
  },

  created() {},

  methods: {
    initMonitorPLUGIN() {
      this.reInit();
    },

    getWidthAndHeight() {
      const playerPreview = document.getElementById("monitorPlayerPlugin");
      if (playerPreview) {
        this.iWidth = playerPreview.clientWidth; // 或者使用 offsetWidth

        this.iHeight = playerPreview.clientHeight; // 或者使用 offsetHeight
      }
      console.log("this.width", this.width);
      console.log("this.height", this.height);
      console.log("this.iWidth", this.iWidth);
      console.log("this.iHeight", this.iHeight);
    },

    destroyMonitorPLUGIN() {
      // console.log("destroyMonitorPlugin");
      if (this.oWebControl != null) {
        this.oWebControl.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
        this.oWebControl.JS_Disconnect().then(
          () => {
            // 断开与插件服务连接成功
          },
          () => {
            // 断开与插件服务连接失败
          }
        );
      }

      this.oWebControl = null;

      this.playback_date = ""; // 录像回放时间
      this.oldplayback_date = ""; // 录像回放时间

      this.cameraIndexCode = "";
    },

    //初始化插件
    init() {
      var that = this;
      let argument = {
        ...this.options,
        ...this.argument,
      };
      that.cameraIndexCode = that.devNo;
      argument.appkey = this.$Base64.decode(this.videoConfig.appKey);
      argument.secret = this.$Base64.decode(
        this.$Base64.decode(this.videoConfig.secret)
      );

      argument.ip = this.videoConfig.ip;
      argument.port = parseInt(this.videoConfig.port);

      if (this.playMode > -1 && this.playMode < 2) {
        argument.playMode = this.playMode;
      }
      // eslint-disable-next-line no-undef

      this.getWidthAndHeight();

      this.oWebControl = initPlugin(
        "monitorPlayerPlugin",
        this.iWidth,
        this.iHeight,
        argument,
        this.cbIntegrationCallBack,
        () => {
          that.isInitComolete = true;

          setTimeout(() => {
            if (that.playMode == 1) {
              that.initPlayBack();
            } else {
              that.startPreview(that.cameraIndexCode);
            }

            this.setResize(this.iWidth, this.iHeight);
          }, 500);
        },
        () => {
          if (this.downloadDialog) {
            this.oWebControl = null;
            this.dialogVisible = true;
          }
          // this.$emit("pluginError");
        }
      );
    },

    initPlayBack() {
      var startTime =
        this.backStartTime != ""
          ? this.backStartTime
          : dateUtil.getCurrentTime() + " 00:00:00";

      var endTime =
        this.backEndTime != ""
          ? this.backEndTime
          : dateUtil.getCurrentTime() + " 23:59:59";

      this.playback_date =
        this.backStartTime != ""
          ? this.backStartTime.split(" ")[0]
          : dateUtil.getCurrentTime();
      this.oldStartTime = startTime;
      this.startPlayBack(this.cameraIndexCode, startTime, endTime);
    },

    /** 根据监控点编号录像回放
     * @param {*} cameraIndexCode :获取输入的监控点编号值，必填
     * @param {*} startTime:开始时间
     * @param {*} endTime:结束时间
     * @param {*} recordLocation://录像存储位置：0-中心存储，1-设施存储
     * @param {*} argument:api参数
     * argument属性cameraIndexCode优先级高于函数参数cameraIndexCode
     */
    startPlayBack(
      cameraIndexCode,
      startTime,
      endTime,
      recordLocation = 0,
      argument = {}
    ) {
      // var  startTime= "2023-06-12  0:00"
      // var endTime="2023-06-12  18:52:35"

      this.oldStartTime = startTime;

      //回放开始时间戳，必填
      var startTimeStamp = new Date(
        startTime.replace("-", "/").replace("-", "/")
      ).getTime();

      //回放结束时间戳，必填
      var endTimeStamp = new Date(
        endTime.replace("-", "/").replace("-", "/")
      ).getTime();

      // var recordLocation = 0; //录像存储位置：0-中心存储，1-设施存储
      var transMode = 1; //传输协议：0-UDP，1-TCP
      var gpuMode = 0; //是否启用GPU硬解，0-不启用，1-启用
      var wndId = -1; //播放窗口序号（在2x2以上布局下可指定播放窗口）

      this.oWebControl.JS_RequestInterface({
        funcName: "startPlayback",
        argument: JSON.stringify({
          cameraIndexCode: cameraIndexCode, //监控点编号
          startTimeStamp: Math.floor(startTimeStamp / 1000).toString(), //录像查询开始时间戳，单位：秒
          endTimeStamp: Math.floor(endTimeStamp / 1000).toString(), //录像结束开始时间戳，单位：秒
          recordLocation: recordLocation, //录像存储类型：0-中心存储，1-设施存储
          transMode: transMode, //传输协议：0-UDP，1-TCP
          gpuMode: gpuMode, //是否启用GPU硬解，0-不启用，1-启用
          wndId: wndId, //可指定播放窗口
          ...argument,
        }),
      });
    },

    //设置窗口尺寸
    setResize(width, height) {
      setTimeout(() => {
        this.oWebControl.JS_Resize(width, height);
      }, 100);

      // this.setWndCover();
    },

    //重新初始化
    reInit() {
      try {
        this.oWebControl
          .JS_RequestInterface({
            funcName: "uninit",
          })
          .then(() => {
            this.init();
          });
      } catch (error) {
        this.init();
      }
    },

    //推送消息回调
    cbIntegrationCallBack(oData) {
      if (oData?.responseMsg?.type) {
        let data = oData.responseMsg;

        //  // console.log(oData);
        switch (data.type) {
          //窗口号改变
          case 1:
            if (this.curWindIndex !== data.msg.wndId) {
              this.curWindIndex = data.msg.wndId;
              // this.$emit("windowChange", this.curWindIndex);
            }
            break;
          //布局改变
          case 6:
            // this.$emit("layoutChange", data.msg);
            break;
        }
      }
    },

    /** 预览
     * @param {*} cameraIndexCode :获取输入的监控点编号值，必填
     * @param {*} argument:api参数,
     * argument属性cameraIndexCode优先级高于函数参数cameraIndexCode
     */
    startPreview(cameraIndexCode, argument = {}) {
      if (this.oWebControl) {
        let params = {
          cameraIndexCode: cameraIndexCode, //监控点编号
          streamMode: 0, //主子码流标识
          transMode: 1, //传输协议
          gpuMode: 1, //是否开启GPU硬解
          wndId: -1,
        };
        this.oWebControl
          .JS_RequestInterface({
            funcName: "startPreview",
            argument: JSON.stringify({
              ...params,
              ...argument,
            }),
          })
          .then(() => {
            // console.log("startPreview");
          });
      }
    },

    startTimeFocus() {
      if (this.oWebControl) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, this.iWidth, this.iHeight);
      }
    },
    startTimeBlur() {
      if (this.oWebControl) {
        this.oWebControl.JS_RepairPartWindow(0, 0, this.iWidth, this.iHeight);
      }
    },
  },

  unmounted() {
    console.log(" monitor-plugin  unmounted");
    this.destroyMonitorPLUGIN();
  },
};
</script>
  
  <style scoped lang="scss">
.monitor-box {
}

.video-view {
  width: 865px;
  height: 425px;
  // border: 1px solid seagreen;
  display: flex;
  position: relative;

  z-index: 99;
  overflow: hidden;
}
</style>
  