
import request from '@/utils/request'

export const getMapUserStatus = (data) =>
	request({
		url: '/user/status/map',
		method: 'get',
		params: data
	})
export const getMapCommuntiyStatus = (data) =>
	request({
		url: '/community/anchorPoint',
		method: 'get',
		params: data
	})
export const getUserGroupTotal = (data) =>
	request({
		url: '/userGroup/total',
		method: 'get',
		params: data
	})
export const getCommunityTotal = (data) =>
	request({
		url: '/community/total',
		method: 'get',
		params: data
	})