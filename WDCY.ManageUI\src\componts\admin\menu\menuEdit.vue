<template>
	<el-dialog top="6vh" draggable width="50%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="menuModel" label-width="120px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="菜单名称" prop="menuName">
						<el-input v-model="menuModel.menuName" placeholder="菜单名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="图标" prop="icon">
						<el-input v-model="menuModel.icon" placeholder="图标"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="排序" prop="sort">
						<!-- <el-input onkeyup = "value=value.replace(/[^\d]/g,'')" v-model="menuModel.sort" placeholder="排序"></el-input> -->
						<el-input-number v-model="menuModel.sort" :min="1" :max="1000" :tep="1"
							controls-position="right" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="路径名" prop="path">
						<el-input v-model="menuModel.path" placeholder="路径名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="权限" prop="permission">
						<el-input v-model="menuModel.permission" placeholder="权限"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="组件路径" prop="componentPath">
						<el-input v-model="menuModel.componentPath" placeholder="组件路径"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="类型" prop="type">
						<el-select style="width: 100%;" v-model="menuModel.type" clearable placeholder="类型">
							<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="状态" prop="status">
						<el-select style="width: 100%;" v-model="menuModel.status" clearable placeholder="状态">
							<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="应用分类" prop="menuCategory">
						<el-select style="width: 100%;" v-model="menuModel.menuCategory" clearable placeholder="应用分类">
							<el-option v-for="item in menuCategoryList" :key="item.nameEn" :label="item.nameCn"
								:value="Number(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="是否入口" prop="isPortal">
						<el-select style="width: 100%;" v-model="menuModel.isPortal" clearable placeholder="是否入口">
							<el-option v-for="item in isOrNotList" :key="item.nameEn" :label="item.nameCn"
								:value="Number(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row v-if="menuModel.parentId != 0">
				<el-col :span="12">
					<el-form-item label="父级" prop="parentId">
						<!-- <el-cascader style="width: 100%;" :props="{ checkStrictly: true }" :options="buildingNodeList"
							@change="handleChange" clearable placeholder="选择父级" /> -->
						<el-tree-select style="width: 100%;" v-model="menuModel.parentId" :data="newMenuList"
							check-strictly :render-after-expand="false" placeholder="选择父级" />
					</el-form-item>

				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input type="textarea" :rows="2" v-model="menuModel.remark" placeholder="备注内容"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="扩展参数" prop="expandParams">
						<!-- <json-editor-vue class="editor" language="cn" v-model="jsonVal" /> -->
						<JsonEditorVue
						class="editor"
						:modelValue="jsonVal"
						@update:modelValue="changeJson"
						/>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提
				交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { editMenu, addMenu } from "@/api/admin/menu";
import { listDictByNameEn } from "@/api/admin/dict"
import JsonEditorVue from "json-editor-vue3";
import mitt from "@/utils/mitt";
export default {
  	components: { JsonEditorVue },
	emits: ['search'],
	props: ['statusList', 'typeList', 'menuList', 'menuCategoryList','isOrNotList'],
	data() {
		return {
			loading: false,
			menuModel: {},
      		jsonVal: {},
			dialog: {
				show: false
			},
			rules: {
				menuName: [{
					required: true,
					message: '请输入菜单名',
					trigger: 'blur',
				}],
				type: [{
					required: true,
					message: '请选择类型',
					trigger: 'change',
				}]
			},
			newMenuList: [],
			// menuCategoryList: []
		}
	},
	methods: {
		changeJson(json) {
			console.log(json);
			this.jsonVal = json;
		},
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					this.menuModel.expandParams = JSON.stringify(this.jsonVal);
					console.log(JSON.stringify(this.jsonVal));
					if (this.menuModel.id == 0) {
						addMenu(this.menuModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						editMenu(this.menuModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		},
		// async init(){
		// 	let menuCategoryList = await listDictByNameEn('menu_category')
		// 	this.menuCategoryList = menuCategoryList.data.result
		// }
	},
	created(){
		// this.init()
	},
	mounted() {
		this.jsonVal = {};
		mitt.on('openMenuEdit', (menu) => {
			let menuStr = JSON.stringify(this.menuList).replaceAll('menuName', 'label').replaceAll('id', 'value')
			this.newMenuList = JSON.parse(menuStr)
			this.menuModel = menu
			this.dialog.show = true
			this.dialog.title = "修改信息"
			this.jsonVal = JSON.parse(this.menuModel.expandParams);
			console.log(this.menuList);
		})
		mitt.on('openMenuAdd', (id) => {
			let menuStr = JSON.stringify(this.menuList).replaceAll('menuName', 'label').replaceAll('id', 'value')
			this.newMenuList = JSON.parse(menuStr)
			this.menuModel = {
				id: 0,
				parentId: id,
				icon: '#',
				path: '',
				status: 0,
				sort: 0
			}
			this.dialog.show = true
			this.dialog.title = "添加菜单"
		})
	}
}
</script>
<style>
	.editor {
	width: 805px;
	}
</style>
