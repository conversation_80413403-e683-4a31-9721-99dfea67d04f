<template>
    <notice-edit :typeList="typeList" :statusList="statusList" @search="search"></notice-edit>
    <el-row :gutter="20">
		<el-col :span="4">
			<el-input v-model="searchModel.noticeTitle" @keydown.enter="search" placeholder="标题" clearable />
		</el-col>
        <el-col :span="4">
			<el-select style="width: 100%;" v-model="searchModel.noticeType" placeholder="公告类型" clearable>
				<el-option v-for="item in typeList" :key="item.nameEn" 
					:label="item.nameCn" :value="parseInt(item.nameEn)">
				</el-option>
			</el-select>
		</el-col>
        <el-col :span="4">
			<el-select style="width: 100%;" v-model="searchModel.status" placeholder="公告状态" clearable>
				<el-option v-for="item in statusList" :key="item.nameEn" 
					:label="item.nameCn" :value="parseInt(item.nameEn)">
				</el-option>
			</el-select>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
		<el-col :span="4" :push="4">
			<el-button style="float: right;" type="primary" @click="add" v-if="hasPerm('sys:notice:add')">发 布 信 息</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
			<el-table row-key="id" stripe :data="noticeList" border style="width: 100%" @selection-change="handleSelectionChange">
				<el-table-column :reserve-selection="true" type="selection" align="center" width="55" />
				<!-- <el-table-column prop="title" align="center" label="序号"/> -->
				<el-table-column prop="image" width="100" align="center" label="导航图">
					<template #default="scope">
						<el-image preview-teleported fit="contain" style="width: 50px; height: 50px"
							:src="scope.row.image" :preview-src-list="[scope.row.image]">
							<template #error>
								<!-- <el-icon>暂无</el-icon> -->
								<el-icon style="width: 100%; height: 100%;"><Picture  style="width: 100%; height: 100%;"/></el-icon>
							</template>
						</el-image>
					</template>
				</el-table-column>

				<el-table-column prop="id" align="center" label="ID"/>
				<el-table-column prop="noticeTitle" align="center" label="标题"/>

				<el-table-column prop="noticeType" align="center" label="类型" width="100">
					<template #default="scope">
						<el-tag :type="getDictCss(typeList, scope.row.noticeType)">{{ formatDict(typeList, scope.row.noticeType) }}</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="status" align="center" label="状态" width="88">
					<template #default="scope">
						<el-tag :type="getDictCss(statusList,scope.row.status)">{{ formatDict(statusList, scope.row.status) }}</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="createBy" align="center" label="发布者" width="125" />
				<el-table-column prop="createTime" align="center" label="创建时间" width="168" />

				<el-table-column align="center" width="200" label="操作" v-if="hasPerm('sys:notice:edit') || hasPerm('sys:notice:delete')">
					<template #default="scope">
						<el-button type="text" size="default" @click="edit(scope.row.id)" v-if="hasPerm('sys:notice:edit')">编辑</el-button>
						<el-button type="text" size="default" @click="deleted(scope.row.id)" v-if="hasPerm('sys:notice:delete')">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>
<script>
import { listNotice, deleteNotice, getNotice } from "@/api/admin/notice"
import mitt from "@/utils/mitt"
import { getDictCss, formatDict } from "@/utils/dict"
import { listDictByNameEn } from "@/api/admin/dict"
import noticeEdit from "@/componts/admin/noticeEdit.vue"

export default {
	components: { noticeEdit },
	data() {
		return {
			searchModel: {},
			noticeList: [],
			typeList: [],
			statusList: [],
			ids: [],
			total: 0,
			pageSize: 10
		}
	},
	methods: {
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		handleSelectionChange(val) {
			let list = []
			for (let item of val) {
				list.push(item.id)
			}
			this.ids = list
		},
		search() {
			listNotice(this.searchModel)
				.then(res => {
					this.noticeList = res.data.result.list
					this.total = res.data.result.total
				})
		},
		edit(id) {
			getNotice(id)
				.then(res => {
					mitt.emit('openNoticeEdit', res.data.result)
				})
		},
		add() {
			mitt.emit('openNoticeAdd')
		},
		deleted(id) {
			this.$confirm('删除信息, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteNotice(id)
					.then(res => {
						this.search()
						this.$message.success(res.data.msg)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num) {
			this.searchModel.pageSize = num
			this.search()
		},
		async init() {
			mitt.off("openNoticeAdd")
			mitt.off("openNoticeEdit")
			try {
				let res = await listNotice()
				this.noticeList = res.data.result.list
				console.log(this.noticeList);
				this.total = res.data.result.total
				let res_type = await listDictByNameEn('notice_type')
				this.typeList = res_type.data.result
				let res_status = await listDictByNameEn('common_status')
				this.statusList = res_status.data.result
			} catch (error) {
			}
		}
	},
	created() {
		this.init()
	}
}
</script>
<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
</style>