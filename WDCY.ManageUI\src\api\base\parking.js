import request from '@/utils/request'

//停车场管理 列表
export const listParking = (data) =>
	request({
		url: '/carParkInfo',
		method: 'get',
		params: data
	})

//停车场管理 增加
export const addParking = (data) =>
	request({
		url: '/carParkInfo',
		method: 'post',
		data: data
	})
//停车场管理 修改
export const editParking = (data) =>
	request({
		url: '/carParkInfo',
		method: 'put',
		data: data
	})
//停车场管理 删除
export const deleteParking = (id) =>
	request({
		url: '/carParkInfo',
		method: 'delete',
		data: {
			id: id
		}
	})
