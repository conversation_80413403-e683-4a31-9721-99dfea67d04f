<template>
	<div id="app" style="height: 100%; ">
		<transition name="el-fade-in">
			<router-view></router-view>
		</transition>
	</div>
</template>
<style>
	body {
		height: 100%;
		width: 100%;
		overflow: hidden;
		margin: 0px;
		padding: 0px;
	}
	.amap-sug-result{
		z-index: 9999;
	}
	html{
		/* -webkit-filter: grayscale(100%);
		-moz-filter: grayscale(100%);
		-ms-filter: grayscale(100%);
		-o-filter: grayscale(100%);
		filter: grayscale(100%);
		filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1); */
	}
</style>