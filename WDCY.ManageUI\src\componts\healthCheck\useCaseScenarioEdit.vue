<template>
	<!-- <production-assert-create></production-assert-create> -->
	<!-- <examine-one-edit :statusList="statusList" :categoryList="categoryList" :methodList="methodList" :hasRightList="hasRightList" @search="search" ></examine-one-edit> -->
	<el-dialog draggable width="40%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="useCaseScenarioModel" label-width="100px">
			<el-row>
				<el-col :span="24">
					<el-form-item label="场景名称" prop="sceneName">
						<el-input maxlength="50" show-word-limit v-model="useCaseScenarioModel.sceneName" placeholder="场景名称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
                    <el-form-item label="状态" prop="thirdSystemId">
                        <el-radio-group v-model="useCaseScenarioModel.status">
                            <el-radio-button  v-for="item in statusList" :key="item.nameEn"
                                :label="parseInt(item.nameEn)">{{ item.nameCn }}</el-radio-button >
                        </el-radio-group>
                    </el-form-item>
            	</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
				<el-form-item label="备注" prop="note">
					<el-input
					v-model="useCaseScenarioModel.note"
					maxlength="200"
					placeholder="请简单说明场景情况"
					show-word-limit
					type="textarea"
					/>
				</el-form-item>
				</el-col>
			</el-row>
            <el-row>
                <el-col :span="24">
                <el-form-item label="扩展参数" prop="expandParams">
                    <JsonEditorVue
                    language="cn"
                    class="editor"
                    :modelValue="jsonVal"
                    @update:modelValue="changeJson"
                    />
                </el-form-item>
                </el-col>
            </el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">保 存
			</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { addCheckScene, editCheckScene } from "@/api/healthCheck/useCaseScenario"
import mitt from "@/utils/mitt";
import JsonEditorVue from "json-editor-vue3";
export default {
	props: ['statusList'],
    components: { JsonEditorVue },
	data() {
		return {
			loading: false,
			useCaseScenarioModel: {},
			dialog: {},
            jsonVal: {},
			personId: "",
			startToEndTime: [],
			imgServer: import.meta.env.VITE_BASE_API,
		}
	},
	methods: {
        changeJson(json) {
        	this.jsonVal = json;
        },

		onSubmit() {
			// this.dialog.show = false
			this.$refs['form'].validate((valid) => {
				if (valid) {
                    this.useCaseScenarioModel.expandParams = JSON.stringify(this.jsonVal);
					if (this.useCaseScenarioModel.id == 0) {
						// this.useCaseScenarioModel.delete(id)
						addCheckScene(this.useCaseScenarioModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						editCheckScene(this.useCaseScenarioModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		},
        init(){
        }
	},
	mounted() {
        this.jsonVal = {};
		this.$nextTick(function () {
			mitt.on('openUseCaseScenarioEdit', (data) => {
				this.useCaseScenarioModel = data
				this.dialog.show = true
				this.dialog.title = "修改信息"
                this.jsonVal = JSON.parse(this.useCaseScenarioModel.expandParams);
			})
			mitt.on('openUseCaseScenarioAdd', () => {
				//新增默认扩展参数
                this.jsonVal = {
				}
				this.useCaseScenarioModel = {
					id: 0,
					status: 1
				}
				this.dialog.show = true
				this.dialog.title = "添加场景"
			})
		})
	},
    created(){
        this.init()
    }
}
</script>
<style scoped>

.editor {
  width: 805px;
}
.avatar-uploader .avatar {
  width: 150px;
  height: 150px;
  display: block;
}

.upload {
  border: 1px dashed #ddd;
  border-radius: 6px;
}

div /deep/.avatar-uploader .el-upload {
  /* border: 1px dashed #ddd; */
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover,
.el-upload:hover {
  border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  text-align: center;
}
</style>
