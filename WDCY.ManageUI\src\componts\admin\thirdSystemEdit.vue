<template>
	<el-dialog top="2vh" draggable width="50%" v-loading="loading" v-model="dialog.show" destroy-on-close
		:title="dialog.title">
		<el-form :rules="rules" ref="form" :model="sysModel" label-width="130px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="系统名称" prop="sysName">
						<el-input v-model="sysModel.sysName" placeholder="系统名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="系统代号" prop="sysCode">
						<el-input v-model="sysModel.sysCode" placeholder="系统代号"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="图标名称" prop="icoInfoId">
						<el-select style="width: 100%;" v-model="sysModel.icoInfoId" placeholder="图标名称">
							<el-option v-for="item in icoList" :key="item.id" :label="item.icoName" :value="item.id"
								style="line-height:34px">
								<img :src="imgServer + item.icoUrl" />
								<div>{{ item.icoName }}</div>
								<div style="flex:1"></div>
							</el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="展示方式" prop="showMode">
						<!-- <el-input v-model="sysModel.showMode" placeholder="展示方式"></el-input> -->
						<el-select style="width: 100%;" v-model="sysModel.showMode" placeholder="展示方式">
							<el-option v-for="item in showModeList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="运行URL" prop="runUrl">
						<el-input v-model="sysModel.runUrl" placeholder="运行URL"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="对接URL" prop="apiUrl">
						<el-input v-model="sysModel.apiUrl" placeholder="对接URL"></el-input>
					</el-form-item>
				</el-col>

			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="系统类型" prop="typeId">
						<!-- <el-input v-model="sysModel.typeId" placeholder="系统类型"></el-input> -->
						<el-select style="width: 100%;" v-model="sysModel.typeId" placeholder="系统类型">
							<el-option v-for="item in sysTypeList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="系统分类" prop="sysCategory">
						<!-- <el-input v-model="sysModel.sysCategory" placeholder="系统分类"></el-input> -->
						<el-select style="width: 100%;" v-model="sysModel.sysCategory" placeholder="系统分类">
							<el-option v-for="item in sysCategoryList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="排序" prop="sort">
						<el-input-number v-model="sysModel.sort" :min="1" size=""
							style="width: 150px"></el-input-number>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="表单账号名称" prop="accountFormName">
						<el-input v-model="sysModel.accountFormName" placeholder="表单账号名称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="表单密码名称" prop="pwdFormName">
						<el-input v-model="sysModel.pwdFormName" placeholder="表单密码名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="表单验证名称" prop="verifyFormName">
						<el-input v-model="sysModel.verifyFormName" placeholder="表单验证名称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="验证码刷新URL" prop="verifyCodeUrl">
						<el-input v-model="sysModel.verifyCodeUrl" placeholder="验证码刷新URL"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="表单密码加密方式" prop="encryptPwdType">
						<!-- <el-input v-model="sysModel.encryptPwdType" placeholder="表单密码加密方式"></el-input> -->
						<el-select style="width: 100%;" v-model="sysModel.encryptPwdType" placeholder="表单密码加密方式">
							<el-option v-for="item in encryptPwdTypeList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="表单登录方式" prop="formLoginType">
						<!-- <el-input v-model="sysModel.formLoginType" placeholder="表单登录方式"></el-input> -->
						<el-select style="width: 100%;" v-model="sysModel.formLoginType" placeholder="表单登录方式">
							<el-option v-for="item in formLoginTypeList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="状态" prop="status">
						<!-- <el-input v-model="sysModel.status" placeholder="表单密码加密方式"></el-input> -->
						<el-select style="width: 100%;" v-model="sysModel.status" placeholder="状态">
							<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="描述" prop="remark">
						<el-input type="textarea" :rows="2" v-model="sysModel.note" placeholder="描述"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="扩展参数" prop="expandParams">
						<!-- <json-editor-vue class="editor" language="cn" v-model="jsonVal" /> -->
						<JsonEditorVue class="editor" :modelValue="jsonVal" @update:modelValue="changeJson" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交
			</el-button>
		</el-row>
		<!-- <vehicle-number></vehicle-number> -->
	</el-dialog>
</template>

<script>
import JsonEditorVue from "json-editor-vue3";
import { addThirdSystem, editThirdSystem } from "@/api/admin/thirdSystem"
import { fileUpload } from "@/api/admin/file";
import { queryList } from "@/api/base/person"
import mitt from "@/utils/mitt";
export default {
	components: { JsonEditorVue },
	props: ['statusList', 'formLoginTypeList', 'encryptPwdTypeList', 'sysTypeList', 'sysCategoryList', 'showModeList', 'icoList'],
	data() {
		return {
			loading: false,
			sysModel: {},
			dialog: {},
			imgServer: import.meta.env.VITE_BASE_API,
			jsonVal: {},
			rules: {
				status: [
					{
						required: true,
						message: '请选择状态',
						trigger: "change"
					}
				],
				typeId: [
					{
						required: true,
						message: '请选择系统类型',
						trigger: "change"
					}
				],
				showMode: [
					{
						required: true,
						message: '请选择展示方式',
						trigger: "change"
					}
				],
				runUrl: [
					{
						required: true,
						message: '请输入运行URL',
						trigger: "blur"
					}
				],
				sysName: [
					{
						required: true,
						message: '请输入系统名称',
						trigger: "blur"
					}
				],
				sysCode: [
					{
						required: true,
						message: '请输入系统代号',
						trigger: "blur"
					}
				],
			},
		}
	},
	methods: {
		changeJson(json) {
			console.log(json);
			this.jsonVal = json;
		},
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					this.sysModel.expandParams = JSON.stringify(this.jsonVal);
					if (this.sysModel.id == 0) {
						this.sysModel.status = Number(this.sysModel.status)
						this.sysModel.showMode = Number(this.sysModel.showMode)
						this.sysModel.typeId = Number(this.sysModel.typeId)
						addThirdSystem(this.sysModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					} else {
						editThirdSystem(this.sysModel)
							.then(res => {
								this.$message.success(res.data.msg)
								this.$emit("search")
								this.dialog.show = false
							})
					}
				}
			})
		}
	},
	mounted() {
		this.jsonVal = {};
		this.$nextTick(function () {
			mitt.on('openThirdSystemEdit', (data) => {
				// this.jsonVal = JSON.parse(this.sysModel.expandParams);
				this.sysModel = data
				this.dialog.show = true
				this.dialog.title = "修改信息"
			})
			mitt.on('openThirdSystemAdd', () => {
				this.sysModel = {
					id: 0,
					status: 1,
					sort: 1
				}
				this.startToEndTime = []
				this.dialog.show = true
				this.dialog.title = "添加图标"
			})
			mitt.on("carNum", (data) => {
				console.log(data, 'yesyes');
			})
		})
	}
}
</script>
<style lang="less" scoped>
.avatar-uploader .avatar {
	width: 150px;
	height: 150px;
	display: block;
}

.upload {
	border: 1px dashed #ddd;
	border-radius: 6px;
}

div /deep/ .avatar-uploader .el-upload {
	/* border: 1px dashed #ddd; */
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	transition: var(--el-transition-duration-fast);
}

.editor {
	width: 100%;
}

.avatar-uploader:hover,
.el-upload:hover {
	border-color: #409eff;
}

.el-icon.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 150px;
	height: 150px;
	text-align: center;
}

.el-select-dropdown__item {
	display: flex;
	align-items: center;
	text-align: center;

	>img {
		background-color: rgba(136, 186, 255, .3);
		width: 20px;
		margin: 0 auto;
		margin-right: 10px;
	}
}

.el-select-dropdown__item:hover {
	background-color: rgba(170, 170, 170, .3);
}
</style>
