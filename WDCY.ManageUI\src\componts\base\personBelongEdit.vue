<template>
	<el-dialog draggable width="25%" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-form :rules="rules" :model="personBelongModel" label-width="80px">
			<el-row>
				<el-col :span="24">
					<el-form-item label="楼栋" prop="buildingId">
						<el-cascader style="width: 100%;" v-model="personBelongModel.buildingId" :options="buildingList" @change="buildingHandleChange" clearable placeholder="楼栋"></el-cascader>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="单元" prop="unitId">
						<el-cascader style="width: 100%;" v-model="personBelongModel.unitId" :options="unitList" @change="unitHandleChange" clearable placeholder="单元"></el-cascader>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="房屋" prop="roomId">
						<el-cascader style="width: 100%;" v-model="personBelongModel.roomId" :options="roomList" @change="roomHandleChange" clearable placeholder="房屋"></el-cascader>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="住户类型" prop="nameEn">
						<el-select style="width: 100%;" v-model="personBelongModel.personType" filterable clearable placeholder="住户类型">
							<el-option v-for="item in personTypeList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="居住类型" prop="liveType">
						<el-select style="width: 100%;" v-model="personBelongModel.liveType" filterable clearable placeholder="住户类型">
							<el-option v-for="item in liveTypeList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="是否户主" prop="isHouseholder">
						<el-radio-group v-model="personBelongModel.isHouseholder">
						<el-radio :label="1">是</el-radio>
						<el-radio :label="0">否</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="状态" prop="sex">
						<el-select disabled style="width: 100%;" v-model="personBelongModel.status" placeholder="状态">
							<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { addPersonBelong,editPersonBelong } from "@/api/base/personBelong"
import { listBuilding, getUnitList, getRoomList } from "@/api/base/building"
import mitt from "@/utils/mitt";
export default {
	props:['personTypeList','liveTypeList','statusList'],
	data() {
		return {
			loading: false,
			personBelongModel: {},
			dialog:{},
			buildingList:[],
			unitList:[],
			roomList:[],
			rules: {
				buildingId: [{
					required: true,
					message: '请选择楼栋',
					trigger: 'change',
				}]
			},
			communityId:localStorage.getItem("communityId")
		}
	},
	methods: {
		// 楼栋改变
		buildingHandleChange(e){
			if (!e) {
				this.personBelongModel.buildingId = null
				this.personBelongModel.unitId = null
				this.personBelongModel.roomId = null
				this.unitList = []
				this.roomList = []
				return
			}
			this.personBelongModel.buildingId = e[0]
			getUnitList({buildingId:e[0],communityId:this.communityId,pageSize:99999999}).then(res => {
				this.unitList = JSON.parse(JSON.stringify(res.data.result.list).replaceAll("unitNumber","label").replaceAll("id","value"))
			})
			// this.personBelongModel.roomId = e[2]
		},
		// 单元改变
		unitHandleChange(e){
			console.log(e);
			if (!e) {
				this.personBelongModel.unitId = null
				this.personBelongModel.roomId = null
				this.roomList = []
				return
			}
			this.personBelongModel.unitId = e[0]
			getRoomList({unitId:e[0],communityId:this.communityId,pageSize:99999999}).then(res => {
				this.roomList = JSON.parse(JSON.stringify(res.data.result.list).replaceAll("roomNumber","label").replaceAll("id","value"))
			})
			// this.personBelongModel.roomId = e[2]
		},
		// 房间改变
		roomHandleChange(e){
			if (!e) {
				this.personBelongModel.roomId = null
				return
			}
			this.personBelongModel.roomId = e[0]
		},
		onSubmit(){
			if(this.personBelongModel.id == 0){
				addPersonBelong(this.personBelongModel)
				.then(res =>{
					this.$message.success(res.data.msg)
					this.$emit("search")
					this.dialog.show = false
				})
			}else{
				editPersonBelong(this.personBelongModel)
				.then(res =>{
					this.$message.success(res.data.msg)
					this.$emit("search")
					this.dialog.show = false
				})
			}
		},
		buildingListReplaceAll(data){
			for (let i = 0; i < data.length; i++) {
					for (let j = 0; j < data[i].children.length; j++) {
						for (let k = 0; k < data[i].children[j].children.length; k++) {
							delete data[i].children[j].children[k].buildingNumber
							delete data[i].children[j].children[k].unitNumber
						}
					}
				}
		},
		async init(){
			this.buildingList = []
			this.unitList = []
			this.roomList = []
			await listBuilding({communityId:this.communityId,pageSize:999999}).then(res=>{
				this.buildingList = JSON.parse(JSON.stringify(res.data.result.list).replaceAll("buildingNumber","label").replaceAll("id","value"))
				console.log(this.buildingList,'123123');
			})
			if (this.personBelongModel.buildingId) {
				await getUnitList({buildingId:this.personBelongModel.buildingId,communityId:this.communityId,pageSize:99999999}).then(res => {
					this.unitList = JSON.parse(JSON.stringify(res.data.result.list).replaceAll("unitNumber","label").replaceAll("id","value"))
				})
			}
			
			if (this.personBelongModel.unitId) {
				await getRoomList({unitId:this.personBelongModel.unitId,communityId:this.communityId,pageSize:99999999}).then(res => {
					this.roomList = JSON.parse(JSON.stringify(res.data.result.list).replaceAll("roomNumber","label").replaceAll("id","value"))
				})
			}
		}
	},
	mounted(){
		this.$nextTick(function() {
			mitt.on('openPersonBelongAdd', (data) => {
				this.init()
				this.personBelongModel = {
					id:0,
					personId:data.personId,
					communityId:data.communityId
				}
				// this.buildingListReplaceAll(data.buildingList)
				// var buildingStr = JSON.stringify(data.buildingList).replaceAll('buildingNumber','label').replaceAll('unitNumber','label').replaceAll('roomNumber','label').replaceAll('id','value')
				// this.buildingList = JSON.parse(buildingStr)
				this.personBelongModel.status = 1
				this.dialog.show = true
				this.dialog.title = "添加房产"
			})
			mitt.on('openPersonBelongEdit', (data) => {
				console.log(data.belong.buildingId, data.belong.unitId, data.belong.roomId);
				this.personBelongModel = data.belong
				this.init()
				// this.buildingListReplaceAll(data.buildingList)
				// 编辑初始化楼栋列表数据
				var buildingStr = JSON.stringify(data.buildingList).replaceAll('buildingNumber','label').replaceAll('unitNumber','label').replaceAll('roomNumber','label').replaceAll('id','value')
				this.buildingList = JSON.parse(buildingStr)
				this.dialog.show = true
				this.dialog.title = "编辑房产"
			})
		})
	}
}
</script>
