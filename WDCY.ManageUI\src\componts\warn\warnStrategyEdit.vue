<template>
  <el-dialog draggable
    width="50%"
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form :rules="rules" :model="personModel" label-width="120px">
      <el-row>
        <el-col :span="18">
          <el-form-item label="预警策略名称" prop="name">
            <el-input
              v-model="personModel.name"
              placeholder="预警策略名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="预警策略简介" prop="note">
            <el-input
              v-model="personModel.note"
              placeholder="预警策略简介"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="生效状态" prop="status">
            <el-radio v-model="personModel.status" :label="0">关闭</el-radio>
            <el-radio v-model="personModel.status" :label="1">开启</el-radio>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="生效开始时间" prop="startTime">
            <el-date-picker
              v-model="personModel.startTime"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="生效结束时间" prop="endTime">
            <el-date-picker
              v-model="personModel.endTime"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="条件类型" prop="conditionType">
            <el-radio v-model="personModel.conditionType" label="AND"
              >并且</el-radio
            >
            <el-radio v-model="personModel.conditionType" label="OR"
              >或者</el-radio
            >
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="事件等级" prop="eventLevel">
            <el-select  v-model="personModel.eventLevel" placeholder="事件等级" clearable>
              <el-option v-for="item in eventLevelList" :key="item.nameEn" :label="item.nameCn"
                :value="parseInt(item.nameEn)"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="事件标识" prop="eventCode">
            <el-select  v-model="personModel.eventCode" placeholder="事件标识" clearable>
              <el-option v-for="item in eventCodeList" :key="item.nameEn" :label="item.nameCn"
                :value="item.nameEn"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="end">
      <el-button type="primary" :disabled="disabled" @click="onSubmit">提 交</el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import {
  warnStrategyListEdit,
  warnStrategyListAdd,
} from "@/api/warn/warnStrategy";
import mitt from "@/utils/mitt";
export default {
  props: ["statusList", "tagList", 'eventLevelList', "eventCodeList"],
  data() {
    return {
      loading: false,
      personModel: {},
      communityId: localStorage.getItem("communityId"),
      dialog: {},
      disabled: false,
      rules: {
        name: [
          {
            required: true,
            message: "请输入预警策略名称",
            trigger: "blur",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择生效状态",
            trigger: "blur",
          },
        ],
        conditionType: [
          {
            required: true,
            message: "请选择条件类型",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    onSubmit() {
      if (this.personModel.tags) {
        this.personModel.tags = JSON.stringify(this.personModel.tags);
      }
      this.personModel.communityId = this.communityId;
      if (this.personModel.id == 0) {
        this.disabled = true
        warnStrategyListAdd(this.personModel)
          .then((res) => {
            this.$message.success(res.data.msg);
            this.$emit("search");
            this.dialog.show = false;
          }).catch(()=>this.disabled = false)
      } else {
        this.disabled = true
        warnStrategyListEdit(this.personModel)
          .then((res) => {
            this.$message.success(res.data.msg);
            this.$emit("search");
            this.dialog.show = false;
          }).catch(()=>this.disabled = false)
      }
    },
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openWarnStrategyEdit", (person) => {
        this.disabled = false
        if (person.tags) {
          person.tags = JSON.parse(person.tags);
        }
        this.personModel = person;
        this.dialog.show = true;
        this.dialog.title = "修改";
      });
      mitt.on("openWarnStrategyAdd", () => {
        this.disabled = false
        this.personModel = {
          id: 0,
        };
        this.dialog.show = true;
        this.dialog.title = "添加";
      });
    });
  },
};
</script>
