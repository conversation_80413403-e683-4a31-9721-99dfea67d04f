<template>
    <el-dialog draggable width="25%" top="5vh" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
      <el-form ref="form" :model="miniAppBindModel" :rules="rules" label-width="100px">
          <el-col :span="24">
            <el-form-item label="菜单" prop="menuId">
              <el-select v-model="miniAppBindModel.menuId" placeholder="请选择绑定菜单" clearable style="width:180px">
                <el-option v-for="item in bindMenuList" :key="item.id" :label="item.menuName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
      </el-form>
      <el-row justify="center">
        <el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提 交</el-button>
      </el-row>
    </el-dialog>
  </template>
  
  <script>
  import mitt from "@/utils/mitt"
  import { miniAppBindMenu, menuList } from "@/api/weChat/miniProgram"
  export default {
    props: [],
    data() {
      return {
        loading: false,
        bindMenuList: [],
        dialog: {},
        mask: false,
        miniAppBindModel: {},
        imgServer: import.meta.env.VITE_BASE_API,
        
      }
    },
    methods: {
      onSubmit() {
        this.$refs["form"].validate(valid => {
          if (valid) {
              miniAppBindMenu(this.miniAppBindModel)
                .then(res => {
                  console.log(res);
                  this.$message.success(res.data.msg)
                  this.$emit("search")
                  this.dialog.show = false
                })
          }
        });
      },
      init(){
        menuList().then(res => {
            this.bindMenuList = res.data.result
            console.log(this.bindMenuList);
        })
      }
    },
    created(){
        this.init()
    },
    mounted() {
      this.$nextTick(function () {
        mitt.on("openMiniAppBind", (data) => {
          this.miniAppBindModel = {
            id: data.id,
            menuId:data.menuId
          }
          this.dialog.show = true
          this.dialog.title = "绑定菜单"
        })
      })
    },
  }
  </script>
  <style scoped>
  .avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  
  .upload {
    border: 1px dashed #ddd;
    border-radius: 6px;
  }
  
  div /deep/.avatar-uploader .el-upload {
    /* border: 1px dashed #ddd; */
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }
  
  .avatar-uploader:hover,
  .el-upload:hover {
    border-color: #409eff;
  }
  
  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
  }
  </style>