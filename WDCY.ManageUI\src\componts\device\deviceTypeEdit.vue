<template>
  <el-dialog draggable width="45%" top="3vh" v-loading="loading" v-model="dialog.show" destroy-on-close :title="dialog.title">
		<el-scrollbar height="620px">
    <el-form ref="formEl" :rules="rules" :model="dataModel" label-width="120px" style="padding-right: 25px;">

      <el-form-item label="类型名称" prop="name">
        <el-input v-model="dataModel.name" placeholder="类型名称"></el-input>
      </el-form-item>

      <el-form-item label="类型代号" prop="code">
        <el-input v-model="dataModel.code" placeholder="类型代号"></el-input>
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <!-- <el-input onkeyup = "value=value.replace(/[^\d]/g,'')" v-model="dataModel.sort" placeholder="排序"></el-input> -->
        <el-input-number v-model="dataModel.sort" :min="1" :max="1000" :tep="1" controls-position="right" />
      </el-form-item>

      <el-form-item label="警报类别" prop="alertType">
        <el-radio v-model="dataModel.alertType" :label="0">普通</el-radio>
        <el-radio v-model="dataModel.alertType" :label="1">条件</el-radio>
        <el-radio v-model="dataModel.alertType" :label="2">动作</el-radio>
      </el-form-item>

      <el-form-item label="大屏展示" prop="enableShow">
        <el-radio v-model="dataModel.enableShow" :label="true">启用</el-radio>
        <el-radio v-model="dataModel.enableShow" :label="false">禁用</el-radio>
      </el-form-item>
      <el-form-item label="图标名称" prop="icoInfoId">
        <el-select v-model="dataModel.icoInfoId" placeholder="图标名称">
          <el-option v-for="item in icoList" :key="item.id" :label="item.icoName" :value="item.id" style="line-height:34px">
            <img :src="imgServer + item.icoUrl" />
            <div>{{ item.icoName }}</div>
            <div style="flex:1"></div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="图片">
        <el-card class="box-card">
          <template #header>
            <div style="display: flex;justify-content: space-between;">
              <el-button @click="uploadImg" class="button" type="text">
                <input style="
                    position: fixed;
                    left: -9999px;
                    display: none;
                    " type="file" accept="image/*" id="imgReader" @change="loadingImg($event)" :value="upload_input"/>裁剪上传
              </el-button>

              <el-button class="button" type="text">
                <el-upload :show-file-list="false" :http-request="imgUpload" :accept="acceptRule">上传</el-upload>
              </el-button>
              <div style="flex:1"></div>
              <el-button class="button" type="text" @click="deletePhoto">删除</el-button>
              <!-- <el-button v-else class="button" type="text" @click="deletePhoto">删除</el-button> -->
            </div>
          </template>
          <el-dialog draggable v-model="viewPic" title="裁剪图片">
            <div style="display: flex">
              <img id="cropImg" style="width: 300px; height: 300px" />
            </div>
            <div>
              <!-- <div class="previewText">裁剪预览</div>
              <div style="display: flex">
                <div style="height: 100px; width: 100px; position: relative">
                  <div class="previewBox"></div>
                </div>
                <div style="
                    height: 100px;
                    width: 100px;
                    position: relative;
                    border-radius: 50%;
                    overflow: hidden;
                  ">
                  <div class="previewBoxRound"></div>
                </div>
              </div> -->
              <div style="display: flex; margin-top: 10px">
                <el-button type="primary" @click="GetData">确认</el-button>
              </div>
            </div>
          </el-dialog>
          <el-image fit="contain" style="height: 100px;width: 200px"
            :preview-src-list="[imgServer + dataModel.typePhoto]" :src="imgServer + dataModel.typePhoto"></el-image>
        </el-card>
      </el-form-item>
      <el-form-item label="上级" prop="parentId">
        <el-select v-model="dataModel.parentId" clearable placeholder="Select">
          <el-option v-for="item in deviceTypeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="运行参数模板" prop="extendParamsTpl">
        <!-- <el-input v-if="false" v-model="dataModel.extendParamsTpl" type="textarea"></el-input> -->
        <!-- <JsonEditorVue class="editor" language="cn" v-model="jsonVal" /> -->
        <JsonEditorVue class="editor" language="cn" :modelValue="jsonVal" @update:modelValue="changeJson" />
      </el-form-item>
    </el-form>
  </el-scrollbar>
    <template #footer>
      <span class="dialog-footer" style="padding-right: 25px;">
        <el-button type="primary" style="width: 100px; height: 30px; margin-top: 20px" @click="onSubmit">提 交
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  deviceTypeListAdd,
  deviceTypeListEdit,
  deviceTypeAllList,
} from "@/api/device/device";
import mitt from "@/utils/mitt";
import JsonEditorVue from "json-editor-vue3";
import { fileUpload } from "@/api/admin/file"
import "cropperjs/dist/cropper.css";
import Cropper from "cropperjs";
import CROPPER from "../../utils/myUtils.js";
import { truncate } from "lodash";
export default {
  components: { JsonEditorVue },
  props: ["statusList", "typeList", 'icoList'],
  data() {
    return {
			// CROPPER: CROPPER,
			imgServer: import.meta.env.VITE_BASE_API,
      viewPic: false,
      loading: false,
      upload_input: '',
      jsonVal: {},
      dataModel: {},
      dialog: {},
      deviceTypeList: [{ id: "0", name: "无上级" }],
      communityId: localStorage.getItem("communityId"),
      rules: {
        name: [
          {
            required: true,
            message: "请输入类型名称",
            trigger: "blur",
          },
        ],
        code: [
          {
            required: true,
            message: "请输入类型代号",
            trigger: "blur",
          },
        ],
        alertType: [
          {
            required: true,
            message: "请选择警报类别",
            trigger: "blur",
          },
        ],
      },
      acceptRule:"image/jpeg,image/jpg,image/png"
    };
  },
  methods: {
    changeJson(json) {
      console.info("changeJson: ", json);
      this.jsonVal = json;
    },
    onSubmit() {
      this.$refs.formEl.validate((valid, fields) => {
        if (valid) {
          console.log('submit!')

          this.dataModel.extendParamsTpl = JSON.stringify(this.jsonVal);
          this.dataModel.communityId = this.communityId;
          if (this.dataModel.id == 0) {
            deviceTypeListAdd(this.dataModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          } else {
            deviceTypeListEdit(this.dataModel).then((res) => {
              this.$message.success(res.data.msg);
              this.$emit("search");
              this.dialog.show = false;
            });
          }
        } else {
          console.log('error submit!', fields)
        }
      })

    },
    deletePhoto() {
			this.dataModel.typePhoto = null
		},

    // 原图上传
    imgUpload(files) {
			let form = new FormData()
			form.append("file", files.file)
			form.append("modulesName", 'device');
			form.append("functionName", 'deviceType');
			form.append("communityId", localStorage.getItem('communityId'));
			fileUpload(form)
				.then(res => {
          this.dataModel.typePhoto = res.data.result.url;
					if (res.data.code == 0) {
						this.$message.success("上传成功")
					}
				})
		},
    //裁剪上传
    loadingImg(eve) {
      this.upload_input = eve.target.files[0]
			this.viewPic = truncate;
			//读取上传文件
			let reader = new FileReader();
			if (eve.target.files[0]) {
				//readAsDataURL方法可以将File对象转化为data:URL格式的字符串（base64编码）
				reader.readAsDataURL(eve.target.files[0]);
				reader.onload = (e) => {
					let dataURL = reader.result;
					//将img的src改为刚上传的文件的转换格式
					document.querySelector("#cropImg").src = dataURL;

					const image = document.getElementById("cropImg");
					//创建cropper实例-----------------------------------------
					let CROPPER = new Cropper(image, {
						// aspectRatio: 16 / 16,
						initialAspectRatio: 2 / 3,
						viewMode: 1,
						autoCropArea: 0.95,
						minCanvasWidth: 100,
						minCanvasHeight: 100,
						// minContainerWidth:500,
						// minContainerHeight:500,
						dragMode: "move"
					});
					this.CROPPER = CROPPER;
				};
			}
      this.upload_input = ''
		},
    GetData() {
      this.viewPic = false;
      //getCroppedCanvas方法可以将裁剪区域的数据转换成canvas数据
      this.CROPPER.getCroppedCanvas({
        maxWidth: 480,
        maxHeight: 480,
        fillColor: "#fff",
        imageSmoothingEnabled: true,
        imageSmoothingQuality: "high",
      }).toBlob((blob) => {
        //然后调用浏览器原生的toBlob方法将canvas数据转换成blob数据

        //之后就可以愉快的将blob数据发送至后端啦，可根据自己情况进行发送，我这里用的是axios
        const form = new FormData();
        // 第三个参数为文件名，可选填.
        form.append("file", blob, "example.jpg");
        form.append("modulesName", 'device');
        form.append("functionName", 'deviceType');
        form.append("communityId", localStorage.getItem('communityId'));
        fileUpload(form).then((res) => {
          this.dataModel.typePhoto = res.data.result.url;
          if (res.data.code == 0) {
            this.$message.success("上传成功");
          }
        });
      }, "image/jpeg", 0.95);
    },

    // 激活裁剪上传
    uploadImg() {
      document.querySelector("#imgReader").click();
			if (this.CROPPER) {
				this.CROPPER.destroy();
			}
		},
    loadList() {
      deviceTypeAllList({
        communityId: localStorage.getItem("communityId"),
      }).then((res) => {
        this.deviceTypeList = res.data.result;
        if (this.dataModel.id != 0) {
          let list = [];
          list = this.deviceTypeList.filter(
            (temp) => this.dataModel.id != temp.id
          );
          this.deviceTypeList = list;
        }
        this.deviceTypeList.unshift({ id: "0", name: "无上级" });
      });
    },
  },
  mounted() {
    this.jsonVal = {};
    mitt.on("openDeviceTypeEdit", (data) => {
      this.dataModel = data;
      this.loadList();
      this.dialog.show = true;
      this.dialog.title = "修改";
      this.jsonVal = JSON.parse(this.dataModel.extendParamsTpl);
    });
    mitt.on("openDeviceTypeAdd", (id) => {
      this.dataModel = {
        id: 0,
        parentId: String(id),
      };
      this.loadList();
      this.dialog.show = true;
      this.dialog.title = "添加";
    });
  },
};
</script>
<style scoped lang="scss">
.editor {
	width: 805px;
}

#cropImg {
	height: 300px;
	width: 300px;
	display: block;
	overflow: hidden;
	box-shadow: 0 0 5px #adadad;
}

.previewText {
	margin-top: 10px;
}

.box-card {
	width: 40%;
}
.el-select-dropdown__item{
	display: flex;align-items: center;text-align:center;
	>img{
		background-color: rgba(136, 186, 255,.3);
		width: 20px;
		margin:0 auto;
		margin-right: 10px;
	}
}
.el-select-dropdown__item:hover{
	background-color: rgba(170, 170, 170,.3);
}
</style>
