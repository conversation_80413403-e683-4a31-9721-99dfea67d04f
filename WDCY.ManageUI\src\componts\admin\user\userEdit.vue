<template>
	<el-dialog draggable width="50%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="userModel" label-width="120px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="头像" prop="avatar">
						<el-dropdown>
							<el-avatar :size="50" :src="imgServer + userModel.avatar">
								<el-icon :size="50"><user-filled></user-filled></el-icon>
							</el-avatar>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item>
										<el-button type="text" size="default">
											<el-upload :show-file-list="false" :http-request="imgUpload">上传</el-upload>
											</el-button>
									</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="用户名" prop="userName">
						<el-input readonly v-model="userModel.userName" placeholder="用户名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="昵称" prop="nickName">
						<el-input v-model="userModel.nickName" placeholder="昵称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="邮箱" prop="email">
						<el-input v-model="userModel.email" placeholder="邮箱"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="手机号" prop="mobile">
						<el-input v-model="userModel.mobile" placeholder="手机号"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="性别" prop="sex">
						<el-select style="width: 100%;" v-model="userModel.sex" clearable placeholder="状态">
							<el-option v-for="item in sexList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="状态" prop="status">
						<el-select style="width: 100%;" v-model="userModel.status" clearable placeholder="状态">
							<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
								:value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="角色" prop="roleIds">
						<el-select style="width: 100%;" v-model="userModel.roleIds" multiple placeholder="选择角色">
							<el-option v-for="item in roleList" :key="item.id" :label="item.roleName"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="用户组" prop="groupId">
						<el-cascader :show-all-levels="false" style="width: 100%;" v-model="userModel.groupId"
							:props="{ checkStrictly: true }" :options="userGroupList" @change="handleChange" clearable
							placeholder="选择组" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="授权过期" prop="groupId">
					
					<el-date-picker
						style="margin-right:10px"
						v-model="userModel.authEndTime"
						type="date"
						placeholder="选择授权过期时间"
						value-format="YYYY-MM-DD HH:mm:ss"
						:size="size"
					/>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input type="textarea" :rows="2" v-model="userModel.remark" placeholder="备注内容"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提
				交</el-button>
		</el-row>
	</el-dialog>
</template>


<script>
import { UserFilled } from '@element-plus/icons-vue'
import { fileUpload } from "@/api/admin/file"
import { registerUser, editUser, getUser } from "@/api/admin/user"
import mitt from "@/utils/mitt";
export default {
	props: ['statusList', 'sexList', 'roleList', 'userGroupList'],
	data() {
		return {
			loading: false,
			userModel: {},
			imgServer: import.meta.env.VITE_BASE_API,
			dialog: {},
			rules: {
				userName: [{
					required: true,
					message: '空用户名，请删除账户',
					trigger: 'blur',
				}],
				roleIds: [{
					required: true,
					message: '请选择角色',
					trigger: 'change',
				}],
			}
		}
	},
	methods: {
		imgUpload(files) {
			let form = new FormData()
			form.append("file", files.file)
			form.append("modulesName", 'admin');
			form.append("functionName", 'userInfo');
			form.append("communityId", localStorage.getItem('communityId'));
			fileUpload(form)
				.then(res => {
					this.userModel.avatar = res.data.result.url
					if (res.data.code == 0) {
						this.$message.success("上传成功")
					}
				})
		},
		handleChange(e) {
			if (e == null) {
				this.userModel.groupId = null
				return
			}
			this.userModel.groupId = e[e.length - 1]
		},
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					editUser(this.userModel)
						.then(res => {
							this.$message.success(res.data.msg)
							this.$emit("search")
							this.dialog.show = false
						})
				}
			})
		}
	},
	mounted() {
		this.$nextTick(function () {
			mitt.on('openUserEdit', (user) => {
				this.userModel = user
				getUser(user.id)
					.then(res => {
						this.userModel = res.data.result
					})
					.catch(err => {
						this.$message.error(err)
					})
				this.dialog.show = true
				this.dialog.title = "修改信息"
			})
		})
	}
}
</script>
