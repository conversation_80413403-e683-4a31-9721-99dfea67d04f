
<template>
  <!-- v-loading="loading" -->
  <el-dialog 
    width="50%"
    v-model="dialog.show"
    :title="dialog.title"
    top="3vh"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane label="个人信息" name="first">
        <div style="display: flex; justify-content: space-between;" >
          <el-descriptions
            style="width: 100%; margin-right: 20px"
            extra
            :column="2"
            border
          >
            <el-descriptions-item
            width="110"
              label="姓名"
              label-align="center"
              align="center"
              label-class-name="my-label"
              class-name="my-content"
              >{{ personModel.name }}</el-descriptions-item
            ><el-descriptions-item
              label="证件号码"
              label-align="center"
              align="center"
              >{{ personModel.idCard }}</el-descriptions-item
            >

            <el-descriptions-item
              label="年龄"
              label-align="center"
              align="center"
              >{{ personModel.age }}</el-descriptions-item
            >
            <el-descriptions-item
              label="籍贯"
              label-align="center"
              align="center"
              >{{ personModel.nativePlace }}</el-descriptions-item
            >
            <el-descriptions-item
              label="性别"
              label-align="center"
              align="center"
              >{{ formatDict(sexList, personModel.sex) }}</el-descriptions-item
            >
            <el-descriptions-item
              label="住址"
              label-align="center"
              align="center"
              >{{ personModel.address }}</el-descriptions-item
            >

            <el-descriptions-item
              label="卡号"
              label-align="center"
              align="center"
              >{{ personModel.cardNumber }}</el-descriptions-item
            >

            <el-descriptions-item label="标签" label-align="center" align="center">
              <!-- <el-tag size="default">{{personModel.tags}}</el-tag> -->
              <el-tag
                style="margin-right: 10px"
                size="default"
                v-for="item in getTagsList"
                :key="item"
                :type="item.type"
                >{{ item.nameCn }}</el-tag
              >
            </el-descriptions-item>
            <el-descriptions-item
              label="状态"
              label-align="center"
              align="center"
              >{{
                personStatus(personModel.status)
              }}</el-descriptions-item
            >

            <el-descriptions-item
              label="联系电话"
              label-align="center"
              align="center"
              >{{ personModel.phone }}</el-descriptions-item
            >
            <el-descriptions-item
              v-if="hasPerm('base:person:check')"
              :column="3"
              width="67px"
              label="审核"
              label-align="center"
              align="right"
            >
              <el-button
                type="text"
                size="default"
                @click="openPersonAuditDialog"
                >个人信息审核</el-button
              >
            </el-descriptions-item>
            <el-descriptions-item
              label="备注"
              label-align="center"
              align="center"
              >{{ personModel.note }}</el-descriptions-item
            >
          </el-descriptions>
          <div style="height: 249px;border: 1px solid #eee;">
            <div
              style="width: 100%;
                text-align: center;
                line-height: 40px;
                background-color: rgb(245, 247, 250);
                border-bottom: 1px solid #eee;
                "
            >
              照片
            </div>
            <div style="height: 210px; width: 210px;display:flex;justify-content: center;">
              <el-image
              style="height:210px;"
              :preview-src-list="[imgServer + personModel.photo]"
              :src="imgServer + personModel.photo"
              fit="contain"
              >
              </el-image>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <el-form :model="personModel" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-tabs v-model="activeName">
            <el-tab-pane label="房产信息" name="first">
            <el-table stripe :data="myroomList" border style="width: 100%">
              <el-table-column width="90px" prop="buildingNumber" align="center" label="楼栋"/>
              <el-table-column width="90px" prop="unitNumber" align="center" label="单元" />
              <el-table-column width="90px" prop="roomNumber" align="center" label="房间编号" />
              <el-table-column prop="personType" align="center" :formatter="formatPersonType" label="住户类型" />
              <el-table-column prop="liveType" align="center" :formatter="formatLiveType" label="居住类型" />
              <el-table-column prop="status" width="100" align="center" :formatter="formatStatus" label="状态" />
              <el-table-column prop="center" width="100" show-overflow-tooltip align="center" label="备注">
                <template #default="scope">
                  <span class="oneline">
                    {{scope.row.note}}
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="center" width="85" label="操作">
                <template #default="scope">
                  <el-button type="text" size="default" @click="openHouseAuditDialog(scope.row.id,scope.$index)">房产审核</el-button ></template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
          
        </el-col>
          
        <el-col :span="24">
          <el-tabs v-model="activeName">
            <el-tab-pane label="车辆信息" name="first">
              <el-table stripe :data="vehicleList" border style="width: 100%">
                <el-table-column prop="vehicleNumber" width="99" align="center" label="车牌号"/>
                <el-table-column prop="vehicleType" width="70" :formatter="formatType" align="center" label="车型" />
                <el-table-column prop="parkingType" width="83" :formatter="formatParking" align="center" label="车位类型" />
                <el-table-column align="center" label="有效期" >
                  <template #default="scope">
                    <span>{{formatTime(scope.row.validBeginTime)}} -- {{formatTime(scope.row.validEndTime)}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" width="83" :formatter="vehicleFormatStatus" align="center" label="状态" />
                <el-table-column prop="center" width="100" show-overflow-tooltip align="center" label="备注">
                  <template #default="scope">
                    <span class="oneline">
                      {{scope.row.note}}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column align="center" width="85" label="操作">
                  <template #default="scope">
                    <el-button type="text" size="default" @click="openVehicleAuditDialog(scope.row.id,scope.$index)" >车辆审核</el-button ></template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </el-form>
  
    <el-dialog
      width="40%"
      v-model="auditDialog.show"
      :title="auditDialog.title"
    >
        <el-form ref="inputForm" :rules="rules">
          <el-row justify="center" style="margin-bottom:40px">
            <el-col :span="3">
                <span style="line-height: 40px;">审核</span>
            </el-col>
                
            <el-col :span="12">
                <el-radio-group v-model="radio" style="width:320px" size="large">
                    <el-radio-button label=0 >待审核</el-radio-button>
                    <el-radio-button label="1">正常</el-radio-button>
                    <el-radio-button label="-1">审核退回</el-radio-button>
                    <el-radio-button label="2" >搬出</el-radio-button>
                </el-radio-group>
            </el-col>
          </el-row>
          <el-row justify="center" v-if="auditDialog.title == '车辆信息审核'">
            <el-col :span="3">
                <span style="line-height: 30px;">有效期</span>
            </el-col>
            <el-col :span="12" >
              <el-form-item style="width:320px;height: 30px;">
                <el-date-picker
                  value-format="YYYY-MM-DD HH:mm:ss"
                  v-model="startToEndTime"
                  type="daterange"
                  start-placeholder="开始时间"
                  end-placeholder="到期时间"
                  :shortcuts="shortcuts"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row justify="center" v-if="radio == '-1'">
            <el-col :span="3">
                <span style="line-height: 72px;">审核退回</span>
            </el-col>
            <el-col :span="12">
                <el-form-item prop="dpassMsg">
                    <el-input
                        type="textarea"
                        style="margin-top: 20px; width: 300px"
                        ref="editTask"
                        v-model="disapproveMsg"
                        placeholder="请输入审核退回理由"
                    ></el-input>
                </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      <el-row justify="center">
        <el-button
          v-if="auditDialog.title == '个人信息审核'"
          type="primary"
          style="width: 100px; height: 30px; margin-top: 20px"
          @click="examine(personModel.id, radio, disapproveMsg)"
          >提交</el-button>
        <el-button
          v-else-if="auditDialog.title == '房产信息审核'"
          type="primary"
          style="width: 100px; height: 30px; margin-top: 20px"
          @click="examine(houseId, radio, disapproveMsg)"
          >提交</el-button>
          <el-button
          v-else-if="auditDialog.title == '车辆信息审核'"
          type="primary"
          style="width: 100px; height: 30px; margin-top: 20px"
          @click="examine(vehicleId, radio, disapproveMsg)"
          >提交</el-button>
      </el-row>
    </el-dialog>
  </el-dialog>
</template>


<script>
import { listVehicle } from "@/api/base/vehicle"
import { getPerson, personExamine } from "@/api/base/person";
import { ElMessage, ElMessageBox } from "element-plus";
import { getDictCss, formatDict } from "@/utils/dict"
import { listDictByNameEn } from "@/api/admin/dict";
import personBelongEdit from "@/componts/base/personBelongEdit.vue";
import { listPersonBelong } from "@/api/base/personBelong";
import { pushUser } from "@/api/admin/auth";
import mitt from "@/utils/mitt";
export default {
  props: ["sexList", "tagList"],
  components: { personBelongEdit },
  data() {
    return {
      searchModel: {
        communityId: localStorage.getItem("communityId"),
      },
      loading: false,
      dialog: {},
      auditDialog: {},
      personModel: {},
      personTypeList: [],
      statusList: [],
      getTagsList: [],
      roomTypeList: [],
      liveTypeList: [],
      myroomList: [],
			typeList: [],
      parkingList: [],
      vehicleList: [],
      vehicleStatusList: [],
      form: {
        resource: "",
      },
      radio: "1",
      disapproveMsg: "",
      imgServer: import.meta.env.VITE_BASE_API,
      houseId: "",
      vehicleId: "",
      examineType: 0,
      startToEndTime: [],
      activeName: "first",
      shortcuts: [{
            text: '7天',
            value: () => {
              const start = new Date()
              const end = new Date()
              end.setTime(end.getTime() + 3600 * 1000 * 24 * 7)
              return [start,end]
            },
            
          }, {
            text: '180天',
            value: () => {
              const start = new Date()
              const end = new Date()
              end.setTime(end.getTime() + 3600 * 1000 * 24 * 180)
              return [start, end]
            },
          },
          {
            text: '一年',
            value: () => {
              const start = new Date()
              const end = new Date()
              end.setTime(end.getTime() + 3600 * 1000 * 24 * 365)
              return [start, end]
            },
          },
          {
            text: '50年',
            value: () => {
              const start = new Date()
              const end = new Date()
              let year = 50
              let newYear = 0
              if(year % 4 == 0 && year % 100 != 0 || year % 400 ==0){
                newYear /= 4 
              }else{
                newYear = year / 4 + 1
              }
              end.setTime(end.getTime() + 3600 * 1000 * 24 * 365 * year + newYear * 3600 * 1000 * 24)
              console.log(end.getFullYear(),end);
              return [start, end]
            },
          }]
    };
  },
  methods: {
    //用户操作
		pushUserAction(actionName) {
			if (actionName != 'home') {
        let senObj = {
          oper: actionName,
          receiveClient: "all",
          tags: ['manager']
        }
				pushUser(senObj).then(res =>{
					if (res !== null && res.code === 0) {
					} else {
					}
				})
			}
		},
    sexFormat(value){
      let sex = {}
      for (let index = 0; index < this.sexList.length; index++) {
        sex[index] = this.sexList[index].nameCn
      }
      for (const i in sex) {
        if (value == i) {
          return sex[i]
        }
      }
    },
    personStatus(value){
        switch(value){
            case 0:return "待审核"
            break
            case 1:return "正常"
            break
            case -1:return "审核退回"
            break
            case 2:return "搬出"
            break
        }
    },
    // 审核
    examine(id, status, value) {
      if (this.startToEndTime) {
        var validBeginTime = this.startToEndTime[0]
        var validEndTime = this.startToEndTime[1]
      } else {
        var validBeginTime = " "
        var validEndTime = " "
      }
      if (!value && status == -1) {
        this.$refs["editTask"].focus();
        this.$confirm("请输入审核退回理由！", "错误", {
          confirmButtonText: "确定",
          type: "error",
        });
      } else {
        if (status != -1) {
          value = " "
        }
        // 车辆提交审核 开始日期,截止日期
        personExamine({
          id: id,
          status: Number(status),
          note: value,
          type: this.examineType,
          communityId: localStorage.getItem('communityId'),
          validBeginTime,
          validEndTime
        }).then((res) => {
          if (res.data.code == -1) {
            this.$message.success(res.data.msg);
            this.search();
          } else if (res.data.code == 0){
            this.$message.success(res.data.msg);
            this.search();
            this.auditDialog.show = false;
          }
        });
        
      }
      this.getTags()
    },
    // 个人信息审核
    openPersonAuditDialog() {
      this.auditDialog.show = true;
      // this.disapproveMsg = "";
      this.auditDialog.title = "个人信息审核";
      this.examineType = 0;
      if (this.personModel.status) {
        this.radio = this.personModel.status
      }else{
        this.radio = "0"
      }
      if (this.radio == "-1") {
        this.disapproveMsg = this.personModel.note
      }
    },
    // 房产信息审核
    openHouseAuditDialog(houseId,index) {
      this.houseId = houseId;
      this.auditDialog.show = true;
      // this.disapproveMsg = "";
      this.auditDialog.title = "房产信息审核";
      this.examineType = 1;
      this.radio = this.myroomList[index].status
      if (this.radio == "-1") {
        this.disapproveMsg = this.myroomList[index].note
      }
    },
    // 车辆信息审核
    openVehicleAuditDialog(vehicleId,index){
      this.vehicleId = vehicleId
      this.auditDialog.show = true;
      this.auditDialog.title = "车辆信息审核";
      this.examineType = 2;
      this.radio = this.vehicleList[index].status
      if (this.radio == "-1") {
        this.disapproveMsg = this.vehicleList[index].note
      }
      this.startToEndTime = [this.vehicleList[index].validBeginTime,this.vehicleList[index].validEndTime]
    },
    getTags() {
      let list = [];
      if (this.personModel.tags) {
        let tempList = this.personModel.tags;
        for (let item of this.tagList) {
          for (let tag of tempList) {
            if (tag == item.nameEn) {
              list.push({ nameCn: item.nameCn, type: item.type });
            }
          }
        }
      }
      this.getTagsList = list
    },
    search() {
      listPersonBelong({
        personId: this.personModel.id,
        communityId: localStorage.getItem("communityId"),
      }).then((res) => {
        this.myroomList = res.data.result;
      });
      getPerson(this.personModel.id).then((res) => {
        this.personModel = res.data.result;
      });

      listVehicle({ 
        personId: this.personModel.id, 
        communityId: localStorage.getItem("communityId") 
      }).then(res =>{
        this.vehicleList = res.data.result.list;
      })
      this.$emit("search");
    },

    // 楼栋数据转换
    formatStatus(row, column, cellValue, index) {
      let result = "";
      for (let item of this.statusList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
        }
      }
      return result;
    },
    formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
    formatRoomType(row, column, cellValue, index) {
      let result = "";
      for (let item of this.roomTypeList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
        }
      }
      return result;
    },
    formatPersonType(row, column, cellValue, index) {
      let result = "";
      for (let item of this.personTypeList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
        }
      }
      return result;
    },
    formatLiveType(row, column, cellValue, index) {
      let result = "";
      for (let item of this.liveTypeList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
        }
      }
      return result;
    },

    // 车辆数据转化
    formatType(row, column, cellValue, index){
			let result = ''
			for(let item of this.typeList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
    formatParking(row, column, cellValue, index){
			let result = ''
			for(let item of this.parkingList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
    formatTime(cellValue){
      if (cellValue) {
        return cellValue.substr(0,10)
      }
    },
    // formatEndTime(row, column, cellValue, index){
    //   if (cellValue) {
    //     return cellValue.substr(0,10)
    //   }
    // },
    vehicleFormatStatus(row, column, cellValue, index){
			let result = ''
			for(let item of this.statusList){
				if(item.nameEn == cellValue){
					result = item.nameCn
				}
			}
			return result
		},
    async init() {
      try {
        let person_res = await listDictByNameEn("person_type");
        let live_res = await listDictByNameEn("live_type");
        let room_res = await listDictByNameEn("room_type");
        let status_res = await listDictByNameEn("person_status");
        this.statusList = status_res.data.result;
        this.personTypeList = person_res.data.result;
        this.roomTypeList = room_res.data.result;
        this.liveTypeList = live_res.data.result;

        let vehicle_type = await listDictByNameEn('vehicle_type')
				this.typeList = vehicle_type.data.result
        let parking_type = await listDictByNameEn('parking_type')
				this.parkingList = parking_type.data.result
        let vehicle_status = await listDictByNameEn('vehicle_status')
				this.vehicleStatusList = vehicle_status.data.result
      } catch (err) {}
    },
  },
  mounted() {
    this.$nextTick(function () {
      mitt.on("openPersonView", (data) => {
        this.sexFormat()
// console.log(this.sexList,"1```````````````");
        this.init();
        if (data.person.tags) {
          data.person.tags = JSON.parse(data.person.tags);
        }
        this.pushUserAction("人员审核")
        this.personModel = data.person;
        this.myroomList = data.data;
        this.vehicleList = data.vehicle.list
        this.dialog.show = true;
        this.dialog.title = "详细信息";
        this.getTags()
      });
    });
  },
  watch: {
    radio(newVal, oldVal) {
      if (newVal !== -1) {
        this.disapproveMsg = "";
      }
    },
    "dialog.show"(newVal,oldVal){
      if (!newVal) {
        this.pushUserAction("人员管理")
      }
      
    }
  },
  created() {
    mitt.off("openPersonBelongAdd");
    mitt.off("openPersonBelongEdit");
  },
};
</script>

<style scoped="scoped">
.el-dialog__body {
  height: 500px !important;
}
.oneline{
  white-space:nowrap;

overflow:hidden;

text-overflow:ellipsis;
}
</style>