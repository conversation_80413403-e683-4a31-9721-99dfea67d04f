
<template>
    <div>
      <div>
        <div
          v-for="(item,index) in chinaShortName"
          :class="chinaShortName[index].isClick?'item-box1':'item-box'" @click="clickShortName(index)" :key="index">
          <a> {{ item.value }}</a>
        </div>
      </div>
      <div>
        <div :class="letters[index].isClick?'item-box2':'item-box'" :style="bgcolor"
             v-for="(item,index) in letters" @click="clickLetters(index)" :key="index"><a>{{ item.value }}</a>
        </div>
      </div>
      <div>
        <div :class="number[index].isClick?'item-box2':'item-box'" :style="bgcolor"
             v-for="(item,index) in number" @click="clickNumber(index)" :key="index"><a>{{ item.value }}</a>
        </div>
      </div>
      <div style="float: right">
      </div>
      <div class="car-number" style="display: inline-block">
        车牌号:
        <div style="display: inline" v-for="(item,index) in firstName" :key="index">{{ item.value }}</div>
        <div style="display: inline;letter-spacing:2px;" v-for="(item,index) in otherName" :key="index">{{ item.value }}</div>
      </div>
      <div class="backspace" @click="backSpace">
        <a>退格</a>
      </div>
      <div class="backspace" @click="comfirmCarNum">
        <a>确认</a>
      </div>
    </div>
  </template>
   
  <script>
	import mitt from "@/utils/mitt";
  export default {
    name: "carnumber",
    data() {
      return {
        bgcolor: {
          backgroundColor: "gray"
        },
        isAllowClickNumber: false,
        isActive: false,
        firstName: [],
        //
        otherName: [],
        //车牌地区简称
        chinaShortName: [
          {
            value: '京',
            label: '京',
            isClick: false
          },
          {
            value: '津',
            label: '津',
            isClick: false
          },
          {
            value: '沪',
            label: '沪',
            isClick: false
          },
          {
            value: '渝',
            label: '渝',
            isClick: false
          },
          {
            value: '冀',
            label: '冀',
            isClick: false
          },
          {
            value: '晋',
            label: '晋',
            isClick: false
          },
          {
            value: '蒙',
            label: '蒙',
            isClick: false
          },
          {
            value: '辽',
            label: '辽',
            isClick: false
          },
          {
            value: '吉',
            label: '吉',
            isClick: false
          },
          {
            value: '黑',
            label: '黑',
            isClick: false
          },
          {
            value: '苏',
            label: '苏',
            isClick: false
          },
          {
            value: '浙',
            label: '浙',
            isClick: false
          },
          {
            value: '皖',
            label: '皖',
            isClick: false
          },
          {
            value: '闽',
            label: '闽',
            isClick: false
          },
          {
            value: '赣',
            label: '赣',
            isClick: false
          },
          {
            value: '鲁',
            label: '鲁',
            isClick: false
          },
          {
            value: '豫',
            label: '豫',
            isClick: false
          },
          {
            value: '鄂',
            label: '鄂',
            isClick: false
          },
          {
            value: '湘',
            label: '湘',
            isClick: false
          },
          {
            value: '粤',
            label: '粤',
            isClick: false
          },
          {
            value: '桂',
            label: '桂',
            isClick: false
          },
          {
            value: '新',
            label: '新',
            isClick: false
          },
          {
            value: '黔',
            label: '黔',
            isClick: false
          },
          {
            value: '云',
            label: '云',
            isClick: false
          }
        ],
        //车牌号码
        letters: [
          {
            label: 'A',
            value: 'A',
            isClick: false
          },
          {
            label: 'B',
            value: 'B',
            isClick: false
          },
          {
            label: 'C',
            value: 'C',
            isClick: false
          },
          {
            label: 'D',
            value: 'D',
            isClick: false
          },
          {
            label: 'E',
            value: 'E',
            isClick: false
          },
          {
            label: 'F',
            value: 'F',
            isClick: false
          },
          {
            label: 'G',
            value: 'G',
            isClick: false
          },
          {
            label: 'H',
            value: 'H',
            isClick: false
          },
          {
            label: 'J',
            value: 'J',
            isClick: false
          },
          {
            label: 'K',
            value: 'K',
            isClick: false
          },
          {
            label: 'L',
            value: 'L',
            isClick: false
          },
          {
            label: 'M',
            value: 'M',
            isClick: false
          },
          {
            label: 'N',
            value: 'N',
            isClick: false
          },
          {
            label: 'P',
            value: 'P',
            isClick: false
          },
          {
            label: 'Q',
            value: 'Q',
            isClick: false
          },
          {
            label: 'R',
            value: 'R',
            isClick: false
          },
          {
            label: 'S',
            value: 'S',
            isClick: false
          },
          {
            label: 'T',
            value: 'T',
            isClick: false
          },
          {
            label: 'U',
            value: 'U',
            isClick: false
          },
          {
            label: 'V',
            value: 'V',
            isClick: false
          },
          {
            label: 'W',
            value: 'W',
            isClick: false
          },
          {
            label: 'X',
            value: 'X',
            isClick: false
          },
          {
            label: 'Y',
            value: 'Y',
            isClick: false
          },
          {
            label: 'Z',
            value: 'Z',
            isClick: false
          }
        ],
        number: [
          {
            label: '0',
            value: '0',
            isClick: false
          },
          {
            label: '1',
            value: '1',
            isClick: false
          },
          {
            label: '2',
            value: '2',
            isClick: false
          },
          {
            label: '3',
            value: '3',
            isClick: false
          },
          {
            label: '4',
            value: '4',
            isClick: false
          },
          {
            label: '5',
            value: '5',
            isClick: false
          },
          {
            label: '6',
            value: '6',
            isClick: false
          },
          {
            label: '7',
            value: '7',
            isClick: false
          },
          {
            label: '8',
            value: '8',
            isClick: false
          },
          {
            label: '9',
            value: '9',
            isClick: false
          }
        ]
      }
    },
    methods: {
      clickShortName: function (index) {
        for (let i = 0; i < this.chinaShortName.length; i++) {
          if (index === i) {
            this.chinaShortName[i].isClick = true
            if (this.firstName.length == 0) {
              this.firstName.push({
                type: "firstName",
                index: index,
                value: this.chinaShortName[i].value
              })
            } else {
              this.firstName.pop();
              this.firstName.push({
                type: "firstName",
                index: index,
                value: this.chinaShortName[i].value
              })
            }
          } else {
            this.chinaShortName[i].isClick = false
          }
        }
        this.isAllowClickNumber = true,
          this.bgcolor.backgroundColor = '';
      },
      clickLetters: function (index) {
        this.limitSize()
        if (!this.isAllowClickNumber) {
          //提示不可以点击
          this.$message("请点击省份简称")
          return;
        }
        for (let i = 0; i < this.letters.length; i++) {
          if (index === i) {
            this.letters[i].isClick = true
            this.otherName.push({
              type: "letters",
              index: index,
              value: this.letters[i].value
            })
          }
        }
      },
      clickNumber: function (index) {
        this.limitSize()
        if (!this.isAllowClickNumber) {
          //提示不可以点击
          this.$message("请点击省份简称")
          return;
        }
        for (let i = 0; i < this.number.length; i++) {
          if (index === i) {
            this.number[i].isClick = true
            this.otherName.push({
              type: "number",
              index: index,
              value: this.number[i].value
            })
          }
        }
      },
      limitSize() {
        if (this.otherName.length > 5) {
          this.$message.warning("输入的车牌号码过长");
          throw new Error("输入的车牌号码过长");
        }
      },
      backSpace: function () {
        if (this.firstName.length == 0) {
          this.isAllowClickNumber = false;
          this.$message("已经清空数据")
          return;
        }
        let pop = this.otherName.pop();
        if (this.otherName.length == 0) {
          let pop1 = this.firstName.pop();
          this.chinaShortName[pop1.index].isClick = false;
          this.isAllowClickNumber = false;
        }
        if (pop.type == 'number') {
          this.number[pop.index].isClick = false
        } else if (pop.type == 'letters') {
          this.letters[pop.index].isClick = false
        }
      },
      comfirmCarNum: function () {
        console.log(this.otherName);
        //确认车牌号
        let arr = [];
        this.firstName.forEach(item => {
          arr.push(item.value)
        })
        this.otherName.forEach(item => {
          arr.push(item.value)
        })
        arr = arr.join("")
        mitt.emit("carNum", arr);
      }
    }
  }
   
  </script>
   
  <style scoped>
  .item-box {
    display: inline-block;
    margin: 10px;
    width: 30px;
    height: 30px;
    font-size: 20px;
    line-height: 30px;
    background-color: black;
    color: white;
    text-align: center;
  }
   
  .item-box1 {
    display: inline-block;
    margin: 10px;
    width: 30px;
    height: 30px;
    font-size: 20px;
    line-height: 30px;
    background-color: white;
    color: black;
    border: 0.5px solid black;
    text-align: center;
  }
   
  .item-box2 {
    display: inline-block;
    margin: 10px;
    width: 30px;
    height: 30px;
    font-size: 20px;
    line-height: 30px;
    background-color: white;
    border: 0.5px solid black;
    color: black;
    text-align: center;
  }
   
  .car-number {
    font-size: 20px;
    font-weight: bolder;
    width: 230px;
    height: 40px;
    margin: 0 auto;
    background-color: darkblue;
    color: white;
    line-height: 40px;
    padding-left: 20px;
    border-radius: 5px;
  }
   
  .backspace {
    display: inline-block;
    font-size: 20px;
    font-weight: bolder;
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin-left: 10px;
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 5px;
    border: 0.5px solid black;
  }
  </style>