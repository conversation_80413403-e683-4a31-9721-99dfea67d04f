<template>
  <div id="index" v-if="!code">
    <el-card id="card">
      <el-dialog
        draggable
        width="26%"
        v-model="dialog.show"
        :title="dialog.title"
      >
        <div style="display: flex; margin-bottom: 10px; align-items: center">
          <el-row>
            <el-col>
              <el-input
                placeholder="请输入验证码"
                v-model="userModel.verifyCodeValue"
              ></el-input>
            </el-col>
          </el-row>
          <el-image :src="verifyCode"></el-image>
          <el-icon @click="getVerifyCode" :size="22" style="cursor: pointer"
            ><RefreshLeft
          /></el-icon>
        </div>
      </el-dialog>
      <p
        class="box2"
        v-show="!isQrLogin"
        v-if="loginStatusList.includes('SCAN')"
      >
        扫码登录更安全
      </p>
      <div
        class="change-login"
        @click="changeLogin"
        v-if="loginStatusList.includes('SCAN')"
      >
        <i
          class="iconfont"
          :class="isQrLogin ? 'icon-diannao' : 'icon-icon-qrcode'"
          style="font-size: 52px; color: #5c6ee3; cursor: pointer"
        ></i>
      </div>
      <!-- <div style="position:relative;left:300px;cursor:pointer" @click="changeLogin">切换微信扫码登录</div> -->
      <el-form :rules="rules" ref="form" :model="userModel" v-if="!isQrLogin">
        <el-form-item style="margin-top: 20px">
          <div
            style="
              height: 60px;
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <span style="font-size: 30px; font-weight: bold; color: #4472fc">{{
              systemName
            }}</span>
            <div
              v-if="
                loginStatusList.includes('PWD') &&
                loginStatusList.includes('MSG')
              "
            >
              <span
                v-if="loginStatusList.includes('PWD') && loginStatus == 'PWD'"
                class="change_login_text"
                @click="changeLoginInput('PWD')"
                >短信登录</span
              >
              <span
                v-else
                class="change_login_text"
                @click="changeLoginInput('MSG')"
                >密码登录</span
              >
            </div>
          </div>
        </el-form-item>
        <el-row v-if="loginStatus == 'PWD'">
          <el-col>
            <el-form-item prop="userName">
              <el-input
                placeholder="用户名"
                v-model="userModel.userName"
                key="userName"
              >
                <template #prefix>
                  <el-icon :size="20">
                    <user></user>
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="loginStatus == 'PWD'">
          <el-col>
            <el-form-item prop="password">
              <el-input
                placeholder="密码"
                :type="passwordType"
                @keyup.enter="onSubmit"
                key="password"
                v-model="userModel.password"
              >
                <template #prefix>
                  <el-icon :size="20">
                    <lock></lock>
                  </el-icon>
                  <i
                    @click="showEyes"
                    class="iconfont eyes"
                    :class="
                      isShowEyes
                        ? 'icon-yanjing_yincang'
                        : 'icon-yanjing_xianshi'
                    "
                  ></i>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="loginStatus == 'MSG'">
          <el-col>
            <el-form-item prop="phone">
              <el-input
                placeholder="请输入手机号"
                key="phone"
                v-model="userModel.phone"
              >
                <template #prefix>
                  <el-icon :size="20">
                    <Iphone></Iphone>
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="loginStatus == 'MSG'">
          <el-col>
            <el-form-item prop="code">
              <div
                style="
                  width: 100%;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                "
              >
                <el-input
                  placeholder="手机验证码"
                  @keydown.enter="onSubmit"
                  key="code"
                  v-model="userModel.code"
                >
                  <template #prefix>
                    <el-icon :size="20">
                      <Message />
                    </el-icon>
                  </template>
                </el-input>
                <div
                  style="position: absolute; right: 2vh; cursor: pointer"
                  :disabled="timerDisabled"
                  @click="getVerifyCode"
                >
                  <b v-if="timerDisabled">{{ timer }}</b> 获取验证码
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="NDA" label="" size="large" /><span
              style="font-size: 13px"
              @click="NDA = true"
              >我已仔细阅读<span
                @click="toNDA"
                style="color: #568efe; cursor: pointer"
                >保密协议</span
              >
            </span>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item>
              <el-button
                :disabled="disabled"
                style="width: 100%; height: 50px; font-size: 20px"
                type="primary"
                @click="onSubmit"
                >{{ loginText }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="d-qr" v-if="isQrLogin">
        <div
          class="change-qr"
          v-if="needChangeQrCode"
          :style="{ 'background-image': 'url(' + imageData + ')' }"
        >
          <div class="change-tips-d">
            <div class="change-tips">二维码已失效</div>
            <el-icon
              @click="getNewQrCode"
              color="#5c6ee3"
              :size="30"
              style="cursor: pointer; margin-top: 15px"
              ><RefreshLeft
            /></el-icon>
            <!-- <img
					src="~@/assets/icons/icon-refresh.png"
					@click="getNewQrCode"
					style="
						width: 25px;
						height: 25px;
						cursor: pointer;
						margin-top: 15px;
					"
					alt=""
					/> -->
          </div>
        </div>
        <canvas class="qecode" id="canvas" v-if="!needChangeQrCode"> </canvas>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
          "
        >
          <i
            class="icon-saoyisao iconfont"
            style="font-size: 28px; margin-right: 10px; color: #5c6ee3"
          ></i>
          <div style="marign-top: 15px; color: #878480">微信扫一扫登录</div>
        </div>
      </div>
    </el-card>
    <!-- <span style="font-size: 38px;font-weight: bold;color: #4472FC;position:fixed;left:58%;top:10%">{{systemName}}</span> -->
    <div class="agree-pop" v-if="agreeStatus">
      <div style="height: 100%">
        <div
          v-html="agreeText"
          style="
            height: calc(100% - 60px);
            overflow: scroll;
            padding: 10px 10px 0 10px;
          "
        ></div>
      </div>
      <div class="agree-bottom" @click="agreeStatus = !agreeStatus">确 认</div>
    </div>
  </div>
  <band-admin @loginSuccess="loginSuccess"></band-admin>
</template>


<script setup>
import store from "../../store/index.js";
import { User, Lock, Key } from "@element-plus/icons-vue";
import { currentUser } from "@/api/admin/user";
import {
  login,
  mcpLogin,
  getCode,
  getAVerifyCode,
  loginCode,
  createQrCode,
  getQrCode,
  getLoginStatus,
  getUserAgreement,
} from "@/api/admin/auth";
import silderCheckCode from "@/componts/admin/silderCheckCode.vue";
import bandAdmin from "@/componts/login/bandAdmin.vue";
import { reactive, ref, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { myListMenu, homeMenu } from "@/api/admin/menu";
// import JSEncrypt from 'jsencrypt'
import { ElLoading } from "element-plus";
import mitt from "@/utils/mitt";
import QRCode from "qrcode";
import "@/assets/iconfonts2/iconfont.css";
import axios from "axios";
import { marked } from "marked";

const modules = import.meta.glob("../../views/**/**.vue");
const router = useRouter();
const { currentRoute } = useRouter();
const route = currentRoute.value;
const code = ref("");
const systemName = ref("");
onMounted(() => {
  //获取登录模式（PWD|SCAN|MSG）
  getLoginStatus().then((res) => {
    if (res.data.result) {
      loginStatusList.value = JSON.parse(res.data.result).loginMode.split("|");
      localStorage.setItem(
        "systemName",
        JSON.parse(res.data.result).systemName
          ? JSON.parse(res.data.result).systemName
          : "数字小区管理后台"
      );
      localStorage.setItem(
        "systemShortName",
        JSON.parse(res.data.result).systemName
          ? JSON.parse(res.data.result).systemShortName
          : "数字小区"
      );
      systemName.value = localStorage.getItem("systemName");
      document.title = systemName.value;
    }

    // if(res.data.result) {
    // 	loginStatusList.value = "MSG|PWD|SCAN".split("|")
    // }
    if (loginStatusList.value.includes("MSG")) {
      loginStatus.value = "MSG";
      console.log(loginStatus);
    }
    if (loginStatusList.value.includes("PWD")) {
      loginStatus.value = "PWD";
      console.log(loginStatus);
    }
  });

  if (!localStorage.getItem("refresh")) {
    localStorage.setItem("refresh", 1);
  }
  if (localStorage.getItem("refresh") == 1) {
    window.location.reload();
    localStorage.setItem("refresh", 2);
  }

  code.value = route.query.code;
  console.log(code);

  // 总控平台跳转子平台
  if (code.value) {
    setTimeout(function () {
      login({
        grantType: "mcp",
        code: code.value,
        clientId: "wx59c45955021001",
      })
        .then((res) => {
          if (res.data.code == 2) {
            mitt.emit("openBandAdmin", res.data.result);
          } else {
            console.log(res);
            disabled.value = true;
            localStorage.setItem("token", res.data.result.access_token);
            localStorage.setItem(
              "expires_time",
              new Date().getTime() +
                parseInt(res.data.result.expires_in * 0.8) * 1000
            );
            localStorage.setItem("refreshToken", res.data.result.refresh_token);
            loginSuccess();
          }
        })
        .catch((err) => {
          console.log(err);
          ElMessage({
            message: err,
            type: "error",
            duration: 5 * 1000,
          });
        });
    }, 1000);
  }
});

const check_code = ref(false);

const form = ref(null);
const userModel = reactive({
  clientId: "wx59c45955021001",
  // grantType: 'password', //登录模式
});

const loginErrTimes = ref(0);
const loginText = ref("登 录");
const disabled = ref(false);
const NDA = ref(false);
const agreeText = ref("");
const agreeStatus = ref(false);

// 保密协议
function toNDA() {
  agreeStatus.value = true;
  getUserAgreement(5).then((res) => {
    agreeText.value = marked(res.data.result);
  });
}
let loginParams = reactive({
  grantType: "phone",
  phone: userModel.phone,
  code: userModel.code,
  password: userModel.password,
  userName: userModel.userName,
  clientId: "wx59c45955021001",
});
const rules = reactive({
  phone: [
    {
      required: true,
      validator: validatePhone,

      trigger: "blur",
    },
  ],
  code: [
    {
      required: true,
      validator: validateCheck,
      trigger: "blur",
    },
  ],
  userName: [
    {
      required: true,
      message: "请输入用户名",
      trigger: "blur",
    },
  ],
  password: [
    {
      required: true,
      validator: validatePassword,
      trigger: "blur",
    },
  ],
});
const fullLoading = ref(null);

// 监听验证码为4位，直接调用登录接口
watch(
  userModel,
  (newVal) => {
    if (newVal.verifyCodeValue && newVal.verifyCodeValue.length == 4) {
      if (loginStatus.value == "PWD" && dialog.show == true) {
        // fullLoading.value = ElLoading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.7)' , text: '正在获取验证码..请稍后'});
        getLoginCode();
        delete userModel.verifyCodeValue;
      } else if (loginStatus.value == "MSG") {
        // fullLoading.value = ElLoading.service({ fullscreen: true, background: 'rgba(0, 0, 0, 0.7)' , text: '正在获取验证码..请稍后'});
        getLoginCode();
        userModel.verifyCodeValue = "";
      }
    }
  },
  { deep: true }
);

function result(e) {
  check_code.value = e.status;
  userModel.code = e.code;
}
function validatePhone(rule, value, callback) {
  var reg = /^1[3456789]\d{9}$/;
  if (value == undefined || value == null) {
    return callback(new Error("手机号不能为空"));
  }
  if (!reg.test(value)) {
    return callback(new Error("请输入正确的手机号"));
  }
  callback();
}

function validateCheck(rule, value, callback) {
  if (!userModel.code) {
    callback(new Error("请先完成验证"));
  }
  callback();
}

function validatePassword(rule, value, callback) {
  var reg = /^\S{6,18}$/;
  if (value == undefined || value == null) {
    return callback(new Error("密码不能为空"));
  }
  if (!reg.test(value)) {
    return callback(new Error("密码长度为6-18位"));
  }
  callback();
}

const dialog = reactive({ show: false, title: "" });
const verifyCode = ref("");
const verifyCodeId = ref("");

// 点击登录获取验证码图片 (4位数字)
function getVerifyCode(params) {
  if (timerDisabled.value == true) {
    return;
  }
  if (userModel.phone && userModel.phone.length != 11) {
    return;
  }
  dialog.show = true;
  getAVerifyCode().then((res) => {
    verifyCode.value = "data:image/png;base64," + res.data.result.verifyCode;
    verifyCodeId.value = res.data.result.key;
  });
}
function getVerifyCode1(params) {
  if (!userModel.password && !userModel.userName) {
    return;
  }
  dialog.show = true;
  getAVerifyCode().then((res) => {
    verifyCode.value = "data:image/png;base64," + res.data.result.verifyCode;
    verifyCodeId.value = res.data.result.key;
  });
}
const QrTimer = ref(null);

//清除二维码
function clearMyTimer() {
  console.log(QrTimer);
  if (QrTimer.value) {
    clearInterval(QrTimer.value);
    QrTimer.value = null;
  }
}

const query = reactive({
  grantType: "phone",
  phone: "",
  code: "",
  clientId: "",
});

//获取二维码
function getQrCodeStateFnc(uuid) {
  var param = {
    uuid: uuid,
  };
  getQrCode(param)
    .then((res) => {
      console.log(res);
      if (res.data.code === 0) {
        if (res.data.result.status == 2) {
          //   this.loading = true;

          query.grantType = "scan";

          query.clientId = "wx59c45955021001";
          query.uuid = uuid;
          query.agreeProtocolList = ["5"];

          login(query)
            .then((res) => {
              disabled.value = true;
              localStorage.setItem("token", res.data.result.access_token);
              localStorage.setItem(
                "expires_time",
                new Date().getTime() +
                  parseInt(res.data.result.expires_in * 0.8) * 1000
              );
              localStorage.setItem(
                "refreshToken",
                res.data.result.refresh_token
              );
              loginSuccess();
            })
            .catch((err) => {
              needChangeQrCode.value = true;
              loginErrTimes.value++;
              loginText.value = "登 录";
              disabled.value = false;
            });

          clearMyTimer();
        }
      } else if (res.data.code === -1) {
        console.log("code = -1");
        needChangeQrCode.value = true;
        clearMyTimer();
      }
    })
    .catch((err) => {
      console.log(err);
      needChangeQrCode.value = true;
      clearMyTimer();
    });
}

const imageData = ref(null);
function createQrCodeFnc() {
  createQrCode()
    .then((res) => {
      //   if (res !== null && res.code === 0) {
      /**
       * 0 初始状态
       * 1 已扫描
       * 2 已确认
       * 3 已失效
       */
      if (res.data.result.status == 0) {
        setTimeout(() => {
          var canvas = document.getElementById("canvas");
          // 调用函数去生成二维码，参数依次为：二维码的容器、要生成的内容、回调函数
          QRCode.toCanvas(canvas, res.data.result.content, function (error) {
            if (error) {
              console.error(error);
            } else {
              imageData.value = canvas.toDataURL("image/png");
              console.log("success!");
            }
          });

          if (!QrTimer.value) {
            QrTimer.value = setInterval(() => {
              console.log(QrTimer);
              getQrCodeStateFnc(res.data.result.uuid);
            }, 3000);
          }
        }, 1);
      } else {
        alert(
          res.data.result.status == 1
            ? "二维码已扫描"
            : res.data.result.status == 2
            ? "二维码已确认"
            : "二维码已失效"
        );
        if (res.data.result.status == -1) {
          needChangeQrCode.value = true;
        }
      }
      //   } else {
      //   }
    })
    .catch((res) => {
      needChangeQrCode.value = true;
    });
}

const needChangeQrCode = ref(false);
function getNewQrCode() {
  needChangeQrCode.value = false;
  createQrCodeFnc();
}

const passwordType = ref("password");
const isShowEyes = ref(false);

//显示隐藏密码
function showEyes() {
  isShowEyes.value = !isShowEyes.value;
  if (isShowEyes.value) {
    passwordType.value = "text";
  } else {
    passwordType.value = "password";
  }
}

const isQrLogin = ref(false);

//改变登录状态
function changeLogin() {
  if (!NDA.value) {
    // ElMessageBox.alert("请先阅读并同意协议", "", {
    //   confirmButtonText: "确认",
    //   customClass: "persdsd",
    // });
    // return;

    ElMessageBox.confirm("我已阅读并同意保密协议", "提示", {
      confirmButtonText: "是",
      cancelButtonText: "否",
      type: "warning",
    })
      .then(() => {
        // 用户点击“是”
        console.log("用户点击了是");
        // 在这里执行用户选择是的逻辑
        NDA.value = true;

        isQrLogin.value = !isQrLogin.value;

        if (needChangeQrCode.value == true) {
          getNewQrCode();
        }
        if (isQrLogin.value) {
          createQrCodeFnc();
        } else {
          clearMyTimer();
        }
      })
      .catch(() => {
        // 用户点击“否”
        console.log("用户点击了否");
        // 在这里执行用户选择否的逻辑
      });

    return;
  }

  isQrLogin.value = !isQrLogin.value;

  if (needChangeQrCode.value == true) {
    getNewQrCode();
  }
  if (isQrLogin.value) {
    createQrCodeFnc();
  } else {
    clearMyTimer();
  }
}
const loginStatusList = ref([]);
const loginStatus = ref("");
function changeLoginInput(mode) {
  delete userModel.phone;
  delete userModel.code;
  delete userModel.userName;
  delete userModel.password;
  if (mode == "PWD") {
    loginStatus.value = "MSG";
  }
  if (mode == "MSG") {
    loginStatus.value = "PWD";
  }
}
const timer = ref(60);
const timerDisabled = ref(false);

//验证码成功后调用的登陆接口 获取token以及refreshtoken
function getLoginCode() {
  // fullLoading.value.close()
  let data = {
    phone: userModel.phone,
    verifyCodeKey: verifyCodeId.value,
    verifyCodeValue: userModel.verifyCodeValue,
  };
  if (loginStatus.value == "PWD") data.grantType = "password";
  if (loginStatus.value == "MSG") data.grantType = "phone";
  loginCode(data)
    .then((res) => {
      console.log(res);
      if (loginStatus.value == "PWD") {
        userModel.agreeProtocolList = ["5"];
        login(userModel)
          .then((res) => {
            dialog.show = false;
            // disabled.value = true
            localStorage.setItem("token", res.data.result.access_token);
            localStorage.setItem(
              "expires_time",
              new Date().getTime() +
                parseInt(res.data.result.expires_in * 0.8) * 1000
            );
            localStorage.setItem("refreshToken", res.data.result.refresh_token);
            // 成功后调用登录接口给获取菜单路由权限等
            loginSuccess();
          })
          .catch((err) => {
            dialog.show = false;
            loginErrTimes.value++;
            loginText.value = "登 录";
            disabled.value = false;
            console.log("密码登录失败");
          });
      } else {
        if (res.data.result.includes("-")) {
          ElMessage.error({
            message: "发送验证码失败",
          });
        } else if (res.data.result.includes("VALID")) {
          const rTimes = res.data.result.substring(
            res.data.result.indexOf(":") + 1
          );
          if (parseInt(rTimes) == 0) {
            ElMessage.error({
              message: `发送验证码频繁，请1秒后重试！`,
            });
          } else {
            ElMessage.error({
              message: `发送验证码频繁，请${rTimes}秒后重试！`,
            });
          }
        } else {
          ElMessage.success({
            message: "发送验证码成功",
          });
        }
      }

      dialog.show = false;
      timerDisabled.value = true;
      let time = 60;
      const daojishi = setInterval(() => {
        time--;
        timer.value = time;
        if (time == 0) {
          timerDisabled.value = false;
          clearInterval(daojishi);
        }
      }, 1000);
    })
    .catch((err) => {
      console.log("进入了", err);
      userModel.verifyCodeValue = "";
      if (loginStatus.value == "PWD") {
        getVerifyCode1();
      } else {
        getVerifyCode();
      }
    });
}

function onSubmit() {
  console.log(loginStatus.value, NDA.value);
  form.value.validate((valid) => {
    if (valid) {
      if (!NDA.value) {
        // ElMessageBox.alert("请先阅读并同意协议", "", {
        //   confirmButtonText: "确认",
        //   customClass: "persdsd",
        // });

        ElMessageBox.confirm("我已阅读并同意保密协议", "提示", {
          confirmButtonText: "是",
          cancelButtonText: "否",
          type: "warning",
        })
          .then(() => {
            // 用户点击“是”
            console.log("用户点击了是");
            NDA.value = true;
            // 在这里执行用户选择是的逻辑
            if (loginErrTimes.value >= 5) {
              disabled.value = false;
            } else {
              // localStorage.clear()
              if (loginStatus.value == "PWD") {
                userModel.grantType = "password";
                getVerifyCode1();
                return;
              }
              userModel.agreeProtocolList = ["5"];
              userModel.grantType = "phone";
              delete userModel.verifyCodeValue;
              delete userModel.userName;
              delete userModel.password;
              login(userModel)
                .then((res) => {
                  disabled.value = true;
                  localStorage.setItem("token", res.data.result.access_token);
                  localStorage.setItem(
                    "expires_time",
                    new Date().getTime() +
                      parseInt(res.data.result.expires_in * 0.8) * 1000
                  );
                  localStorage.setItem(
                    "refreshToken",
                    res.data.result.refresh_token
                  );
                  loginSuccess();
                })
                .catch((err) => {
                  loginErrTimes.value++;
                  loginText.value = "登 录";
                  disabled.value = false;
                });
            }
          })
          .catch(() => {
            // 用户点击“否”
            console.log("用户点击了否");
            // 在这里执行用户选择否的逻辑
          });

        return;
      }
      // loginText.value = "登 录 中 ..."
      // disabled.value = true
    }

    if (loginErrTimes.value >= 5) {
      disabled.value = false;
    } else {
      // localStorage.clear()
      if (loginStatus.value == "PWD") {
        userModel.grantType = "password";
        getVerifyCode1();
        return;
      }
      userModel.agreeProtocolList = ["5"];
      userModel.grantType = "phone";
      delete userModel.verifyCodeValue;
      delete userModel.userName;
      delete userModel.password;
      login(userModel)
        .then((res) => {
          disabled.value = true;
          localStorage.setItem("token", res.data.result.access_token);
          localStorage.setItem(
            "expires_time",
            new Date().getTime() +
              parseInt(res.data.result.expires_in * 0.8) * 1000
          );
          localStorage.setItem("refreshToken", res.data.result.refresh_token);
          loginSuccess();
        })
        .catch((err) => {
          loginErrTimes.value++;
          loginText.value = "登 录";
          disabled.value = false;
        });
    }
  });
}

//登录成功获取路由列表菜单
function loginSuccess() {
  localStorage.setItem("menuIndex", 0);
  currentUser().then((res) => {
    localStorage.setItem("userInfo", JSON.stringify(res.data.result));
  });
  myListMenu().then((res) => {
    localStorage.setItem("router", JSON.stringify(res.data.result));
    routerList();
  });
}

function routerList() {
  let list = [];
  if (localStorage.getItem("router")) {
    let routerAry = JSON.parse(localStorage.getItem("router"));
    for (let item of routerAry) {
      const component = modules["../../views/" + item.componentPath + ".vue"];
      if (component) {
        list.push({
          path: item.path,
          name: item.name,
          icon: item.icon,
          component: component,
          meta: item.menuName,
        });
      } else {
        list.push({
          path: item.path,
          name: item.name,
          icon: item.icon,
          component: () => import("../../views/error/NotFound.vue"),
        });
      }
    }
  }
  list.push({
    path: "home",
    name: "home",
    component: modules["../../views/home/<USER>"],
  });
  list.push({
    hidden: true,
    path: "/jobLog",
    component: () => import("@/views/monitor/job/log.vue"),
    name: "jobLog",
    meta: { title: "调度日志", activeMenu: "/jobTask" },
  });
  list.push({
    path: "lssData",
    name: "lssData",
    component: modules["../../views/base/lssDataList.vue"],
  });
  const rot = {
    path: "/",
    name: "index",
    component: () => import("../../views/home/<USER>"),
    children: list,
    redirect: "/home",
  };

  router.push("/home");
  router.addRoute(rot);
  homeMenu({ menuCategory: 0, isPortal: 1 }).then((res) => {
    console.log(res.data.result, res.data.result.path, "------------------");
    if (
      !res.data.result &&
      !res.data.result.path &&
      res.data.result.path != ""
    ) {
    } else {
      router.push("/" + res.data.result.path);
    }
  });

  // if (localStorage.getItem("systemName") == "新港社区治理管理系统") {
  // 	router.push("/person")
  // }
  // console.log(router.getRoutes(),list,'`````````````');
}
</script>
<style scoped lang="less">
* {
  user-select: none;
}
#index {
  overflow: hidden;
  box-sizing: border-box;
  height: 100vh;
  background-repeat: no-repeat;
  background-image: url(@/assets/login/login_background.png);
}

#card {
  position: absolute;
  // width: 24vw;
  // height: 40vh;
  margin-left: 57%;
  margin-top: 10%;
  min-width: 350px;
  max-width: 24vw;
}
.change-login {
  position: absolute;
  //   padding: 2px;
  user-select: none;
  /* color: #fff; */
  border-radius: 3px;
  top: 0.5vh;
  font-size: 14px;
  right: 0.5vh;
  z-index: 999;
}
.eyes {
  cursor: pointer;
  font-size: 28px;
  position: absolute;
  right: 0px;
}

.d-qr {
  width: 100%;
  height: 23vh;
  min-width: 300px;
  max-height: 28vh;
  min-height: 300px;
  //   margin-top: 5.7vh;
  //   margin-left: 2vw;
  // border: 1px solid red;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  .qecode {
    width: 150px;
    height: 150px;
  }

  .change-qr {
    width: 180px;
    height: 180px;

    display: flex;
    align-items: center;
    justify-content: center;

    background-size: 100% 100%;

    .change-tips-d {
      width: 150px;
      height: 100px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
    }
    .change-tips {
      color: #19667d;
      font-size: 17px;
      font-family: Source Han Sans CN;
      font-weight: 700;
    }
  }
}

div /deep/ .el-input__prefix {
  width: 28px;
}
div /deep/ .el-input__inner {
  height: 50px !important;
  font-size: 17px;
}

div /deep/ .el-input--small {
  line-height: 50px;
}

div /deep/ .el-input__prefix {
  display: flex;
  align-items: center;
}
div /deep/ .el-dialog__header {
  background-color: #fff !important;
  box-shadow: none;
  color: #b91a1a;
}
div /deep/ .el-dialog__headerbtn:hover {
  background-color: #fff !important;
  box-shadow: none;
  color: #b91a1a !important;
}
div /deep/ .el-dialog__close {
  background-color: #fff !important;
  box-shadow: none;
  color: #b91a1a !important;
}
div /deep/ .el-dialog__close:hover {
  color: #b91a1a !important;
}
div /deep/ .el-dialog__headerbtn:hover {
  color: #b91a1a !important;
}

.box2 {
  color: #487ce4;
  top: -0.5vh;
  right: calc(0.5vh + 54px);
  position: absolute;
  width: 106px;
  height: 28px;
  line-height: 16px;
  padding: 5px 10px;
  border: 1px solid #9898f3;
  box-sizing: border-box;
  font-size: 12px;
  // box-shadow:0 0 2px rgba(0,0,0,.5);
  background-color: #e2e7f7;
}
.box2:before,
.box2:after {
  position: absolute;
  content: "";
  border: 6px solid;
}
.box2:before {
  right: -13px;
  top: 8px;
  border-color: transparent transparent transparent #5f5ff7;
}
.box2:after {
  border-color: transparent transparent transparent #e2e7f7;
  right: -12px;
  top: 8px;
}
.change_login_text {
  color: #555;
  text-decoration: underline;
  cursor: pointer;
}
.agree-pop {
  position: absolute;
  top: 50vh;
  left: 50vw;
  transform: translate(-50%, -50%);
  // border: 1px solid red;
  width: 700px;
  height: 530px;
  background: rgb(245, 245, 245);
  z-index: 999;
  box-shadow: 0 0 30px #555;
  display: flex;
  justify-content: center;
  align-items: center;
}
.agree-pop .agree-bottom {
  cursor: pointer;
  border-radius: 15px;
  width: 15%;
  line-height: 30px;
  background: rgb(218, 216, 216);
  // border: 1px solid seagreen;
  position: absolute;
  bottom: 0px;
  text-align: center;
  margin-bottom: 10px;
}
</style>
<style lang="less">
.persdsd {
  .el-message-box__content {
    text-align: center !important;
  }
  .el-message-box__btns {
    justify-content: center !important;
  }
  .el-overlay {
    background-color: rgb(16 16 16 / 50%) !important;
  }
}
</style>