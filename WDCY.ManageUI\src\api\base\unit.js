import request from '@/utils/request'

export const listUnit = (data) =>
	request({
		url: '/unit',
		method: 'get',
		params: data
	})
export const getUnit = (id) =>
	request({
		url: '/unit/'+id,
		method: 'get',
	})
export const addUnit = (data) =>
	request({
		url: '/unit',
		method: 'post',
		data: data
	})
export const editUnit = (data) =>
	request({
		url: '/unit',
		method: 'put',
		data: data
	})
export const deleteUnit = (id) =>
	request({
		url: '/unit',
		method: 'delete',
		params: {
			id: id
		}
	})
