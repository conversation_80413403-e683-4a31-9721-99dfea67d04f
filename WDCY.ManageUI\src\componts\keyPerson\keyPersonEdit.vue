<template>
  	<canvas style="position: absolute; left: -9999px" ref="canvas" id="canvas" width="640" height="480"></canvas>

	<el-dialog draggable width="50%" v-loading="loading" v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="personModel" label-width="80px">
			<el-tabs v-model="activeName">
				<el-tab-pane label="基础信息" name="first">
			<el-row>
				<el-col :span="18">
					<el-form-item label="姓名" prop="name">
						<el-input v-model="personModel.name" placeholder="姓名"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item v-if="personModel.certificateType !== 'IDCARD'" label="性别" prop="gender">
						<el-select style="width: 100%" v-model="personModel.gender" placeholder="性别">
						<el-option v-for="item in sexList" :key="item.nameEn" :label="item.nameCn" :value="parseInt(item.nameEn)"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item v-if="personModel.certificateType !== 'IDCARD'" label="生日" prop="birthday">
						<el-date-picker value-format="YYYY-MM-DD" v-model="personModel.birthday" type="date" placeholder="请选择生日"/>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="证件类型" prop="certificateType">
						<el-select
						clearable
						style="width: 100%"
						v-model="personModel.certificateType"
						placeholder="选择证件类型"
						>
						<el-option
							v-for="item in certificateTypeTagList"
							:key="item.nameEn"
							:label="item.nameCn"
							:value="item.nameEn"
						></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="证件号" prop="idCard">
						<el-input v-model="personModel.idCard" placeholder="证件号"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="人员类型">
						<template #default="scope">
							<el-select
								@change="keyPersonTagChange"
								v-model="personModel.type"
								multiple
								placeholder="请选择">
								<el-option
									v-for="item in typeList"
									:key="item.id"
									:label="item.name"
									:value="item.id">
								</el-option>
							</el-select>
						</template>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="地址" prop="address">
						<el-input v-model="personModel.address" placeholder="地址"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="状态" prop="status">
						<template #default="scope">
							<el-radio v-model="personModel.status" :label="1">正常</el-radio>
							<el-radio v-model="personModel.status" :label="0">禁用</el-radio>
						</template>
					</el-form-item>
				</el-col>
			</el-row>
				</el-tab-pane>
			<!-- <el-tab-pane label="照片" name="second">
				<el-row>
					<el-col :span="8">
						<el-form-item label-width="0">
							<el-card class="box-card">
								<template #header>
								  <div style="display: flex;justify-content: space-between;">
									<span>照片</span>
									<el-button v-if="personModel.photo == undefined || personModel.photo == null || personModel.photo == ''" class="button" type="text">
										<el-upload :show-file-list="false" :http-request="imgUpload">上传</el-upload>
									</el-button>
									<el-button v-else class="button" type="text" @click="deletePhoto">删除</el-button>
								  </div>
								</template>
								<el-image fit="contain" style="width: 100%; height: 200px" :preview-src-list="[imgServer+personModel.photo]" :src="imgServer+personModel.photo"></el-image>
							  </el-card>
						</el-form-item>
					</el-col>
				</el-row>
			</el-tab-pane> -->

			<el-tab-pane label="照片" name="second">
          <el-row>
            <el-col :span="24">
              <el-form-item label-width="0">
                <el-card class="box-card">
                  <template #header>
                    <div style="display: flex; justify-content: space-between">
                      <span>照片</span>
                      <div>
                        <el-button @click="uploadImg" class="button" type="text">
                          <input style="
                              position: fixed;
                              left: -9999px;
                              display: none;
                            " type="file" accept="image/*" id="imgReader" @change="loadingImg" :value="upload_input"/>裁剪上传
                          <!-- <el-button @click="uploadImg">上传</el-button> -->
                        </el-button>
                        <el-button class="button" type="text">
                          <el-upload :show-file-list="false" :http-request="loadingImg2" accept="image/jpeg,image/jpg,image/png">原图上传</el-upload>
                        </el-button>
                        <el-button @click="callCamera" class="button" type="text">采集</el-button>
                        <!--canvas截取流-->
                        <!--图片展示-->
                        <!--确认-->
                        <el-button class="button" type="text" @click="deletePhoto">删除</el-button>
                      </div>
                    </div>
                  </template>
                  <el-image id="canvas" fit="contain" style="height: 200px; width: 200px"
                    :preview-src-list="[imgServer + personModel.photo]" :src="imgServer + personModel.photo">
                    <template v-if=" personModel.photo == undefined || personModel.photo == null || personModel.photo == '' " #error>
                      <div style="line-height: 200px; text-align: center;background-color: #eee;color: #999;">
                        暂无数据
                      </div>
                    </template>
                  </el-image>
                </el-card>
              </el-form-item>
            </el-col>
          </el-row>

          <el-dialog draggable width="50%" v-loading="loading" v-model="videoDialog.show" destroy-on-close :title="videoDialog.title">
            <div style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              ">
              <video ref="video" width="640" height="480" autoplay></video>
              <el-button size="default" style="width: 120px; margin-top: 15px" type="primary"
                @click="photograph">确认</el-button>
            </div>
          </el-dialog>

          <el-dialog draggable v-model="viewPic" title="裁剪头像">
            <div style="display: flex">
              <img id="cropImg" style="width: 300px; height: 300px" />
            </div>
            <div>
              <div class="previewText">裁剪预览</div>
              <div style="display: flex">
                <div style="height: 100px; width: 100px; position: relative">
                  <div class="previewBox"></div>
                </div>
                <div style="
                    height: 100px;
                    width: 100px;
                    position: relative;
                    border-radius: 50%;
                    overflow: hidden;
                  ">
                  <!-- <div class="previewBoxRound"></div> -->
                </div>
              </div>
              <div style="display: flex; margin-top: 10px">
                <el-button type="primary" @click="GetData">确认</el-button>
              </div>
            </div>
          </el-dialog>
			</el-tab-pane>

			</el-tabs>
		</el-form>
		<el-row justify="end">
			<el-button type="primary" @click="onSubmit">提 交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { keyPersonListEdit,keyPersonListAdd,typeList } from "@/api/keyPerson/keyPerson"
import mitt from "@/utils/mitt";
import { fileUpload } from "@/api/admin/file"
import "cropperjs/dist/cropper.css";
import Cropper from "cropperjs";
import CROPPER from "../../utils/myUtils.js";
import { truncate } from "lodash";
import personPhoto from "@/componts/personPhoto/personPhoto.vue"
export default {
  	components:{ personPhoto },
	props:['statusList','tagList',"certificateTypeTagList", "sexList"],
	data() {
		return {
			loading: false,
			personModel: {},
			communityId:localStorage.getItem("communityId"),
      upload_input: '',
			imgServer: import.meta.env.VITE_BASE_API,
			dialog:{},
      viewPic: false,
			videoDialog: {},
			typeList:[],
			activeName:'first',
			personType:[],
			rules: {
				name: [{
					required: true,
					message: '请输入用户名',
					trigger: 'blur',
				}],
				status: [{
					required: true,
					message: '请选择状态',
					trigger: 'change',
				}],
				certificateType:[{
					required: true,
					message: '请选择证件类型',
					trigger: "change"
				}]
			}
		}
	},
	methods: {
		callCamera() {
      // H5调用电脑摄像头API
      this.videoDialog.show = true;
      this.videoDialog.title = "拍摄头像";
      navigator.mediaDevices
        .getUserMedia({
          video: true,
        })
        .then((success) => {
          // 摄像头开启成功
          console.log(this.$refs);
          console.log(this.$refs['video']);
          this.$refs["video"].srcObject = success;
          // 实时拍照效果
          this.$refs["video"].play();
        })
        .catch((error) => {
          console.error("摄像头开启失败，请检查摄像头是否可用！");
          this.$message.error("摄像头开启失败，请检查摄像头是否可用！");
        });
    },
    //拍照

    photograph() {
      this.videoDialog.show = false;
      let ctx = this.$refs["canvas"].getContext("2d");
      // 把当前视频帧内容渲染到canvas上
      ctx.drawImage(this.$refs["video"], 0, 0, 640, 480);
      // 转base64格式、图片格式转换、图片质量压缩
      let imgBase64 = this.$refs["canvas"].toDataURL("image/jpeg", 1.0);

      document.querySelector("#canvas").src = imgBase64;

      const image = document.getElementById("canvas");
      image.toBlob((blob) => {
        //然后调用浏览器原生的toBlob方法将canvas数据转换成blob数据

        //之后就可以愉快的将blob数据发送至后端啦，可根据自己情况进行发送，我这里用的是axios
        const form = new FormData();
        // 第三个参数为文件名，可选填.
        form.append("file", blob, "example.jpg");
        form.append("modulesName", 'base');
        form.append("functionName", 'keyperson');
        form.append("communityId", localStorage.getItem('communityId'));
        console.log(form);
        fileUpload(form).then((res) => {
          this.personModel.photo = res.data.result.url;
          if (res.data.code == 0) {
            this.$message.success("上传成功");
            this.closeCamera()
          }
        });
      }, "image/jpeg", 0.95);
    },
    //关闭摄像头
    closeCamera() {
      console.log('进入', '··················');
      if (!this.$refs["video"].srcObject) {
        this.dialogCamera = false;
        return;
      }
      let stream = this.$refs["video"].srcObject;
      let tracks = stream.getTracks();
      tracks.forEach((track) => {
        track.stop();
      });
      this.$refs["video"].srcObject = null;
    },
    loadingImg(eve) {
      this.upload_input = eve.target.files[0]
      this.viewPic = truncate;
      //读取上传文件
      let reader = new FileReader();
      if (event.target.files[0]) {
        //readAsDataURL方法可以将File对象转化为data:URL格式的字符串（base64编码）
        reader.readAsDataURL(eve.target.files[0]);
        reader.onload = (e) => {
          let dataURL = reader.result;
          //将img的src改为刚上传的文件的转换格式
          console.log(document.getElementById("#cropImg"));
          document.querySelector("#cropImg").src = dataURL;

          const image = document.getElementById("cropImg");
          //创建cropper实例-----------------------------------------
          let CROPPER = new Cropper(image, {
            // aspectRatio: 16 / 16,
            initialAspectRatio: 2 / 3,
            viewMode: 1,
            autoCropArea: 0.95,
            minCanvasWidth: 100,
            minCanvasHeight: 100,
            // minContainerWidth:500,
            // minContainerHeight:500,
            dragMode: "move",
            preview: [
              document.querySelector(".previewBox"),
              // document.querySelector(".previewBoxRound"),
            ],
          });
          this.CROPPER = CROPPER;
        };
      }
      this.upload_input = ''
    },
    loadingImg2(files) {
      let form = new FormData();
      form.append("file", files.file);
      form.append("modulesName", 'base');
      form.append("functionName", 'keyperson');
      form.append("communityId", localStorage.getItem('communityId'));
      fileUpload(form).then((res) => {
        this.personModel.photo = res.data.result.url;
        if (res.data.code == 0) {
          this.$message.success("上传成功");
        }
      });
    },
    uploadImg() {
      document.querySelector("#imgReader").click();
      if (this.CROPPER) {
        this.CROPPER.destroy();
      }
    },
    uploadImg2() {
      document.querySelector("#imgReader2").click();
    },
    GetData() {
      this.viewPic = false;
      //getCroppedCanvas方法可以将裁剪区域的数据转换成canvas数据
      this.CROPPER.getCroppedCanvas({
        maxWidth: 480,
        maxHeight: 480,
        fillColor: "#fff",
        imageSmoothingEnabled: true,
        imageSmoothingQuality: "high",
      }).toBlob((blob) => {
        //然后调用浏览器原生的toBlob方法将canvas数据转换成blob数据

        //之后就可以愉快的将blob数据发送至后端啦，可根据自己情况进行发送，我这里用的是axios
        const form = new FormData();
        // 第三个参数为文件名，可选填.
        form.append("file", blob, "example.jpg");
        form.append("modulesName", 'base');
        form.append("functionName", 'keyperson');
        form.append("communityId", localStorage.getItem('communityId'));
        fileUpload(form).then((res) => {
          this.personModel.photo = res.data.result.url;
          if (res.data.code == 0) {
            this.$message.success("上传成功");
          }
        });
      }, "image/jpeg", 0.95);
    },
    deletePhoto() {
      this.personModel.photo = null;
    },
		keyPersonTagChange(e){
			console.log(e)
		},
		onSubmit(){
				this.$refs['form'].validate((valid) =>{
				if(valid){
					if (this.personModel.birthday) {
						this.personModel.birthday = this.personModel.birthday.substr(0,10)
					}
					if(this.personModel.tags){
						this.personModel.tags = JSON.stringify(this.personModel.tags)
					}
					this.personModel.communityId = this.communityId
					if(this.personModel.id == 0){
						keyPersonListAdd(this.personModel)
						.then(res =>{
							this.$message.success(res.data.msg)
							this.$emit("search")
							this.dialog.show = false
						})
					}else{
						keyPersonListEdit(this.personModel)
						.then(res =>{
							this.$message.success(res.data.msg)
							this.$emit("search")
							this.dialog.show = false
						})
					}
				}
		})}
	},
	mounted(){
		this.$nextTick(function() {
			mitt.on('openKeyPersonEdit', (person) => {
				if(person.tags){
					person.tags	= JSON.parse(person.tags)
				}
				let param = {
					communityId:this.communityId,
					isPerson:true,
					isRuleRecognize:false
				}
				typeList(param).then(res =>{
					this.typeList = res.data.result
				})
				this.personModel = person
				this.dialog.show = true
				this.dialog.title = "修改"
			})
			mitt.on('openKeyPersonAdd', () => {
				this.personModel = {
					id:0,
					status:1,
				}
				let params = {
					communityId:this.communityId,
					isPerson:true,
					isRuleRecognize:false
				}
				typeList(params).then(res =>{
					this.typeList = res.data.result
				})
				this.dialog.show = true
        		this.personModel.gender = 0;
				this.dialog.title = "添加"
				this.personModel.certificateType = "IDCARD";

			})
		})
	}
}
</script>
<style scoped>
	.upload{
		width: 200px;
		height: 200px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px dashed var(--el-color-primary);
		flex-direction: column;
		border-radius: 5px;
	}
	.box-card {
    width: 100%;
}
.previewBox {
    background-color: #eee;
    box-shadow: 0 0 5px #adadad;
    width: 100px;
    height: 100px;
    position: absolute;
    left: 50%;
    /* top: 50%; */
    margin-left: -50px;
    margin-top: -50px;
    margin-right: 10px;
    margin-top: 10px;
    overflow: hidden;
}

#cropImg {
  height: 300px;
  width: 300px;
  display: block;
  overflow: hidden;
  box-shadow: 0 0 5px #adadad;
}

.previewText {
  margin-top: 10px;
}
</style>
