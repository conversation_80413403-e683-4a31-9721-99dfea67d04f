<template>
  <governance-object-edit
    @searchObject="searchObject"
    @searchObject1="searchObject1"
  ></governance-object-edit>
  <governance-responsibility-edit
    @searchResponsibility="searchResponsibility"
    @searchResponsibility1="searchResponsibility1"
  ></governance-responsibility-edit>
  <el-dialog
    draggable
    width="60%"
    destroy-on-close
    v-loading="loading"
    v-model="dialog.show"
    :title="dialog.title"
    :close-on-click-modal="false"
  >
    <el-scrollbar height="542px">
      <el-form
        :rules="rules"
        ref="form"
        :model="governanceModel.governanceEvent"
        style="width: calc(100% - 10px)"
        label-width="100px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="治理类型" prop="governanceType">
              <el-select
                style="width: 100%"
                v-model="governanceModel.governanceEvent.governanceType"
                placeholder="治理类型"
                filterable
                clearable
              >
                <el-option
                  style="with: 20px"
                  v-for="item in typeList"
                  :key="item.nameEn"
                  :label="item.nameCn"
                  :value="item.nameEn"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="事件等级" prop="eventLevelValue">
              <el-select
                style="width: 100%"
                v-model="governanceModel.governanceEvent.eventLevelValue"
                placeholder="事件等级"
                clearable
              >
                <el-option
                  style="width: 100%"
                  v-for="item in levelList"
                  :key="item.nameEn"
                  :label="item.nameCn"
                  :value="parseInt(item.nameEn)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="事件状态" prop="status">
              <el-select
                style="width: 100%"
                v-model="governanceModel.governanceEvent.status"
                placeholder="事件状态"
                clearable
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.nameEn"
                  :label="item.nameCn"
                  :value="parseInt(item.nameEn)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="发生时间" prop="eventTime">
              <el-date-picker
                v-model="governanceModel.governanceEvent.eventTime"
                style="width: 100%"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="选择日期"
                clearable
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="事件位置" prop="eventLocation">
              <el-input
                v-model="governanceModel.governanceEvent.eventLocation"
                show-overflow-tooltip
                placeholder="事件位置"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row> </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="事件说明" prop="eventNote">
              <el-input
                v-model="governanceModel.governanceEvent.eventNote"
                maxlength="200"
                placeholder="请简单说明治理事件"
                show-word-limit
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-form-item label="事件对象" prop="governanceObjectList">
            <el-col :span="24">
              <el-table
                stripe
                :data="governanceModel.governanceObjectList"
                border
                style="width: 100%"
              >
                <el-table-column
                  prop="communityName"
                  show-overflow-tooltip
                  align="center"
                  label="小区"
                />
                <el-table-column
                  prop="buildingNumber"
                  show-overflow-tooltip
                  align="center"
                  label="楼栋"
                />
                <el-table-column
                  prop="unitNumber"
                  show-overflow-tooltip
                  align="center"
                  label="单元"
                />
                <el-table-column
                  prop="roomNumber"
                  show-overflow-tooltip
                  align="center"
                  label="房间"
                />
                <el-table-column
                  prop="name"
                  show-overflow-tooltip
                  align="center"
                  label="姓名"
                />
                <el-table-column
                  prop="idCard"
                  show-overflow-tooltip
                  align="center"
                  label="证件号"
                />
                <el-table-column
                  prop="phone"
                  show-overflow-tooltip
                  align="center"
                  label="电话"
                />
                <el-table-column align="center" label="操作">
                  <template #default="scope">
                    <el-button
                      type="text"
                      size="default"
                      @click="toEdit(scope.row, scope.$index)"
                      v-if="hasPerm('base:governanceEvent:edit')"
                      >编辑</el-button
                    >
                    <el-button
                      type="text"
                      size="default"
                      @click="remove(scope.row)"
                      v-if="hasPerm('base:governanceEvent:delete')"
                      >移除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col
              v-if="hasPerm('base:governanceEvent:add')"
              :span="24"
              style="
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <el-button
                style="font-size: 25px; width: 100%; border-radius: 0"
                size="default"
                @click="add"
              >
                <el-icon :size="20">
                  <plus></plus>
                </el-icon>
              </el-button>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row :gutter="20">
          <el-form-item label="经办人员" prop="governanceResponsibilityList">
            <el-col :span="24">
              <el-table
                stripe
                :data="governanceModel.governanceResponsibilityList"
                border
                style="width: 100%"
              >
                <el-table-column
                  prop="name"
                  show-overflow-tooltip
                  align="center"
                  label="姓名"
                />
                <el-table-column
                  prop="certificateType"
                  align="center"
                  :formatter="formatCertificateType"
                  label="证件类型"
                />
                <el-table-column
                  prop="idCard"
                  show-overflow-tooltip
                  align="center"
                  label="证件号"
                />
                <el-table-column
                  prop="phone"
                  show-overflow-tooltip
                  align="center"
                  label="电话"
                />
                <el-table-column
                  prop="dutyNote"
                  show-overflow-tooltip
                  align="center"
                  label="职责描述"
                />
                <el-table-column align="center" label="操作">
                  <template #default="scope">
                    <el-button
                      type="text"
                      size="default"
                      @click="toEditResponsibility(scope.row, scope.$index)"
                      v-if="hasPerm('base:governanceEvent:edit')"
                      >编辑</el-button
                    >
                    <el-button
                      type="text"
                      size="default"
                      @click="removeResponsibility(scope.row)"
                      v-if="hasPerm('base:governanceEvent:delete')"
                      >移除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col
              v-if="hasPerm('base:governanceEvent:add')"
              :span="24"
              style="
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <el-button
                style="font-size: 25px; width: 100%; border-radius: 0"
                size="default"
                @click="addResponsibility"
              >
                <el-icon :size="20">
                  <plus></plus>
                </el-icon>
              </el-button>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="相关图片">
              <el-scrollbar
                class="pic--video"
                style="height: 198px; width: 100%"
              >
                <el-upload
                  accept="image/jpeg,image/jpg,image/png"
                  multiple
                  v-model:file-list="fileList"
                  action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                  list-type="picture-card"
                  :on-preview="handlePictureCardPreview"
                  :on-remove="deletePic"
                  :http-request="loadingImg"
                  :before-upload="beforeUploadImg"
                >
                  <el-icon style="display: flex; flex-wrap: nowrap"
                    ><Plus
                  /></el-icon>
                </el-upload>
                <div style="display: flex; position: relative; top: -10px">
                  <div
                    v-for="(item, idx) in fileList"
                    :key="idx"
                    style="
                      width: 146px;
                      display: flex;
                      flex-shrink: 0;
                      margin-right: 10px;
                    "
                  >
                    <el-input
                      v-model="item.name"
                      style="width: 100%"
                      placeholder="请输入文件名"
                    ></el-input>
                  </div>
                </div>
              </el-scrollbar>

              <el-dialog
                v-model="dialogVisible"
                :title="dialogVisibleText"
                class="picView"
              >
                <img
                  w-full
                  style="width: 100%"
                  :src="dialogImageUrl"
                  alt="Preview Image"
                />
              </el-dialog>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="相关视频">
              <el-scrollbar
                class="pic--video"
                style="height: 198px; width: 100%"
              >
                <el-upload
                  multiple
                  accept="video/*"
                  v-model:file-list="fileList1"
                  :action="imgServer + '/file/upload'"
                  list-type="picture-card"
                  :on-preview="handleVideoCardPreview"
                  :on-remove="deleteVideo"
                  :http-request="loadingVideo"
                  :before-upload="beforeUploadVideo"
                  :on-success="getVideoCover"
                >
                  <el-icon style="display: flex; flex-wrap: nowrap"
                    ><Plus
                  /></el-icon>
                </el-upload>
                <div style="display: flex; position: relative; top: -10px">
                  <div
                    v-for="(item, idx) in fileList1"
                    :key="idx"
                    style="
                      width: 146px;
                      display: flex;
                      flex-shrink: 0;
                      margin-right: 10px;
                    "
                  >
                    <el-input
                      v-model="item.name"
                      style="width: 100%"
                      placeholder="请输入文件名"
                    ></el-input>
                  </div>
                </div>
              </el-scrollbar>
              <el-dialog
                v-model="dialogVisibleVideo"
                :title="dialogVisibleVideoText"
                class="picView videoView"
              >
                <video
                  :src="videoUrl"
                  style="width: 100%; max-height: 520px"
                  controls
                ></video>
              </el-dialog>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="相关文件">
              <el-scrollbar style="height: 198px">
                <el-upload
                  multiple
                  accept=".pdf, .doc, .docx, .xls, .xlsx"
                  v-model:file-list="fileList2"
                  :action="imgServer + '/file/upload'"
                  :on-preview="handleOtherCardPreview"
                  :on-remove="deleteOther"
                  :http-request="loadingOther"
                  :before-upload="beforeUploadOther"
                >
                  <el-button size="nornaml" type="primary">点击上传</el-button>
                </el-upload>
              </el-scrollbar>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        :disabled="isDisabled"
        >提 交</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script setup>
import { LocationFilled } from "@element-plus/icons-vue";
</script>

<script>
import {
  editGovernance,
  addGovernance,
} from "@/api/governance/governanceEvent";
import { listGovernanceObject } from "@/api/governance/governanceObject";
import { getUserGroup } from "@/api/admin/userGroup";
import { listGovernanceResponsibility } from "@/api/governance/governanceResponsibility";
import mitt from "@/utils/mitt";
import { openMap } from "@/utils/myUtils";
import { listDictByNameEn } from "@/api/admin/dict";
import JsonEditorVue from "json-editor-vue3";
import { fileUpload, fileRemove } from "@/api/admin/file";
import governanceObjectEdit from "@/componts/governance/governanceObjectEdit.vue";
import governanceResponsibilityEdit from "@/componts/governance/governanceResponsibilityEdit.vue";

export default {
  components: {
    JsonEditorVue,
    governanceObjectEdit,
    governanceResponsibilityEdit,
  },
  props: ["statusList", "typeList", "levelList"],
  data() {
    return {
      group: {},
      imgServer: import.meta.env.VITE_BASE_API,
      searchObjectModel: {
        governanceEventId: localStorage.getItem("id"),
      },
      searchResponsibilityModel: {
        governanceEventId: localStorage.getItem("id"),
      },
      loading: false,
      isDisabled: false,
      jsonVal: {},
      governanceModel: {},
      governanceEvent: {},
      certificateTypeList: [],
      dialog: {},
      fileList: [],
      fileList1: [],
      fileList2: [],
      dialogVisible: false,
      dialogVisibleText: "",
      dialogVisibleVideo: false,
      dialogVisibleVideoText: "",
      dialogVisibleOther: false,
      videoUrl: "",
      viewUrl: "",
      otherUrl: "",
      governanceObjectList: [],
      delGovernanceObjectList: [],
      governanceResponsibilityList: [],
      delGovernanceResponsibilityList: [],
      governanceEventId: "",
      uploadImgType: [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".JPG",
        ".JPEG",
        ".PNG",
        ".GIF",
      ],
      uploadVideoType: [".mp4"],
      uploadOtherType: [".pdf", ".docx", ".xlsx", ".xls"],
      viewStatus: true,
      rules: {
        governanceType: [
          {
            required: true,
            message: "请选择治理类型",
            trigger: "change",
          },
        ],
        eventTime: [
          {
            required: true,
            message: "请选择发生时间",
            trigger: "change",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择事件状态",
            trigger: "change",
          },
        ],
        eventLevelValue: [
          {
            required: true,
            message: "请选择事件等级",
            trigger: "change",
          },
        ],
        eventLocation: [
          {
            required: true,
            message: "请输入事件位置",
            trigger: "change",
          },
        ],
        eventNote: [
          {
            required: true,
            message: "请输入事件说明",
            trigger: "change",
          },
        ],
      },
    };
  },
  methods: {
    changeJson(json) {
      this.jsonVal = json;
    },
    selectPoint() {
      //未调用
      openMap(
        mitt,
        true,
        [
          this.governanceModel.governanceEvent.lng,
          this.governanceModel.governanceEvent.lat,
        ],
        this.governanceModel.governanceEvent.eventLocation
      );
    },
    handlePictureCardPreview(uploadFile) {
      this.dialogVisibleText = "相关图片--" + uploadFile.name.split(".")[0];
      this.dialogImageUrl = uploadFile.url;
      this.dialogVisible = true;
    },
    handleOtherCardPreview(uploadFile) {
      var uploadFile1 = {};
      this.governanceModel.governanceEvent.otherList.some((item) => {
        if (item.name == uploadFile.name) {
          uploadFile1 = item;
        }
      });
      window.open(this.imgServer + uploadFile1.url);
    },
    handleVideoCardPreview(uploadFile) {
      this.dialogVisibleVideoText =
        "相关视频--" + uploadFile.name.split(".")[0];
      this.videoUrl = [];
      this.videoUrl = uploadFile.videoUrl;
      this.dialogVisibleVideo = true;
    },
    deletePic(uploadFile, uploadFiles) {
      fileRemove({ fileUrl: uploadFile.url.replace(this.imgServer, "") });
      this.governanceModel.governanceEvent.photoList = uploadFiles;
    },
    deleteVideo(uploadFile, uploadFiles) {
      fileRemove({ fileUrl: uploadFile.videoUrl.replace(this.imgServer, "") });
    },
    deleteOther(uploadFile, uploadFiles) {
      var uploadFile1 = {};
      this.governanceModel.governanceEvent.otherList.some((item) => {
        if (item.fileName == uploadFile.name) {
          uploadFile1 = item;
        }
      });
      fileRemove({ fileUrl: uploadFile1.url.replace(this.imgServer, "") });
      this.governanceModel.governanceEvent.otherList = uploadFiles;
    },
    loadingImg(files) {
      let form = new FormData();
      form.append("needCompress", false);
      form.append("file", files.file);
      form.append("modulesName", "base");
      form.append("functionName", "governanceEvent");
      form.append("communityId", localStorage.getItem("communityId"));
      fileUpload(form).then((res) => {
        this.governanceModel.governanceEvent.photoList.push(res.data.result);
        if (res.data.code == 0) {
        }
      });
    },

    loadingOther(files) {
      let form = new FormData();
      form.append("file", files.file);
      form.append("modulesName", "base");
      form.append("functionName", "governanceEvent");
      form.append("communityId", localStorage.getItem("communityId"));
      fileUpload(form).then((res) => {
        const data = res.data.result;
        data.fileName = files.file.name;
        this.governanceModel.governanceEvent.otherList.push(data);
        if (res.data.code == 0) {
        }
      });
    },
    beforeUploadImg(file) {
      const type = file.name.substring(file.name.lastIndexOf(".")); // 获取文件后缀，取文件格式
      const isLt10M = file.size / 1024 / 1024 < 25;
      if (!this.uploadImgType.includes(type)) {
        this.$message({
          type: "error",
          message: "只支持jpg,jpeg,png,gif,JPG,JPEG,PNG,GIF文件格式！",
        });
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: "上传文件大小不能超过 25MB!",
          type: "warning",
        });
        return false;
      }
    },
    beforeUploadVideo(file) {
      const type = file.name.substring(file.name.lastIndexOf(".")); // 获取文件后缀，取文件格式
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!this.uploadVideoType.includes(type)) {
        this.$message({ type: "error", message: "只支持mp4文件格式！" });
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: "上传文件大小不能超过 100MB!",
          type: "warning",
        });
        return false;
      }
    },
    beforeUploadOther(file) {
      const type = file.name.substring(file.name.lastIndexOf(".")); // 获取文件后缀，取文件格式
      const isLt10M = file.size / 1024 / 1024 < 25;
      if (!this.uploadOtherType.includes(type)) {
        this.$message({
          type: "error",
          message: "只支持pdf,docx,xlsx,xls文件格式！",
        });
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: "上传文件大小不能超过 25MB!",
          type: "warning",
        });
        return false;
      }
    },
    loadingVideo(files) {
      let form = new FormData();
      form.append("file", files.file);
      form.append("modulesName", "base");
      form.append("functionName", "governanceEvent");
      form.append("communityId", localStorage.getItem("communityId"));
      fileUpload(form).then((res) => {
        this.governanceModel.governanceEvent.videoList.push(res.data.result);
      });
      files.onSuccess((res) => {});
      this.setFileList(this.fileList1);
    },
    getVideoCover(file) {
      const video = document.createElement("video"); // 也可以自己创建video
      video.src = file.url; // url地址 url跟 视频流是一样的

      var canvas = document.createElement("canvas"); // 获取 canvas 对象
      const ctx = canvas.getContext("2d"); // 绘制2d
      video.crossOrigin = "anonymous"; // 解决跨域问题，也就是提示污染资源无法转换视频
      video.currentTime = 1; // 第一帧

      video.oncanplay = () => {
        canvas.width = video.clientWidth ? video.clientWidth : 320; // 获取视频宽度
        canvas.height = video.clientHeight ? video.clientHeight : 320; //获取视频高度
        // 利用canvas对象方法绘图
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        // 转换成base64形式
        let videoFirstimgsrc = canvas.toDataURL("image/png"); // 截取后的视频封面
        let videoUrl = file.url;
        file.url = videoFirstimgsrc; // file的url储存封面图片
        file.videoUrl = videoUrl; // file的videoUrl储存视频

        video.remove();
        canvas.remove();
      };
      return file;
    },
    setFileList(_fileList) {
      for (let obj of _fileList) {
        //视频附件，获取第一帧画面作为 封面展示
        this.getVideoCover(obj);
      }
      this.fileList1 = _fileList; //fileList 为 Element file-list 参数值
    },
    onSubmit() {
      if (this.isDisabled) {
        return;
      }
      this.isDisabled = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 判断是否有人员
          if (this.governanceModel.governanceObjectList.length == 0) {
            this.$message.error("请先添加事件对象");
            return;
          }
          if (this.governanceModel.governanceResponsibilityList.length == 0) {
            this.$message.error("请先添加经办人员");
            return;
          }
          const list = [];
          // 格式化处理图片
          for (const key in this.fileList) {
            if (this.fileList[key].url.includes(this.imgServer)) {
              list.push({
                name: this.fileList[key].name.split(".")[0],
                url: this.fileList[key].url.replace(this.imgServer, ""),
              });
            } else {
              list.push({
                name: this.fileList[key].name.split(".")[0],
                url: this.governanceModel.governanceEvent.photoList[key].url,
              });
            }
          }

          const list1 = [];
          // 格式化处理视频
          for (const key in this.fileList1) {
            if (this.fileList1[key].url.includes(this.imgServer)) {
              list1.push({
                name: this.fileList1[key].name.split(".")[0],
                url: this.governanceModel.governanceEvent.videoList[
                  key
                ].url.replace(this.imgServer, ""),
              });
            } else {
              list1.push({
                name: this.fileList1[key].name,
                url: this.governanceModel.governanceEvent.videoList[key].url,
              });
            }
          }

          const list2 = [];
          // 格式化处理文件
          for (const key in this.fileList2) {
            if (
              this.fileList2[key].url &&
              this.fileList2[key].url.includes(this.imgServer)
            ) {
              list2.push({
                name: this.fileList2[key].name.split(".")[0],
                url: this.governanceModel.governanceEvent.otherList[
                  key
                ].url.replace(this.imgServer, ""),
              });
            } else {
              list2.push({
                name: this.fileList2[key].name,
                url: this.governanceModel.governanceEvent.otherList[key].url,
              });
            }
          }

          this.governanceModel.governanceEvent.photoList = list;
          this.governanceModel.governanceEvent.videoList = list1;
          this.governanceModel.governanceEvent.otherList = list2;
          getUserGroup(localStorage.getItem("groupId"))
            .then((re) => {
              this.governanceModel.governanceEvent.groupId =
                re.data.result.parentId;
              if (this.governanceModel.governanceEvent.id == 0) {
                addGovernance(this.governanceModel).then((res) => {
                  this.$message.success(res.data.msg);
                  this.$emit("search");
                  this.dialog.show = false;
                });
              } else {
                editGovernance(this.governanceModel).then((res) => {
                  this.$message.success(res.data.msg);
                  this.$emit("search");
                  this.dialog.show = false;
                });
              }
            })
            .catch((err) => {});
        }
      });
      setTimeout(() => {
        this.isDisabled = false; // 200ms后再次启用按钮（根据需求调整时间）
      }, 200);
    },
    search() {
      this.$emit("search");
    },
    searchObject(data) {
      this.governanceModel.governanceObjectList.push(data);
    },
    searchObject1(data) {
      this.governanceModel.governanceObjectList[data.tableIndex] = data.data;
    },
    searchResponsibility(data) {
      let checkItem = this.governanceModel.governanceResponsibilityList.find(
        (item) => {
          console.log(item.idCard, data.idCard);
          return item.idCard == data.idCard;
        }
      );
      if (checkItem) {
        this.$message.error("该治理人员已添加");
      } else {
        this.governanceModel.governanceResponsibilityList.push(data);
      }
    },
    searchResponsibility1(data) {
      let checkItem = this.governanceModel.governanceResponsibilityList.find(
        (item) => {
          console.log(item.idCard, data);
          if (
            item.idCard == data.data.idCard &&
            item.dutyNote == data.data.dutyNote
          ) {
            return item;
          }
        }
      );
      if (checkItem) {
        this.$message.error("该治理人员已添加");
      } else {
        this.governanceModel.governanceResponsibilityList[data.tableIndex] =
          data.data;
        // this.governanceModel.governanceResponsibilityList.push(data);
      }
    },
    remove(row) {
      this.$confirm("删除事件对象, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log(this.governanceModel.delGovernanceObjectList);
          this.governanceModel.delGovernanceObjectList.push(row);
          this.governanceModel.governanceObjectList.splice(
            this.governanceModel.governanceObjectList.findIndex(
              (item) => item === row
            ),
            1
          );
        })
        .catch(() => {});
    },
    toEdit(row, index) {
      const data = {
        row,
        index,
      };
      mitt.emit("openGovernanceObjectEdit", data);
    },
    add() {
      mitt.emit("openGovernanceObjectAdd", this.governanceEventId);
    },
    removeResponsibility(row) {
      this.$confirm("删除经办人员, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.governanceModel.delGovernanceResponsibilityList.push(row);
          this.governanceModel.governanceResponsibilityList.splice(
            this.governanceModel.governanceResponsibilityList.findIndex(
              (item) => item === row
            ),
            1
          );
        })
        .catch(() => {});
    },
    toEditResponsibility(row, index) {
      const data = {
        row,
        index,
        type: "event",
      };
      mitt.emit("openGovernanceResponsibilityEdit", data);
    },
    addResponsibility() {
      const params = {
        governanceEventId: this.governanceEventId,
        type: "event",
      };
      mitt.emit("openGovernanceResponsibilityAdd", params);
    },
    formatCertificateType(row, column, cellValue, index) {
      let result = "";
      for (let item of this.certificateTypeList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
        }
      }
      return result;
    },
    async init() {
      try {
        let governanceObject = await listGovernanceObject(
          this.searchObjectModel
        );
        this.governanceModel.governanceObjectList =
          governanceObject.data.result.list;
        let governanceResponsibility = await listGovernanceResponsibility(
          this.searchResponsibilityModel
        );
        this.governanceModel.governanceResponsibilityList =
          governanceResponsibility.data.result.list;
        let certificate_res = await listDictByNameEn("certificate_type");
        this.certificateTypeList = certificate_res.data.result;
      } catch (err) {}
    },
  },

  watch: {
    "governanceModel.governanceEvent.eventNote"(newVal, oldVal) {
      if (newVal && newVal.length > 200) {
        this.governanceModel.governanceEvent.eventNote = String(newVal).slice(
          0,
          200
        );
      }
    },
  },
  mounted() {
    this.jsonVal = {};
    this.$nextTick(function () {
      mitt.on("openGovernanceEdit", (governanceEvent) => {
        delete governanceEvent["governanceObjectList"];
        delete governanceEvent["governanceResponsibilityList"];
        this.searchObjectModel.governanceEventId = governanceEvent.id;
        this.searchResponsibilityModel.governanceEventId = governanceEvent.id;
        this.init();
        this.governanceModel.governanceEvent = governanceEvent;
        this.governanceEventId = governanceEvent.id;
        this.dialog.show = true;
        this.governanceModel.governanceEvent.videoList = [];
        this.governanceModel.governanceEvent.photoList = [];
        this.governanceModel.governanceEvent.otherList = [];
        this.governanceModel.delGovernanceObjectList = [];
        this.governanceModel.delGovernanceResponsibilityList = [];
        this.dialog.title = "修改治理事件";

        let photoList = JSON.parse(
          this.governanceModel.governanceEvent.extraData
        ).photoList;
        let videoList = JSON.parse(
          this.governanceModel.governanceEvent.extraData
        ).videoList;
        let otherList = JSON.parse(
          this.governanceModel.governanceEvent.extraData
        ).otherList;
        //图片
        if (!photoList) {
          photoList = [];
        } else {
          this.fileList = [];
          for (const item of photoList) {
            if (item.url.includes(this.imgServer)) {
              this.fileList.push({ name: item.name, url: item.url });
            } else {
              this.fileList.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
            }
          }
        }

        //视频
        if (!videoList) {
          videoList = [];
        } else {
          this.fileList1 = [];
          for (const item of videoList) {
            if (item.url.includes(this.imgServer)) {
              this.fileList1.push({ name: item.name, url: item.url });
            } else {
              this.fileList1.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
            }
          }
        }

        //文件
        if (!otherList) {
          otherList = [];
        } else {
          this.fileList2 = [];
          for (const item of otherList) {
            if (item.url.includes(this.imgServer)) {
              this.fileList2.push({ name: item.name, url: item.url });
            } else {
              this.fileList2.push({
                name: item.name,
                url: this.imgServer + "" + item.url,
              });
            }
          }
        }

        this.governanceModel.governanceEvent.videoList = videoList;
        this.governanceModel.governanceEvent.photoList = photoList;
        this.governanceModel.governanceEvent.otherList = otherList;
        this.setFileList(this.fileList1);
      });
      mitt.on("openGovernanceAdd", () => {
        this.governanceModel.delGovernanceResponsibilityList = [];
        this.governanceModel.delGovernanceObjectList = [];
        this.governanceModel.governanceResponsibilityList = [];
        this.governanceModel.governanceObjectList = [];
        (this.fileList = []),
          (this.fileList1 = []),
          (this.fileList2 = []),
          (this.governanceModel.governanceEvent = {
            id: 0,
            status: 0,
            videoList: [],
            photoList: [],
            otherList: [],
          });
        this.governanceEventId = "";
        this.dialog.show = true;
        this.dialog.title = "添加治理事件";
      });
      mitt.on("setPointValue", (e) => {
        this.governanceModel.governanceEvent.lng = e[0];
        this.governanceModel.governanceEvent.lat = e[1];
      });
      mitt.on("setAddress", (e) => {
        this.governanceModel.governanceEvent.eventLocation =
          e.regeocode.formattedAddress;
      });
    });
  },
  created() {
    mitt.off("openMap");
  },
};
</script>

<style scoped lang="less">
// div /deep/.picView .el-dialog__header {
//   background-color: #fff;
//   box-shadow: none;
// }
// div /deep/ .picView .el-dialog__close {
//   color: #ccc;
// }

.pic--video div /deep/ .el-upload-list {
  display: flex;
  width: 100%;
  flex-wrap: nowrap;
  > li,
  div {
    display: flex;
    flex-shrink: 0;
  }
}
</style>
