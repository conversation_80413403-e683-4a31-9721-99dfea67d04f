<template>
  <governance-edit
    :statusList="statusList"
    :typeList="typeList"
    :levelList="levelList"
    @search="search"
  ></governance-edit>
  <governance-view
    :statusList="statusList"
    :typeList="typeList"
    :levelList="levelList"
    @search="search"
  ></governance-view>
  <governance-log-edit></governance-log-edit>
  <el-row :gutter="20">
    <el-col :span="3" style="display: flex">
      <el-select
        clearable
        style="width: 100%"
        collapse-tags
        multiple
        v-model="typeIds"
        placeholder="治理类型"
      >
        <el-option
          v-for="item in typeList"
          :key="item.nameEn"
          :label="item.nameCn"
          :value="item.nameEn"
        ></el-option>
      </el-select>
    </el-col>
    <el-col :span="3" style="display: flex; width: 500px">
      <el-input
        v-model="searchModel.keyword"
        @keydown.enter="search"
        placeholder="事件说明"
        clearable
      />
    </el-col>
    <el-col :span="2" style="display: flex">
      <el-select
        clearable
        style="margin-right: 10px"
        v-model="searchModel.status"
        class="m-2"
        placeholder="状态"
      >
        <el-option
          v-for="(item, index) in statusList"
          :key="index"
          :label="item.nameCn"
          :value="item.nameEn"
        />
      </el-select>
    </el-col>
    <el-col :span="6" style="display: flex">
      <el-date-picker
        v-model="searchModel.eventStartTime"
        type="date"
        placeholder="选择开始日期"
        value-format="YYYY-MM-DD"
        :size="size"
        style="margin-right: 10px"
        clearable
      />
      <el-date-picker
        style="margin-right: 10px"
        v-model="searchModel.eventEndTime"
        type="date"
        placeholder="选择结束日期"
        value-format="YYYY-MM-DD"
        :size="size"
        clearable
      />
    </el-col>
    <el-col :span="4">
      <el-button type="primary" @click="search">搜 索</el-button>
    </el-col>
    <el-col :span="4" :push="2">
      <el-button
        style="float: right"
        :disabled="ids.length == 0"
        type="primary"
        @click="batchDelete"
        >批量删除</el-button
      >
      <el-button
        style="float: right; margin-right: 10px"
        type="primary"
        @click="add"
        v-if="hasPerm('base:governanceEvent:add')"
        >添 加</el-button
      >
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-table
        row-key="id"
        stripe
        :data="governanceList"
        border
        ref="multipleTable"
        height="calc(100vh - 300px)"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          :reserve-selection="true"
          type="selection"
          align="center"
          width="55"
        />
        <el-table-column
          prop="governanceType"
          :formatter="formatType"
          align="left"
          label="事件类型"
        >
        <template #default="scope">
						<div :style="{color:getDictCss(typeList, scope.row.governanceType)}">{{ formatDict(typeList, scope.row.governanceType) }}</div>
					</template>
      </el-table-column>
        <el-table-column prop="eventNote" align="left" label="事件说明" />
        <el-table-column prop="eventLocation" align="left" label="事件位置" />
        <el-table-column
          width="100"
          :style="getDictCss()"
          prop="eventLevelValue"
          :formatter="formatLevel"
          align="center"
          label="事件等级"
        >
        <template #default="scope">
						<div :style="{background:getDictCss(levelList, scope.row.eventLevelValue)}">{{ formatDict(levelList, scope.row.eventLevelValue) }}</div>
					</template>
      </el-table-column>
        <el-table-column prop="status" align="center" label="事件状态" width="115">
          <template #default="scope">
            <el-select
              v-model="scope.row.status"
              class="m-2"
              placeholder="状态"
              @change="editStatus(scope.row)"
            >
              <el-option
                v-for="(item, index) in statusList"
                :style="{background:item.cssClass.split(',')[0]}"
                :key="index"
                :label="item.nameCn"
                :value="Number(item.nameEn)"
              >
                <el-tag :type="item.cssClass">{{item.nameCn}}</el-tag>            
            </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          align="center"
          label="创建时间"
          width="168"
        />
        <el-table-column
          prop="endTime"
          align="center"
          label="完成时间"
          width="168"
        />
        <el-table-column align="center" width="210" label="操作">
          <template #default="scope">
            <el-button
              type="text"
              size="default"
              @click="view(scope.row.id, 'video')"
              >详情</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="edit(scope.row.id)"
              :disabled="scope.row.status !== 0"
              v-if="hasPerm('base:governanceEvent:edit')"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="deleted(scope.row.id)"
              :disabled="scope.row.status !== 0"
              v-if="hasPerm('base:governanceEvent:delete')"
              >删除</el-button
            >
            <el-button
              type="text"
              size="default"
              @click="grvernanceLog(scope.row)"
              v-if="scope.row.status == 0 && hasPerm('base:governanceEvent:traceManagement')"
              >追踪管理</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col style="display: flex; justify-content: flex-end; margin-top: 10px">
      <el-pagination
        background
        v-model:page-size="searchModel.pageSize"
        :page-sizes="[12, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :total="Number(total)"
      ></el-pagination>
    </el-col>
  </el-row>
</template>

<script>
import {
  listGovernance,
  getGovernance,
  governanceEventNotRequireMask,
  deleteGovernance,
  batchDeleteGovernance,
  editStatus,
} from "@/api/governance/governanceEvent";
import { listDictByNameEn } from "@/api/admin/dict";
import mitt from "@/utils/mitt";
import { getDictCss, formatDict } from "@/utils/dict";
import governanceEdit from "@/componts/governance/governanceEdit.vue";
import governanceView from "@/componts/governance/governanceView.vue";
import governanceLogEdit from "@/componts/governance/governanceLogEdit.vue";
import { getUserGroup } from "@/api/admin/userGroup";
export default {
  components: {
    governanceEdit,
    governanceLogEdit,
    governanceView
  },
  data() {
    return {
      searchModel: {
        pageSize: 12,
        groupId: JSON.parse(localStorage.getItem("userInfo")).groupId,
      },
      sexList: [],
      governanceList: [],
      statusList: [],
      typeList: [],
      levelList: [],
      total: 0,
      ids: [],
      typeIds: [],
      params: {},
      imgServer: import.meta.env.VITE_BASE_API,
    };
  },
  methods: {
    // 追踪管理
    grvernanceLog(row) {
      const params = {
        id:row.id,
        status:row.status
      }
      mitt.emit("governanceLogEdit", params);
    },
    getDictCss(dicList, cellValue) {
      return getDictCss(dicList, cellValue);
    },
    formatDict(dicList, cellValue) {
      return formatDict(dicList, cellValue);
    },
    formatType(row, column, cellValue, index) {
      return formatDict(this.typeList, cellValue);
    },
    formatLevel(row, column, cellValue, index) {
      return formatDict(this.levelList, cellValue);
    },
    async editStatus(data) {
      this.params.id = data.id;
      this.params.status = data.status;
      editStatus(this.params);
      this.search();
    },
    search() {
      const startTime = new Date(this.searchModel.eventStartTime);
      const endTime = new Date(this.searchModel.eventEndTime);
      if (startTime > endTime) {
        this.$message({
          type: "error",
          message: "开始时间需小于结束时间！",
        });
      } else {
        getUserGroup(localStorage.getItem("groupId"))
          .then((re) => {
            this.searchModel.groupId = re.data.result.parentId;
            this.searchModel.lists = this.typeIds + "";
            listGovernance(this.searchModel).then((res) => {
              this.governanceList = res.data.result.list;
              this.total = res.data.result.total;
            });
          })
          .catch((err) => {});
      }
    },
    // 批量操作
    handleSelectionChange(val) {
      let list = [];
      for (let item of val) {
        list.push(item.id);
      }
      this.ids = list;
    },
    edit(id) {
      getGovernance(id).then((res) => {
        mitt.emit("openGovernanceEdit", res.data.result);
      });
    },
    add() {
      mitt.emit("openGovernanceAdd");
    },
    batchDelete() {
      this.$confirm(`批量删除治理事件, 是否继续? `, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          batchDeleteGovernance(this.ids).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
            this.$refs.multipleTable.clearSelection();
          });
        })
        .catch(() => {});
    },
    deleted(id) {
      this.$confirm("删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteGovernance(id).then((res) => {
            this.search();
            this.$message.success(res.data.msg);
          });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    nextClick(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    handleSizeChange(num) {
      this.searchModel.pageSize = num;
      this.search();
    },
    openFile(row){
      window.open(this.imgServer + row.url)
    },
    // 查看详情
    view(id){
      governanceEventNotRequireMask(id).then((res) => {
        mitt.emit("openGovernanceView", res.data.result);
        console.log(res.data.result);
      });
    },
    async init() {
      mitt.off("quickEntry");
      mitt.off("openGovernanceAdd");
      try {
        let userGroup_res = await getUserGroup(localStorage.getItem("groupId"));
        this.searchModel.groupId = userGroup_res.data.result.parentId;
        let res = await listGovernance(this.searchModel);
        this.governanceList = res.data.result.list;
        this.total = res.data.result.total;
        let governance_status = await listDictByNameEn("governance_status");
        this.statusList = governance_status.data.result;
        this.activeText = this.statusList[0].nameCn;
        this.inactiveText = this.statusList[1].nameCn;
        let governance_type = await listDictByNameEn("governance_type");
        this.typeList = governance_type.data.result;
        let governance_level = await listDictByNameEn("event_level");
        this.levelList = governance_level.data.result;
        let sex_res = await listDictByNameEn("sex");
        this.sexList = sex_res.data.result;
      } catch (err) {
        console.log(err);
      }
    },
  },
  created() {
    this.init();
  },
};
</script>

<style scoped lang="less">
.el-row {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 5px;
}
div /deep/ .el-dialog__body {
  padding: 0 34px;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
</style>
