import request from '@/utils/request'

export const listGovernanceResponsibility = (data) =>
	request({
		url: '/governanceResponsibility/page',
		method: 'get',
		params: data
	})

export const getGovernanceResponsibility = (id) =>
	request({
		url: '/governanceResponsibility/'+id,
		method: 'get'
	})	

export const addGovernanceResponsibility = (data) =>
	request({
		url: '/governanceResponsibility',
		method: 'post',
		data: data
	})

export const editGovernanceResponsibility = (data) =>
	request({
		url: '/governanceResponsibility',
		method: 'put',
		data: data
	})	
export const editEvaluation = (data) =>
	request({
		url: '/governanceResponsibility/editEvaluation',
		method: 'put',
		data: data
	})	

export const deleteGovernanceResponsibility = (id) =>
	request({
		url: '/governanceResponsibility/'+id,
		method: 'delete'
	})	
	