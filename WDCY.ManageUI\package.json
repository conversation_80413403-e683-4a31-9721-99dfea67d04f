{"name": "wdcy", "version": "3.8.2", "scripts": {"dev": "vite --mode dev", "build": "vite build --mode prod", "serve": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^0.2.6", "@turf/turf": "^6.5.0", "axios": "^0.22.0", "cropperjs": "^1.5.12", "echarts": "^5.3.2", "element-plus": "^2.2.9", "encryptlong": "^3.1.4", "flv.js": "^1.5.0", "hls.js": "^1.1.5", "js-base64": "^3.7.5", "jsencrypt": "^3.3.2", "json-editor-vue3": "^1.0.6", "less": "^4.1.3", "marked": "^12.0.1", "mitt": "^3.0.0", "moment": "^2.30.1", "mui-player": "^1.6.0", "nprogress": "^0.2.0", "qrcode": "^1.5.3", "quill": "^1.3.7", "sass": "^1.57.1", "sass-loader": "^13.2.0", "scss": "^0.2.4", "video.js": "^7.18.1", "videojs-contrib-hls": "^5.15.0", "vue": "^3.4.15", "vue-router": "^4.0.4", "vue3-puzzle-vcode": "^1.0.12", "vuex": "^4.1.0", "wangeditor": "^4.7.11", "xlsx": "^0.18.5"}, "devDependencies": {"@types/quill": "^2.0.10", "@vitejs/plugin-vue": "^1.6.0", "@vue/compiler-sfc": "^3.2.6", "element-theme-chalk": "^2.15.6", "vite": "^2.5.2", "vite-plugin-mars3d": "^3.1.3"}}