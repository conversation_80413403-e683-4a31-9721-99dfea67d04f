import request from '@/utils/request'

// 查询主题分页
export function pagingTheme(query) {
  return request({
    url: '/mapi/v3_8_0/assy/theme',
    method: 'get',
    params: query
  })
}

// 查询主题列表
export function listTheme(query) {
  return request({
    url: '/mapi/v3_8_0/assy/theme/list',
    method: 'get',
    params: query
  })
}

// 查询主题详细
export function getTheme(id) {
  return request({
    url: '/mapi/v3_8_0/assy/theme/' + id,
    method: 'get'
  })
}

// 新增主题
export function addTheme(data) {
  return request({
    url: '/mapi/v3_8_0/assy/theme',
    method: 'post',
    data: data
  })
}

// 修改主题
export function updateTheme(data) {
  return request({
    url: '/mapi/v3_8_0/assy/theme',
    method: 'put',
    data: data
  })
}

// 删除主题
export function delTheme(id) {
  return request({
    url: '/mapi/v3_8_0/assy/theme/' + id,
    method: 'delete'
  })
}
