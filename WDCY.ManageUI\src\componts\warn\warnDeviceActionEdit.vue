<template>
	<el-dialog draggable width="50%" destroy-on-close v-loading="loading" v-model="dialog.show" :title="dialog.title">
    	<el-form :rules="rules" :model="conditionModel" label-width="120px">
			<el-row>	
				<el-col :span="18">
					<el-form-item label="预警动作名称" prop="name">
						<el-input v-model="personModel.name" placeholder="预警动作名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="18">
					<el-form-item label="预警参数" prop="expandParams">
						<el-input v-model="personModel.expandParams" type="textarea" placeholder="预警参数"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="end">
			<el-button type="primary" @click="onSubmit">提 交</el-button>
		</el-row>
  </el-dialog>
</template>

<script>
import { warnDeviceActionAdd,warnDeviceActionEdit } from "@/api/warn/warn"
import mitt from "@/utils/mitt";
export default {
	props:['statusList','tagList'],
	data() {
		return {
			loading: false,
			personModel: {},
      		devTypeId:0,
			dialog:{},
			rules: {
				name: [{
					required: true,
					message: '请输入预警动作名称',
					trigger: 'blur',
				}],
			}
		}
	},
	methods: {
		onSubmit(){
			if(this.personModel.tags){
				this.personModel.tags = JSON.stringify(this.personModel.tags)
			}
      this.personModel.devTypeId = this.devTypeId
			if(this.personModel.id == 0){
				warnDeviceActionAdd(this.personModel)
				.then(res =>{
					this.$message.success(res.data.msg)
					this.$emit("search")
					this.dialog.show = false
				})
			}else{
				warnDeviceActionEdit(this.personModel)
				.then(res =>{
					this.$message.success(res.data.msg)
					this.$emit("search")
					this.dialog.show = false
				})
			}
		}
	},
	mounted(){
		this.$nextTick(function() {
			mitt.on('openWarnDeviceActionEdit', (person) => {
        this.devTypeId = person.devTypeId
				this.personModel = person.data
				this.dialog.show = true
				this.dialog.title = "修改"
			})
			mitt.on('openWarnDeviceActionAdd', (person) => {
				this.personModel = {
					id:0
				}
        this.devTypeId = person
				this.dialog.show = true
				this.dialog.title = "添加"
			})
		})
	}
}
</script>
