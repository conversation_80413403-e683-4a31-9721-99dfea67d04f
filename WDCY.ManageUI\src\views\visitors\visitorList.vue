<template>
	<vehicle-detail :statusList = "statusList" :certificateTypeTagList="certificateTypeTagList"></vehicle-detail>
		<people-run></people-run>
		<car-run></car-run>
	<el-row :gutter="20">
		<el-col :span="4" style="display:flex">
			<el-input v-model="searchModel.visitorInfo" @keydown.enter="search" placeholder="访客信息" clearable />
		</el-col>
		<el-col :span="4">
			<el-input v-model="searchModel.ownerInfo" @keydown.enter="search" placeholder="被访信息" clearable />
		</el-col>
		<el-col :span="2">
			<el-select style="width: 100%;" v-model="searchModel.status" placeholder="审核状态" clearable>
				<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn"
					:value="parseInt(item.nameEn)"></el-option>
			</el-select>
		</el-col>
		<el-col :span="4">
			<el-button type="primary" @click="search">搜 索</el-button>
		</el-col>
	</el-row>
	<el-row :gutter="20">
		<el-col :span="24">
		<el-table :data="personList" border height="calc(100vh - 300px)" style="width: 100%">
			<el-table-column prop="visitorName" :formatter="formatName" width="100" align="center" label="访客姓名" />
			<el-table-column prop="visitorCertificateType" :formatter="formatCertificateType" width="100" align="center" label="证件类型" />
			<el-table-column prop="visitorIdCard" width="180" align="center" label="访客证件号" />
			<el-table-column prop="visitorPhone" width="120" align="center" label="访客手机号" />
			<el-table-column prop="plateNo" width="100" :formatter="formatPlateNo" align="center" label="访客车牌号" />
			<el-table-column prop="photo" width="100" align="center" label="访客照片">
				<template #default="scope">

					<el-image preview-teleported fit="contain" style="width: 40px; height: 40px"
						:src="imgServer + scope.row.photo" :preview-src-list="[imgServer + scope.row.photo]">
						<template #error>
							<el-image preview-teleported fit="contain" style="width: 40px; height: 40px"
								:src="personImg" :preview-src-list="[personImg]">
							</el-image>
						</template>
					</el-image>
				</template>
			</el-table-column>
			<el-table-column prop="ownerName" :formatter="formatName" width="100" align="center" label="被访业主" />
			<el-table-column prop="ownerAddress" width="300" align="center" label="拜访地址" />
			<el-table-column prop="createTime" width="163" align="center" label="申请时间" />

			<el-table-column prop="status" align="center" label="审核状态" width="95">
				<template #default="scope">
					<span class="status-tag" :style="'background-color:'+ getDictCss(statusList, scope.row.status)">{{ formatStatus(statusList, scope.row.status) }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="startTime" width="163" align="center" label="剩余时间">
				<template #default="scope">
					<div v-if="hours(scope.row.lastTime) != '已过期'" style="font-size: 16px;color:#0DBF3D">
						{{ hours(scope.row.lastTime) }}
					</div>
					<div class="status-tag" style="background:#a7a4a7" v-else>
						已过期
					</div>
				</template>
			</el-table-column>

			<el-table-column align="center" label="操作">
				<template #default="scope">
					<el-button type="text" size="default" @click="personRecord(scope.row)">人行</el-button>
					<el-button type="text" size="default" @click="vehicleRecord(scope.row)">车行</el-button>
					<el-button type="text" size="default" @click="detail(scope.row)" v-if="hasPerm('base:visitors:detial')">详情</el-button>
				</template>
			</el-table-column>
		</el-table>
		</el-col>
		<el-col style="display: flex;justify-content: flex-end;margin-top: 10px;">
			<el-pagination background v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"  @current-change="currentChange" @size-change="handleSizeChange" :total="Number(total)"></el-pagination>
		</el-col>
	</el-row>
</template>
<script>
import { visitorsRecordList ,visitorstDelete ,getVisitorsDetail} from "@/api/visitor/visitor"
import personImg from "@/assets/img/defaultHead.png"
import { listDictByNameEn } from "@/api/admin/dict"
import { getDictCss, formatDict } from "@/utils/dict"
import vehicleDetail from "@/componts/visitor/vehicleDetail.vue"
import carRun from "@/componts/visitor/carRun.vue"
import peopleRun from "@/componts/visitor/peopleRun.vue"
import mitt from "@/utils/mitt"
export default {
	components:{ vehicleDetail, carRun, peopleRun },
	data() {
		return {
			searchModel: {},
			personList: [],
			communityId: localStorage.getItem("communityId"),
			statusList: [],
			certificateTypeTagList: [],
			tagList:[],
			total:0,
			pageSize:10,
			imgServer: import.meta.env.VITE_BASE_API,
			personImg: personImg
		}
	},
	methods: {
		search() {
			// this.searchModel.communityId=this.communityId
			visitorsRecordList(this.searchModel)
			.then(res => {
				this.personList = res.data.result.list
				this.personList.forEach((e) => {
					this.daojishi(e)
				})
				this.total = res.data.result.total
			})
		},
		getDictCss(dicList, cellValue) {
			return getDictCss(dicList, cellValue)
		}, 
		formatDict(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatStatus(dicList, cellValue) {
			return formatDict(dicList, cellValue)
		},
		formatCertificateType(row, column, cellValue, index){
			return formatDict(this.certificateTypeTagList, cellValue)
		},
		formatPlateNo(row, column, cellValue, index){
			if (cellValue) {
				if (cellValue.length == 8) {
					const beginNum = String(cellValue).substring(0,2)
					const endNum = String(cellValue).substring(6)
					return beginNum + '***' + endNum
				} else if (cellValue.length == 7) {
					const beginNum = String(cellValue).substring(0,2)
					const endNum = String(cellValue).substring(5)
					return beginNum + '***' + endNum
				}
			}
		},
		formatName(row, column, cellValue, index){
			if (cellValue) {
				if (cellValue.length == 2) {
					const xing = String(cellValue).substring(0,1)
					return xing + '*'
				} else if ( cellValue.length == 3 ) {
					const xing = String(cellValue).substring(0,1)
					const ming = String(cellValue).substring(2,3)
					return xing + '*' + ming
				} else if(cellValue.length>3){
					return cellValue.substring(0,1)+"*"+'*'+cellValue.substring(3,cellValue.length)
				}	
			}
		},
		personRecord(res){
			mitt.emit("peopleRun",res)
		},
		vehicleRecord(res){
			mitt.emit("carRun",res)
		},
		detail(row){
				mitt.emit('openVehicleRecordDetail', row)
		},
		deleted(id){
			this.$confirm('删除访客, 是否继续?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					visitorstDelete(id)
					.then(res =>{
						this.search()
						this.$message.success(res.data.msg)
					})
				}).catch(()=>{})
		},
		currentChange(num){
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num){
			this.searchModel.pageNum = num
			this.search()
		},
		handleSizeChange(num){
			this.searchModel.pageSize = num
			this.search()
		},

		// 格式化时间
		hours(times){
			if (times) {
				let days = this.addZero(
					Math.floor(times / 1000 / 60 / 60 / 24)
				)
				let hours = this.addZero(
					Math.floor((times / 1000 / 60 / 60) % 24)
				)
				let minutes = this.addZero(
					Math.floor((times / 1000 / 60 ) % 60)
				)
				let seconds = this.addZero(
					Math.floor((times / 1000 ) % 60)
				)
				return days +"天"+ hours +':'+ minutes + ':'+ seconds
			} else {
				return "已过期"
			}
		},
		// 补零
		addZero(d){
			return parseInt(d) < 10 ? "0" + d : d
		},
		// 倒计时
		daojishi(row){
			row.countDown = setInterval(() => {
				let residue = new Date(row.visitEndTime).getTime() - new Date()
				if (residue > 0) {
					row.lastTime = residue
				} else {
					clearInterval(row.countDown)
				}
			}, 1000);
		},
		async init(){
			try{
				this.searchModel.communityId=this.communityId
				let res = await visitorsRecordList(this.searchModel)
				this.personList = res.data.result.list
				this.personList.forEach((e) => {
					this.daojishi(e)
				})
				this.total = res.data.result.total
				let status = await listDictByNameEn('visit_status')
				this.statusList = status.data.result
				let certificateType_res = await listDictByNameEn('certificate_type')
				this.certificateTypeTagList = certificateType_res.data.result
			}catch(err){
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
	.el-row {
		margin-bottom: 20px;
		background-color: #fff;
		padding: 20px 10px;
		border-radius: 5px;
	}
	.status-tag {
		display: inline-block;
		margin-bottom: 5px;
		margin-right: 5px;
		width: 59px;
		border-radius: 10px;
		color: #fff;
		font-size: 12px;
		line-height: 14px;
		font-family: Microsoft YaHei;
		padding: 5px 5px 5px 5px;

	}
</style>
