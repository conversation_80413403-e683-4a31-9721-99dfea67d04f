/**
 * Mars3D平台插件,结合mapv可视化功能插件  mars3d-mapv
 *
 * 版本信息：v3.7.22
 * 编译日期：2024-07-15 21:21:06
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2024-01-15
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mapv || require('mapv')), (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mapv', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-mapv"] = {}, global.mapv, global.mars3d));
})(this, (function (exports, mapv, mars3d) { 
'use strict';(function(_0x4460fe,_0x4d9acb){const _0x3718c7={_0x1579b6:0x96,_0x2522b4:0xd6,_0x353bc7:0xd0,_0x55ec4b:0xfa,_0x136341:0x526,_0x559794:0x83,_0x99c870:0x47,_0xb69e5:0x57a},_0x3c5d52=_0x4460fe();function _0x24202a(_0x4cbbdf,_0x5ad8ff){return _0x1e3d(_0x4cbbdf-0x353,_0x5ad8ff);}function _0x325f19(_0x3d97e7,_0xd950a9){return _0x1e3d(_0x3d97e7- -0x17b,_0xd950a9);}while(!![]){try{const _0x2b2a79=parseInt(_0x24202a(0x58b,0x595))/0x1+-parseInt(_0x325f19(0xb4,0xab))/0x2*(parseInt(_0x325f19(_0x3718c7._0x1579b6,_0x3718c7._0x2522b4))/0x3)+parseInt(_0x325f19(0x5d,0x64))/0x4+parseInt(_0x325f19(_0x3718c7._0x353bc7,_0x3718c7._0x55ec4b))/0x5*(parseInt(_0x24202a(_0x3718c7._0x136341,0x55f))/0x6)+-parseInt(_0x325f19(0x7c,0x83))/0x7+-parseInt(_0x325f19(_0x3718c7._0x559794,_0x3718c7._0x99c870))/0x8*(-parseInt(_0x24202a(0x546,_0x3718c7._0xb69e5))/0x9)+parseInt(_0x24202a(0x567,0x552))/0xa;if(_0x2b2a79===_0x4d9acb)break;else _0x3c5d52['push'](_0x3c5d52['shift']());}catch(_0x1f1402){_0x3c5d52['push'](_0x3c5d52['shift']());}}}(_0x3eb0,0xab53c));function _0x3eb0(){const _0x4e6a58=['zhjHD0nVBNrLEhq','rxzLBNruExbL','mtiYnZiYndDptK5SELC','zgvMAw5LuhjVCgvYDhK','z2XVyMu','ywrKrgf0yq','nJaYmtu2oePIBg9wEG','Bw91C2vnB3zL','Aw5PDerLDMLJzvbPEgvSuMf0Aw8','Aw5PDa','C3rLChm','z2v0rxH0zw50','x29Utw92zvn0yxj0rxzLBNq','oen3teLVza','BwfWDKrLChrOvgvZDa','x2nYzwf0zunHBNzHCW','zMLSBfn0EwXL','C3r5Bgu','D2LKDgG','Cg9PBNrLCKv2zw50CW','BwfYCZnKlw1HChy','zgvWDgHuzxn0','zgvMAw5Lza','C2v0','txvSDgLmAw5Lu3rYAw5N','CMDIysGWlcaWlcaWlcaUmsK','DxbKyxrLq2fSBgjHy2S','CMvTB3zLqwXSrgf0yq','x29UtwfWq2XPy2S','x2fKzgvKsg9VAW','y2XLyxjeyxrH','x19LC01VzhvSzq','oxPjExf2AG','y2XPy2S','Bw91C2veB3DU','nduXmZm2mg1tvhDdyG','DhjHBNnMzxjdB29YzgLUyxrL','AxnfBMfIBgvKvgLTzq','CMvUzgvY','yxjNq2HLy2S','Bwv0Ag9KCW','Aw5PDefUAw1HDg9Y','y2fUDMfZtgf5zxi','ChjVy2vZC0rHDge','yxv0BW','zhjHDW','DhLWzq','CMvTB3zLq2HPBgq','y2X1C3rLCL9Pza','z2v0uMvJDgfUz2XL','x29Utw92zuvUzev2zw50','x2nHBNzHC1vWzgf0zq','Bw9Kzq','x21HCfzszw5KzxjLCG','y2fTzxjHtw92zuvUza','yw5PBwf0B3jnB3zLC3rHCNrfDMvUDa','B3b0Aw9UCW','Bw91C2vTB3zL','DxbKyxrL','DxbKyxrLrgf0yq','yMXVy2S','mhb4','odiWmdeYsvv4suLk','y2X1C3rLCKrHDgftzxq','AgvPz2H0','C2nLBMu','rgf0yvnLDa','ChvZAa','zgvZDgLUyxrPB24TB3v0','BwLUu2L6zq','EKLUzgv4','mZa5ndf2yw54vuC','z2v0q29SB3i','CMvTB3zLrgf0yq','y3jLyxrL','x2nHy2HLx2v2zw50','y2fUDMfZ','tgLUzvn0CMLUzW','txvSDgLqB2X5z29U','z2v0rgf0yq','u2nLBMvnB2rL','x2rHDge','C3vWzxjJBhvZDgvY','zgvMyxvSDa','Cg9PBNrFy291BNq','q2vZAxvT','zgf0yvnLDa','z2v0q29UDgv4Da','q09mt1jFqLvgrKvsx0jjva','C3rVCefUAwfTyxrPB24','mtvSEfHoDKi','BgvMDa','C2L6zq','x21VDw50zwriB29R','zwXSAxbZB2LK','Ew1HEa','yMLUza','Eg1HEa','BwfWDKzPEgvKsgvPz2H0','x3nOB3DiB29R','zgf0yq','Bgf5zxi','nJCWndCWwfvTwvH4','y2fYDgvZAwfUvg9dyw52yxndB29YzgLUyxrLCW','zgv2AwnLugL4zwXsyxrPBW','y29UDgfPBMvY','y2XLyxjszwn0','mJq0mZyXmNLvEvfmEa','B2zM','Cg9PBNrdB3vUDe1PBG','Cg9PBNrdB3vUDe1HEa','CMvTB3zL','Bwf4','x3jLC2v0','x3bVAw50zxjfDMvUDhm','z2v0','Ew1PBG','y2fTzxjH','ywjZB2X1Dgu','zNjVBurLz3jLzxm','zNvUy3rPB24','Cg9ZDfjLBMrLCG','Cg9ZAxrPB24','y2X1C3rLCG','Bwf4u2L6zq','CgfYzw50rwXLBwvUDa','yw5PBwf0B3jnB3zLzw5KrxzLBNq','BgvUz3rO','x21HCa','C3rHCNq','y2XLyxi','BwfW'];_0x3eb0=function(){return _0x4e6a58;};return _0x3eb0();}function _interopNamespace(_0x1bfe9a){const _0x43f593={_0x955d3f:0x167,_0x1360d0:0x14e},_0x54b671={_0x3048cc:0xa9},_0x2ecd47={_0x3adfbd:0x5f3,_0x2ec896:0x5c8},_0x1817ca={_0x56a1c1:0x1fb};if(_0x1bfe9a&&_0x1bfe9a[_0x7c0517(_0x43f593._0x955d3f,_0x43f593._0x1360d0)])return _0x1bfe9a;var _0x247314=Object['create'](null);function _0x52c20b(_0x1ebe9c,_0x27d53f){return _0x1e3d(_0x27d53f-_0x1817ca._0x56a1c1,_0x1ebe9c);}_0x1bfe9a&&Object['keys'](_0x1bfe9a)['forEach'](function(_0x28059d){const _0x48fa5d={_0x55b1ef:0x47d};function _0x27d940(_0x4c4af5,_0x46a5a4){return _0x7c0517(_0x46a5a4-_0x48fa5d._0x55b1ef,_0x4c4af5);}if(_0x28059d!=='default'){var _0x169268=Object['getOwnPropertyDescriptor'](_0x1bfe9a,_0x28059d);Object[_0x27d940(_0x2ecd47._0x3adfbd,_0x2ecd47._0x2ec896)](_0x247314,_0x28059d,_0x169268['get']?_0x169268:{'enumerable':!![],'get':function(){return _0x1bfe9a[_0x28059d];}});}});_0x247314[_0x7c0517(0x19b,0x15c)]=_0x1bfe9a;function _0x7c0517(_0x3c9200,_0x54d7da){return _0x1e3d(_0x3c9200- -_0x54b671._0x3048cc,_0x54d7da);}return _0x247314;}var mapv__namespace=_interopNamespace(mapv),mars3d__namespace=_interopNamespace(mars3d);const Cesium$1=mars3d__namespace[_0x3d626f(0x3ba,0x3b5)],baiduMapLayer=mapv__namespace?mapv__namespace['baiduMapLayer']:null,BaseLayer$1=baiduMapLayer?baiduMapLayer['__proto__']:Function;class MapVRenderer extends BaseLayer$1{constructor(_0x57859e,_0x3580d9,_0x253d8a,_0x3fcd4d){const _0x3c080c={_0x4eafcc:0x34b,_0x5d7cdc:0x3f0,_0x4ab878:0x3db,_0x31c0da:0x3ab},_0x1edd2d={_0x2a697e:0x22};super(_0x57859e,_0x3580d9,_0x253d8a);if(!BaseLayer$1)return;this[_0x237447(0x381,_0x3c080c._0x4eafcc)]=_0x57859e,this['scene']=_0x57859e[_0x237447(0x3c3,_0x3c080c._0x5d7cdc)],this['dataSet']=_0x3580d9,_0x253d8a=_0x253d8a||{};function _0x237447(_0x3cdfe8,_0x4c8a81){return _0x3d626f(_0x4c8a81,_0x3cdfe8-_0x1edd2d._0x2a697e);}function _0x2d45d5(_0x1a8c4b,_0x88b050){return _0x3d626f(_0x88b050,_0x1a8c4b- -0x543);}this['init'](_0x253d8a),this[_0x2d45d5(-0x1bc,-0x198)](_0x253d8a),this['initDevicePixelRatio'](),this['canvasLayer']=_0x3fcd4d,this[_0x237447(_0x3c080c._0x4ab878,_0x3c080c._0x31c0da)]=!0x1,this['animation']=_0x253d8a['animation'];}['initDevicePixelRatio'](){this['devicePixelRatio']=window['devicePixelRatio']||0x1;}['addAnimatorEvent'](){}[_0x3d626f(0x399,0x397)](){const _0x515d85={_0x4a2b05:0x4d8,_0x2634f9:0x47e,_0x2732b7:0x49d},_0x537359={_0x5b81e5:0x33c};function _0x7900cc(_0x258475,_0xb31b5e){return _0x4ecc02(_0x258475,_0xb31b5e-_0x537359._0x5b81e5);}function _0x5af476(_0x39cbad,_0x571bb2){return _0x3d626f(_0x39cbad,_0x571bb2-0x264);}const _0x166550=this[_0x7900cc(0x4e5,_0x515d85._0x4a2b05)]['animation'];this['isEnabledTime']()&&this['animator']&&(this[_0x7900cc(_0x515d85._0x2634f9,0x4aa)]['step']=_0x166550['stepsRange'][_0x7900cc(0x4c3,_0x515d85._0x2732b7)]);}[_0x3d626f(0x32e,0x35a)](){const _0x6968fc={_0x467b09:0x42a};function _0x372dd3(_0x3db372,_0x4d8a2a){return _0x4ecc02(_0x3db372,_0x4d8a2a-_0x6968fc._0x467b09);}this[_0x372dd3(0x571,0x5b3)]()&&this['animator'];}[_0x3d626f(0x3f8,0x3b7)](){const _0x337c51={_0x2f0f33:0x39};function _0x305b4e(_0x560a89,_0x19d3df){return _0x3d626f(_0x19d3df,_0x560a89- -0x391);}return this[_0x305b4e(-0x7,_0x337c51._0x2f0f33)]['canvas']['getContext'](this['context']);}[_0x4ecc02(0x135,0x16d)](_0x51c793){const _0x569463={_0x40b995:0x199,_0x15f199:0x11a,_0x11ab57:0x118,_0x543019:0x19a,_0x1c9abc:0x1c9,_0x2354a9:0x129},_0xf02203={_0xf447fd:0x4b2};this['options']=_0x51c793;function _0x5b69b7(_0x1c88cd,_0x31825e){return _0x3d626f(_0x31825e,_0x1c88cd- -_0xf02203._0xf447fd);}function _0x4fa9ff(_0x2cd3da,_0x3074b6){return _0x4ecc02(_0x3074b6,_0x2cd3da-0x1f);}this['initDataRange'](_0x51c793),this['context']=this['options']['context']||'2d',Cesium$1[_0x4fa9ff(_0x569463._0x40b995,0x16a)](this[_0x5b69b7(-_0x569463._0x15f199,-_0x569463._0x11ab57)]['zIndex'])&&this[_0x4fa9ff(0x1ad,_0x569463._0x543019)]&&this[_0x5b69b7(-0x128,-0x166)]['setZIndex']&&this['canvasLayer']['setZIndex'](this['options'][_0x4fa9ff(_0x569463._0x1c9abc,0x18c)]),this[_0x5b69b7(-_0x569463._0x2354a9,-0x121)]();}[_0x3d626f(0x3a6,0x393)](_0x5b0a86){const _0x19c627={_0x5af4b8:0x1ce,_0x385227:0x291,_0x230116:0x28c,_0x534c71:0x257,_0x3d28e9:0x249,_0x2319d7:0x28f,_0x56bc42:0x192,_0x4342dd:0x1a6,_0x79df50:0x25d,_0x3f0528:0x297,_0x57f35d:0x229,_0x4337fc:0x235,_0x338719:0x1d4,_0x42051a:0x262,_0x12125e:0x17a,_0x55cd61:0x1ba,_0x13b213:0x1fb,_0x2e2aeb:0x283,_0x20386c:0x172,_0x2e149b:0x1ca,_0x402a9f:0x1d9,_0xdfeb07:0x1d6,_0x556d47:0x1c1,_0x5e3c8b:0x183,_0x927f46:0x283,_0x27dd95:0x2c0,_0x1587f5:0x169,_0x23d3b5:0x2a8,_0xc54a47:0x28d,_0x7cad52:0x265,_0x49f334:0x19c},_0x4e8e24={_0x4941c:0x6f,_0x1a2f1d:0x39a,_0x27c75c:0x361};if(!this[_0x16bf4f(0x1ac,_0x19c627._0x5af4b8)]||this['stopAniamation'])return;const _0xe7cb6b=this[_0x1f334a(_0x19c627._0x385227,_0x19c627._0x230116)],_0x46e248=this['options']['animation'],_0x1e220c=this[_0x16bf4f(0x1d9,0x1fd)]();if(this['isEnabledTime']()){if(void 0x0===_0x5b0a86)return void this[_0x1f334a(_0x19c627._0x534c71,_0x19c627._0x3d28e9)](_0x1e220c);this['context']==='2d'&&(_0x1e220c['save'](),_0x1e220c['globalCompositeOperation']=_0x1f334a(0x261,_0x19c627._0x2319d7),_0x1e220c[_0x16bf4f(_0x19c627._0x56bc42,0x173)]=_0x16bf4f(0x19b,0x18f),_0x1e220c['fillRect'](0x0,0x0,_0x1e220c[_0x16bf4f(0x1ce,_0x19c627._0x4342dd)][_0x1f334a(0x261,_0x19c627._0x79df50)],_0x1e220c[_0x1f334a(0x279,_0x19c627._0x3f0528)]['height']),_0x1e220c['restore']());}else this[_0x16bf4f(0x180,0x1b3)](_0x1e220c);if(this['context']==='2d')for(const _0x5a3811 in this['options']){_0x1e220c[_0x5a3811]=this['options'][_0x5a3811];}else _0x1e220c['clear'](_0x1e220c[_0x16bf4f(0x1da,0x1bb)]);const _0x2701ef={'transferCoordinate':function(_0xc89209){const _0x1242ec=null;let _0x671b16=_0xe7cb6b[_0x52c314(0x3f8,0x3ce)];function _0x52c314(_0x5ef3b5,_0x3d2b5b){return _0x16bf4f(_0x5ef3b5-0x214,_0x3d2b5b);}_0xe7cb6b['mapvAutoHeight']&&(_0x671b16=_0xe7cb6b['getHeight'](Cesium$1['Cartographic']['fromDegrees'](_0xc89209[0x0],_0xc89209[0x1])));const _0x15dc61=Cesium$1['Cartesian3']['fromDegrees'](_0xc89209[0x0],_0xc89209[0x1],_0x671b16);function _0x2597e9(_0x4ab57c,_0x288ba0){return _0x16bf4f(_0x4ab57c- -0x163,_0x288ba0);}if(!_0x15dc61)return _0x1242ec;const _0x2f6a3a=_0xe7cb6b['cartesianToCanvasCoordinates'](_0x15dc61);if(!_0x2f6a3a)return _0x1242ec;if(_0xe7cb6b['mapvDepthTest']&&_0xe7cb6b[_0x52c314(0x3ca,0x3e0)]===Cesium$1[_0x2597e9(_0x4e8e24._0x4941c,0x8d)]['SCENE3D']){const _0x3a9653=new Cesium$1['EllipsoidalOccluder'](_0xe7cb6b[_0x52c314(_0x4e8e24._0x1a2f1d,0x3d5)][_0x2597e9(0x7d,0xa4)],_0xe7cb6b[_0x52c314(0x387,_0x4e8e24._0x27c75c)]['positionWC']),_0x23eaaa=_0x3a9653['isPointVisible'](_0x15dc61);if(!_0x23eaaa)return _0x1242ec;}return[_0x2f6a3a['x'],_0x2f6a3a['y']];}};void 0x0!==_0x5b0a86&&(_0x2701ef['filter']=function(_0xf40074){const _0x598e12=_0x46e248['trails']||0xa;return!!(_0x5b0a86&&_0xf40074['time']>_0x5b0a86-_0x598e12&&_0xf40074['time']<_0x5b0a86);});function _0x16bf4f(_0x51538e,_0x4f77a0){return _0x3d626f(_0x4f77a0,_0x51538e- -0x1de);}let _0x315558;if(this['options'][_0x1f334a(0x252,0x278)]===_0x1f334a(0x217,0x242)&&(!this['options']['maxClusterZoom']||this['options']['maxClusterZoom']>=this['getZoom']())){this['map'][_0x16bf4f(0x18d,0x1bc)]();const _0x358d92=this['getZoom'](),_0x55b185=this['supercluster']['getClusters']([-0xb4,-0x5a,0xb4,0x5a],_0x358d92);this[_0x1f334a(_0x19c627._0x57f35d,_0x19c627._0x4337fc)]=this[_0x16bf4f(_0x19c627._0x338719,0x196)]['trees'][_0x358d92]['max'],this[_0x1f334a(_0x19c627._0x42051a,0x234)]=this['supercluster']['trees'][_0x358d92]['min'];let _0x486f94={},_0x5e23ea=null,_0x1885e3=null;if(this[_0x1f334a(0x247,0x235)]===this['pointCountMin'])_0x5e23ea=this['options'][_0x1f334a(0x274,0x25b)],_0x1885e3=this['options'][_0x1f334a(0x298,0x290)]||0x8;else{const _0x3714d8={};_0x3714d8['min']=this['pointCountMin'],_0x3714d8[_0x16bf4f(0x16e,_0x19c627._0x12125e)]=this['pointCountMax'],_0x3714d8['minSize']=this[_0x16bf4f(_0x19c627._0x55cd61,_0x19c627._0x13b213)]['minSize']||0x8,_0x3714d8[_0x16bf4f(0x17a,0x193)]=this[_0x1f334a(0x29a,_0x19c627._0x2e2aeb)]['maxSize']||0x1e,_0x3714d8['gradient']=this[_0x1f334a(0x2bf,_0x19c627._0x2e2aeb)]['gradient'],_0x486f94=new mapv__namespace['utilDataRangeIntensity'](_0x3714d8);}for(let _0x4c9c07=0x0;_0x4c9c07<_0x55b185['length'];_0x4c9c07++){const _0x3e5215=_0x55b185[_0x4c9c07];_0x3e5215['properties']&&_0x3e5215['properties'][_0x16bf4f(0x1b2,_0x19c627._0x20386c)]?(_0x55b185[_0x4c9c07]['size']=_0x1885e3||_0x486f94['getSize'](_0x3e5215['properties']['point_count']),_0x55b185[_0x4c9c07]['fillStyle']=_0x5e23ea||_0x486f94[_0x16bf4f(_0x19c627._0x2e149b,_0x19c627._0x402a9f)](_0x3e5215['properties'][_0x16bf4f(_0x19c627._0xdfeb07,0x1ee)])):_0x55b185[_0x4c9c07][_0x16bf4f(0x1de,0x1d9)]=this['options'][_0x16bf4f(0x1de,_0x19c627._0x556d47)];}this['clusterDataSet']['set'](_0x55b185),_0x315558=this[_0x16bf4f(0x1c1,_0x19c627._0x5e3c8b)]['get'](_0x2701ef);}else _0x315558=this['dataSet']['get'](_0x2701ef);function _0x1f334a(_0x59ef1d,_0x1ec2a2){return _0x4ecc02(_0x59ef1d,_0x1ec2a2-0xe7);}this[_0x16bf4f(0x1ad,0x1ca)](_0x315558);this['options']['unit']==='m'&&this['options']['size']&&(this[_0x1f334a(0x2ab,_0x19c627._0x927f46)]['_size']=this['options'][_0x1f334a(_0x19c627._0x27dd95,0x2a7)]);const _0x3d9e8c=_0xe7cb6b[_0x1f334a(0x25a,0x22e)](Cesium$1['Cartesian3'][_0x1f334a(0x20f,0x23e)](0x0,0x0));if(!_0x3d9e8c)return;this[_0x16bf4f(0x182,_0x19c627._0x1587f5)](_0x1e220c,new mapv__namespace[(_0x1f334a(_0x19c627._0x23d3b5,_0x19c627._0xc54a47))](_0x315558),this['options'],_0x3d9e8c),this['options'][_0x1f334a(0x223,_0x19c627._0x7cad52)]&&this['options'][_0x16bf4f(_0x19c627._0x49f334,0x1a2)](_0x5b0a86);}[_0x4ecc02(0x181,0x19f)](_0x57bd9b,_0xd9db53){const _0x48ac3c={_0x96f2b:0x2bd,_0x52193f:0x308},_0x34e21a={_0x474a7c:0xec};let _0x10668b=_0x57bd9b;_0x10668b&&_0x10668b['get']&&(_0x10668b=_0x10668b[_0x10f611(0x29c,_0x48ac3c._0x96f2b)]()),void 0x0!==_0x10668b&&this['dataSet']['set'](_0x10668b);const _0x4724af={};function _0x10f611(_0x41ce5b,_0x510b84){return _0x3d626f(_0x41ce5b,_0x510b84- -0x92);}function _0x322dc7(_0xcabbb2,_0x2bb0c2){return _0x3d626f(_0x2bb0c2,_0xcabbb2- -_0x34e21a._0x474a7c);}_0x4724af['options']=_0xd9db53,super[_0x10f611(0x323,_0x48ac3c._0x52193f)](_0x4724af);}['addData'](_0x278ddd,_0x1f51d1){const _0x46c54a={_0x5127d6:0x1c1},_0x5c1579={_0x44225b:0x17e};let _0x5cf579=_0x278ddd;function _0x31ba8a(_0x363b2e,_0x5685a6){return _0x4ecc02(_0x363b2e,_0x5685a6-_0x5c1579._0x44225b);}const _0x440ddf={};_0x440ddf[_0x31ba8a(0x35a,0x31a)]=_0x1f51d1;function _0x4d2747(_0x305b55,_0x1d5796){return _0x4ecc02(_0x1d5796,_0x305b55-0x4f);}_0x278ddd&&_0x278ddd[_0x31ba8a(0x2f2,0x2d1)]&&(_0x5cf579=_0x278ddd[_0x4d2747(0x1a2,_0x46c54a._0x5127d6)]()),this[_0x4d2747(0x209,0x1dc)]['add'](_0x5cf579),this[_0x31ba8a(0x34f,0x31c)](_0x440ddf);}['getData'](){return this['dataSet'];}[_0x4ecc02(0x199,0x1ad)](_0x2859dd){const _0x40dbf5={_0x4db8e8:0x474,_0xc6a4a8:0x19a,_0x3899a2:0x18f},_0x2fc07f={_0x17691e:0x2bf},_0x29d694={_0x52433d:0x47c},_0x178b17={_0x269467:0x511};function _0x473d4f(_0xd5134d,_0x25dd5c){return _0x4ecc02(_0xd5134d,_0x25dd5c-0x2bf);}function _0x5a561d(_0x50b366,_0x3264de){return _0x3d626f(_0x3264de,_0x50b366- -_0x178b17._0x269467);}if(this['dataSet']){const _0x4b210b=this[_0x5a561d(-0x15b,-0x12f)]['get']({'filter':function(_0x22d850){function _0x1e3d01(_0x37b0be,_0x2bccb9){return _0x5a561d(_0x2bccb9-_0x29d694._0x52433d,_0x37b0be);}return _0x2859dd==null||typeof _0x2859dd!==_0x1e3d01(0x2ad,_0x2fc07f._0x17691e)||!_0x2859dd(_0x22d850);}});this[_0x473d4f(_0x40dbf5._0x4db8e8,0x479)][_0x5a561d(-_0x40dbf5._0xc6a4a8,-_0x40dbf5._0x3899a2)](_0x4b210b);const _0xe4b991={};_0xe4b991[_0x5a561d(-0x179,-0x149)]=null,this[_0x5a561d(-0x177,-0x162)](_0xe4b991);}}[_0x3d626f(0x393,0x37e)](){this['dataSet']&&this['dataSet']['clear']();const _0x18ad37={};_0x18ad37[_0x4bcac0(0x5d8,0x5a4)]=null;function _0x4bcac0(_0x484227,_0x2d4985){return _0x3d626f(_0x2d4985,_0x484227-0x240);}this['update'](_0x18ad37);}[_0x3d626f(0x37e,0x38d)](){const _0x5201c9={_0x5b5963:0x21b};function _0x5ec3e3(_0x2358fa,_0x1cbca3){return _0x4ecc02(_0x2358fa,_0x1cbca3-0x8d);}this[_0x5ec3e3(0x22e,_0x5201c9._0x5b5963)]['draw']();}[_0x4ecc02(0x157,0x162)](_0x47f461){const _0x50d292={_0x964bb3:0x449,_0x520a5b:0x44b};function _0xc8903f(_0x3c39ad,_0x5ef2c6){return _0x4ecc02(_0x5ef2c6,_0x3c39ad-0x2e);}function _0x25f051(_0x454584,_0x305045){return _0x4ecc02(_0x305045,_0x454584-0x2ff);}_0x47f461&&_0x47f461[_0x25f051(_0x50d292._0x964bb3,0x45c)]&&_0x47f461['clearRect'](0x0,0x0,_0x47f461['canvas'][_0x25f051(0x475,_0x50d292._0x520a5b)],_0x47f461['canvas']['height']);}['getZoom'](){return this['map']['level'];}['destroy'](){this[_0x1ea9e3(0x16f,0x19f)](this['getContext']()),this['clearData'](),this['animator']&&this['animator']['stop'](),this['animator']=null;function _0x1ea9e3(_0x4a21b2,_0x39fcb8){return _0x4ecc02(_0x4a21b2,_0x39fcb8-0x3d);}this['canvasLayer']=null;}}if(mapv__namespace!==null&&mapv__namespace!==void 0x0&&mapv__namespace['DataSet'])mapv__namespace['DataSet']['prototype'][_0x3d626f(0x352,0x384)]=function(_0x4f7bef,_0x455461,_0x567b7f,_0x545b4d){const _0x27d610={_0x211029:0x54d,_0x436aa3:0x53d},_0x1d2f3b={_0x5b53cf:0x2dc},_0x26a53b={_0x472f23:0x4e7};function _0x5c4f7e(_0x27a85a,_0x2d05db){return _0x3d626f(_0x27a85a,_0x2d05db- -_0x26a53b._0x472f23);}_0x545b4d=_0x545b4d||'_coordinates',_0x567b7f=_0x567b7f||'coordinates';for(let _0xe2c5fc=0x0;_0xe2c5fc<_0x4f7bef['length'];_0xe2c5fc++){const _0x10ce0f=_0x4f7bef[_0xe2c5fc]['geometry'],_0x5960ac=_0x10ce0f[_0x567b7f];switch(_0x10ce0f['type']){case'Point':{const _0x2767f9=_0x455461(_0x5960ac);_0x2767f9?_0x10ce0f[_0x545b4d]=_0x2767f9:_0x10ce0f[_0x545b4d]=[-0x3e7,-0x3e7];}break;case _0x5c4f7e(-0xf8,-0x13a):{const _0x6a314a=[];for(let _0x487822=0x0;_0x487822<_0x5960ac['length'];_0x487822++){const _0x42ec4c=_0x455461(_0x5960ac[_0x487822]);_0x42ec4c&&_0x6a314a['push'](_0x42ec4c);}_0x10ce0f[_0x545b4d]=_0x6a314a;}break;case _0x233077(_0x27d610._0x211029,0x545):case'Polygon':{const _0x39e769=_0x5c6710(_0x5960ac);_0x10ce0f[_0x545b4d]=_0x39e769;}break;case _0x5c4f7e(-0x162,-0x139):{const _0x5d70c0=[];for(let _0x44c00a=0x0;_0x44c00a<_0x5960ac[_0x233077(_0x27d610._0x436aa3,0x528)];_0x44c00a++){const _0x12f5b0=_0x5c6710(_0x5960ac[_0x44c00a]);_0x12f5b0['length']>0x0&&_0x5d70c0['push'](_0x12f5b0);}_0x10ce0f[_0x545b4d]=_0x5d70c0;}break;}}function _0x233077(_0x49d01e,_0x2615f7){return _0x4ecc02(_0x49d01e,_0x2615f7-0x3c9);}function _0x5c6710(_0x204207){function _0x3a0c9c(_0x4c3d6e,_0x5a0bc4){return _0x5c4f7e(_0x4c3d6e,_0x5a0bc4-0x420);}const _0x14b308=[];for(let _0x238fda=0x0;_0x238fda<_0x204207['length'];_0x238fda++){const _0x4b649f=_0x204207[_0x238fda],_0x5d5ce0=[];for(let _0x1ff834=0x0;_0x1ff834<_0x4b649f['length'];_0x1ff834++){const _0x1851ce=_0x455461(_0x4b649f[_0x1ff834]);_0x1851ce&&_0x5d5ce0['push'](_0x1851ce);}_0x5d5ce0['length']>0x0&&_0x14b308[_0x3a0c9c(0x2da,_0x1d2f3b._0x5b53cf)](_0x5d5ce0);}return _0x14b308;}return _0x4f7bef;};else throw new Error('请引入\x20mapv\x20库\x20');const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace[_0x3d626f(0x336,0x341)]['BaseLayer'];class MapVLayer extends BaseLayer{constructor(_0x3db2d5,_0x5043b6){const _0x1bbcce={_0x2ed2f8:0x7f,_0x4a2c45:0xbb,_0x14c297:0x3c0,_0x1c1ff2:0x3d0};super(_0x3db2d5),this['_pointerEvents']=this['options'][_0x22f60f(-0xba,-0xea)];function _0x22f60f(_0x47537c,_0x23017b){return _0x4ecc02(_0x47537c,_0x23017b- -0x261);}function _0x17f5c8(_0x5c33a6,_0xae08de){return _0x3d626f(_0xae08de,_0x5c33a6- -0x4);}this[_0x22f60f(-_0x1bbcce._0x2ed2f8,-0xa7)]=_0x5043b6||new mapv__namespace[(_0x22f60f(-0xba,-_0x1bbcce._0x4a2c45))](_0x3db2d5[_0x17f5c8(_0x1bbcce._0x14c297,_0x1bbcce._0x1c1ff2)]),this[_0x17f5c8(0x3a8,0x398)]=null;}get['pointerEvents'](){return this['_pointerEvents'];}set['pointerEvents'](_0x3a20d4){const _0x3ffbf7={_0x5c09ad:0xa6,_0x521a6c:0xe5,_0x2a941a:0x104,_0x3e7ec1:0xf4},_0x29e933={_0x50f261:0x2a8};function _0x368553(_0x40a4c6,_0x596947){return _0x3d626f(_0x40a4c6,_0x596947-0x6f);}function _0xcb41e(_0x4b96ac,_0x75ee72){return _0x3d626f(_0x75ee72,_0x4b96ac- -_0x29e933._0x50f261);}this[_0xcb41e(_0x3ffbf7._0x5c09ad,_0x3ffbf7._0x521a6c)]=_0x3a20d4,this[_0xcb41e(_0x3ffbf7._0x2a941a,0xe2)]&&(_0x3a20d4?this['canvas']['style']['pointerEvents']='all':this[_0xcb41e(0x104,_0x3ffbf7._0x3e7ec1)][_0x368553(0x3ff,0x3e0)]['pointerEvents']='none');}[_0x4ecc02(0x1a6,0x1c7)](_0xb6abfe){function _0x1a9b13(_0x1e7722,_0x37e728){return _0x3d626f(_0x37e728,_0x1e7722- -0x504);}_0xb6abfe?this['canvas']['style']['display']=_0x1a9b13(-0x168,-0x137):this['canvas']['style']['display']='none';}[_0x4ecc02(0x1ab,0x1c1)](){const _0x137a9e={_0x2f74a4:0x58d,_0x528ada:0x3bc,_0x40edfa:0x3cc,_0x1e9693:0x3db,_0x4da204:0x3a9,_0x1998bb:0x5d2,_0x394ec8:0x5d2,_0x54f2c4:0x59c},_0x49f450={_0x3a1f4d:0x24a},_0x16edda={_0x5eeda0:0x231};function _0x376807(_0x503e7f,_0x290a77){return _0x3d626f(_0x290a77,_0x503e7f-_0x16edda._0x5eeda0);}this[_0x376807(_0x137a9e._0x2f74a4,0x56e)]['scene'][_0x4282c1(_0x137a9e._0x528ada,0x37c)]=this[_0x4282c1(0x3e6,_0x137a9e._0x40edfa)][_0x4282c1(0x3c3,_0x137a9e._0x1e9693)]??!![],this[_0x4282c1(0x3aa,_0x137a9e._0x4da204)][_0x376807(_0x137a9e._0x1998bb,0x5b8)]['mapvAutoHeight']=this['options']['clampToGround']??![];function _0x4282c1(_0x1fd703,_0x132b41){return _0x4ecc02(_0x132b41,_0x1fd703-_0x49f450._0x3a1f4d);}this['_map'][_0x376807(_0x137a9e._0x394ec8,_0x137a9e._0x54f2c4)]['mapvFixedHeight']=this[_0x4282c1(0x3e6,0x3cf)]['fixedHeight']??0x0;}['_addedHook'](){const _0xe5a07b={_0x4a9da7:0x167,_0x5aab08:0x131,_0x3bab67:0x197,_0x4aa600:0x167,_0x430955:0x146,_0x387a59:0xe9,_0x15692e:0x120,_0x41539f:0x96},_0x19ea03={_0x52f5d8:0x24f},_0x2152c9={_0x4153c7:0xf4};function _0x315a40(_0x37b6b4,_0x5601e6){return _0x4ecc02(_0x37b6b4,_0x5601e6- -_0x2152c9._0x4153c7);}this[_0xcd9c6c(0x14b,_0xe5a07b._0x4a9da7)]&&(!this[_0xcd9c6c(_0xe5a07b._0x5aab08,0x167)][_0x315a40(0xe0,0xc1)]||this[_0xcd9c6c(_0xe5a07b._0x3bab67,0x167)]['_data'][_0xcd9c6c(0xd3,0x10c)]===0x0)&&(this['dataSet'][_0xcd9c6c(0x19d,0x162)]=[]['concat'](this['dataSet']['_dataCache']));function _0xcd9c6c(_0x22f5c6,_0x59b284){return _0x3d626f(_0x22f5c6,_0x59b284- -_0x19ea03._0x52f5d8);}this[_0xcd9c6c(_0xe5a07b._0x4aa600,_0xe5a07b._0x430955)]=new MapVRenderer(this['_map'],this[_0x315a40(0xc0,0xc6)],this['options'],this),this['initDevicePixelRatio'](),this['canvas']=this[_0xcd9c6c(_0xe5a07b._0x387a59,_0xe5a07b._0x15692e)](),this['render']=this[_0x315a40(0x5d,_0xe5a07b._0x41539f)]['bind'](this),this['bindEvent'](),this['_reset']();}['_removedHook'](){const _0x509a74={_0x46ae33:0x169};this['unbindEvent']();this['_mapVRenderer']&&(this[_0x254ece(0x194,0x18f)]['destroy'](),this['_mapVRenderer']=null);function _0x254ece(_0x108db0,_0x1d37d9){return _0x3d626f(_0x108db0,_0x1d37d9- -0x206);}function _0x5565fe(_0x228345,_0x38b116){return _0x3d626f(_0x228345,_0x38b116- -0x32b);}this['canvas'][_0x254ece(0x148,0x153)][_0x254ece(_0x509a74._0x46ae33,0x189)](this['canvas']);}[_0x4ecc02(0x135,0x16c)](){const _0x597e39={_0x1267e0:0x10f};function _0x597f01(_0x57e236,_0x4c078c){return _0x4ecc02(_0x57e236,_0x4c078c- -0x257);}this['devicePixelRatio']=window[_0x597f01(-0x120,-_0x597e39._0x1267e0)]||0x1;}['bindEvent'](){const _0x300b98={_0x132f65:0x2ca,_0x20cbe1:0x2bf,_0x35e8cf:0x2a,_0xb74552:0x51,_0x36d44e:0x38,_0x4a7d26:0x64,_0x9b2b51:0x15};var _0x33aae1,_0x292bdf;function _0x4a00e5(_0xf34b96,_0x3cbf67){return _0x4ecc02(_0xf34b96,_0x3cbf67-0x16a);}this[_0x4a00e5(0x2cc,_0x300b98._0x132f65)]['on'](mars3d__namespace['EventType'][_0x4a00e5(0x2f0,0x2f0)],this[_0x4a00e5(0x2bf,0x2da)],this),this[_0x4a00e5(_0x300b98._0x20cbe1,_0x300b98._0x132f65)]['on'](mars3d__namespace['EventType']['cameraMoveStart'],this['_onMoveStartEvent'],this),this['_map']['on'](mars3d__namespace['EventType'][_0x20002e(-_0x300b98._0x35e8cf,-_0x300b98._0xb74552)],this['_onMoveEndEvent'],this);(_0x33aae1=this['options'])!==null&&_0x33aae1!==void 0x0&&(_0x33aae1=_0x33aae1[_0x20002e(-_0x300b98._0x36d44e,-0x7a)])!==null&&_0x33aae1!==void 0x0&&_0x33aae1['click']&&this[_0x20002e(-_0x300b98._0x4a7d26,-0x3e)]['on'](mars3d__namespace['EventType'][_0x20002e(-0x3f,-0x1)],this['_onMapClick'],this);function _0x20002e(_0x3835f6,_0x1af98a){return _0x4ecc02(_0x1af98a,_0x3835f6- -0x1c4);}(_0x292bdf=this['options'])!==null&&_0x292bdf!==void 0x0&&(_0x292bdf=_0x292bdf[_0x20002e(-0x38,-0x30)])!==null&&_0x292bdf!==void 0x0&&_0x292bdf[_0x20002e(-0x27,_0x300b98._0x9b2b51)]&&this['_map']['on'](mars3d__namespace['EventType']['mouseMove'],this['_onMapMouseMove'],this);}['unbindEvent'](){const _0x14621f={_0x1dc49e:0x14e,_0x147354:0x18a,_0x5c3549:0x198,_0x39a314:0x148,_0x40e701:0x7b,_0x5eb7ca:0x58,_0x120ae8:0x174,_0x57cc5c:0x9e,_0x546aa4:0x14d};function _0x280992(_0x8f8f2,_0x5123cb){return _0x4ecc02(_0x8f8f2,_0x5123cb- -0x18);}function _0x17d248(_0x4a9e77,_0x5502f4){return _0x4ecc02(_0x5502f4,_0x4a9e77- -0x10a);}var _0x4cbb1c,_0x289440;this['_map']['off'](mars3d__namespace['EventType'][_0x280992(_0x14621f._0x1dc49e,0x16e)],this['_onMoveStartEvent'],this),this[_0x280992(0x181,0x148)]['off'](mars3d__namespace['EventType']['cameraMoveStart'],this['_onMoveStartEvent'],this),this[_0x280992(_0x14621f._0x147354,0x148)]['off'](mars3d__namespace['EventType']['cameraMoveEnd'],this['_onMoveEndEvent'],this),this['_map']['off'](mars3d__namespace['EventType']['postRender'],this[_0x17d248(0x47,0x5a)],this),(_0x4cbb1c=this['options'])!==null&&_0x4cbb1c!==void 0x0&&(_0x4cbb1c=_0x4cbb1c['methods'])!==null&&_0x4cbb1c!==void 0x0&&_0x4cbb1c[_0x280992(_0x14621f._0x5c3549,0x16d)]&&this[_0x280992(0x13f,_0x14621f._0x39a314)]['off'](mars3d__namespace['EventType'][_0x17d248(_0x14621f._0x40e701,_0x14621f._0x5eb7ca)],this['_onMapClick'],this),(_0x289440=this['options'])!==null&&_0x289440!==void 0x0&&(_0x289440=_0x289440[_0x280992(0x13b,_0x14621f._0x120ae8)])!==null&&_0x289440!==void 0x0&&_0x289440[_0x17d248(0x93,_0x14621f._0x57cc5c)]&&this['_map']['off'](mars3d__namespace[_0x280992(0x117,_0x14621f._0x546aa4)]['mouseMove'],this['_onMapMouseMove'],this);}['_onMoveStartEvent'](){const _0x5d845a={_0x2067e5:0x2a,_0x2524b0:0x23},_0x5235c6={_0x257443:0x272};function _0x3fa700(_0xb35bb9,_0x2a615b){return _0x3d626f(_0xb35bb9,_0x2a615b-_0x5235c6._0x257443);}function _0xd9316a(_0x4681b4,_0x2e0fd1){return _0x3d626f(_0x4681b4,_0x2e0fd1- -0x36b);}this[_0xd9316a(0x38,_0x5d845a._0x2067e5)]&&(this['_mapVRenderer'][_0xd9316a(-0x16,0x2c)](),this['_map'][_0xd9316a(-0x1c,-_0x5d845a._0x2524b0)](mars3d__namespace[_0xd9316a(-0x32,-0xa)][_0x3fa700(0x5c1,0x5c7)],this['_reset'],this),this[_0xd9316a(-0x1b,-0xf)]['on'](mars3d__namespace['EventType'][_0xd9316a(-0x5,-0x16)],this['_reset'],this));}[_0x3d626f(0x3d2,0x392)](){const _0x5c3e5e={_0x2e09e4:0x4de},_0x38f846={_0x34b4e7:0x45d};function _0x1ea921(_0x1691f0,_0x446a07){return _0x3d626f(_0x446a07,_0x1691f0-0x182);}function _0x55607a(_0x57cde7,_0x130ec5){return _0x3d626f(_0x130ec5,_0x57cde7- -_0x38f846._0x34b4e7);}this['_mapVRenderer']&&(this[_0x1ea921(0x4de,_0x5c3e5e._0x2e09e4)]['off'](mars3d__namespace['EventType']['postRender'],this['_reset'],this),this[_0x55607a(-0xc8,-0xf7)]['animatorMoveendEvent'](),this['_reset']());}['_setOptionsHook'](_0x37cd5c,_0x27cd99){this['_removedHook']();function _0x8b6838(_0x3db58b,_0x39343c){return _0x4ecc02(_0x39343c,_0x3db58b- -0xf0);}this[_0x8b6838(0x91,0x65)]();}[_0x3d626f(0x38d,0x365)](_0xd29f50){const _0x51efc8={_0x3e7e21:0x345,_0x57cff6:0x34e},_0x194747={_0x530848:0x1ac};function _0x45e998(_0x7109c9,_0x2b6d11){return _0x3d626f(_0x7109c9,_0x2b6d11-0xb6);}function _0x200536(_0x1062e0,_0x35a51b){return _0x4ecc02(_0x35a51b,_0x1062e0-_0x194747._0x530848);}this['_mapVRenderer']&&this[_0x200536(_0x51efc8._0x3e7e21,0x34a)][_0x200536(0x315,_0x51efc8._0x57cff6)](_0xd29f50,this['options']);}[_0x3d626f(0x35c,0x39b)](_0x52c336){const _0x4c877b={_0xfc01cc:0x445,_0x548a0e:0x44b,_0x109905:0x43a},_0x9a1378={_0x212069:0xb0};function _0x2548a5(_0x25dcc2,_0x2b1bcd){return _0x3d626f(_0x2b1bcd,_0x25dcc2-_0x9a1378._0x212069);}function _0x430a8e(_0x51ba32,_0x19d493){return _0x4ecc02(_0x19d493,_0x51ba32- -0x302);}this[_0x2548a5(_0x4c877b._0xfc01cc,0x475)]&&this[_0x430a8e(-0x169,-0x193)][_0x2548a5(_0x4c877b._0x548a0e,0x47c)](_0x52c336,this[_0x2548a5(0x448,_0x4c877b._0x109905)]);}['getData'](){const _0x534aae={_0x461db7:0x2f1,_0x50ee0e:0x303,_0x299610:0x2cf};this[_0x48f022(_0x534aae._0x461db7,0x2b9)]&&(this['dataSet']=this[_0x48f022(_0x534aae._0x461db7,_0x534aae._0x50ee0e)][_0x48f022(0x30b,_0x534aae._0x299610)]());function _0x366cee(_0x2a3eba,_0x2aab9a){return _0x4ecc02(_0x2aab9a,_0x2a3eba-0x2ab);}function _0x48f022(_0x1ba887,_0x29c947){return _0x4ecc02(_0x29c947,_0x1ba887-0x158);}return this['dataSet'];}[_0x3d626f(0x398,0x3a9)](_0x70c0f3){const _0x54f1b7={_0x158285:0x12},_0x101cb8={_0x2bdf90:0x187};function _0x35ba4a(_0x565ed1,_0x27bc37){return _0x4ecc02(_0x27bc37,_0x565ed1- -_0x101cb8._0x2bdf90);}function _0x56223d(_0x1b7965,_0x11dd7b){return _0x3d626f(_0x11dd7b,_0x1b7965- -0x47);}this[_0x56223d(0x34e,0x321)]&&this[_0x35ba4a(_0x54f1b7._0x158285,-0x15)][_0x56223d(0x362,0x372)](_0x70c0f3);}[_0x3d626f(0x34f,0x37b)](){this['_mapVRenderer']&&this['_mapVRenderer']['clearData']();}['_createCanvas'](){const _0x2520eb={_0xcb8e6b:0x1ba,_0x3f43dc:0x4f0,_0x58acdc:0x4dd,_0x2f0d0b:0x4f3,_0x25bf22:0x15c,_0x2e2619:0x506,_0x1ecf72:0x512,_0x51e288:0x4da,_0x3cd1ad:0x4fb,_0x12c6c9:0x4db,_0x3a28f0:0x17f,_0x3e8c06:0x4c5,_0xe6d025:0x526,_0x19a354:0x17d,_0x1b5216:0x19a,_0x90e370:0x1dd,_0x250f3e:0x1a6,_0x467a3a:0x50f,_0x78d05f:0x4a2},_0x5fb1d=mars3d__namespace['DomUtil'][_0x3b132c(0x53e,0x513)](_0x22e21e(_0x2520eb._0xcb8e6b,0x1ba),_0x3b132c(_0x2520eb._0x3f43dc,_0x2520eb._0x58acdc),this[_0x3b132c(_0x2520eb._0x2f0d0b,0x4c5)][_0x3b132c(0x46f,0x4ae)]);_0x5fb1d['id']=this['id'];function _0x22e21e(_0x49536f,_0x598112){return _0x3d626f(_0x49536f,_0x598112- -0x1f2);}_0x5fb1d['style'][_0x3b132c(0x4f0,0x4bf)]=_0x22e21e(_0x2520eb._0x25bf22,0x160),_0x5fb1d[_0x22e21e(0x1a2,0x17f)]['top']=_0x3b132c(0x50c,_0x2520eb._0x2e2619),_0x5fb1d[_0x3b132c(_0x2520eb._0x1ecf72,_0x2520eb._0x51e288)][_0x3b132c(_0x2520eb._0x3cd1ad,0x524)]='0px',_0x5fb1d[_0x3b132c(0x4ac,_0x2520eb._0x12c6c9)]=parseInt(this['_map']['canvas'][_0x3b132c(0x4a5,0x4db)]),_0x5fb1d['height']=parseInt(this['_map']['canvas']['height']);function _0x3b132c(_0x35999b,_0x5423c5){return _0x3d626f(_0x35999b,_0x5423c5-0x169);}_0x5fb1d[_0x22e21e(0x1ac,_0x2520eb._0x3a28f0)][_0x22e21e(0x163,0x180)]=this[_0x3b132c(0x4f2,_0x2520eb._0x3e8c06)]['canvas']['style'][_0x3b132c(0x516,0x4db)],_0x5fb1d[_0x3b132c(0x4b7,0x4da)]['height']=this[_0x22e21e(0x166,0x16a)]['canvas']['style'][_0x3b132c(_0x2520eb._0xe6d025,0x509)],_0x5fb1d[_0x22e21e(0x1bc,0x17f)]['pointerEvents']=this[_0x3b132c(0x4c5,0x4b7)]?_0x22e21e(_0x2520eb._0x19a354,_0x2520eb._0x1b5216):'none',_0x5fb1d[_0x3b132c(0x4ea,0x4da)]['zIndex']=this[_0x22e21e(_0x2520eb._0x90e370,_0x2520eb._0x250f3e)][_0x3b132c(0x52a,_0x2520eb._0x467a3a)]??0x9;if(this['options']['context']==='2d'){const _0x38481d=this[_0x3b132c(_0x2520eb._0x78d05f,0x4ad)];_0x5fb1d['getContext'](this['options']['context'])['scale'](_0x38481d,_0x38481d);}return _0x5fb1d;}['_reset'](){this['resize'](),this['render']();}['draw'](){this['_reset']();}[_0x4ecc02(0x16a,0x14f)](){const _0x5000c2={_0x545612:0x195,_0x393f8a:0x15a};function _0x5cae73(_0x1e91a3,_0x302c76){return _0x4ecc02(_0x1e91a3,_0x302c76- -0x3);}function _0x81694d(_0x1a3882,_0x57f116){return _0x4ecc02(_0x1a3882,_0x57f116- -0x1f6);}this['_mapVRenderer']&&(this[_0x5cae73(_0x5000c2._0x545612,0x196)]['destroy'](),this['_mapVRenderer']=null),this['canvas'][_0x5cae73(0x12c,_0x5000c2._0x393f8a)]['removeChild'](this['canvas']);}['render'](){this['_mapVRenderer']['_canvasUpdate']();}['resize'](){const _0x419c04={_0x364e33:0x587,_0x826003:0x552,_0x3fbd4d:0x4d4,_0x5a570a:0x563,_0x2abc3d:0x561,_0x39a9cf:0xb2,_0x1ce88f:0x58b,_0x2bf195:0x9d,_0x264a38:0xe3,_0x1f86b9:0x502,_0x1b3e91:0xf2};function _0x1ec954(_0x5ef740,_0x5bc23b){return _0x3d626f(_0x5ef740,_0x5bc23b-0x1a6);}function _0x2cb7ed(_0x268c90,_0xd67d3){return _0x3d626f(_0xd67d3,_0x268c90- -0x2eb);}if(this['canvas']){const _0x36f9f7=this[_0x1ec954(_0x419c04._0x364e33,_0x419c04._0x826003)];_0x36f9f7['style'][_0x2cb7ed(0x6b,0x39)]=_0x1ec954(_0x419c04._0x3fbd4d,0x4f8),_0x36f9f7['style']['top']='0px',_0x36f9f7['style'][_0x1ec954(_0x419c04._0x5a570a,_0x419c04._0x2abc3d)]=_0x2cb7ed(_0x419c04._0x39a9cf,0x81),_0x36f9f7['width']=parseInt(this['_map'][_0x1ec954(_0x419c04._0x1ce88f,_0x419c04._0x826003)][_0x2cb7ed(0x87,_0x419c04._0x2bf195)]),_0x36f9f7[_0x1ec954(0x53b,0x546)]=parseInt(this[_0x2cb7ed(0x71,0xaa)][_0x1ec954(0x58e,0x552)][_0x2cb7ed(0xb5,_0x419c04._0x264a38)]),_0x36f9f7['style'][_0x2cb7ed(0x87,0x64)]=this[_0x1ec954(0x4f2,_0x419c04._0x1f86b9)][_0x1ec954(0x530,0x552)]['style']['width'],_0x36f9f7[_0x2cb7ed(0x86,0x94)]['height']=this['_map'][_0x1ec954(0x55e,_0x419c04._0x826003)]['style'][_0x2cb7ed(0xb5,_0x419c04._0x1b3e91)];}}[_0x3d626f(0x367,0x391)](_0x28a4a8){const _0x3b2dea={_0x5cfb54:0x4eb,_0x46ed3a:0x408,_0x4c4339:0x4ed,_0x498d98:0x4f4},_0x5e0179={_0x37e4e7:0x51},_0x5b0fbd={_0x5d6078:0x135};if(!this['dataSet']||!this[_0x449b43(0x4e3,_0x3b2dea._0x5cfb54)]['_data'])return;const _0x48b5c3={};_0x48b5c3[_0x344d6d(_0x3b2dea._0x46ed3a,0x3df)]='FeatureCollection',_0x48b5c3['features']=this[_0x449b43(0x522,0x4eb)]['_data'];function _0x449b43(_0x776b46,_0xe170a8){return _0x3d626f(_0x776b46,_0xe170a8-_0x5b0fbd._0x5d6078);}const _0x47c97f=mars3d__namespace['Util']['getExtentByGeoJSON'](_0x48b5c3);function _0x344d6d(_0x382a05,_0x29c7c4){return _0x3d626f(_0x382a05,_0x29c7c4-_0x5e0179._0x37e4e7);}if(!_0x47c97f)return;return _0x28a4a8!==null&&_0x28a4a8!==void 0x0&&_0x28a4a8['isFormat']?_0x47c97f:Cesium['Rectangle']['fromDegrees'](_0x47c97f['xmin'],_0x47c97f[_0x449b43(0x455,0x485)],_0x47c97f[_0x344d6d(0x408,0x412)],_0x47c97f[_0x449b43(_0x3b2dea._0x4c4339,_0x3b2dea._0x498d98)]);}['_onMapClick'](_0x12d728){const _0x5ae491={_0x2fc7c9:0x196};this[_0x40b803(-0x1b2,-_0x5ae491._0x2fc7c9)]=_0x12d728;function _0x40b803(_0x5e4a37,_0xcfa34c){return _0x3d626f(_0x5e4a37,_0xcfa34c- -0x541);}this['_mapVRenderer']&&this['_mapVRenderer']['clickEvent'](_0x12d728['windowPosition'],_0x12d728);}['_onMapMouseMove'](_0x1a43e0){const _0x30a8af={_0x563087:0x4a3};this['_cache_event']=_0x1a43e0;function _0x4b3295(_0x28638c,_0x4de29d){return _0x4ecc02(_0x28638c,_0x4de29d-0x30a);}this['_mapVRenderer']&&this[_0x4b3295(0x47c,_0x30a8af._0x563087)]['mousemoveEvent'](_0x1a43e0['windowPosition'],_0x1a43e0);}['on'](_0x5ad219,_0xe2a060,_0x33896c){const _0x1b87f5={_0x56c26b:0x28d,_0x3dffe6:0x288,_0x408ee2:0x273,_0x267782:0x54,_0x17bf09:0x92,_0x4fd9c4:0x93,_0x1683cc:0x51},_0x4a61ad={_0x3ed1b9:0xf4},_0x4a73ac={_0x1b4a33:0x2b6,_0x536a16:0x36d,_0x3420f:0x386},_0x1e29a7={_0x2b1ce2:0x149,_0x201481:0x125,_0xaa55b4:0x199};this['options']['methods']=this['options']['methods']||{};if(_0x5ad219===mars3d__namespace['EventType'][_0x47aa2d(_0x1b87f5._0x56c26b,0x2aa)])this['options']['methods']['click']=_0x5162c4=>{const _0x1e6b11={_0x2a89fa:0x104};function _0x47407c(_0x519796,_0x3c4330){return _0x47aa2d(_0x519796- -_0x1e6b11._0x2a89fa,_0x3c4330);}function _0x51a845(_0x53b579,_0x285c04){return _0x47aa2d(_0x53b579- -0x133,_0x285c04);}if(_0x5162c4){const _0x55759b={...this['_cache_event']};_0x55759b[_0x47407c(_0x1e29a7._0x2b1ce2,_0x1e29a7._0x201481)]=this,_0x55759b['data']=_0x5162c4,_0xe2a060[_0x51a845(_0x1e29a7._0xaa55b4,0x1a2)](_0x33896c)(_0x55759b);}},this[_0x34922a(0x27,0x56)]['on'](mars3d__namespace['EventType']['click'],this[_0x47aa2d(_0x1b87f5._0x3dffe6,0x298)],this);else _0x5ad219===mars3d__namespace['EventType'][_0x47aa2d(_0x1b87f5._0x408ee2,0x2b4)]&&(this[_0x34922a(_0x1b87f5._0x267782,_0x1b87f5._0x17bf09)]['methods'][_0x34922a(0xd5,_0x1b87f5._0x4fd9c4)]=_0x3ceee1=>{function _0x552959(_0x484eb6,_0x3679b7){return _0x34922a(_0x484eb6,_0x3679b7- -0x1b);}function _0x4c4c3a(_0x3df28b,_0x1cd52b){return _0x34922a(_0x1cd52b,_0x3df28b-0x2af);}if(_0x3ceee1){const _0x2b4276={...this['_cache_event']};_0x2b4276[_0x4c4c3a(0x2ea,_0x4a73ac._0x1b4a33)]=this,_0x2b4276[_0x4c4c3a(_0x4a73ac._0x536a16,_0x4a73ac._0x3420f)]=_0x3ceee1,_0xe2a060['bind'](_0x33896c)(_0x2b4276);}},this['_map']['on'](mars3d__namespace[_0x34922a(0x5e,0x5b)][_0x34922a(_0x1b87f5._0x1683cc,0x61)],this['_onMapMouseMove'],this));function _0x47aa2d(_0x346bfb,_0x902d38){return _0x3d626f(_0x902d38,_0x346bfb- -_0x4a61ad._0x3ed1b9);}function _0x34922a(_0x15ba8b,_0x34f4d6){return _0x4ecc02(_0x15ba8b,_0x34f4d6- -0x10a);}return this;}[_0x4ecc02(0x184,0x14c)](_0x347acd,_0x46bde2){const _0x4c02b6={_0x2c505c:0x3c6,_0x12b50b:0x3a1,_0x22badd:0x54a,_0x14cca5:0x590};if(_0x347acd===_0x12c427(_0x4c02b6._0x2c505c,0x3f0)){var _0x495eaa;this[_0x12c427(_0x4c02b6._0x12b50b,0x3af)]['off'](_0x347acd,this[_0x3616e9(_0x4c02b6._0x22badd,0x584)],this),(_0x495eaa=this['options']['methods'])!==null&&_0x495eaa!==void 0x0&&_0x495eaa['mousemove']&&delete this['options'][_0x3616e9(0x5bb,0x590)]['click'];}else{if(_0x347acd===_0x12c427(0x3ac,0x3a9)){var _0x2b171a;this['_map']['off'](_0x347acd,this['_onMapMouseMove'],this),(_0x2b171a=this['options']['methods'])!==null&&_0x2b171a!==void 0x0&&_0x2b171a['mousemove']&&delete this['options'][_0x3616e9(0x5a8,_0x4c02b6._0x14cca5)]['mousemove'];}}function _0x3616e9(_0x44cefe,_0x37e9e4){return _0x3d626f(_0x44cefe,_0x37e9e4-0x208);}function _0x12c427(_0x603d38,_0x5856e6){return _0x3d626f(_0x5856e6,_0x603d38-0x45);}return this;}}function _0x1e3d(_0x173996,_0x173232){const _0x3eb0d7=_0x3eb0();return _0x1e3d=function(_0x1e3df3,_0x1c02a6){_0x1e3df3=_0x1e3df3-0x1d2;let _0x3b90b0=_0x3eb0d7[_0x1e3df3];if(_0x1e3d['huOQSo']===undefined){var _0xebec9a=function(_0x2e0d0a){const _0x563df3='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';let _0x25e456='',_0x635af7='';for(let _0x5ebe84=0x0,_0x50501b,_0x137458,_0x28fc76=0x0;_0x137458=_0x2e0d0a['charAt'](_0x28fc76++);~_0x137458&&(_0x50501b=_0x5ebe84%0x4?_0x50501b*0x40+_0x137458:_0x137458,_0x5ebe84++%0x4)?_0x25e456+=String['fromCharCode'](0xff&_0x50501b>>(-0x2*_0x5ebe84&0x6)):0x0){_0x137458=_0x563df3['indexOf'](_0x137458);}for(let _0x56f366=0x0,_0x1bfe9a=_0x25e456['length'];_0x56f366<_0x1bfe9a;_0x56f366++){_0x635af7+='%'+('00'+_0x25e456['charCodeAt'](_0x56f366)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x635af7);};_0x1e3d['YDeVuk']=_0xebec9a,_0x173996=arguments,_0x1e3d['huOQSo']=!![];}const _0x57e403=_0x3eb0d7[0x0],_0x1bdde9=_0x1e3df3+_0x57e403,_0x5cd020=_0x173996[_0x1bdde9];return!_0x5cd020?(_0x3b90b0=_0x1e3d['YDeVuk'](_0x3b90b0),_0x173996[_0x1bdde9]=_0x3b90b0):_0x3b90b0=_0x5cd020,_0x3b90b0;},_0x1e3d(_0x173996,_0x173232);}mars3d__namespace['LayerUtil']['register']('mapv',MapVLayer);function _0x4ecc02(_0x185604,_0x347e26){return _0x1e3d(_0x347e26- -0x8d,_0x185604);}mars3d__namespace['layer']['MapVLayer']=MapVLayer,mars3d__namespace['mapv']=mapv__namespace,exports['MapVLayer']=MapVLayer;function _0x3d626f(_0xb16038,_0x57e5d4){return _0x1e3d(_0x57e5d4-0x16f,_0xb16038);}Object['keys'](mapv)['forEach'](function(_0x115401){const _0x7832d3={_0x165f7b:0x10c,_0x2e964f:0x128};function _0x92a836(_0x17c250,_0x3efd31){return _0x4ecc02(_0x3efd31,_0x17c250- -0x5b);}if(_0x115401!=='default'&&!exports['hasOwnProperty'](_0x115401))Object[_0x92a836(_0x7832d3._0x165f7b,_0x7832d3._0x2e964f)](exports,_0x115401,{'enumerable':!![],'get':function(){return mapv[_0x115401];}});});const _0x56f366={};_0x56f366['value']=!![],Object['defineProperty'](exports,'__esModule',_0x56f366);
}));
